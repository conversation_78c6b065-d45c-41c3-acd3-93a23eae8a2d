<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zatech.genesis</groupId>
		<artifactId>openapi-platform</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>openapi-platform-bootstrap</artifactId>
	<packaging>jar</packaging>
	<name>openapi-platform-bootstrap</name>
	<description>The OpenApi Platform Applicaton Bootstrap Module</description>

	<dependencies>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-exception</artifactId>
			<version>${portal-toolbox-version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-security-inbuilt</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>genesis-fusion-util</artifactId>
					<groupId>com.zatech</groupId>
				</exclusion>
				<exclusion>
					<artifactId>swagger-models</artifactId>
					<groupId>io.swagger.core.v3</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-web</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-modelmapping</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-application-scenario-instance</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-mock-inbuilt</artifactId>
			<version>${project.version}</version>
		</dependency>
        <dependency>
            <groupId>com.zhongan.multitenancy</groupId>
            <artifactId>tenant-trace-spring-boot-starter</artifactId>
        </dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>genesis-spring-boot-starter-logging-desensitization</artifactId>
		</dependency>
		<!-- Load Plugin Here -->
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-application-scenario-schema-plugin-nano</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>swagger-core</artifactId>
					<groupId>io.swagger.core.v3</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-knowledge-procedure-plugin-nano</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-knowledge-procedure-plugin-nano-legacy</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-security-plugin-nano</artifactId>
			<version>${project.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.zatech.genesis</groupId>-->
<!--			<artifactId>openapi-knowledge-procedure-plugin-nano-jp-tenant</artifactId>-->
<!--			<version>DEVELOP-NANOJP-SNAPSHOT</version>-->
<!--&lt;!&ndash;			<version>2.84.x-mt30-NANOJP-SNAPSHOT</version>&ndash;&gt;-->
<!--		</dependency>-->
    </dependencies>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.json</include>
				</includes>
				<filtering>false</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>3.5.6</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<layout>ZIP</layout>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptorRefs>
						<descriptorRef>jar-with-dependencies</descriptorRef>
					</descriptorRefs>
					<archive>
						<manifest>
							<mainClass>com.zatech.genesis.openapi.platform.OpenapiApplication</mainClass>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestEntries>
							<Add-Exports>java.base/sun.reflect.generics.reflectiveObjects</Add-Exports>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.zatech</groupId>
				<artifactId>openapi-maven-plugin</artifactId>
				<configuration>
					<skipSwaggerGeneration>false</skipSwaggerGeneration>
					<apiSources>
						<apiSource>
							<!-- 必须配置-->
							<locations>
								<location>com.zatech.genesis.openapi.platform.web</location>
							</locations>
							<info>  <!-- 必须配置-->
								<title>genesis-openapi-platform</title>
								<description>
									Genesis Openapi platform Open API
								</description>
							</info>

						</apiSource>

						<apiSource>
							<locations>com.zatech.genesis.openapi.platform.integration.outer</locations>
							<feignClientApi>true</feignClientApi>
							<info>
								<title>genesis-openapi-platform-feign-client</title>
								<description>
									Genesis Openapi platform feign client API
								</description>
							</info>
						</apiSource>
					</apiSources>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>
