spring:
  ai:
    mcp:
      server:
        enabled: true
        protocol: STATELESS
        name: Open Api MCP Server
        type: SYNC
        streamable-http:
          mcp-endpoint: /mcp
  jpa:
    show-sql: true
  shardingsphere:
    datasource:
      names: "test-mds-0"

octopus:
  governance:
    limiter:
      enabled: false
      excludeUrlPattern: "/(mgmt/v1|openapi).*"
  mq:
    kafka:
      producer:
        - name: procedureDataProducer
          topic: ${za.graphene.kafka.topic.openapi-platform}
          server-url: ${kafka.server.config}
        - name: claimResultDataProducer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
        - name: policyResultDataProducer
          topic: ${za.graphene.kafka.topic.policy}
          server-url: ${kafka.server.config}
        - name: posResultDataProducer
          topic: ${za.graphene.kafka.topic.posonline}
          server-url: ${kafka.server.config}
        - name: notificationProducer
          topic: ${za.graphene.kafka.topic.notification-customized}
          server-url: ${kafka.server.config}
        - name: flowDataProducer
          topic: ${za.graphene.kafka.topic.openapi-platform-flow-data}
          server-url: ${kafka.server.config}
        - name: workOrderDataProducer
          topic: ${za.graphene.kafka.topic.openapi-platform-work-order}
          server-url: ${kafka.server.config}
      consumer:
        - name: procedureDateConsumer
          topic: ${za.graphene.kafka.topic.openapi-platform}
          server-url: ${kafka.server.config}
          group-id: procedure
          allow-consumer-tag: '*'
        - name: claimResultConsumer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks-felton
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-claim-reject-case,${DEPLOY_ENV}-${projectName}-claim-settle-case,${DEPLOY_ENV}-${projectName}-claim-withdraw-case
        - name: posResultConsumer
          topic: ${za.graphene.kafka.topic.posonline}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks-felton
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-pos-online-case-status-change
        - name: createIssuanceResultConsumer
          topic: ${za.graphene.kafka.topic.policy}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks
          allow-consumer-tag: ${DEPLOY_ENV}-za-graphene-policy-effectivePolicy
        - name: createProposalResultConsumer
          topic: ${za.graphene.kafka.topic.issuance}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks
          allow-consumer-tag: ${DEPLOY_ENV}-za-graphene-issuance-insure
        - name: underwritingResultConsumer
          topic: ${za.graphene.kafka.topic.underwriting}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks
          allow-consumer-tag: ${DEPLOY_ENV}-za-graphene-underwriting-decision
        - name: claimApplicationResultConsumer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
          group-id: openapi-hooks-application
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-claim-application-withdraw, ${DEPLOY_ENV}-${projectName}-claim-finish-application, ${DEPLOY_ENV}-${projectName}-claim-finsish-registration
        - name: claimCaseProcessConsumer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
          group-id: openapi-hooks-case
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-claim-reject-case,${DEPLOY_ENV}-${projectName}-claim-settle-case,${DEPLOY_ENV}-${projectName}-claim-withdraw-case, ${DEPLOY_ENV}-${projectName}-claim-case-status-change,${DEPLOY_ENV}-${projectName}-claim-finish-assessment,${DEPLOY_ENV}-${projectName}-claim-finish-verification,${DEPLOY_ENV}-${projectName}-claim-finish-acceptance
        - name: flowDataConsumer
          topic: ${za.graphene.kafka.topic.openapi-platform-flow-data}
          server-url: ${kafka.server.config}
          group-id: flowData
          allow-consumer-tag: '*'
        - name: workOrderDefaultConsumer
          topic: ${za.graphene.kafka.topic.openapi-platform-work-order}
          server-url: ${kafka.server.config}
          group-id: openapi-work-order-default
          allow-consumer-tag: '*'
        - name: pendingCaseResultConsumer
          topic: ${za.graphene.kafka.topic.pending-case}
          server-url: ${kafka.server.config}
          group-id: baseline_openapi_hooks_pending_case_${GROUP_ENV}
          allow-consumer-tag: ${TOPIC_ENV}-za-graphene-notification-pendingCase
  db:
    shardingsphere:
      enabled: true
      type: RAIN_DROP
      max-vibration-offset: 8 # 震荡值，避免在分表场景下低并发时，所有数据都落入了下标为0的表中。不配置默认1。
      max-tolerance-dial-back-seconds: 10 # 当时时间回拨时，容忍的最大回拨等待时间, 时间单位为秒,不配置默认10。
      seqs:
        - seqName: flow_data_record
        - seqName: flow_id_generated
        - seqName: flow_id_generate_input_model_meta
        - seqName: scenario_instance_doc
        - seqName: procedure_data_record
        - seqName: scenario_instance
        - seqName: scenario_instance_plan
        - seqName: scenario_instance_dynamic_model_meta
        - seqName: scenario_instance_model_meta
        - seqName: scenario_schema
        - seqName: scenario_schema_group
        - seqName: scenario_schema_model_meta
        - seqName: flow_schema
        - seqName: flow_instance
        - seqName: domain
        - seqName: namespace
        - seqName: model_mapping
        - seqName: model_value_dictionary
        - seqName: model_value_mock
        - seqName: procedure_meta
        - seqName: procedure_model_meta
        - seqName: scenario_schema_sample_template
        - seqName: open_api_doc
        - seqName: meta_doc
        - seqName: r_scenario_instance_flow_schema
        - seqName: web_hook_registry
        - seqName: web_hook_event_subscription
        - seqName: web_hook_key
        - seqName: web_hook_business_event_bound
        - seqName: web_hook_business_event_trigger_record
        - seqName: web_hook_business_event_received
        - seqName: claim_incident_compensation_bill
        - seqName: callback_key
        - seqName: scenario_instance_insurer
        - seqName: migration_operate_record
        - seqName: scenario_instance_case_example
        - seqName: scenario_instance_case_example_i18n
        - seqName: scenario_instance_doc_i18n
        - seqName: work_order_label
        - seqName: work_order
        - seqName: work_order_label_relation
        - seqName: work_order_menu
        - seqName: work_order_menu_relation
        - seqName: work_order_file
        - seqName: work_order_comment
        - seqName: scenario_api_config
        - seqName: api_key
        - seqName: app_id
        - seqName: app_limiter_config
        - seqName: tenant_limiter_config
        - seqName: seq_fallback_message
        - seqName: api_mcp_config
  es:
    serviceUrl: http://genesis-xtest1-genesis-genesis-searcher-service.fv.eks.za-gj-aws.net
    esAppName:
      - openapi-data-${projectName}-${SHARED_ENV}
    openapiData: openapi-data-${projectName}-${SHARED_ENV}

logging:
  level:
    com.zatech.genesis.openapi.knowledge.flow.inbuilt: DEBUG
    org.springframework.security: DEBUG
    com.alibaba.sentinel: DEBUG
    com.alibaba.csp.sentinel: DEBUG

custom:
  rest:
    connect-timeout: 3000
    read-timeout: 30000
  work-order:
    use-default-consumer:
      switch: true
    file:
      max-size: 10485760
      support-types: .png,.jpeg,.jpg,.txt,.rar,.doc,.xls,.zip,.7z,.mp4,.avi,.flv
      bucket: default
      ticket-max-file-count: 10
      comment-ticket-max-file-count: 3
  lark:
    callback:
      url: https://open.larksuite.com/open-apis/bot/v2/hook/7a83e345-e3b9-4405-91aa-fd67cb4094a5

za:
  graphene:
    feign:
      cdc: http://genesis-${DEPLOY_ENV}-genesis-genesis-cdc.${DEPLOY_ENV}.eks.za-gj-aws.net
      policy: http://genesis-${DEPLOY_ENV}-genesis-genesis-policy.${DEPLOY_ENV}.eks.za-gj-aws.net
      metadata: http://genesis-${DEPLOY_ENV}-genesis-genesis-metadata.${DEPLOY_ENV}.eks.za-gj-aws.net
      #auth: http://*************:8081
      auth: http://genesis-x${DEPLOY_ENV}-genesis-genesis-api-auth.${DEPLOY_ENV}.eks.za-gj-aws.net
      notification: http://genesis-${DEPLOY_ENV}-genesis-genesis-notification.${DEPLOY_ENV}.eks.za-gj-aws.net
      product: http://genesis-${DEPLOY_ENV}-genesis-genesis-product.${DEPLOY_ENV}.eks.za-gj-aws.net
      market: http://genesis-${DEPLOY_ENV}-genesis-genesis-market.${DEPLOY_ENV}.eks.za-gj-aws.net
      #channel-auth: http://*************:8082
      channel-auth: http://genesis-x${DEPLOY_ENV}-genesis-genesis-channel-auth.${DEPLOY_ENV}.eks.za-gj-aws.net
      zeus: http://genesis-x${DEPLOY_ENV}-genesis-genesis-zeus.${DEPLOY_ENV}.eks.za-gj-aws.net
      dp: http://genesis-${DEPLOY_ENV}-genesis-genesis-gemini.${DEPLOY_ENV}.eks.za-gj-aws.net
      fileService: http://genesis-x${DEPLOY_ENV}-genesis-genesis-file.${DEPLOY_ENV}.eks.za-gj-aws.net
      claim: http://genesis-${DEPLOY_ENV}-genesis-genesis-claim.${DEPLOY_ENV}.eks.za-gj-aws.net
      customer: http://genesis-${DEPLOY_ENV}-genesis-genesis-customer.${DEPLOY_ENV}.eks.za-gj-aws.net
      bcp: http://genesis-${DEPLOY_ENV}-genesis-genesis-bcp.${DEPLOY_ENV}.eks.za-gj-aws.net
      neuron: http://neuron-${DEPLOY_ENV}-neuron-neuron-laboratory.${DEPLOY_ENV}.za-gj-aws.net
      insurer-gateway: http://genesis-${DEPLOY_ENV}-genesis-genesis-insurer-gateway.${DEPLOY_ENV}.eks.za-gj-aws.net
      pos: genesis-${DEPLOY_ENV}-genesis-genesis-pos-online.${DEPLOY_ENV}.eks.za-gj-aws.net
      renew: http://genesis-${DEPLOY_ENV}-genesis-genesis-renew.${DEPLOY_ENV}.eks.za-gj-aws.net
      calculator: http://genesis-${DEPLOY_ENV}-genesis-genesis-calculator.${DEPLOY_ENV}.eks.za-gj-aws.net
      uwe: http://questionnaire-genesis-${DEPLOY_ENV}.${DEPLOY_ENV}.eks.za-gj-aws.net
      payment-gateway: http://genesis-${DEPLOY_ENV}-genesis-genesis-payment-gateway.${DEPLOY_ENV}.eks.za-gj-aws.net
      fund: http://genesis-${DEPLOY_ENV}-genesis-genesis-fund.${DEPLOY_ENV}.eks.za-gj-aws.net
      fayol: http://genesis-${DEPLOY_ENV}-fullerene-fullerene-fayol.${DEPLOY_ENV}.eks.za-gj-aws.net
      channel: channel-genesis-${DEPLOY_ENV}.${DEPLOY_ENV}.eks.za-gj-aws.net
      report: report-genesis-${DEPLOY_ENV}.${DEPLOY_ENV}.eks.za-gj-aws.net
      c360-libra: http://genesis-${DEPLOY_ENV}-c360-c360-libra.${DEPLOY_ENV}.eks.za-gj-aws.net
      customer-center: http://genesis-${DEPLOY_ENV}-genesis-genesis-customer-center.${DEPLOY_ENV}.eks.za-gj-aws.net
      campaign: http://genesis-${DEPLOY_ENV}-genesis-genesis-campaign.${DEPLOY_ENV}.eks.za-gj-aws.net
      printerx: http://genesis-x${DEPLOY_ENV}-genesis-genesis-printer.${DEPLOY_ENV}.eks.za-gj-aws.net
      searcher-service: http://genesis-x${DEPLOY_ENV}-genesis-genesis-searcher-service.${DEPLOY_ENV}.eks.za-gj-aws.net
      bot-fastapi: http://bot-fastapi-genesis-${DEPLOY_ENV}.${DEPLOY_ENV}.eks.za-gj-aws.net
    fallback:
      enable-local-scheduler: false
      local-scheduler-period-seconds: 6  # 本地跑批间隔 默认值 600
      multiple-of-incr-retry-diff-time: 1.0 # 每次重试间隔变更的倍数 默认每次间隔时间为上一次的2倍

genesis-feign-campaign: http://genesis-${DEPLOY_ENV}-genesis-genesis-campaign.${DEPLOY_ENV}.eks.za-gj-aws.net

taylor:
  endpoint: http://genesis-${DEPLOY_ENV}-fullerene-fullerene-taylor.${DEPLOY_ENV}.eks.za-gj-aws.net

multitenancy:
  trace:
    attributes:
      x-za-region: eu
    web:
      url-pattern: "/(?!openapi/mgmt/v1/global/tenant)(mgmt|api|openapi|mcp).*"
  security:
    enabled: true
    oauth2:
      resource-server:
        mgmt:
          permitted-urls: /mgmt/v3/**
          authenticated-urls: /mgmt/v1/**
          ant-matcher-url: /mgmt/**
          filter:
            url-patterns: /mgmt/*
          #check-token-url: http://gaia-poc-iam-channel-auth.dev.za-gj-aws.net/oauth/check_token
          client-id: test
          client-secret: 1Q2w3e4r%25
          check-token-url: http://genesis-x${DEPLOY_ENV}-genesis-genesis-channel-auth.${DEPLOY_ENV}.eks.za-gj-aws.net/oauth/check_token
        api:
          authenticated-urls: /api/**,/openapi/inner/**,/mcp,/mcp/**
          ant-matcher-url: /api/**,/openapi/inner/**,/mcp,/mcp/**
          permitted-urls: /api/callback/**,/api/ai/v1/**
          filter:
            url-patterns: /api/*,/openapi/inner/*,/mcp,/mcp/**
          check-token-url: http://genesis-x${DEPLOY_ENV}-genesis-genesis-api-auth.${DEPLOY_ENV}.eks.za-gj-aws.net/oauth/check_token

openapi:
  mode: DEBUG
  refresh:
    gemini_schema:
      enabled: false
  spec:
    suite: graphene
    #rootFolder: 'D:\project\genesis-openapi-platform'
    #    rootFolder: '/Users/<USER>/Workspace/zatech/genesis/genesis-openapi-platform'
    rootFolder: ${localRootFolder:/Users/<USER>/Workspace/zatech/genesis/openapi-platform}
  #    rootFolder: '/home/<USER>/Workspace/zatech/genesis/openapi-platform'
  flow:
    procedure:
      timeout: 30000
    activity:
      timeout: 50000
  sandbox:
    enabled: true
  schema:
    switch: true
  callback:
    forbidden:
      ips: ***************,***************
  shardingsphere:
    props:
      sql.show: "false"

mq:
  store:
    switch: 'off'

sensitiveData:
  allRun: false

openapi-platform-domain: http://openapi-platform-genesis-dev1.dev1.eks.za-gj-aws.net
