spring:
  application:
    name: gemini
  jpa:
    open-in-view: false
    show-sql: false
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
  sleuth:
    baggage-keys:
      - x-za-tenant
      - x-za-region
      - x-za-source
  cloud:
    nacos:
      config:
        file-extension: yml
        server-addr: ${CONFIG_SERVER_URL}
        namespace: ${DEPLOY_ENV}
        group: ${NACOS_GROUP}
        enabled: ${NACOS_ENABLE:false}
        refresh-enabled: ${NACOS_REFRESH:false}
        extension-configs[0]:
          data-id: common.yml
          group: ${NACOS_GROUP}
        extension-configs[1]:
          data-id: ${SCHEMA_NAME:gemini}.yml
          group: ${SS_NACOS_GROUP:ssmt.${NACOS_GROUP}}
          refresh: true

feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
      remote-service:
        connectTimeout: 10000

genesis-feign-iam: http://genesis-sit-genesis-genesis-user-auth.sit.za-gj-aws.net
za.product.endpoint: http://genesis-sit-genesis-genesis-product.sit.za-gj-aws.net
za.market.endpoint: http://genesis-sit-genesis-genesis-market.sit.za-gj-aws.net
genesis-feign-market: http://genesis-sit-genesis-genesis-market.sit.za-gj-aws.net
genesis-feign-metadata: http://genesis-sit-genesis-genesis-metadata.sit.za-gj-aws.net
genesis-feign-product: http://genesis-sit-genesis-genesis-product.sit.za-gj-aws.net
genesis-feign-calculator: http://genesis-sit-genesis-genesis-calculator.sit.za-gj-aws.net
genesis-feign-policy: http://genesis-sit-genesis-genesis-policy.sit.za-gj-aws.net
genesis-feign-customer: http://genesis-sit-genesis-genesis-customer.sit.za-gj-aws.net
genesis-feign-bcp: http://genesis-sit-genesis-genesis-bcp.sit.za-gj-aws.net
genesis-feign-pos: http://genesis-sit-genesis-genesis-pos-online.sit.za-gj-aws.net
genesis-feign-campaign: http://genesis-sit-genesis-genesis-campaign.sit.za-gj-aws.net
genesis-feign-fund: http://genesis-sit-genesis-genesis-fund.sit.za-gj-aws.net
genesis-feign-uwe: http://genesis-xsit-genesis-genesis-uwe.sit.za-gj-aws.net
genesis-feign-file: http://genesis-xsit-genesis-genesis-file.sit.za-gj-aws.net
genesis-feign-claim: http://genesis-sit-genesis-genesis-claim.sit.za-gj-aws.net
genesis-feign-cdc: http://genesis-sit-genesis-genesis-cdc.sit.za-gj-aws.net

multitenancy:
  security:
    enabled: false

octopus:
  mq:
    kafka:
      producer:
        - name: schemaProducer
          topic: gemini-schema-event-${projectName}-${DEPLOY_ENV}
          server-url: ${kafka.server.config}

server:
  port: 8081