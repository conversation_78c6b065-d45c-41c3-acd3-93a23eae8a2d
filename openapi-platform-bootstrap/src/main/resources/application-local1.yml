#spring:
#  servlet:
#    multipart:
#      max-file-size: 100MB
#      max-request-size: 105MB
#  shardingsphere:
#    sharding:
#      default-data-source-name: genesis-mds-0
#    datasource:
#      refresh:
#        enabled: true
#        purge-delay: 5
#      names: "genesis-mds-0"
#      genesis-mds-0:
#        type: "com.zaxxer.hikari.HikariDataSource"
#        driver-class-name: "com.mysql.cj.jdbc.Driver"
#        jdbc-url: "*********************************************************************************************************************************************************************"
#        username: "root"
#        password: "zatech"
#        #jdbc-url: ************************************************************************************************************************************************************************************
#        #username: genesis_openapi_platform_dev1
#        #password: RPSPRx2hk7yc7b18
#        minimum-idle: 1
#        maximum-pool-size: 5
#        idle-timeout: 540000
#        connection-timeout: 60000
#        connection-test-query: "select 1"
#        pool-name: "genesis-mds-0"
#  data:
#    mongodb:
#      #uri: *************************************************************************************************************************************************************************************************************
#      uri: mongodb://localhost:27017/genesis_openapi_platform

octopus:
#  mybatis:
#    configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  #  cache:
  #    redis:
  #      port: 6379
  #      database: 8
  #      hostname: localhost
  #      #hostname: awschina-nx-redis-dev-app-01.dqimer.0001.cnw1.cache.amazonaws.com.cn
  #      name: ${spring.application.name}
  mq:
    kafka:
      producer:
        - name: procedureDataProducer
          topic: ${za.graphene.kafka.topic.openapi-platform}
          server-url: ${kafka.server.config}
        - name: claimResultDataProducer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
        - name: policyResultDataProducer
          topic: ${za.graphene.kafka.topic.policy}
          server-url: ${kafka.server.config}
        - name: posResultDataProducer
          topic: ${za.graphene.kafka.topic.posonline}
          server-url: ${kafka.server.config}
        - name: workOrderDataProducer
          topic: openapi-work-order-genesis-dev1
          server-url: ${kafka.server.config}
      consumer:
        - name: procedureDateConsumer
          topic: ${za.graphene.kafka.topic.openapi-platform}
          server-url: ${kafka.server.config}
          group-id: procedure-mine
          allow-consumer-tag: '*'
        - name: posResultConsumer
#          topic: ${za.graphene.kafka.topic.posonline}
          topic: posonline-genesis-dev1
          server-url: ${kafka.server.config}
          group-id: openpai-hooks-mine
#          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-pos-online-case-status-change
          allow-consumer-tag: dev1-genesis-posonline-settle-case
        - name: createIssuanceResultConsumer
#          topic: ${za.graphene.kafka.topic.policy}
          topic: policy-genesis-dev1
          server-url: ${kafka.server.config}
          group-id: openpai-hooks-mine
#          allow-consumer-tag: ${DEPLOY_ENV}-za-graphene-policy-effectivePolicy
          allow-consumer-tag: dev1-genesis-policy
        - name: createProposalResultConsumer
          topic: ${za.graphene.kafka.topic.issuance}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks
          allow-consumer-tag: ${DEPLOY_ENV}-za-graphene-issuance-insure
        - name: underwritingResultConsumer
          topic: ${za.graphene.kafka.topic.underwriting}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks
          allow-consumer-tag: ${DEPLOY_ENV}-za-graphene-underwriting-decision
        - name: claimResultConsumer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
          group-id: openpai-hooks-felton
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-claim-reject-case,${DEPLOY_ENV}-${projectName}-claim-settle-case,${DEPLOY_ENV}-${projectName}-claim-withdraw-case
        - name: claimApplicationResultConsumer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
          group-id: openapi-hooks-application
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-claim-application-withdraw, ${DEPLOY_ENV}-${projectName}-claim-finish-application, ${DEPLOY_ENV}-${projectName}-claim-finsish-registration
        - name: claimCaseProcessConsumer
          topic: ${za.graphene.kafka.topic.claim}
          server-url: ${kafka.server.config}
          group-id: openapi-hooks-case
          allow-consumer-tag: ${DEPLOY_ENV}-${projectName}-claim-reject-case,${DEPLOY_ENV}-${projectName}-claim-settle-case,${DEPLOY_ENV}-${projectName}-claim-withdraw-case, ${DEPLOY_ENV}-${projectName}-claim-case-status-change,${DEPLOY_ENV}-${projectName}-claim-finish-assessment,${DEPLOY_ENV}-${projectName}-claim-finish-verification,${DEPLOY_ENV}-${projectName}-claim-finish-acceptance
        - name: workOrderDefaultConsumer
          topic: openapi-work-order-genesis-dev1
          server-url: ${kafka.server.config}
          group-id: openapi-work-order-default
          allow-consumer-tag: '*'
  db:
    shardingsphere:
      enabled: true
      type: RAIN_DROP
      max-vibration-offset: 8 # 震荡值，避免在分表场景下低并发时，所有数据都落入了下标为0的表中。不配置默认1。
      max-tolerance-dial-back-seconds: 10 # 当时时间回拨时，容忍的最大回拨等待时间, 时间单位为秒,不配置默认10。
      seqs:
        - seqName: flow_data_record
        - seqName: flow_id_generated
        - seqName: flow_id_generate_input_model_meta
        - seqName: scenario_instance_doc
        - seqName: procedure_data_record
        - seqName: scenario_instance
        - seqName: scenario_instance_plan
        - seqName: scenario_instance_dynamic_model_meta
        - seqName: scenario_instance_model_meta
        - seqName: scenario_schema
        - seqName: scenario_schema_group
        - seqName: scenario_schema_model_meta
        - seqName: flow_schema
        - seqName: flow_instance
        - seqName: domain
        - seqName: namespace
        - seqName: model_mapping
        - seqName: model_value_dictionary
        - seqName: model_value_mock
        - seqName: procedure_meta
        - seqName: procedure_model_meta
        - seqName: scenario_schema_sample_template
        - seqName: open_api_doc
        - seqName: meta_doc
        - seqName: r_scenario_instance_flow_schema
        - seqName: web_hook_registry
        - seqName: web_hook_event_subscription
        - seqName: web_hook_key
        - seqName: web_hook_business_event_bound
        - seqName: web_hook_business_event_trigger_record
        - seqName: web_hook_business_event_received
        - seqName: migration_operate_record
        - seqName: scenario_instance_case_example
        - seqName: scenario_instance_case_example_i18n
        - seqName: scenario_instance_doc_i18n
        - seqName: tenancy_channel_limiter_rule
        - seqName: tenancy_limiter_rule
        - seqName: work_order_label
        - seqName: work_order
        - seqName: work_order_label_relation
        - seqName: work_order_menu
        - seqName: work_order_menu_relation
        - seqName: work_order_file
        - seqName: work_order_comment
        - seqName: api_view_document
        - seqName: api_view_group
        - seqName: api_view_record
        - seqName: api_view_schema
        - seqName: r_view_document
        - seqName: scenario_api_config
        - seqName: api_key

  governance:
    limiter:
      enabled: false
#  trace:
#    web:
#      url-pattern: "/(?!openapi/mgmt/v1/global/tenant-list)(mgmt|openapi).*"
#  security:
#    oauth2:
#      resource-server:
#        mgmt:
#          resource-id: GENESIS/OPENAPI
#          authenticated-urls: /mgmt/**
#          ant-matcher-url: /mgmt/**
#          filter:
#            url-patterns: /mgmt/*
#          check-token-url: http://genesis-xdev1-genesis-genesis-channel-auth.dev1.eks.za-gj-aws.net/oauth/check_token
#        api:
#          resource-id: GENESIS/OPENAPI-SANDBOX   #环境调试，release需要根据环境设置
#          authenticated-urls: /openapi/**
#          ant-matcher-url: /openapi/**
#          filter:
#            url-patterns: /openapi/*
#          check-token-url: http://genesis-xdev1-genesis-genesis-api-auth.dev1.eks.za-gj-aws.net/oauth/check_token
multitenancy:
  security:
    oauth2:
      resource-server:
        mgmt:
          #check-token-url: http://gaia-poc-iam-channel-auth.dev.za-gj-aws.net/oauth/check_token
          client-id: test
          client-secret: 1Q2w3e4r%25
          check-token-url: http://genesis-xdev1-genesis-genesis-channel-auth.dev1.eks.za-gj-aws.net/oauth/check_token
        api:
          #check-token-url: http://gaia-poc-iam-api-auth.dev.za-gj-aws.net/oauth/check_token
          check-token-url: http://genesis-xdev1-genesis-genesis-api-auth.dev1.eks.za-gj-aws.net/oauth/check_token

logging:
  level:
    com.zatech.genesis.openapi.knowledge.flow.inbuilt: DEBUG

genesis-feign-saas-customized-open-api: 111
openapi-platform-url: http://genesis-sit-genesis-genesis-openapi-plugin.sit.eks.za-gj-aws.net
za:
  graphene:
    feign:
      policy: http://genesis-sit-genesis-genesis-policy.sit.eks.za-gj-aws.net
      metadata: http://genesis-dev1-genesis-genesis-metadata.dev1.eks.za-gj-aws.net
#      metadata: http://genesis-sit-genesis-genesis-metadata.sit.eks.za-gj-aws.net
      auth: http://genesis-xdev1-genesis-genesis-api-auth.dev1.eks.za-gj-aws.net
      product: http://genesis-dev1-genesis-genesis-product.dev1.eks.za-gj-aws.net
      zeus: http://genesis-xdev1-genesis-genesis-zeus.dev1.eks.za-gj-aws.net
#      dp: http://genesis-sit-genesis-genesis-gemini.sit.eks.za-gj-aws.net
#      dp: http://genesis-dev1-genesis-genesis-gemini.dev1.eks.za-gj-aws.net
      dp: http://localhost:8081
      fileService: http://genesis-xdev1-genesis-genesis-file.dev1.eks.za-gj-aws.net
#      claim: http://genesis-dev1-genesis-genesis-claim.dev1.eks.za-gj-aws.net
      claim: http://genesis-sit-genesis-genesis-claim.sit.eks.za-gj-aws.net
      customer:  http://genesis-sit-genesis-genesis-customer.sit.eks.za-gj-aws.net
#      market: genesis-dev1-genesis-genesis-market.dev1.eks.za-gj-aws.net
      market: genesis-sit-genesis-genesis-market.sit.eks.za-gj-aws.net
      #      market: http://saas-nx-dev-saas-nx-saas-nx-market.test.eks.saas.za-gj-aws.net
      pos: http://genesis-sit-genesis-genesis-pos-online.sit.eks.za-gj-aws.net
#      pos: http://genesis-sit-genesis-genesis-pos-online.sit.za-gj-aws.net
      insurer-gateway: http://genesis-sit-genesis-genesis-insurer-gateway.sit.za-gj-aws.net
#      renew: http://genesis-dev1-genesis-genesis-renew.dev1.eks.za-gj-aws.net
#      renew: http://genesis-sit-genesis-genesis-renew.sit.eks.za-gj-aws.net
      renew: http://aia-test3-aia-aia-renew.test3.za-gj-aws.net
#      bcp: http://aia-test3-aia-aia-bcp.test3.za-gj-aws.net
      bcp: http://genesis-sit-genesis-genesis-bcp.sit.eks.za-gj-aws.net
      cdc: http://genesis-sit-genesis-genesis-cdc.sit.eks.za-gj-aws.net
      query: http://genesis-sit-genesis-genesis-query.sit.eks.za-gj-aws.net
      fund: http://genesis-sit-genesis-genesis-fund.sit.eks.za-gj-aws.net


log:
  provider:
    enabled: true
security:
  basic:
    enabled: true

openapi:
  spec:
    suite: graphene
    rootFolder: 'D:\project\genesis-openapi-platform'
  flow:
    procedure:
      timeout: 30000
    activity:
      timeout: 50000
  sandbox:
    enabled: true
  schema:
    switch: true
  callback:
    forbidden:
      ips: ***************,***************
management:
  tracing:
    baggage:
      remote-fields:
        - x-za-app-id
        - x-za-tenant
        - x-za-region
        - x-za-channel
        - x-za-subscription
        - x-za-source
        - openapi-authentication-type
      correlation:
        fields:
          - x-za-tenant
          - x-za-region
          - openapi-authentication-type

  shardingsphere:
    props:
      sql.show: "false"

mq:
  store:
    switch: 'off'


#spring:
#  shardingsphere:
#    props:
#      sql.show: "true"







