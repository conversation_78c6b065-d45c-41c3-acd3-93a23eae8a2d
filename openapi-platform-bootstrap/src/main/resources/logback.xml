<?xml version="1.0" encoding="UTF-8"?>
<!-- 只需配置好 logging.path 和 project.name 属性-->
<configuration debug="false">
    <property name="project.name" value="ZatechOpenapiPlatform"/>
    <property name="logging.path" value="/alidata1/admin/zatech-openapi-platform"/>
    <include resource="log/logback-sensitive-appender.xml"/>

    <conversionRule conversionWord="ex" converterClass="com.zatech.octopus.component.log.SensitiveExceptionConverter"/>

    <root>
        <level value="INFO"/>
        <appender-ref ref="infoAppenderAsync"/>
        <appender-ref ref="errorAppenderAsync"/>
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
