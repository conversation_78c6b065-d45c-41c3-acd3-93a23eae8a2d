temporary.enums.1 = はい
temporary.enums.2 = いいえ

isRenewalPolicy.enums.1 = はい
isRenewalPolicy.enums.2 = いいえ

issuanceTransactionType.enums.1 = rule check & create issuance
issuanceTransactionType.enums.3 = rule check & create issuance & confirm issuance
issuanceTransactionType.enums.9 = data saved
issuanceTransactionType.enums.10 = update issuance

paymentMethod.enums.40 = SBPS-クレカ支払い
paymentMethod.enums.41 = SBPS-ドコモ払い
paymentMethod.enums.42 = SBPS-AUかんたん決済
paymentMethod.enums.43 = クまとめて支払い、ワイモバイル決済
paymentMethod.enums.44 = SBPS-LINE_PAY
paymentMethod.enums.45 = SBPS-Yahooウォレット
paymentMethod.enums.46 = SBPS-楽天ペイ
paymentMethod.enums.47 = SBPS-APPLE_Pay
paymentMethod.enums.48 = SBPS-PayPay
paymentMethod.enums.100 = 現金
paymentMethod.enums.101 = クレジットカード
paymentMethod.enums.102 = 銀行振込
paymentMethod.enums.103 = 支付宝（废弃）
paymentMethod.enums.104 = 微信支付（废弃）
paymentMethod.enums.105 = 积分抵扣（废弃）


type.enums.1 = 续保_保单号
type.enums.2 = 关系单号
type.enums.3 = 三方号
type.enums.4 = Upsell
type.enums.5 = 连带保单
type.enums.6 = 团体保单
type.enums.7 = 多被保人批次号

liabilityId.enums.544 = Personal Injury
liabilityId.enums.600 = 車両保険（タイプ）
liabilityId.enums.606 = 対物超過修理費用補償特約
liabilityId.enums.618 = 人身傷害保険（保険金額

sumInsured.title = 保険金額

birthday.title = 生年月日

gender.title = 性別
gender.enums.1 = 女
gender.enums.2 = 男
fullName.title = フルネーム
certiNo.title = 身分証明書番号
certiType.title = 身分証明書種類


engineNo.title = エンジン識別番号
vehicleUsage.title = 车辆用途
thirdPartyUniqueID.title = Third Party Unique ID
vehicleEngine.title = 汽车发动机类型
plateNo.title = 车牌号
yearOfManufacturing.title = 出厂年份
weight.title = 重量
vinNo.title = 车架号
vehicleMake.title = 制造品牌
mtplNo.title = 三者险保单号
registrationNo.title = 车辆注册号
registrationDate.title = 初登日期
colorOfPlateNo.title = 车牌颜色
vehicleModel.title = 车辆型号
registrationCategory.title = 车牌分类番号
E_vehicleAge.title = 車齢_ヶ月
E_vehicleAge.enums.1 = 0-11ヶ月
E_vehicleAge.enums.2 = 12-23ヶ月
E_vehicleAge.enums.3 = 24-35ヶ月
E_vehicleAge.enums.4 = 36-120ヶ月
E_vehicleAge.enums.5 = 120ヶ月以上

equipmentCode.title = 設備コード
loanCompany.title = 自動車ローン会社
loanCompany.enums.*********** = Samoborska banka d.d., Samobor
loanCompany.enums.*********** = J&T banka d.d., Varaždin
loanCompany.enums.*********** = Banka Kovanica d.d., Varaždin
loanContractNo.title = 自動車ローンのコンタクト番号

idType.title = 身分証明書種類
idNumber.title = 身分証明書番号

driverIdNumber.title = 身分証明書番号
occupationCode.title = 職業
driverTier.title = 運転者範囲
driverTier.enums.1 = 被保険者のみ
driverTier.enums.2 = 被保険者と配偶者のみ
driverTier.enums.3 = 家族
driverTier.enums.4 = 無制限
drivingLicenseNo.title = 免許番号
driverMaritalStatus.title = 婚姻状況
driverBirthday.title = 生年月日

premiumPeriod.title = 支払期間
coveragePeriod.title = 補償期間
premiumFrequencyType.title = 払込方法
salesCurrency.title = 通貨
relationshipWithPolicyholder.title = 契約者との続柄



parameter.description.api-instance = APIのインスタンス識別子
parameter.description.api-timestamp = リクエストを送信するタイムスタンプ。 format： 'yyyy-MM-dd'T'HH:mm:ss.SSSX', 例： '2021-07-17T11:01:09.564+08:00'.
parameter.description.api-version = APIのバージョン


goodsId.enums.828599301062661 = ZA自動車保険2
planId.enums.828602035748870 = 新自動車保険プラン
productId.enums.828414030266375 = 日本市場デモ用自動車保険New2
