occupationClass.enums.A1 = a1_occupation_code
occupationClass.enums.A2 = a2_occupation_code
occupationClass.enums.A3 = a3_occupation_code
parameter.description.api-instance=The instance identity of API.
parameter.description.api-timestamp=The timestamp of sending the request, format: 'yyyy-MM-dd'T'HH:mm:ss.SSSX', example: '2021-07-17T11:01:09.564+08:00'.
parameter.description.api-version=The version of API, not required, default is 'v1'.
E_ProposalUMEDecision.title=E_ProposalUMEDecision
E_ProposalUMEDecision.description=UME Info
residenceCountry.title=residenceCountry
residenceCountry.description=Residence country.
E_TaxResidenceConflictReason.title=E_TaxResidenceConflictReason
E_TaxResidenceConflictReason.description=if E_TaxResidenceConflictReasonCode choose others , put value here
E_TaxResidenceConflictReasonCode.title=E_TaxResidenceConflictReasonCode
E_TaxResidenceConflictReasonCode.description=residential address not correspond with  declared country(ies)   options 1,2,3,4,5
E_TaxResidenceIndicatorClarification.title=E_TaxResidenceIndicatorClarification
E_TaxResidenceIndicatorClarification.description=If your TIN is not your NRIC or FIN, please state it here
E_SourceOfWealth.title=E_SourceOfWealth
E_SourceOfWealth.description=source of wealth
E_SourceOfWealthDetails.title=E_SourceOfWealthDetails
E_SourceOfWealthDetails.description=if E_SourceOfWealth choose others details of wealth details
relationNo.title=relationNo
relationNo.description=Relational policy number.
kycStatus.title=kycStatus
kycStatus.description=0 No KYC Form,1 Full Fact Find,2 Partial Fact Find,3 Product Advice,4 Opt Out,5 Not Applicable,9 Internal Use
temporary.title=temporary
temporary.description=Temporary issuance or not.
temporary.enums.1=Yes
temporary.enums.2=No
education.title=education
education.description=Education.
periodStandardPlannedPremium.title=periodStandardPlannedPremium
periodStandardPlannedPremium.description=periodStandardPlannedPremium
agentCode.title=agentCode
agentCode.description=Agent code.
ckaIndicator.title=ckaIndicator
ckaIndicator.description=Cka indicator.
policyNo.title=policyNo
policyNo.description=Policy number.
secondNationality.title=secondNationality
secondNationality.description=secondNationality
payerType.title=payerType
payerType.description=payer Type
payerType.enums.1=FIRST
payerType.enums.2=RENEWAL
payerType.enums.3=POS
payerType.enums.4=SB
payerType.enums.5=CB
payerType.enums.6=MB
payerType.enums.7=ANNUITY
payerType.enums.8=RSTU
payerType.enums.9=REGULAR_WITHDRAWAL
payerType.enums.10=FUND_DIVIDEND_DISTRIBUTION
type.title=type
type.description=Policy type.
type.enums.8=bundled policy
type.enums.5=Joint and several insurance policies
type.enums.4=Upsell
type.enums.7=Multi-insured batch number
type.enums.6=Group policy
type.enums.1=Renewal Policy Number
type.enums.3=Tripartite number
type.enums.2=Tracking number
industryCode.title=industryCode
industryCode.description=Industry code.
ownerYn.title=ownerYn
ownerYn.description=ownerYn
sendPaperPolicy.title=sendPaperPolicy
sendPaperPolicy.description=Send paper policy.
E_UWTag.title=E_UWTag
E_UWTag.description=description placeholder
falculative.title=falculative
falculative.description=Falculative
publicTenderNo.title=publicTenderNo
publicTenderNo.description=Public tender number
pass.title=pass
pass.description=Pass
E_CountryOfBirth.title=E_CountryOfBirth
E_CountryOfBirth.description=Country  of birth.
organizationSize.title=organizationSize
organizationSize.description=Organization size.
relationshipWithPolicyholder.title=relationshipWithPolicyholder
relationshipWithPolicyholder.description=Relationship with Policyholder.
relationshipWithPolicyholder.enums.1=Self
organizationAddressType.title=organizationAddressType
organizationAddressType.description=Organization address type.
payers.title=payers
payers.description=Payers info.
sumInsured.title=sumInsured
sumInsured.description=Sum insured.
rejectReason.title=rejectReason
rejectReason.description=Reject reason.
fundCode.title=fundCode
fundCode.description=Code of fund.
sendPaperPolicy.enums.1=Yes
sendPaperPolicy.enums.2=No
planId.title=planId
planId.description=Plan id.
planId.enums.1289354920099840=InvestPro Link - Plan A (Test)
planId.enums.1289355339530241=InvestPro Link - Plan B (Test)
height.title=height
height.description=height(cm)
writtenLanguage.title=writtenLanguage
writtenLanguage.description=written Language
writtenLanguage.enums.19=HUNGARIAN
writtenLanguage.enums.18=GREEK
writtenLanguage.enums.17=FINNISH
writtenLanguage.enums.39=VIETNAMESE
writtenLanguage.enums.16=ESTONIAN
writtenLanguage.enums.38=TAMIL
writtenLanguage.enums.6=SimplifiedChinese
writtenLanguage.enums.40=MALAY
writtenLanguage.enums.4=Italian
writtenLanguage.enums.3=French
writtenLanguage.enums.8=Japanese
writtenLanguage.enums.7=TraditionalChinese
writtenLanguage.enums.26=ROMANIAN
writtenLanguage.enums.25=PORTUGUESE
writtenLanguage.enums.24=POLISH
writtenLanguage.enums.23=MALTESE
writtenLanguage.enums.2=German
writtenLanguage.enums.22=LITHUANIAN
writtenLanguage.enums.1=English
writtenLanguage.enums.21=LATVIAN
writtenLanguage.enums.20=IRISH
writtenLanguage.enums.29=SPANISH
writtenLanguage.enums.28=SLOVENIAN
writtenLanguage.enums.27=SLOVAK
writtenLanguage.enums.15=DUTCH
writtenLanguage.enums.37=ARABIC
writtenLanguage.enums.14=DANISH
writtenLanguage.enums.36=HINDIHINDI
writtenLanguage.enums.13=CZECH
writtenLanguage.enums.35=RUSSIAN
writtenLanguage.enums.12=CROATIAN
writtenLanguage.enums.34=NORWEGIAN
writtenLanguage.enums.11=BULGARIAN
writtenLanguage.enums.33=TAKAFUL
writtenLanguage.enums.10=THAI
writtenLanguage.enums.32=BAHASA
writtenLanguage.enums.31=KOREAN
writtenLanguage.enums.30=SWEDISH
E_SingaporePR.title=E_SingaporePR
E_SingaporePR.description=permanently reside options:- Yes- No
productId.title=productId
productId.description=Product id.
productId.enums.1277926431539200=InvestPro Link
productId.enums.1349517177798657=Dread Disease Premium Waiver (ILP)
publicTender.title=publicTender
publicTender.description=Public tender.
publicTender.enums.1=Y
publicTender.enums.2=N
E_UMEDecisionDate.title=E_UMEDecisionDate
E_UMEDecisionDate.description=UME info
E_System_Source.title=E_System_Source
E_System_Source.description=SystemSource
E_UMEIndicator.title=E_UMEIndicator
E_UMEIndicator.description=UME info
E_UMEReason.title=E_UMEReason
E_UMEReason.description=UME info
E_MarketingConsent.title=E_MarketingConsent
E_MarketingConsent.description=marketing consent info
E_Declarations.title=E_Declarations
E_Declarations.description=declarations
E_PolicySource.title=E_PolicySource
E_PolicySource.description=Option:1 Singapore Policy ,2 Offshore Policy
E_IntroducerNric.title=E_IntroducerNric
E_IntroducerNric.description=Introducer NRIC
E_Reinvestment.title=E_Reinvestment
E_Reinvestment.description=When any policy owned by the client reaches maturity date, and client decides to buy a new policy, then the new policy can be marked as ‘Reinvestment’.Option:1 Yes,2  No
E_StaffPurchaseCode.title=E_StaffPurchaseCode
E_StaffPurchaseCode.description=Staff Purchase Code
E_PremiumFinancing.title=E_PremiumFinancing
E_PremiumFinancing.description=AN to collect this info and pass this value to ZA system (Graphene) for storage only.
certiType.title=certiType
certiType.description=ID Type.
certiType.enums.2=Passport
certiType.enums.4=Military ID Card
certiType.enums.5=Police Card
certiType.enums.9=Singapore NRIC
certiType.enums.10=Singapore Permanent Resident Card
certiType.enums.19=Certificate of Birth
certiType.enums.99=Other
certiType.enums.13=FIN
ckaEffectiveDate.title=ckaEffectiveDate
ckaEffectiveDate.description=cka EffectiveDate
nbConfigurationTypeCode.title=nbConfigurationTypeCode
nbConfigurationTypeCode.description=configuration type: 2 - verification, 3 – underwriting, 4 - compliance, 5 - policyEddective 6 - policyIssuanceCompliance
singleTopUpType.title=singleTopUpType
singleTopUpType.description=The type of single top up
branchCode.title=branchCode
branchCode.description=Company Organization code
issuanceTransactionType.title=issuanceTransactionType
issuanceTransactionType.description=Transaction type of issuance
address11.title=address11
address11.description=address11
nationality.title=nationality
nationality.description=nationality
address12.title=address12
address12.description=address12
address13.title=address13
address13.description=address13
iban.title=iban
iban.description=IBAN
fundAllocation.title=fundAllocation
fundAllocation.description=Allocation of fund
E_UWEnquiryID.title=E_UWEnquiryID
E_UWEnquiryID.description=External uw unique ref No
cardNumber.title=cardNumber
cardNumber.description=card number
gender.title=gender
gender.description=Gender
bankBranchName.title=bankBranchName
bankBranchName.description=Bank Branch Name
phoneNo.title=phoneNo
phoneNo.description=phone No
channelRole.title=channelRole
channelRole.description=Channel role:1-Officer, 2-Non-officer channel
birthPlace.title=birthPlace
birthPlace.description=birthPlace
beneficiaryRatio.title=beneficiaryRatio
beneficiaryRatio.description=Beneficiary Ratio
premium.title=premium
premium.description=Period standard premium
countryCode.title=countryCode
countryCode.description=country Code
taxNo.title=taxNo
taxNo.description=taxNo
email.title=email
email.description=Email
campaignCode.title=campaignCode
campaignCode.description=Campaign Code
bankCode.title=bankCode
bankCode.description=bank code
phoneType.title=phoneType
phoneType.description=phone type
cardHolderName.title=cardHolderName
cardHolderName.description=card holder name
holder.title=holder
holder.description=Holder info
userId.title=userId
userId.description=User ID
ruleCode.title=ruleCode
ruleCode.description=
partTimeJob.title=partTimeJob
partTimeJob.description=partTimeJob
issuanceNo.title=issuanceNo
issuanceNo.description=If you want to specify issuanceNo, you can pass in.
paymentOrderNo.title=paymentOrderNo
paymentOrderNo.description=Payment serial number
effectiveDate.title=effectiveDate
effectiveDate.description=effective Date
zipCode.title=zipCode
zipCode.description=zipCode
E_MyInfo.title=E_MyInfo
E_MyInfo.description=MyInfo
ruleDecisionCode.title=ruleDecisionCode
ruleDecisionCode.description=
expiryDateOfCertificate.title=expiryDateOfCertificate
expiryDateOfCertificate.description=expiryDate Of Certificate
countryOfBirth.title=countryOfBirth
countryOfBirth.description=country of birth
salesChannelCode.title=salesChannelCode
salesChannelCode.description=sales Channel Code
taxResidentialCountry.title=taxResidentialCountry
taxResidentialCountry.description=tax residential country
E_TaxNoNotAvailableReasonCode.title=E_TaxNoNotAvailableReasonCode
E_TaxNoNotAvailableReasonCode.description=if taxNo is not available,give reasoncode A or B or C 
E_TaxNoNotAvailableReason.title=E_TaxNoNotAvailableReason
E_TaxNoNotAvailableReason.description=if taxNo is not available,give reasoncode B,must fill in reason
insureDate.title=insureDate
insureDate.description=insureDate(esubmission date)
zoneId.title=zoneId
zoneId.description=Time zone
beneficiaries.title=beneficiaries
beneficiaries.description=beneficiaries
numberOfIssuance.title=numberOfIssuance
numberOfIssuance.description=The number of issuance
previousPolicyNo.title=previousPolicyNo
previousPolicyNo.description=Original policy number
issueWithoutPayment.title=issueWithoutPayment
issueWithoutPayment.description=The policy effective without pay
orderNo.title=orderNo
orderNo.description=Order number
organizationName.title=organizationName
organizationName.description=organizationName
addressType.title=addressType
addressType.description=address type
accountType.title=accountType
accountType.description=Account Type
promotionCode.title=promotionCode
promotionCode.description=Promotion Code
weight.title=weight
weight.description=weight
certiNo.title=certiNo
certiNo.description=ID Number
salesCurrency.title=salesCurrency
salesCurrency.description=Sales Currency
salesCurrency.enums.SGD=SGD
salesCurrency.enums.MYR=MYR
paymentMethod.title=paymentMethod
paymentMethod.description=Payment methods
trustees.title=trustees
trustees.description=trustees
income.title=income
income.description=income
birthday.title=birthday
birthday.description=Date of Birth
premiumPeriod.title=premiumPeriod
premiumPeriod.description=Premium Period
isRenewalPolicy.title=isRenewalPolicy
isRenewalPolicy.description=Whether it is renewal
coveragePeriod.title=coveragePeriod
coveragePeriod.description=
accountSubType.title=accountSubType
accountSubType.description=Account Sub Type
swiftCode.title=swiftCode
swiftCode.description=SWIFT Code
bankName.title=bankName
bankName.description=bank name
remark.title=remark
remark.description=remark
expiryDate.title=expiryDate
expiryDate.description=card expiry date
rate.title=rate
rate.description=rate
bankBranchCode.title=bankBranchCode
bankBranchCode.description=Bank Branch Code
amount.title=amount
amount.description=Fund amount
race.title=race
race.description=race
insureds.title=insureds
insureds.description=insureds
premiumFrequencyType.title=premiumFrequencyType
premiumFrequencyType.description=Premium Frequency Type
smoke.title=smoke
smoke.description=smoke
typeOfPass.title=typeOfPass
typeOfPass.description=typeOfPass
fullName.title=fullName
fullName.description=Full Name
marriageStatus.title=marriageStatus
marriageStatus.description=marriageStatus
premiumType.title=premiumType
premiumType.description=The type of premium
bankCity.title=bankCity
bankCity.description=Bank City
occupationCode.title=occupationCode
occupationCode.description=occupationCode
singleTopUpType.enums.1=AD_HOC_SINGLE_TOP_UP
singleTopUpType.enums.2=RECURRING_SINGLE_TOP_UP
issuanceTransactionType.enums.3=UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE
issuanceTransactionType.enums.1=UW_AND_CREATEISSUANCE
issuanceTransactionType.enums.9=WAITING_FOR_CREATEISSUANCE
issuanceTransactionType.enums.10=UPDATE_ISSUANCE
gender.enums.1=Male
gender.enums.2=Female
channelRole.enums.2=NON_OFFICE
channelRole.enums.1=OFFICEE
issueWithoutPayment.enums.1=Y
issueWithoutPayment.enums.2=N
title.enums.1=Mr.
title.enums.2=Ms.
title.enums.3=Mrs.
title.enums.4=Master
title.enums.5=Miss
addressType.enums.1=Address
addressType.enums.2=Home
addressType.enums.3=Work
addressType.enums.4=Address
addressType.enums.5=Overseas
addressType.enums.6=Certificate
addressType.enums.7=Contact
addressType.enums.8=Permanent
addressType.enums.9=Residential
addressType.enums.10=Mailing
addressType.enums.11=Billing
accountType.enums.2=Credit Card
accountType.enums.8=GMO_PAY_WALLET
accountType.enums.38=Business Registration
accountType.enums.61=FPX
accountType.enums.62=E wallet
accountType.enums.63=Spare Change
paymentMethod.enums.114=Stripe Pay
paymentMethod.enums.115=PayNow
paymentMethod.enums.119=Pay Pay
paymentMethod.enums.110=Cheque
paymentMethod.enums.112=INTERNAL_TRANSFER
paymentMethod.enums.201=Insurance benefit
paymentMethod.enums.127=Other Pay
paymentMethod.enums.213=BP_PHX
paymentMethod.enums.135=BP
paymentMethod.enums.130=Offline
paymentMethod.enums.102=Bank Transfer
paymentMethod.enums.107=Insurance Pay
paymentMethod.enums.2=Credit Card
paymentMethod.enums.1=Debit Card
paymentMethod.enums.101=Credit Card
paymentMethod.enums.100=Cash
isRenewalPolicy.enums.1=YES
isRenewalPolicy.enums.2=NO
premiumFrequencyType.enums.5=Monthly
premiumFrequencyType.enums.2=Yearly
smoke.enums.2=N
smoke.enums.1=Y
premiumPeriod.enums.5-120=MONTH-120
coveragePeriod.enums.5-10=YEARFULL-10
marriageStatus.enums.3=DIVORCED
marriageStatus.enums.2=MARRIED
marriageStatus.enums.5=MARRYING
marriageStatus.enums.4=WIDOWED
marriageStatus.enums.99=UNKNOWN
marriageStatus.enums.1=UNMARRIED
marriageStatus.enums.7=MARRIEDAGAIN
marriageStatus.enums.6=OTHERMARRIED
marriageStatus.enums.9=MARRIED_WITH_NO_REGISTER
marriageStatus.enums.8=UNINTRODUCE
premiumType.enums.3=Single Top Up
premiumType.enums.2=Regular Top Up
premiumType.enums.1=Planned Premium
nationality.enums.SDN=Sudan
nationality.enums.GMB=Gambia
nationality.enums.CUB=Cuba
nationality.enums.MHL=Marshall Islands
nationality.enums.WLF=Wallis and Futuna Islands
nationality.enums.STP=Sao Tome and Principe
nationality.enums.MYT=Mayotte
nationality.enums.MYS=Malaysia
nationality.enums.POL=Poland
nationality.enums.TWN=Taiwan, China
nationality.enums.CUW=Curaçao
nationality.enums.ARG=Argentina
nationality.enums.ARM=Armenia
nationality.enums.BTN=Bhutan
nationality.enums.UZB=Uzbekistan
nationality.enums.SEN=Senegal
nationality.enums.TGO=Togo
nationality.enums.GNB=Guinea-Bissau
nationality.enums.OMN=Oman
nationality.enums.SUR=Suriname
nationality.enums.ARE=U.A.E
nationality.enums.KEN=Kenya
nationality.enums.IRQ=Iraq
nationality.enums.SVK=Slovakia
nationality.enums.BDI=Burundi
nationality.enums.NLD=Netherlands
nationality.enums.THA=Thailand
nationality.enums.SVN=Slovenia
nationality.enums.GNQ=Equatorial Guinea
nationality.enums.FLK=Malvinas Islands (Falkland Islands)
nationality.enums.IRN=Iran
nationality.enums.QAT=Qatar
nationality.enums.IRL=Ireland
nationality.enums.ABW=Aruba
nationality.enums.ASM=American Samoa
nationality.enums.BEL=Belgium
nationality.enums.ISR=Israel
nationality.enums.KWT=Kuwait
nationality.enums.LIE=Liechtenstein
nationality.enums.MKD=North Macedonia
nationality.enums.BEN=Benin
nationality.enums.DZA=Algeria
nationality.enums.SWE=Sweden
nationality.enums.ISL=Iceland
nationality.enums.ITA=Italy
nationality.enums.SWZ=Eswatini
nationality.enums.PAN=Panama
nationality.enums.BFA=Burkina Faso
nationality.enums.CXR=Christmas Island
nationality.enums.SGP=Singapore
nationality.enums.UKR=Ukraine
nationality.enums.TZA=Tanzania
nationality.enums.PAK=Pakistan
nationality.enums.BES=Caribbean Netherlands
nationality.enums.RUS=Russia
nationality.enums.ATA=Antarctica
nationality.enums.ATG=Antigua and Barbuda
nationality.enums.ATF=French Southern Territories
nationality.enums.PRK=North Korea
nationality.enums.CHN=China
nationality.enums.SXM=Sint Maarten
nationality.enums.PRI=Puerto Rico
nationality.enums.CHL=Chile
nationality.enums.BWA=Botswana
nationality.enums.HRV=Croatia
nationality.enums.MLI=Mali
nationality.enums.BVT=Bouvet Island
nationality.enums.CHE=Switzerland
nationality.enums.SGS=South Georgia and South Sandwich Island
nationality.enums.JEY=Jersey
nationality.enums.KGZ=Kyrgyzstan
nationality.enums.DJI=Djibouti
nationality.enums.REU=Réunion
nationality.enums.SHN=Saint Helena
nationality.enums.PRY=Paraguay
nationality.enums.CYM=Cayman Islands
nationality.enums.SYC=Seychelles
nationality.enums.RWA=Rwanda
nationality.enums.CYP=Cyprus
nationality.enums.KHM=Cambodia
nationality.enums.MLT=Malta
nationality.enums.TJK=Tajikistan
nationality.enums.VNM=Vietnam
nationality.enums.IDN=Indonesia
nationality.enums.PRT=Portugal
nationality.enums.GAB=Gabon
nationality.enums.ZWE=Zimbabwe
nationality.enums.BGR=Bulgaria
nationality.enums.SYR=Syria
nationality.enums.AUT=Austria
nationality.enums.AUS=Australia
nationality.enums.BGD=Bangladesh
nationality.enums.LKA=Sri Lanka
nationality.enums.PSE=Palestine
nationality.enums.GRD=Grenada
nationality.enums.GRC=Greece
nationality.enums.TKM=Turkmenistan
nationality.enums.HTI=Haiti
nationality.enums.PCN=Pitcairn Islands
nationality.enums.CIV=Côte d’Ivoire
nationality.enums.NOR=Norway
nationality.enums.UMI=Small outer islands of the United States mainland
nationality.enums.CZE=Czech Republic
nationality.enums.TKL=Tokelau
nationality.enums.KIR=Kiribati
nationality.enums.MMR=Myanmar
nationality.enums.MNG=Mongolia
nationality.enums.BHR=Bahrain
nationality.enums.BHS=Bahamas
nationality.enums.NPL=Nepal
nationality.enums.GRL=Greenland
nationality.enums.YEM=Yemen
nationality.enums.MNE=Montenegro
nationality.enums.AFG=Afghanistan
nationality.enums.DMA=Dominica
nationality.enums.GBR=United Kingdom
nationality.enums.SJM=Svalbard and Janma
nationality.enums.TLS=Timor-Leste
nationality.enums.MNP=Northern Mariana
nationality.enums.AGO=Angola
nationality.enums.OTHERS=Unknown
nationality.enums.HUN=Hungary
nationality.enums.BIH=Bosnia and Herzegovina
nationality.enums.MOZ=Mozambique
nationality.enums.PER=Peru
nationality.enums.NAM=Namibia
nationality.enums.WSM=Western Samoa
nationality.enums.FRA=France
nationality.enums.SLB=Solomon Islands
nationality.enums.VAT=Vatican City
nationality.enums.SLE=Sierra Leone
nationality.enums.DNK=Denmark
nationality.enums.GTM=Guatemala
nationality.enums.FRO=Faroe Islands
nationality.enums.AIA=Anguilla
nationality.enums.GUF=French Guiana
nationality.enums.SLV=El Salvador
nationality.enums.NRU=Nauru
nationality.enums.CMR=Cameroon
nationality.enums.AZE=Azerbaijan
nationality.enums.GUY=Guyana
nationality.enums.FSM=Micronesia
nationality.enums.GUM=Guam
nationality.enums.DOM=Dominican Republic
nationality.enums.SMR=San Marino
nationality.enums.NCL=New Caledonia
nationality.enums.ERI=Eritrea
nationality.enums.GEO=Georgia
nationality.enums.MAC=Macau
nationality.enums.TON=Tonga
nationality.enums.MAF=Saint Martin, France
nationality.enums.BLR=Belarus
nationality.enums.VCT=Saint Vincent and the Grenadines
nationality.enums.KNA=Saint Kitts and Nevis
nationality.enums.MAR=Morocco
nationality.enums.BLM=Saint Barthélemy
nationality.enums.COD=DRC
nationality.enums.PYF=French Polynesia
nationality.enums.URY=Uruguay
nationality.enums.ESH=Western Sahara
nationality.enums.COG=Congo Brazzaville
nationality.enums.BLZ=Belize
nationality.enums.MRT=Mauritania
nationality.enums.PHL=Philippines
nationality.enums.EST=Estonia
nationality.enums.MSR=Montserrat
nationality.enums.BMU=Bermuda
nationality.enums.COL=Colombia
nationality.enums.COM=Comoros
nationality.enums.COK=Cook Islands
nationality.enums.ESP=Spain
nationality.enums.USA=United States
nationality.enums.GGY=Guernsey
nationality.enums.MCO=Monaco
nationality.enums.ETH=Ethiopia
nationality.enums.ALA=Åland Islands (Finnish genus)
nationality.enums.ALB=Albania
nationality.enums.ECU=Ecuador
nationality.enums.NER=Niger
nationality.enums.LAO=Laos
nationality.enums.SOM=Somalia
nationality.enums.ZMB=Zambia
nationality.enums.KOR=Korea
nationality.enums.VUT=Vanuatu
nationality.enums.CPV=Cape Verde
nationality.enums.MDA=Moldova
nationality.enums.MTQ=Martinique
nationality.enums.GHA=Ghana
nationality.enums.VEN=Venezuela
nationality.enums.LBN=Lebanon
nationality.enums.LBR=Liberia
nationality.enums.MDG=Madagascar
nationality.enums.SPM=Saint Pierre and Miquelon
nationality.enums.NFK=Norfolk Island
nationality.enums.CAF=Middle Africa
nationality.enums.LSO=Lesotho
nationality.enums.NGA=Nigeria
nationality.enums.JOR=Jordan
nationality.enums.GIN=Guinea
nationality.enums.MUS=Mauritius
nationality.enums.IMN=Isle of Man
nationality.enums.LCA=Saint Lucia
nationality.enums.GIB=Gibraltar
nationality.enums.MDV=Maldives
nationality.enums.BOL=Bolivia
nationality.enums.HKG=China (Hong Kong)
nationality.enums.LBY=Libya
nationality.enums.AND=Andorra
nationality.enums.CRI=Costa Rica
nationality.enums.CAN=Canada
nationality.enums.VGB=British Virgin Islands
nationality.enums.ROM=Romanian
nationality.enums.TCD=Chad
nationality.enums.TCA=Turks and Caicos Islands
nationality.enums.JPN=Japan
nationality.enums.SAU=Saudi Arabia
nationality.enums.ANT=Netherlands Antilles
nationality.enums.LTU=Lithuania
nationality.enums.MEX=Mexico
nationality.enums.IND=India
nationality.enums.XKX=Kosovo
nationality.enums.KAZ=Kazakhstan
nationality.enums.SRB=Serbia and Montenegro
nationality.enums.PLW=Palau
nationality.enums.TTO=Trinidad and Tobago
nationality.enums.NIC=Nicaragua
nationality.enums.TUN=Tunisia
nationality.enums.FIN=Finland
nationality.enums.CCK=Cocos (Keeling) Islands
nationality.enums.HMD=Heard Island and McDonald Island
nationality.enums.MWI=Malawi
nationality.enums.SSD=Republic of South Sudan
nationality.enums.LVA=Latvia
nationality.enums.EGY=Egypt
nationality.enums.JAM=Jamaica
nationality.enums.NIU=Niue
nationality.enums.TUR=Turkey
nationality.enums.BRA=Brazil
nationality.enums.LUX=Luxembourg
nationality.enums.UGA=Uganda
nationality.enums.IOT=British Indian Ocean Territory
nationality.enums.DEU=Germany
nationality.enums.TUV=Tuvalu
nationality.enums.BRB=Barbados
nationality.enums.PNG=Papua New Guinea
nationality.enums.GLP=Guadeloupe
nationality.enums.NZL=New Zealand
nationality.enums.BRN=Brunei
nationality.enums.VIR=United States Virgin Islands
nationality.enums.ZAF=South Africa
nationality.enums.HND=Honduras
nationality.enums.FJI=Fiji
countryCode.enums.+1809=+1809
countryCode.enums.+91=+91
countryCode.enums.+993=+993
countryCode.enums.+92=+92
countryCode.enums.+992=+992
countryCode.enums.+93=+93
countryCode.enums.+94=+94
countryCode.enums.BVT=BVT
countryCode.enums.SGS=SGS
countryCode.enums.+90=+90
countryCode.enums.+1242=+1242
countryCode.enums.+998=+998
countryCode.enums.+84=+84
countryCode.enums.+1246=+1246
countryCode.enums.+996=+996
countryCode.enums.+86=+86
countryCode.enums.+995=+995
countryCode.enums.+994=+994
countryCode.enums.ATF=ATF
countryCode.enums.+880=+880
countryCode.enums.+1473=+1473
countryCode.enums.+95=+95
countryCode.enums.+886=+886
countryCode.enums.+98=+98
countryCode.enums.+1340=+1340
countryCode.enums.+1345=+1345
countryCode.enums.UMI=UMI
countryCode.enums.+421=+421
countryCode.enums.+420=+420
countryCode.enums.+423=+423
countryCode.enums.+674=+674
countryCode.enums.+673=+673
countryCode.enums.+672=+672
countryCode.enums.+670=+670
countryCode.enums.+44=+44
countryCode.enums.+45=+45
countryCode.enums.+46=+46
countryCode.enums.+679=+679
countryCode.enums.+47=+47
countryCode.enums.+40=+40
countryCode.enums.+678=+678
countryCode.enums.+41=+41
countryCode.enums.+677=+677
countryCode.enums.+1441=+1441
countryCode.enums.+676=+676
countryCode.enums.+43=+43
countryCode.enums.+675=+675
countryCode.enums.+48=+48
countryCode.enums.+49=+49
countryCode.enums.+685=+685
countryCode.enums.+684=+684
countryCode.enums.+60=+60
countryCode.enums.+683=+683
countryCode.enums.+61=+61
countryCode.enums.+682=+682
countryCode.enums.+681=+681
countryCode.enums.+680=+680
countryCode.enums.+55=+55
countryCode.enums.+56=+56
countryCode.enums.+1671=+1671
countryCode.enums.+57=+57
countryCode.enums.+58=+58
countryCode.enums.+51=+51
countryCode.enums.+689=+689
countryCode.enums.+52=+52
countryCode.enums.+688=+688
countryCode.enums.+53=+53
countryCode.enums.+687=+687
countryCode.enums.+54=+54
countryCode.enums.+686=+686
countryCode.enums.+44-1624=+44-1624
countryCode.enums.+212=+212
countryCode.enums.+211=+211
countryCode.enums.+692=+692
countryCode.enums.+691=+691
countryCode.enums.+690=+690
countryCode.enums.+66=+66
countryCode.enums.+218=+218
countryCode.enums.+62=+62
countryCode.enums.+216=+216
countryCode.enums.+1784=+1784
countryCode.enums.+1664=+1664
countryCode.enums.+63=+63
countryCode.enums.+64=+64
countryCode.enums.+213=+213
countryCode.enums.+65=+65
countryCode.enums.+223=+223
countryCode.enums.+81=+81
countryCode.enums.+222=+222
countryCode.enums.+82=+82
countryCode.enums.+221=+221
countryCode.enums.+220=+220
countryCode.enums.+229=+229
countryCode.enums.+228=+228
countryCode.enums.+73=+73
countryCode.enums.+227=+227
countryCode.enums.+226=+226
countryCode.enums.+225=+225
countryCode.enums.+224=+224
countryCode.enums.+355=+355
countryCode.enums.+234=+234
countryCode.enums.+597=+597
countryCode.enums.+233=+233
countryCode.enums.+354=+354
countryCode.enums.+596=+596
countryCode.enums.+353=+353
countryCode.enums.+595=+595
countryCode.enums.+232=+232
countryCode.enums.+594=+594
countryCode.enums.+231=+231
countryCode.enums.+352=+352
countryCode.enums.+593=+593
countryCode.enums.+230=+230
countryCode.enums.+351=+351
countryCode.enums.+350=+350
countryCode.enums.+592=+592
countryCode.enums.+591=+591
countryCode.enums.+590=+590
countryCode.enums.+239=+239
countryCode.enums.+359=+359
countryCode.enums.+238=+238
countryCode.enums.+358=+358
countryCode.enums.+237=+237
countryCode.enums.+599=+599
countryCode.enums.+236=+236
countryCode.enums.+357=+357
countryCode.enums.+356=+356
countryCode.enums.+235=+235
countryCode.enums.+598=+598
countryCode.enums.+1767=+1767
countryCode.enums.+1649=+1649
countryCode.enums.+245=+245
countryCode.enums.+244=+244
countryCode.enums.+243=+243
countryCode.enums.+242=+242
countryCode.enums.+241=+241
countryCode.enums.+240=+240
countryCode.enums.+249=+249
countryCode.enums.+248=+248
countryCode.enums.+246=+246
countryCode.enums.+1876=+1876
countryCode.enums.+1758=+1758
countryCode.enums.+377=+377
countryCode.enums.+256=+256
countryCode.enums.+376=+376
countryCode.enums.+255=+255
countryCode.enums.+375=+375
countryCode.enums.+254=+254
countryCode.enums.+374=+374
countryCode.enums.+253=+253
countryCode.enums.+373=+373
countryCode.enums.+252=+252
countryCode.enums.+372=+372
countryCode.enums.+251=+251
countryCode.enums.+371=+371
countryCode.enums.+250=+250
countryCode.enums.+370=+370
countryCode.enums.+20=+20
countryCode.enums.+258=+258
countryCode.enums.+379=+379
countryCode.enums.+257=+257
countryCode.enums.+378=+378
countryCode.enums.+1869=+1869
countryCode.enums.+27=+27
countryCode.enums.+1868=+1868
countryCode.enums.+380=+380
countryCode.enums.+1=+1
countryCode.enums.+267=+267
countryCode.enums.+387=+387
countryCode.enums.+266=+266
countryCode.enums.+44-1481=+44-1481
countryCode.enums.+265=+265
countryCode.enums.+386=+386
countryCode.enums.+385=+385
countryCode.enums.+264=+264
countryCode.enums.+263=+263
countryCode.enums.HMD=HMD
countryCode.enums.+262=+262
countryCode.enums.+383=+383
countryCode.enums.+261=+261
countryCode.enums.+382=+382
countryCode.enums.+381=+381
countryCode.enums.+260=+260
countryCode.enums.+33=+33
countryCode.enums.+34=+34
countryCode.enums.+36=+36
countryCode.enums.+30=+30
countryCode.enums.+269=+269
countryCode.enums.+31=+31
countryCode.enums.+32=+32
countryCode.enums.+389=+389
countryCode.enums.+268=+268
countryCode.enums.+7=+7
countryCode.enums.+39=+39
countryCode.enums.+1284=+1284
countryCode.enums.+960=+960
countryCode.enums.+167=+167
countryCode.enums.+968=+968
countryCode.enums.+967=+967
countryCode.enums.+6723=+6723
countryCode.enums.+966=+966
countryCode.enums.+965=+965
countryCode.enums.+964=+964
countryCode.enums.+721=+721
countryCode.enums.+963=+963
countryCode.enums.+962=+962
countryCode.enums.+961=+961
countryCode.enums.+291=+291
countryCode.enums.+290=+290
countryCode.enums.+971=+971
countryCode.enums.+850=+850
countryCode.enums.+299=+299
countryCode.enums.+970=+970
countryCode.enums.+298=+298
countryCode.enums.+297=+297
countryCode.enums.+1264=+1264
countryCode.enums.+856=+856
countryCode.enums.+977=+977
countryCode.enums.+855=+855
countryCode.enums.+976=+976
countryCode.enums.+975=+975
countryCode.enums.+1268=+1268
countryCode.enums.+853=+853
countryCode.enums.+974=+974
countryCode.enums.+973=+973
countryCode.enums.+852=+852
countryCode.enums.+972=+972
countryCode.enums.MAF=MAF
countryCode.enums.+506=+506
countryCode.enums.+505=+505
countryCode.enums.+504=+504
countryCode.enums.+503=+503
countryCode.enums.+502=+502
countryCode.enums.+501=+501
countryCode.enums.+500=+500
countryCode.enums.+509=+509
countryCode.enums.+508=+508
countryCode.enums.+507=+507
goodsId.title=goodsId
goodsId.description=goodsId
goodsId.enums.1278100746813440=InvestPro Link
premiumPeriod.enums.3=WHOLELIFE
preferredLanguage.title=preferredLanguage
preferredLanguage.description=Preferred language.
preferredLanguage.enums.20=IRISH
preferredLanguage.enums.40=MALAY
preferredLanguage.enums.29=SPANISH
preferredLanguage.enums.28=SLOVENIAN
preferredLanguage.enums.27=SLOVAK
preferredLanguage.enums.26=ROMANIAN
preferredLanguage.enums.25=PORTUGUESE
preferredLanguage.enums.24=POLISH
preferredLanguage.enums.23=MALTESE
preferredLanguage.enums.22=LITHUANIAN
preferredLanguage.enums.21=LATVIAN
preferredLanguage.enums.31=KOREAN
preferredLanguage.enums.30=SWEDISH
preferredLanguage.enums.19=HUNGARIAN
preferredLanguage.enums.18=GREEK
preferredLanguage.enums.7=TraditionalChinese
preferredLanguage.enums.17=FINNISH
preferredLanguage.enums.39=VIETNAMESE
preferredLanguage.enums.8=Japanese
preferredLanguage.enums.16=ESTONIAN
preferredLanguage.enums.38=TAMIL
preferredLanguage.enums.15=DUTCH
preferredLanguage.enums.37=ARABIC
preferredLanguage.enums.6=SimplifiedChinese
preferredLanguage.enums.14=DANISH
preferredLanguage.enums.36=HINDIHINDI
preferredLanguage.enums.3=French
preferredLanguage.enums.13=CZECH
preferredLanguage.enums.35=RUSSIAN
preferredLanguage.enums.4=Italian
preferredLanguage.enums.12=CROATIAN
preferredLanguage.enums.34=NORWEGIAN
preferredLanguage.enums.1=English
preferredLanguage.enums.11=BULGARIAN
preferredLanguage.enums.33=TAKAFUL
preferredLanguage.enums.2=German
preferredLanguage.enums.10=THAI
preferredLanguage.enums.32=BAHASA
taxResidentialCountry.enums.GAB=Gabon
taxResidentialCountry.enums.ZWE=Zimbabwe
taxResidentialCountry.enums.SYR=Syria
taxResidentialCountry.enums.AUT=Austria
taxResidentialCountry.enums.AUS=Australia
taxResidentialCountry.enums.BGD=Bangladesh
taxResidentialCountry.enums.SYC=Seychelles
taxResidentialCountry.enums.LKA=Sri Lanka
taxResidentialCountry.enums.PSE=Palestine
taxResidentialCountry.enums.CYM=Cayman Islands
taxResidentialCountry.enums.PRY=Paraguay
taxResidentialCountry.enums.MLT=Malta
taxResidentialCountry.enums.TJK=Tajikistan
taxResidentialCountry.enums.VNM=Vietnam
taxResidentialCountry.enums.RWA=Rwanda
taxResidentialCountry.enums.CYP=Cyprus
taxResidentialCountry.enums.SHN=Saint Helena
taxResidentialCountry.enums.KHM=Cambodia
taxResidentialCountry.enums.IDN=Indonesia
taxResidentialCountry.enums.PRT=Portugal
taxResidentialCountry.enums.OTHERS=Unknown
taxResidentialCountry.enums.MNG=Mongolia
taxResidentialCountry.enums.BHR=Bahrain
taxResidentialCountry.enums.NPL=Nepal
taxResidentialCountry.enums.HTI=Haiti
taxResidentialCountry.enums.MNE=Montenegro
taxResidentialCountry.enums.AFG=Afghanistan
taxResidentialCountry.enums.GRL=Greenland
taxResidentialCountry.enums.YEM=Yemen
taxResidentialCountry.enums.GRC=Greece
taxResidentialCountry.enums.TKM=Turkmenistan
taxResidentialCountry.enums.TKL=Tokelau
taxResidentialCountry.enums.PCN=Pitcairn Islands
taxResidentialCountry.enums.GRD=Grenada
taxResidentialCountry.enums.CIV=Côte d’Ivoire
taxResidentialCountry.enums.CZE=Czech Republic
taxResidentialCountry.enums.BGR=Bulgaria
taxResidentialCountry.enums.KIR=Kiribati
taxResidentialCountry.enums.MMR=Myanmar
taxResidentialCountry.enums.NOR=Norway
taxResidentialCountry.enums.UMI=Small outer islands of the United States mainland
taxResidentialCountry.enums.AGO=Angola
taxResidentialCountry.enums.BIH=Bosnia and Herzegovina
taxResidentialCountry.enums.HUN=Hungary
taxResidentialCountry.enums.DMA=Dominica
taxResidentialCountry.enums.GBR=United Kingdom
taxResidentialCountry.enums.SJM=Svalbard and Janma
taxResidentialCountry.enums.TLS=Timor-Leste
taxResidentialCountry.enums.MNP=Northern Mariana
taxResidentialCountry.enums.BHS=Bahamas
taxResidentialCountry.enums.SLE=Sierra Leone
taxResidentialCountry.enums.SLB=Solomon Islands
taxResidentialCountry.enums.VAT=Vatican City
taxResidentialCountry.enums.GTM=Guatemala
taxResidentialCountry.enums.PER=Peru
taxResidentialCountry.enums.FRO=Faroe Islands
taxResidentialCountry.enums.DNK=Denmark
taxResidentialCountry.enums.MOZ=Mozambique
taxResidentialCountry.enums.FRA=France
taxResidentialCountry.enums.NAM=Namibia
taxResidentialCountry.enums.WSM=Western Samoa
taxResidentialCountry.enums.MYT=Mayotte
taxResidentialCountry.enums.MYS=Malaysia
taxResidentialCountry.enums.CUW=Curaçao
taxResidentialCountry.enums.TWN=Taiwan, China
taxResidentialCountry.enums.SDN=Sudan
taxResidentialCountry.enums.GMB=Gambia
taxResidentialCountry.enums.CUB=Cuba
taxResidentialCountry.enums.STP=Sao Tome and Principe
taxResidentialCountry.enums.MHL=Marshall Islands
taxResidentialCountry.enums.WLF=Wallis and Futuna Islands
taxResidentialCountry.enums.IRQ=Iraq
taxResidentialCountry.enums.SVK=Slovakia
taxResidentialCountry.enums.BDI=Burundi
taxResidentialCountry.enums.NLD=Netherlands
taxResidentialCountry.enums.FLK=Malvinas Islands (Falkland Islands)
taxResidentialCountry.enums.IRN=Iran
taxResidentialCountry.enums.SVN=Slovenia
taxResidentialCountry.enums.GNQ=Equatorial Guinea
taxResidentialCountry.enums.UZB=Uzbekistan
taxResidentialCountry.enums.QAT=Qatar
taxResidentialCountry.enums.IRL=Ireland
taxResidentialCountry.enums.ARG=Argentina
taxResidentialCountry.enums.ARM=Armenia
taxResidentialCountry.enums.BTN=Bhutan
taxResidentialCountry.enums.SEN=Senegal
taxResidentialCountry.enums.TGO=Togo
taxResidentialCountry.enums.GNB=Guinea-Bissau
taxResidentialCountry.enums.OMN=Oman
taxResidentialCountry.enums.SUR=Suriname
taxResidentialCountry.enums.POL=Poland
taxResidentialCountry.enums.ARE=U.A.E
taxResidentialCountry.enums.KEN=Kenya
taxResidentialCountry.enums.ISR=Israel
taxResidentialCountry.enums.KWT=Kuwait
taxResidentialCountry.enums.LIE=Liechtenstein
taxResidentialCountry.enums.MKD=North Macedonia
taxResidentialCountry.enums.BEN=Benin
taxResidentialCountry.enums.DZA=Algeria
taxResidentialCountry.enums.BEL=Belgium
taxResidentialCountry.enums.ISL=Iceland
taxResidentialCountry.enums.SWE=Sweden
taxResidentialCountry.enums.ABW=Aruba
taxResidentialCountry.enums.ASM=American Samoa
taxResidentialCountry.enums.THA=Thailand
taxResidentialCountry.enums.SXM=Sint Maarten
taxResidentialCountry.enums.CHL=Chile
taxResidentialCountry.enums.PRI=Puerto Rico
taxResidentialCountry.enums.BWA=Botswana
taxResidentialCountry.enums.HRV=Croatia
taxResidentialCountry.enums.MLI=Mali
taxResidentialCountry.enums.CHN=China
taxResidentialCountry.enums.PRK=North Korea
taxResidentialCountry.enums.BVT=Bouvet Island
taxResidentialCountry.enums.CHE=Switzerland
taxResidentialCountry.enums.SGS=South Georgia and South Sandwich Island
taxResidentialCountry.enums.JEY=Jersey
taxResidentialCountry.enums.KGZ=Kyrgyzstan
taxResidentialCountry.enums.BFA=Burkina Faso
taxResidentialCountry.enums.CXR=Christmas Island
taxResidentialCountry.enums.PAN=Panama
taxResidentialCountry.enums.DJI=Djibouti
taxResidentialCountry.enums.REU=Réunion
taxResidentialCountry.enums.ITA=Italy
taxResidentialCountry.enums.SWZ=Eswatini
taxResidentialCountry.enums.SGP=Singapore
taxResidentialCountry.enums.UKR=Ukraine
taxResidentialCountry.enums.TZA=Tanzania
taxResidentialCountry.enums.PAK=Pakistan
taxResidentialCountry.enums.BES=Caribbean Netherlands
taxResidentialCountry.enums.RUS=Russia
taxResidentialCountry.enums.ATA=Antarctica
taxResidentialCountry.enums.ATG=Antigua and Barbuda
taxResidentialCountry.enums.ATF=French Southern Territories
taxResidentialCountry.enums.CAF=Middle Africa
taxResidentialCountry.enums.LSO=Lesotho
taxResidentialCountry.enums.NGA=Nigeria
taxResidentialCountry.enums.GIN=Guinea
taxResidentialCountry.enums.MUS=Mauritius
taxResidentialCountry.enums.IMN=Isle of Man
taxResidentialCountry.enums.LCA=Saint Lucia
taxResidentialCountry.enums.GIB=Gibraltar
taxResidentialCountry.enums.BOL=Bolivia
taxResidentialCountry.enums.MDV=Maldives
taxResidentialCountry.enums.HKG=China (Hong Kong)
taxResidentialCountry.enums.LBY=Libya
taxResidentialCountry.enums.LBN=Lebanon
taxResidentialCountry.enums.LBR=Liberia
taxResidentialCountry.enums.MDG=Madagascar
taxResidentialCountry.enums.SPM=Saint Pierre and Miquelon
taxResidentialCountry.enums.NFK=Norfolk Island
taxResidentialCountry.enums.JPN=Japan
taxResidentialCountry.enums.ANT=Netherlands Antilles
taxResidentialCountry.enums.SAU=Saudi Arabia
taxResidentialCountry.enums.XKX=Kosovo
taxResidentialCountry.enums.MEX=Mexico
taxResidentialCountry.enums.IND=India
taxResidentialCountry.enums.KAZ=Kazakhstan
taxResidentialCountry.enums.SRB=Serbia and Montenegro
taxResidentialCountry.enums.AND=Andorra
taxResidentialCountry.enums.TCD=Chad
taxResidentialCountry.enums.CRI=Costa Rica
taxResidentialCountry.enums.CAN=Canada
taxResidentialCountry.enums.ROM=Romanian
taxResidentialCountry.enums.JOR=Jordan
taxResidentialCountry.enums.TCA=Turks and Caicos Islands
taxResidentialCountry.enums.VGB=British Virgin Islands
taxResidentialCountry.enums.NIC=Nicaragua
taxResidentialCountry.enums.CCK=Cocos (Keeling) Islands
taxResidentialCountry.enums.FIN=Finland
taxResidentialCountry.enums.HMD=Heard Island and McDonald Island
taxResidentialCountry.enums.MWI=Malawi
taxResidentialCountry.enums.SSD=Republic of South Sudan
taxResidentialCountry.enums.PLW=Palau
taxResidentialCountry.enums.TTO=Trinidad and Tobago
taxResidentialCountry.enums.LTU=Lithuania
taxResidentialCountry.enums.FJI=Fiji
taxResidentialCountry.enums.GLP=Guadeloupe
taxResidentialCountry.enums.PNG=Papua New Guinea
taxResidentialCountry.enums.NZL=New Zealand
taxResidentialCountry.enums.VIR=United States Virgin Islands
taxResidentialCountry.enums.ZAF=South Africa
taxResidentialCountry.enums.BRN=Brunei
taxResidentialCountry.enums.HND=Honduras
taxResidentialCountry.enums.EGY=Egypt
taxResidentialCountry.enums.LVA=Latvia
taxResidentialCountry.enums.JAM=Jamaica
taxResidentialCountry.enums.TUV=Tuvalu
taxResidentialCountry.enums.NIU=Niue
taxResidentialCountry.enums.LUX=Luxembourg
taxResidentialCountry.enums.UGA=Uganda
taxResidentialCountry.enums.IOT=British Indian Ocean Territory
taxResidentialCountry.enums.TUN=Tunisia
taxResidentialCountry.enums.DEU=Germany
taxResidentialCountry.enums.BRB=Barbados
taxResidentialCountry.enums.BRA=Brazil
taxResidentialCountry.enums.TUR=Turkey
taxResidentialCountry.enums.CMR=Cameroon
taxResidentialCountry.enums.AZE=Azerbaijan
taxResidentialCountry.enums.GUY=Guyana
taxResidentialCountry.enums.FSM=Micronesia
taxResidentialCountry.enums.GUM=Guam
taxResidentialCountry.enums.SLV=El Salvador
taxResidentialCountry.enums.DOM=Dominican Republic
taxResidentialCountry.enums.GUF=French Guiana
taxResidentialCountry.enums.AIA=Anguilla
taxResidentialCountry.enums.NRU=Nauru
taxResidentialCountry.enums.BLR=Belarus
taxResidentialCountry.enums.VCT=Saint Vincent and the Grenadines
taxResidentialCountry.enums.KNA=Saint Kitts and Nevis
taxResidentialCountry.enums.MAR=Morocco
taxResidentialCountry.enums.BLM=Saint Barthélemy
taxResidentialCountry.enums.SMR=San Marino
taxResidentialCountry.enums.NCL=New Caledonia
taxResidentialCountry.enums.ERI=Eritrea
taxResidentialCountry.enums.MAC=Macau
taxResidentialCountry.enums.TON=Tonga
taxResidentialCountry.enums.MAF=Saint Martin, France
taxResidentialCountry.enums.GEO=Georgia
taxResidentialCountry.enums.EST=Estonia
taxResidentialCountry.enums.MSR=Montserrat
taxResidentialCountry.enums.BMU=Bermuda
taxResidentialCountry.enums.COL=Colombia
taxResidentialCountry.enums.COK=Cook Islands
taxResidentialCountry.enums.PYF=French Polynesia
taxResidentialCountry.enums.USA=United States
taxResidentialCountry.enums.ESP=Spain
taxResidentialCountry.enums.COM=Comoros
taxResidentialCountry.enums.COD=DRC
taxResidentialCountry.enums.URY=Uruguay
taxResidentialCountry.enums.COG=Congo Brazzaville
taxResidentialCountry.enums.ESH=Western Sahara
taxResidentialCountry.enums.BLZ=Belize
taxResidentialCountry.enums.MRT=Mauritania
taxResidentialCountry.enums.PHL=Philippines
taxResidentialCountry.enums.MDA=Moldova
taxResidentialCountry.enums.MTQ=Martinique
taxResidentialCountry.enums.CPV=Cape Verde
taxResidentialCountry.enums.GHA=Ghana
taxResidentialCountry.enums.VEN=Venezuela
taxResidentialCountry.enums.ALB=Albania
taxResidentialCountry.enums.ECU=Ecuador
taxResidentialCountry.enums.ALA=Åland Islands (Finnish genus)
taxResidentialCountry.enums.VUT=Vanuatu
taxResidentialCountry.enums.NER=Niger
taxResidentialCountry.enums.LAO=Laos
taxResidentialCountry.enums.ETH=Ethiopia
taxResidentialCountry.enums.GGY=Guernsey
taxResidentialCountry.enums.MCO=Monaco
taxResidentialCountry.enums.ZMB=Zambia
taxResidentialCountry.enums.KOR=Korea
taxResidentialCountry.enums.SOM=Somalia
liabilityId.enums.670540259934208=Total & Permanent Disability (TPD)
liabilityId.enums.595=Death
phoneType.enums.1=PHONE
phoneType.enums.3=COMPANYPHONE
phoneType.enums.2=LANDLINE
phoneType.enums.4=FAX
liabilityId.title=liabilityId
liabilityId.enums.416=Terminal disease
liabilityId.enums.216=Critical illness exemption
country.title=country
country.description=Country.
transactionNo.title=transactionNo
transactionNo.description=Transaction number.
falculative.enums.2=N
falculative.enums.1=Y
contactPersonName.title=contactPersonName
contactPersonName.description=Contact person name.
legalRepresentativeIdNo.title=legalRepresentativeIdNo
legalRepresentativeIdNo.description=Legal representative id number.
organizationIdType.title=organizationIdType
organizationIdType.description=Organization id type.
E_recalculateGSTOrNot.title=E_recalculateGSTOrNot
E_recalculateGSTOrNot.description=E_recalculateGSTOrNot
E_recalculateGSTOrNot.enums.2=N
E_recalculateGSTOrNot.enums.1=Y
legalRepresentativeName.title=legalRepresentativeName
legalRepresentativeName.description=Legal representative name.
industry.title=industry
industry.description=industry
agreeTaxDeduction.title=agreeTaxDeduction
agreeTaxDeduction.description=Agree tax deduction or not.
agreeTaxDeduction.enums.1=Y
agreeTaxDeduction.enums.2=N
issuanceStatus.title=issuanceStatus
issuanceStatus.description=issuanceStatus
issuanceStatus.enums.DECLINED=DECLINED
issuanceStatus.enums.WITHDRAWN=WITHDRAWN
issuanceStatus.enums.WAITING_FOR_ISSUANCE=WAITING_FOR_ISSUANCE
issuanceStatus.enums.PENDING_PROPOSAL_CHECK=PENDING_PROPOSAL_CHECK
issuanceStatus.enums.EFFECTIVE=EFFECTIVE
issuanceStatus.enums.DATA_ENTRY_IN_PROGRESS=DATA_ENTRY_IN_PROGRESS
issuanceStatus.enums.WAITING_FOR_DATA_ENTRY=WAITING_FOR_DATA_ENTRY
relationshipWithPolicyholder_1.title=relationshipWithPolicyholder_1
relationshipWithPolicyholder_1.description=relationshipWithPolicyholder_1
decision.title=decision
decision.description=decision
E_esgFlag.title=E_esgFlag
E_esgFlag.description=E_esgFlag
numberOfObjects.title=numberOfObjects
numberOfObjects.description=Number of objects.
nonStandardTariff.title=nonStandardTariff
nonStandardTariff.description=Is non standard tariff or not.
nonStandardTariff.enums.1=Y
nonStandardTariff.enums.2=N
E_EducationLevel.title=E_EducationLevel
E_EducationLevel.description=E_EducationLevel
attachedToProductId.title=attachedToProductId
attachedToProductId.description=attachedToProductId
E_calculateTaxOrNot.title=E_calculateTaxOrNot
E_calculateTaxOrNot.description=Calculate tax or not.
E_calculateTaxOrNot.enums.2=N
E_calculateTaxOrNot.enums.1=Y
adjustedAnnualStandardPremium.title=adjustedAnnualStandardPremium
adjustedAnnualStandardPremium.description=Adjusted annual standard premium.
TPD_Limitation_byPerson_test.title=TPD_Limitation_byPerson_test
TPD_Limitation_byPerson_test.description=TPD_Limitation_byPerson_test
E_UWReason.title=E_UWReason
E_UWReason.description=UW reason.
E_TypeOfPass.title=E_TypeOfPass
E_TypeOfPass.description=Type of pass.
E_MinimumProtectionValue.title=E_MinimumProtectionValue
E_MinimumProtectionValue.description=Minimum protection value.
agreePrivacyConsent.title=agreePrivacyConsent
agreePrivacyConsent.description=Agree privacy consent or not.
agreePrivacyConsent.enums.1=Y
agreePrivacyConsent.enums.2=N
consentees.title=consentees
consentees.description=Consentee.
E_UWIndicator.title=E_UWIndicator
E_UWIndicator.description=UW indicator.
adjustedAnnualStandardPremiumRate.title=adjustedAnnualStandardPremiumRate
adjustedAnnualStandardPremiumRate.description=Adjusted annual standard premium rate.
paymentMethod.enums.235=TNG eWallet-AutoDebit
paymentMethod.enums.113=LinePay
paymentMethod.enums.116=Hyperwallet
paymentMethod.enums.237=JomPay
paymentMethod.enums.236=Maxis-DCB
paymentMethod.enums.118=Yahoo Wallet
paymentMethod.enums.239=SEPA Direct debit
paymentMethod.enums.117=GrabPay
paymentMethod.enums.238=Claim Waiver
paymentMethod.enums.45=SBPS-Yahoo Wallet
paymentMethod.enums.46=SBPS-Rakuten Pay
paymentMethod.enums.47=SBPS-APPLE_Pay
paymentMethod.enums.48=SBPS-PayPay
paymentMethod.enums.41=SBPS-docomo Payment
paymentMethod.enums.42=SBPS-AU Easy Payment
paymentMethod.enums.43=SBPS-SoftBank Lump Sum Payment, Ymobile Payment
paymentMethod.enums.44=SBPS-LINE_PAY
paymentMethod.enums.230=WSPay
paymentMethod.enums.111=Fund Deduction
paymentMethod.enums.125=Agency payment
paymentMethod.enums.202=FPS
paymentMethod.enums.245=Erste
paymentMethod.enums.204=DBS
paymentMethod.enums.126=SoftBank payment services
paymentMethod.enums.203=MPGS
paymentMethod.enums.129=Online payment
paymentMethod.enums.206=Trustly
paymentMethod.enums.205=PayPayfusion
paymentMethod.enums.208=ZA Bank App
paymentMethod.enums.207=Coinspaid
paymentMethod.enums.209=IPay88
paymentMethod.enums.40=SBPS-Credit Card Payment
paymentMethod.enums.240=kwi
paymentMethod.enums.121=Japan internet pay
paymentMethod.enums.242=External Transfer
paymentMethod.enums.120=internet pay
paymentMethod.enums.241=mastercard
paymentMethod.enums.123=T-point pay
paymentMethod.enums.200=SCB-DuitNow
paymentMethod.enums.244=CWS
paymentMethod.enums.122=Convenient Store pay
paymentMethod.enums.243=CWS
paymentMethod.enums.136=Bank transfer
paymentMethod.enums.212=Novus
paymentMethod.enums.138=Allo Pay
paymentMethod.enums.215=midtrans
paymentMethod.enums.137=kbank payments
paymentMethod.enums.214=2c2p
paymentMethod.enums.217=KG INICIS
paymentMethod.enums.139=QR Code
paymentMethod.enums.216=SamSung APP
paymentMethod.enums.219=Service Partner
paymentMethod.enums.218=DOKU
paymentMethod.enums.132=Grab bank card pack
paymentMethod.enums.134=OVO wallet
paymentMethod.enums.133=Grab earns into the wallet
paymentMethod.enums.210=Prepaid
paymentMethod.enums.103=Alipay
paymentMethod.enums.223=Internal Transfer - APL
paymentMethod.enums.105=Points Deduction
paymentMethod.enums.104=Wechat
paymentMethod.enums.225=Monri Pay
paymentMethod.enums.106=Coupon deduction
paymentMethod.enums.109=Policy Balance Deduction
paymentMethod.enums.108=Agent commission payment
paymentMethod.enums.12=QR Code
paymentMethod.enums.9=VA
paymentMethod.enums.13=QRIS
paymentMethod.enums.7=ovowallet
paymentMethod.enums.10=Allo Wallet
paymentMethod.enums.11=Allo Paylater
paymentMethod.enums.222=Oreo Pay
paymentMethod.enums.221=ticket
legalRepresentativeIdType.title=legalRepresentativeIdType
legalRepresentativeIdType.description=Legal representative id type.
sendEPolicy.title=sendEPolicy
sendEPolicy.description=Send E-Policy or not.
sendEPolicy.enums.2=N
sendEPolicy.enums.1=Y
premiumPeriod.enums.2-84=SUIFULL-84
coveragePeriod.enums.7=WHOLELIFE
updateOcrResult.title=updateOcrResult
updateOcrResult.description=To update Ocr result or not.
updateOcrResult.enums.1=Y
updateOcrResult.enums.2=N
organizationAbbreviationName.title=organizationAbbreviationName
organizationAbbreviationName.description=Organization abbreviation name.
collectionReferenceNo.title=collectionReferenceNo
collectionReferenceNo.description=Collection reference number.
extraPremiumDueDays.title=extraPremiumDueDays
extraPremiumDueDays.description=Extra premium due days.
E_recalculateGSTDate.title=E_recalculateGSTDate
E_recalculateGSTDate.description=Recalculate GST date.
calculationMethod.title=calculationMethod
calculationMethod.description=calculation method.
calculationMethod.enums.2=Calculate SA
calculationMethod.enums.1=Calculate premium
nominees.title=nominees
nominees.description=Nominees
plannedPremiumCalMethod.title=plannedPremiumCalMethod
plannedPremiumCalMethod.description=Planned premium cal method.
plannedPremiumCalMethod.enums.1=Manual Agreed Premium & SA
workingPlace.title=workingPlace
workingPlace.description=Working place.
premiumFrequencyType.enums.3=Semi-annually
premiumFrequencyType.enums.4=Quarterly
byPassUwCheck.title=byPassUwCheck
byPassUwCheck.description=By pass Uw check.
byPassUwCheck.enums.1=Y
byPassUwCheck.enums.2=N
fronting.title=fronting
fronting.description=fronting
fronting.enums.1=Y
fronting.enums.2=N
organizationIdNo.title=organizationIdNo
organizationIdNo.description=Organization id number.
reason.title=reason
reason.description=The reason for doing this POS item. It is free text.
posFundCode.title=posFundCode
posFundCode.description=Fund code.
posFundAmount.title=posFundAmount
posFundAmount.description=Fund amount.
productCode.title=productCode
productCode.description=Product code
requestDate.title=requestDate
requestDate.description=The actual date of user request. The default value is system date and time.
effectiveDateType.title=effectiveDateType
effectiveDateType.description=The POS case transaction effective date.
reasonCode.title=reasonCode
reasonCode.description=The reason code for doing this POS item.
message.title=message
message.description=Error message.
errCode.title=errCode
errCode.description=Error code defined in Core system.
validationSuccess.title=validationSuccess
validationSuccess.description=Validation result.
feeType.title=feeType
feeType.description=
feeType.enums.ILP_ACC_OUT=Fund withdrawal
feeType.enums.AR_SINGLE_TOP_UP=Single top up
withdrawalType.title=withdrawalType
withdrawalType.description=Withdrawal type.
withdrawalType.enums.REGULAR_WITHDRAWAL=REGULAR_WITHDRAWAL
withdrawalType.enums.PARTIAL_WITHDRAWAL=PARTIAL_WITHDRAWAL
withdrawalType.enums.FULL_WITHDRAWAL=FULL_WITHDRAWAL
feeAmount.title=feeAmount
feeAmount.description=Total fee amount.
collectionOrRefundFlag.title=collectionOrRefundFlag
collectionOrRefundFlag.description=Yes - fee involved,No - No fee involved
collectionOrRefundFlag.enums.NO=NO
collectionOrRefundFlag.enums.YES=YES
currency.title=currency
currency.description=Currency
currency.enums.SGD=SGD
currency.enums.EUR=EUR
payFeeType.title=payFeeType
payFeeType.description=Charge or Pay
payFeeType.enums.PRESERVATION_SHOULD_PAY=Pay
payFeeType.enums.PRESERVATION_RECEIVABLE=Collect Fees
posFundCurrency.title=posFundCurrency
posFundCurrency.description=Pos currency.
posFundCurrency.enums.EUR=EUR
posFundCurrency.enums.SGD=SGD
posFundCurrency.enums.UYU=UYU
posFundCurrency.enums.RON=RON
posFundCurrency.enums.PKR=PKR
posFundCurrency.enums.CZK=CZK
posFundCurrency.enums.SEK=SEK
posFundCurrency.enums.KES=KES
posFundCurrency.enums.UAH=UAH
posFundCurrency.enums.RSD=RSD
posFundCurrency.enums.TWD=TWD
posFundCurrency.enums.AED=AED
posFundCurrency.enums.HKD=HKD
posFundCurrency.enums.MYR=MYR
posFundCurrency.enums.CAD=CAD
posFundCurrency.enums.DKK=DKK
posFundCurrency.enums.DOP=DOP
posFundCurrency.enums.NOK=NOK
posFundCurrency.enums.MMK=MMK
posFundCurrency.enums.BGN=BGN
posFundCurrency.enums.PLN=PLN
posFundCurrency.enums.JPY=JPY
posFundCurrency.enums.KRW=KRW
posFundCurrency.enums.GBP=GBP
posFundCurrency.enums.PHP=PHP
posFundCurrency.enums.HUF=HUF
posFundCurrency.enums.PYG=PYG
posFundCurrency.enums.RUB=RUB
posFundCurrency.enums.ARS=ARS
posFundCurrency.enums.GRL=GRL
posFundCurrency.enums.SAR=SAR
posFundCurrency.enums.KZT=KZT
posFundCurrency.enums.CNY=CNY
posFundCurrency.enums.THB=THB
posFundCurrency.enums.INR=INR
posFundCurrency.enums.PAB=PAB
posFundCurrency.enums.OTHER=OTHER
posFundCurrency.enums.NZD=NZD
posFundCurrency.enums.PEN=PEN
posFundCurrency.enums.BRL=BRL
posFundCurrency.enums.ISK=ISK
posFundCurrency.enums.MKD=MKD
posFundCurrency.enums.USD=USD
posFundCurrency.enums.COP=COP
posFundCurrency.enums.EGP=EGP
posFundCurrency.enums.BAM=BAM
posFundCurrency.enums.BSD=BSD
posFundCurrency.enums.AUD=AUD
posFundCurrency.enums.ILS=ILS
posFundCurrency.enums.IDR=IDR
posFundCurrency.enums.TRY=TRY
posFundCurrency.enums.MXN=MXN
posFundCurrency.enums.FJD=FJD
posFundCurrency.enums.CHF=CHF
posFundCurrency.enums.HRK=HRK
posFundCurrency.enums.ALL=ALL
posFundCurrency.enums.CLP=CLP
posFundCurrency.enums.GTQ=GTQ
posFundCurrency.enums.ZAR=ZAR
posFundCurrency.enums.VND=VND
caseNo.title=caseNo
caseNo.description=Case number.
bizApplyNo.title=bizApplyNo
bizApplyNo.description=Biz apply number.
effectiveDateType.enums.POS_NEXT_PREMIUM_DUE_DATE=POS_NEXT_PREMIUM_DUE_DATE
effectiveDateType.enums.LAST_ANNIVERSARY_DATE=LAST_ANNIVERSARY_DATE
effectiveDateType.enums.POS_USER_INPUT=POS_USER_INPUT
effectiveDateType.enums.POS_IMMEDIATELY_DATE=POS_IMMEDIATELY_DATE
effectiveDateType.enums.POS_RISK_START_DATE=POS_RISK_START_DATE
effectiveDateType.enums.POS_PREMIUM_DUE_DATE_OF_NEXT_BILL=POS_PREMIUM_DUE_DATE_OF_NEXT_BILL
effectiveDateType.enums.ANNIVERSARY_DATE=ANNIVERSARY_DATE
effectiveDateType.enums.RENEWAL_POLICY=RENEWAL_POLICY
effectiveDateType.enums.POLICY_EFFECTIVE_DATE=POLICY_EFFECTIVE_DATE
effectiveDateType.enums.NEXT_CHARGE_DUE_DATE=NEXT_CHARGE_DUE_DATE
effectiveDateType.enums.POS_LAST_PREMIUM_DUE_DATE=POS_LAST_PREMIUM_DUE_DATE
tax.title=tax
tax.description=Tax
posNo.title=posNo
posNo.description=Pos number.
feeType.enums.GST_OUTPUT_DISCOUNT=GST_OUTPUT_DISCOUNT
feeType.enums.COMMISSION_CHARGE=COMMISSION_CHARGE
feeType.enums.DEPOSIT_ACCOUNT_REFUND=DEPOSIT_ACCOUNT_REFUND
feeType.enums.U_VAT_AR=U_VAT_AR
feeType.enums.PAYABLE_DISTRIBUTION=PAYABLE_DISTRIBUTION
feeType.enums.REAL_PAYMENT_NO_TAX=REAL_PAYMENT_NO_TAX
feeType.enums.AR_PLANED_PREMIUM=AR_PLANED_PREMIUM
feeType.enums.GST_OUTPUT_PREMIUM=GST_OUTPUT_PREMIUM
feeType.enums.FX_GAIN=FX_GAIN
feeType.enums.U_STAMP_DUTY_AR_REVERSE=U_STAMP_DUTY_AR_REVERSE
feeType.enums.GST_INPUT_PREMIUM=GST_INPUT_PREMIUM
feeType.enums.BASIC_SERVICE_FEE_PAYABLE_AP=BASIC_SERVICE_FEE_PAYABLE_AP
feeType.enums.PREMIUM_DISCOUNT=PREMIUM_DISCOUNT
feeType.enums.SERVICE_FEE_GST_AP=SERVICE_FEE_GST_AP
feeType.enums.U_POLICY_ADMIN_FEE=U_POLICY_ADMIN_FEE
feeType.enums.UNRECOGNIZED_PREMIUM_REFUND=UNRECOGNIZED_PREMIUM_REFUND
feeType.enums.BALANCE_IN=BALANCE_IN
feeType.enums.CASH_VALUE=CASH_VALUE
feeType.enums.UNRECOGNIZED_PREMIUM=UNRECOGNIZED_PREMIUM
feeType.enums.LEVY_PROVISION_AR=LEVY_PROVISION_AR
feeType.enums.MEDICAL_FEE=MEDICAL_FEE
feeType.enums.INVESTIGATION_FEE=INVESTIGATION_FEE
feeType.enums.NO_TAX_OUTPUT_PREMIUM=NO_TAX_OUTPUT_PREMIUM
feeType.enums.LAWYER_SERVICE_FEE=LAWYER_SERVICE_FEE
feeType.enums.COMMISSION_PAYMENT=COMMISSION_PAYMENT
feeType.enums.NONE_INSURANCE_PREMIUM=NONE_INSURANCE_PREMIUM
feeType.enums.AUTOMATIC_PREMIUM_LOAN_INTEREST=AUTOMATIC_PREMIUM_LOAN_INTEREST
feeType.enums.LAWYER_SERVICE_FEE_TAX=LAWYER_SERVICE_FEE_TAX
feeType.enums.REFUND_INVESTIGATION_FEE=REFUND_INVESTIGATION_FEE
feeType.enums.U_PARTIAL_SURRENDER_CHARGE=U_PARTIAL_SURRENDER_CHARGE
feeType.enums.BASIC_COMMISSION=BASIC_COMMISSION
feeType.enums.INTEREST=INTEREST
feeType.enums.MEDICAL_DOCTOR_SERVICE_FEE_TAX=MEDICAL_DOCTOR_SERVICE_FEE_TAX
feeType.enums.SERVICE_FEE_WITHHOLDING_AR=SERVICE_FEE_WITHHOLDING_AR
feeType.enums.LEVY_CALLBACK_PAYMENT=LEVY_CALLBACK_PAYMENT
feeType.enums.VAT_AP=VAT_AP
feeType.enums.BASIC_SERVICE_FEE_RECEIVABLE_AR=BASIC_SERVICE_FEE_RECEIVABLE_AR
feeType.enums.ADMIN_FEE_GST=ADMIN_FEE_GST
feeType.enums.REFUND_PUBLIC_NOTARY_SERVICE_FEE=REFUND_PUBLIC_NOTARY_SERVICE_FEE
productFundInfoList.title=productFundInfoList
productFundInfoList.description=Fund list
transactionList.title=transactionList
transactionList.description=Support multiple transactions in one single POS case.One transaction means a kind of change. The detailed information that needs to be changed is in this layer.
validationErrors.title=validationErrors
validationErrors.description=Errors information of validation.
posTransType.title=posTransType
posTransType.description=POS trans type.
posTransType.enums.LEVY=LEVY
posTransType.enums.FREELOOKSURRENDER=FREELOOKSURRENDER
posTransType.enums.PROPOSAL_ISSUANCE=PROPOSAL_ISSUANCE
posTransType.enums.POS_BENEFICIARY_INFO_CHANGES=POS_BENEFICIARY_INFO_CHANGES
posTransType.enums.POS_HOLDER_INFO_CHANGES=POS_HOLDER_INFO_CHANGES
posTransType.enums.MASTER_ADD_DELETE_INSURED_OBJECT=MASTER_ADD_DELETE_INSURED_OBJECT
posTransType.enums.PAYMENT_CHANNEL_SETTLE=PAYMENT_CHANNEL_SETTLE
posTransType.enums.POLICY_TRANSFER=POLICY_TRANSFER
posTransType.enums.POLICY_NOMINEE_CHANGE=POLICY_NOMINEE_CHANGE
posTransType.enums.CLAIM=CLAIM
posTransType.enums.ISSUANCE_MODIFY=ISSUANCE_MODIFY
posTransType.enums.MASTER_AGREEMENT_MODIFY=MASTER_AGREEMENT_MODIFY
posTransType.enums.RIDER_ADD_OR_DELETE=RIDER_ADD_OR_DELETE
posTransType.enums.PLAN_CHANGE=PLAN_CHANGE
posTransType.enums.ILP_SUM_ASSURED_CHANGE=ILP_SUM_ASSURED_CHANGE
posTransType.enums.PAYMENT_INFORMATION_CHANGE=PAYMENT_INFORMATION_CHANGE
posTransType.enums.ILP_REGULAR_TOP_UP_CHANGE=ILP_REGULAR_TOP_UP_CHANGE
posTransType.enums.POS_CANEL_CHEAT_CANCELLATION=POS_CANEL_CHEAT_CANCELLATION
posTransType.enums.INSURED_IDENTIFICATION_CHANGE=INSURED_IDENTIFICATION_CHANGE
posTransType.enums.POLICY_BASE_INFORMATION_CHANGE=POLICY_BASE_INFORMATION_CHANGE
posTransType.enums.ILP_DIVIDEND_DISTRIBUTION_METHOD_CHANGE=ILP_DIVIDEND_DISTRIBUTION_METHOD_CHANGE
posTransType.enums.POLICY_VESTING_OPTION=POLICY_VESTING_OPTION
posTransType.enums.POS_BIG_POSONLINE=POS_BIG_POSONLINE
posTransType.enums.POS_NON_PAYMENT_CANCELLATION=POS_NON_PAYMENT_CANCELLATION
posTransType.enums.ILP_ADD_OR_DELETE_RIDER=ILP_ADD_OR_DELETE_RIDER
posTransType.enums.COMMISSION=COMMISSION
posTransType.enums.LIABILITY_TERMINATION=LIABILITY_TERMINATION
posTransType.enums.RETREAT_POLICY=RETREAT_POLICY
posTransType.enums.INSURANCE_AGREEMENT=INSURANCE_AGREEMENT
posTransType.enums.EFECTIVE_POLICY=EFECTIVE_POLICY
posTransType.enums.ONLINE_ISSUANCE=ONLINE_ISSUANCE
posTransType.enums.POS_CANCELLATION=POS_CANCELLATION
posTransType.enums.FULL_SURRENDER=FULL_SURRENDER
posTransType.enums.MASTER_POLICY_ADD_DELETE_INSURED=MASTER_POLICY_ADD_DELETE_INSURED
posTransType.enums.POLICY_ACCOUNT_WITHDRAWAL=POLICY_ACCOUNT_WITHDRAWAL
posTransType.enums.SMOKING_STATUS_CHANGE=SMOKING_STATUS_CHANGE
posTransType.enums.POLICY_ASSIGNEE_CHANGE=POLICY_ASSIGNEE_CHANGE
posTransType.enums.POS_UPDATE_RENEWAL_STATUS=POS_UPDATE_RENEWAL_STATUS
posTransType.enums.USAGE_PREMIUM=USAGE_PREMIUM
posTransType.enums.PROVISION=PROVISION
posTransType.enums.NO_POLICY_ISSUED_REFUND=NO_POLICY_ISSUED_REFUND
posTransType.enums.POS_INSURANCE_MONEY_REDUCE=POS_INSURANCE_MONEY_REDUCE
posTransType.enums.POLICY_REPRINT=POLICY_REPRINT
posTransType.enums.ILP_PLANNED_PREMIUM_CHANGE=ILP_PLANNED_PREMIUM_CHANGE
posTransType.enums.CLAIM_EXPENSE=CLAIM_EXPENSE
posTransType.enums.INSTALLMENT_PREMIUM=INSTALLMENT_PREMIUM
posTransType.enums.ORDER_REFUND=ORDER_REFUND
posTransType.enums.POS_CUSTOMER_NAME_CHANGE=POS_CUSTOMER_NAME_CHANGE
posTransType.enums.AUTO_FUND_SWITCH=AUTO_FUND_SWITCH
posTransType.enums.EVENT_POLICY_ISSUE_SWITCH_CHANGE=EVENT_POLICY_ISSUE_SWITCH_CHANGE
posTransType.enums.ISSUANCE_REFUND=ISSUANCE_REFUND
posTransType.enums.POS_EFFECTIVE_DATE_CHANGE=POS_EFFECTIVE_DATE_CHANGE
posTransType.enums.SUM_INSURED_CHANGE=SUM_INSURED_CHANGE
posTransType.enums.ILP_COUPON_ALLOCATION=ILP_COUPON_ALLOCATION
posTransType.enums.ILP_FUND_APPOINTMENT_CHANGE=ILP_FUND_APPOINTMENT_CHANGE
posTransType.enums.POLICY_ASSIGNMENT=POLICY_ASSIGNMENT
posTransType.enums.HOLDER_IDENTIFICATION_CHANGE=HOLDER_IDENTIFICATION_CHANGE
posTransType.enums.EMPLOYEE_MANAGEMENT=EMPLOYEE_MANAGEMENT
posTransType.enums.ILP_BONUS=ILP_BONUS
posTransType.enums.BENEFIT_BONUS_ACCOUNT_CHANGE=BENEFIT_BONUS_ACCOUNT_CHANGE
posTransType.enums.AUTO_RENEWAL_CHANGE=AUTO_RENEWAL_CHANGE
posTransType.enums.INSURE_OBJECT=INSURE_OBJECT
posTransType.enums.PREMIUM_RESET=PREMIUM_RESET
posTransType.enums.EVENT_POLICY_DRIVER_CHANGE=EVENT_POLICY_DRIVER_CHANGE
posTransType.enums.ILP_UNIT_ADJUSTMENT=ILP_UNIT_ADJUSTMENT
posTransType.enums.ILP_CHARGE_DEDUCTION=ILP_CHARGE_DEDUCTION
posTransType.enums.PARTIAL_WITHDRAW=PARTIAL_WITHDRAW
posTransType.enums.POS_PAY_ACCOUNT_CHANGED=POS_PAY_ACCOUNT_CHANGED
posTransType.enums.AGENT=AGENT
posTransType.enums.RETIREMENT_OPTION_CHANGE=RETIREMENT_OPTION_CHANGE
posTransType.enums.POLICY_REISSUANCE=POLICY_REISSUANCE
posTransType.enums.EXTENDED_TERM=EXTENDED_TERM
posTransType.enums.PRESERVATION=PRESERVATION
posTransType.enums.ILP_RECURRING_SINGLE_TOP_UP_CHANGE=ILP_RECURRING_SINGLE_TOP_UP_CHANGE
posTransType.enums.BREAK_OFF=BREAK_OFF
posTransType.enums.POS_AGREEMENT_HOLDER_CONTACTINFO=POS_AGREEMENT_HOLDER_CONTACTINFO
posTransType.enums.POLICYHOLDER_CHANGE=POLICYHOLDER_CHANGE
posTransType.enums.AUTO_RENEWAL_SWITCH_CHANGE=AUTO_RENEWAL_SWITCH_CHANGE
posTransType.enums.WRITE_OFF=WRITE_OFF
posTransType.enums.HOLDER_CONTACTINFO_DEL=HOLDER_CONTACTINFO_DEL
posTransType.enums.PARCEL_ARRIVED=PARCEL_ARRIVED
posTransType.enums.ILP_FUND_SWITCH=ILP_FUND_SWITCH
posTransType.enums.INSTITUTE_CREATION=INSTITUTE_CREATION
posTransType.enums.POS_INSURANCE_CANCELLATION=POS_INSURANCE_CANCELLATION
posTransType.enums.MASTER_AGREEMENT_EFFECTIVE=MASTER_AGREEMENT_EFFECTIVE
posTransType.enums.REINSTATEMENT=REINSTATEMENT
posTransType.enums.EFECTIVE_EVENT_POLICY=EFECTIVE_EVENT_POLICY
posTransType.enums.BIZ_APPLY=BIZ_APPLY
posTransType.enums.POS_SEAL_UP=POS_SEAL_UP
posTransType.enums.POS_COVERAGE_DATE_CHANGE=POS_COVERAGE_DATE_CHANGE
posTransType.enums.POS_UNDO=POS_UNDO
posTransType.enums.SINGLE_TOP_UP=SINGLE_TOP_UP
posTransType.enums.HKIA=HKIA
posTransType.enums.POLICY_MATURITY=POLICY_MATURITY
posTransType.enums.BONUS_ALLOCATION=BONUS_ALLOCATION
posTransType.enums.POS_LOSE_EFFICACY_CANCELLATION=POS_LOSE_EFFICACY_CANCELLATION
posTransType.enums.PLUSINSURANCE=PLUSINSURANCE
posTransType.enums.UNRECOGNIZED_PREMIUM=UNRECOGNIZED_PREMIUM
posTransType.enums.POLICY_E_POLICY_DISPATCH=POLICY_E_POLICY_DISPATCH
posTransType.enums.POLICY_SIGN_OFF=POLICY_SIGN_OFF
posTransType.enums.POS_NO_REASON_CANCELLATION=POS_NO_REASON_CANCELLATION
posTransType.enums.HOME_PROTECTION_SCHEME_EXEMPTION=HOME_PROTECTION_SCHEME_EXEMPTION
posTransType.enums.RENEWAL_POLICY_PAY=RENEWAL_POLICY_PAY
posTransType.enums.SEIZURE=SEIZURE
posTransType.enums.POLICY_TERMINATION_WITHOUT_PAY=POLICY_TERMINATION_WITHOUT_PAY
posTransType.enums.RENEWAL_POLICY=RENEWAL_POLICY
posTransType.enums.ILP_MATURITY=ILP_MATURITY
posTransType.enums.CONSUMPTION_TAX=CONSUMPTION_TAX
posTransType.enums.POS_INSURANCE_MONEY_ADD=POS_INSURANCE_MONEY_ADD
posTransType.enums.FEE_INCREASE=FEE_INCREASE
posTransType.enums.ADD_OR_DELETE_INSURED_PERSON=ADD_OR_DELETE_INSURED_PERSON
posTransType.enums.REVERSIONARY_BONUS_ALLOCATION=REVERSIONARY_BONUS_ALLOCATION
posTransType.enums.TRUST_CHANGE=TRUST_CHANGE
posTransType.enums.MANUAL_CHANGE_PARTY_INFO=MANUAL_CHANGE_PARTY_INFO
posTransType.enums.CUSTOMER_INFO_CHANGES=CUSTOMER_INFO_CHANGES
posTransType.enums.POS_ALL_LOSE_CANCELLATION=POS_ALL_LOSE_CANCELLATION
posTransType.enums.PAYMENT_CHANNEL_PAYOUT_CHARGE=PAYMENT_CHANNEL_PAYOUT_CHARGE
posTransType.enums.POLICY_LOAN=POLICY_LOAN
posTransType.enums.POLICY_CHANNEL_CHANGE=POLICY_CHANNEL_CHANGE
posTransType.enums.PAYMENT_TRANSACTION=PAYMENT_TRANSACTION
posTransType.enums.ACCOUNT_WITHDRAW=ACCOUNT_WITHDRAW
posTransType.enums.POS_INVALID_CANCELLATION=POS_INVALID_CANCELLATION
posTransType.enums.EVENT_POLICY_TERMINATION=EVENT_POLICY_TERMINATION
posTransType.enums.REDUCED_PAID_UP=REDUCED_PAID_UP
posTransType.enums.DIRECT_USER_CREATE=DIRECT_USER_CREATE
posTransType.enums.POS_CANEL_SIMPLE_CANCELLATION=POS_CANEL_SIMPLE_CANCELLATION
posTransType.enums.SUSPENSE_TRANSACTION=SUSPENSE_TRANSACTION
posTransType.enums.PREMIUM_FREQUENCY_CHANGE=PREMIUM_FREQUENCY_CHANGE
posTransType.enums.POS_REMOVE=POS_REMOVE
posTransType.enums.POS_INSURED_INFO_CHANGES=POS_INSURED_INFO_CHANGES
posTransType.enums.POS_PAYEE_CREATE=POS_PAYEE_CREATE
posTransType.enums.SOCIAL_SECURITY_IND_CHANGE=SOCIAL_SECURITY_IND_CHANGE
posTransType.enums.SECONDARY_LIFE_INSURED_OPTION=SECONDARY_LIFE_INSURED_OPTION
posTransType.enums.POS_CONTRACT_INFORMATION_CHANGE=POS_CONTRACT_INFORMATION_CHANGE
posTransType.enums.DEBIT_NOTE_SETTlE=DEBIT_NOTE_SETTlE
posTransType.enums.ILP_WITHDRAWAL=ILP_WITHDRAWAL
posTransType.enums.POLICY_LOAN_REPAYMENT=POLICY_LOAN_REPAYMENT
posTransType.enums.ILP_PREMIUM_HOLIDAY=ILP_PREMIUM_HOLIDAY
feeType.enums.INTERNAL_PAYMENT=INTERNAL_PAYMENT
feeType.enums.COMMISSION_WITHHOLDING_AR=COMMISSION_WITHHOLDING_AR
feeType.enums.LEGAL_FEE_TAX=LEGAL_FEE_TAX
feeType.enums.REFUND_CLAIM_HANDLING_FEE_TAX=REFUND_CLAIM_HANDLING_FEE_TAX
feeType.enums.LEGAL_FEE=LEGAL_FEE
feeType.enums.PREMIUM_RESET=PREMIUM_RESET
feeType.enums.COMMISSION_WITHHOLDING_AP=COMMISSION_WITHHOLDING_AP
feeType.enums.U_STAMP_DUTY_AR=U_STAMP_DUTY_AR
feeType.enums.REFUND_CLAIM_PAYOUT=REFUND_CLAIM_PAYOUT
feeType.enums.U_INSURANCE_PREMIUM=U_INSURANCE_PREMIUM
feeType.enums.U_FUND_TRADING_GAINS=U_FUND_TRADING_GAINS
feeType.enums.REFUND_TRANSLATIONS_FEE=REFUND_TRANSLATIONS_FEE
feeType.enums.VAT_AR=VAT_AR
feeType.enums.CLAIM_SA=CLAIM_SA
feeType.enums.PAYMENT_CHANNEL_SERVICE_CHARGES_TAX_AP=PAYMENT_CHANNEL_SERVICE_CHARGES_TAX_AP
feeType.enums.U_FUND_TRADING_LOSSES=U_FUND_TRADING_LOSSES
feeType.enums.GST_AP=GST_AP
feeType.enums.INTERNAL_USE_BALANCE=INTERNAL_USE_BALANCE
feeType.enums.BALANCE_DRAW=BALANCE_DRAW
feeType.enums.EXPERT_FEE=EXPERT_FEE
feeType.enums.GST_AR=GST_AR
feeType.enums.CLAIM_RESERVE=CLAIM_RESERVE
feeType.enums.BANK_CHARGES=BANK_CHARGES
feeType.enums.FUND_SWITCH_CHARGE=FUND_SWITCH_CHARGE
feeType.enums.U_VAT_AR_REVERSE=U_VAT_AR_REVERSE
feeType.enums.U_FULL_WITHDRAWAL_CHARGE=U_FULL_WITHDRAWAL_CHARGE
feeType.enums.FX_LOSS=FX_LOSS
feeType.enums.CONSUMPTION_TAX=CONSUMPTION_TAX
feeType.enums.REAL_PAYMENT_FEE=REAL_PAYMENT_FEE
feeType.enums.REAL_REFUND_FEE=REAL_REFUND_FEE
feeType.enums.POLICY_REPRINT_COST=POLICY_REPRINT_COST
feeType.enums.LEVY_PAYMENT=LEVY_PAYMENT
feeType.enums.U_GST_AR_REVERSE=U_GST_AR_REVERSE
feeType.enums.REFUND_LEGAL_FEE=REFUND_LEGAL_FEE
feeType.enums.DISCOUNT_COUPON=DISCOUNT_COUPON
feeType.enums.TOLERANCE_OFFSET=TOLERANCE_OFFSET
feeType.enums.PUBLIC_NOTARY_SERVICE_FEE_TAX=PUBLIC_NOTARY_SERVICE_FEE_TAX
feeType.enums.OTHER_COSTS=OTHER_COSTS
feeType.enums.ASSESSMENT_FEE_TAX=ASSESSMENT_FEE_TAX
feeType.enums.SERVICE_CHARGES=SERVICE_CHARGES
feeType.enums.POLICY_TEMPORARILY=POLICY_TEMPORARILY
feeType.enums.POLICY_LOAN_INTEREST=POLICY_LOAN_INTEREST
feeType.enums.PAYMENT_CHANNEL_SERVICE_CHARGES_AP=PAYMENT_CHANNEL_SERVICE_CHARGES_AP
feeType.enums.INVESTIGATION_FEE_TAX=INVESTIGATION_FEE_TAX
feeType.enums.POLICY_LEVY_REFUND=POLICY_LEVY_REFUND
feeType.enums.OTHER_COSTS_TAX=OTHER_COSTS_TAX
feeType.enums.REFUND_ASSESSMENT_FEE=REFUND_ASSESSMENT_FEE
feeType.enums.PAYMENT_CROSS_CURRENCY_CONTROL=PAYMENT_CROSS_CURRENCY_CONTROL
feeType.enums.U_PARTIAL_WITHDRAWAL_CHARGE=U_PARTIAL_WITHDRAWAL_CHARGE
feeType.enums.INSURANCE_PREMIUM=INSURANCE_PREMIUM
feeType.enums.REFUND_MEDICAL_DOCTOR_SERVICE_FEE=REFUND_MEDICAL_DOCTOR_SERVICE_FEE
feeType.enums.REFUND_LAWYER_SERVICE_FEE=REFUND_LAWYER_SERVICE_FEE
feeType.enums.PUBLIC_NOTARY_SERVICE_FEE=PUBLIC_NOTARY_SERVICE_FEE
feeType.enums.INTERNAL_TRANSFER_PREMIUM=INTERNAL_TRANSFER_PREMIUM
feeType.enums.REFUND_EXPERT_FEE=REFUND_EXPERT_FEE
feeType.enums.U_SERVICE_TAX_AR=U_SERVICE_TAX_AR
feeType.enums.CLAIM_OVERDUE_INTEREST=CLAIM_OVERDUE_INTEREST
feeType.enums.REFUND_OTHER_COSTS=REFUND_OTHER_COSTS
feeType.enums.GST_PAYMENT=GST_PAYMENT
feeType.enums.BASIC_COMMISSION_RECEIVABLE_AR=BASIC_COMMISSION_RECEIVABLE_AR
feeType.enums.ORDER_TEMPORARILY=ORDER_TEMPORARILY
feeType.enums.TRANSLATIONS_FEE_TAX=TRANSLATIONS_FEE_TAX
feeType.enums.OVERDUE_PREMIUM_INTEREST=OVERDUE_PREMIUM_INTEREST
feeType.enums.CLAIMS_PAYOUT=CLAIMS_PAYOUT
feeType.enums.U_PREMIUM_HOLIDAY_CHARGE=U_PREMIUM_HOLIDAY_CHARGE
feeType.enums.REFUND_CLAIM_HANDLING_FEE=REFUND_CLAIM_HANDLING_FEE
feeType.enums.REFUND_TRANSFER_POLICY_BALANCE=REFUND_TRANSFER_POLICY_BALANCE
feeType.enums.COMMISSION_VAT_AP=COMMISSION_VAT_AP
feeType.enums.COMMISSION_COLLECTION=COMMISSION_COLLECTION
feeType.enums.GST_DISCOUNT=GST_DISCOUNT
feeType.enums.POLICY_BALANCE_DRAW=POLICY_BALANCE_DRAW
feeType.enums.USE_BALANCE=USE_BALANCE
feeType.enums.CLAIM_HANDING_FEE_TAX=CLAIM_HANDING_FEE_TAX
feeType.enums.INSURANCE_PREMIUM_TAX_AP=INSURANCE_PREMIUM_TAX_AP
feeType.enums.U_WITHHOLDING_TAX_AR_REVERSE=U_WITHHOLDING_TAX_AR_REVERSE
feeType.enums.INSURANCE_PREMIUM_TAX_AR=INSURANCE_PREMIUM_TAX_AR
feeType.enums.GL_TRANSFER_FOR_INCOMING_TRANSACTION=GL_TRANSFER_FOR_INCOMING_TRANSACTION
feeType.enums.VALUE_ADDED_TAX=VALUE_ADDED_TAX
feeType.enums.CAMPAIGN_PREMIUM_DISCOUNT=CAMPAIGN_PREMIUM_DISCOUNT
feeType.enums.U_INSURANCE_PREMIUM_TAX_AR=U_INSURANCE_PREMIUM_TAX_AR
feeType.enums.ADMIN_FEE=ADMIN_FEE
feeType.enums.SERVICE_FEE_VAT_AP=SERVICE_FEE_VAT_AP
feeType.enums.COMMISSION_PROVISION_AR=COMMISSION_PROVISION_AR
feeType.enums.INTERNAL_COLLECTION=INTERNAL_COLLECTION
feeType.enums.COMMISSION_VAT_AR=COMMISSION_VAT_AR
feeType.enums.U_SERVICE_TAX_AR_REVERSE=U_SERVICE_TAX_AR_REVERSE
feeType.enums.U_INSURANCE_PREMIUM_REVERSE=U_INSURANCE_PREMIUM_REVERSE
feeType.enums.INSTALLMENT_PREMIUM_TEMPORARILY=INSTALLMENT_PREMIUM_TEMPORARILY
feeType.enums.COMMISSION_GST_AP=COMMISSION_GST_AP
feeType.enums.COMMISSION_GST_AR=COMMISSION_GST_AR
feeType.enums.WITHHOLDING_TAX_AR=WITHHOLDING_TAX_AR
feeType.enums.ACCOUNT_BALANCE_LOCKING=ACCOUNT_BALANCE_LOCKING
feeType.enums.SUNDRY_INCOME=SUNDRY_INCOME
feeType.enums.U_INSURANCE_PREMIUM_TAX_AR_REVERSE=U_INSURANCE_PREMIUM_TAX_AR_REVERSE
feeType.enums.GL_TRANSFER_FOR_OUTGOING_TRANSACTION=GL_TRANSFER_FOR_OUTGOING_TRANSACTION
feeType.enums.U_FUND_SWITCH_CHARGE=U_FUND_SWITCH_CHARGE
feeType.enums.POLICY_PREMIUM_REFUND=POLICY_PREMIUM_REFUND
feeType.enums.ASSESSMENT_FEE=ASSESSMENT_FEE
feeType.enums.TRANSLATIONS_FEE=TRANSLATIONS_FEE
feeType.enums.POLICY_LEVY=POLICY_LEVY
feeType.enums.U_WITHHOLDING_TAX_AR=U_WITHHOLDING_TAX_AR
feeType.enums.BASIC_COMMISSION_PAYABLE_AP=BASIC_COMMISSION_PAYABLE_AP
feeType.enums.AR_REGULAR_TOP_UP=AR_REGULAR_TOP_UP
feeType.enums.WITHHOLDING_TAX_AP=WITHHOLDING_TAX_AP
feeType.enums.INVESTMENT_ORIENTED_POLICY_PREMIUM=INVESTMENT_ORIENTED_POLICY_PREMIUM
feeType.enums.STAMP_DUTY_AP=STAMP_DUTY_AP
feeType.enums.OVERPAYMENT_REFUND=OVERPAYMENT_REFUND
feeType.enums.DISCOUNT=DISCOUNT
feeType.enums.CLAIM_HANDING_FEE=CLAIM_HANDING_FEE
feeType.enums.STAMP_DUTY_AR=STAMP_DUTY_AR
feeType.enums.TERMINAL_BONUS_ALLOCATION=TERMINAL_BONUS_ALLOCATION
feeType.enums.REGULAR_TOP_UP_REVERSE=REGULAR_TOP_UP_REVERSE
feeType.enums.U_GST_AR=U_GST_AR
feeType.enums.SERVICE_TAX_AP=SERVICE_TAX_AP
feeType.enums.SERVICE_TAX_AR=SERVICE_TAX_AR
feeType.enums.PLANNED_PREMIUM_REVERSE=PLANNED_PREMIUM_REVERSE
feeType.enums.ACCOUNT_VALUE_RECEIVABLE=ACCOUNT_VALUE_RECEIVABLE
feeType.enums.MEDICAL_DOCTOR_SERVICE_FEE=MEDICAL_DOCTOR_SERVICE_FEE
feeType.enums.PREMIUM_PROVISION_AR=PREMIUM_PROVISION_AR
feeType.enums.SURRENDER_VALUE=SURRENDER_VALUE
feeType.enums.EXPERT_FEE_TAX=EXPERT_FEE_TAX
feeType.enums.AR_RECURRING_SINGLE_TOP_UP=AR_RECURRING_SINGLE_TOP_UP
feeType.enums.CLAIM_EXPENSE_RESERVE=CLAIM_EXPENSE_RESERVE
currencyName.title=currencyName
currencyName.description=Currency name.
currency.enums.RUB=RUB
currency.enums.PYG=PYG
currency.enums.HUF=HUF
currency.enums.USD=USD
currency.enums.MKD=MKD
currency.enums.COP=COP
currency.enums.ISK=ISK
currency.enums.EGP=EGP
currency.enums.BAM=BAM
currency.enums.NZD=NZD
currency.enums.PAB=PAB
currency.enums.UAH=UAH
currency.enums.SEK=SEK
currency.enums.KES=KES
currency.enums.KZT=KZT
currency.enums.SAR=SAR
currency.enums.ARS=ARS
currency.enums.GRL=GRL
currency.enums.KRW=KRW
currency.enums.THB=THB
currency.enums.CNY=CNY
currency.enums.INR=INR
currency.enums.PHP=PHP
currency.enums.GBP=GBP
currency.enums.JPY=JPY
currency.enums.PLN=PLN
currency.enums.AED=AED
currency.enums.TRY=TRY
currency.enums.MYR=MYR
currency.enums.CAD=CAD
currency.enums.DOP=DOP
currency.enums.DKK=DKK
currency.enums.RSD=RSD
currency.enums.HKD=HKD
currency.enums.TWD=TWD
currency.enums.MMK=MMK
currency.enums.BGN=BGN
currency.enums.NOK=NOK
currency.enums.PKR=PKR
currency.enums.CZK=CZK
currency.enums.RON=RON
currency.enums.UYU=UYU
currency.enums.FJD=FJD
currency.enums.BRL=BRL
currency.enums.PEN=PEN
currency.enums.OTHER=OTHER
currency.enums.CLP=CLP
currency.enums.GTQ=GTQ
currency.enums.MXN=MXN
currency.enums.ALL=ALL
currency.enums.CHF=CHF
currency.enums.HRK=HRK
currency.enums.ILS=ILS
currency.enums.AUD=AUD
currency.enums.VND=VND
currency.enums.ZAR=ZAR
currency.enums.IDR=IDR
currency.enums.BSD=BSD
payFeeType.enums.RECEIPTS=RECEIPTS
payFeeType.enums.UNDO_PRESERVATION=UNDO_PRESERVATION
payFeeType.enums.RENEW_UNDO=RENEW_UNDO
payFeeType.enums.SURRENDER=SURRENDER
payFeeType.enums.RENEWAL_GUARANTEE=RENEWAL_GUARANTEE
payFeeType.enums.PREMIUM_SCHEDULE=PREMIUM_SCHEDULE
payFeeType.enums.RENEW_RECEIVABLE=RENEW_RECEIVABLE
payFeeType.enums.PRESERVATION_RECEIPTS=PRESERVATION_RECEIPTS
payFeeType.enums.LEVY_RECEIPTS=LEVY_RECEIPTS
payFeeType.enums.COMMISSION=COMMISSION
payFeeType.enums.DEDUCT_MONEY=DEDUCT_MONEY
payFeeType.enums.FEE_REFUND=FEE_REFUND
payFeeType.enums.PRESERVATION_ACTUAL_PAY=PRESERVATION_ACTUAL_PAY
payFeeType.enums.FEE_INCREASE_RECEIPTS=FEE_INCREASE_RECEIPTS
payFeeType.enums.RECEIVABLE=RECEIVABLE
payFeeType.enums.RENEWABLE_INSURANCE_RECEIVABLE=RENEWABLE_INSURANCE_RECEIVABLE
feeDetailList.title=feeDetailList
feeDetailList.description=Fee details.
posFundInfoList.title=posFundInfoList
posFundInfoList.description=The funds list
noTaxPremium.title=noTaxPremium
noTaxPremium.description=No tax premium.
i18nCurrency.title=i18nCurrency
i18nCurrency.description=I18n currency.
proposalConfirmDate.description=Application form confirmation date
policyStatus.description=Policy status
issueDate.description=Policy issue date
goodsCode.description=Goods code
goodsPlanCode.description=Goods Plan code
effectivePaidPremium.description=Policy premium
policyHolder.description=Holder
policyProductList.description=Product list
tradeNo.description=Serial number
policyPayerList.description=Payer list
beneficialOwnerList.description=Beneficial Owner
policySecondaryInsurantList.description=secondary Insurant
policyProductList.mainProduct.description=Main product or rider. YES-main product. NO-rider
policyProductList.sumInsured.description=Sum insured
policyProductList.productCode.description=Product code
policyProductList.status.description=Product status
policyProductList.actualPremium.description=Actual premium
policyProductList.effectiveDate.description=Policy effective date
policyProductList.expiryDate.description=Policy expiry Date
policyProductList.premiumPeriod.description=Premium period
policyProductList.coveragePeriod.description=Coverage period
policyProductList.premiumFrequency.description=Premium frequency type
policyProductList.premiumPeriodType.description=Premium period type
policyProductList.coveragePeriodType.description=Coverage period type
policyProductList.policyInsurantList.description=Policy insurant list
policyProductList.policyProductPremiumList.description=Policy product premium
policyProductList.premiumStartDate.description=Premium start date
policyProductList.premiumExpiryDate.description=Premium expiry date
policyProductList.policyInsurantList.gender.description=Gender
policyProductList.policyInsurantList.certiNo.description=Customer identity NO
policyProductList.policyInsurantList.birthday.description=Birthday
policyProductList.policyInsurantList.fullName.description=Full name
policyProductList.policyInsurantList.userType.description=User type
policyProductList.policyInsurantList.certiType.description=Customer identity type
policyProductList.policyInsurantList.account.payAccountId.description=It is necessary when updating the payer account information.
policyProductList.policyInsurantList.account.bankCode.description=Bank code
policyProductList.policyInsurantList.account.bankName.description=Bank name
policyProductList.policyInsurantList.account.cardHolderName.description=Card holder name
policyProductList.policyInsurantList.account.cardNumber.description=Card Number
policyProductList.policyInsurantList.account.accountSubType.description=Account sub type
policyProductList.policyInsurantList.account.expiryDate.description=Expiry date
policyProductList.policyInsurantList.account.mobileNo.description=Mobile No
policyProductList.policyInsurantList.account.thirdPartyPayVoucher.description=Third party pay voucher No. If using the third-party pay method.
policyProductList.policyInsurantList.relationshipWithPolicyholder.description=Relationship with policy holder
policyProductList.policyInsurantList.policyBeneficiaryList.description=Policy beneficiary list
policyProductList.policyInsurantList.attachmentList.description=Attachment list
policyProductList.policyInsurantList.policyBeneficiaryList.gender.description=Gender
policyProductList.policyInsurantList.policyBeneficiaryList.certiNo.description=Customer identity NO
policyProductList.policyInsurantList.policyBeneficiaryList.birthday.description=Birthday
policyProductList.policyInsurantList.policyBeneficiaryList.fullName.description=Full name
policyProductList.policyInsurantList.policyBeneficiaryList.userType.description=User type
policyProductList.policyInsurantList.policyBeneficiaryList.certiType.description=Customer identity type
policyProductList.policyInsurantList.policyBeneficiaryList.benefitRatio.description=Benefit ratio
policyProductList.policyInsurantList.policyBeneficiaryList.attachmentList.description=Attachment list
policyProductList.policyInsurantList.policyBeneficiaryList.attachmentList.attachmentUrl.description=Attachment URL
policyProductList.policyInsurantList.policyBeneficiaryList.attachmentList.attachmentName.description=Attachment name
policyProductList.policyInsurantList.policyBeneficiaryList.attachmentList.attachmentType.description=Attachment type
policyProductList.policyInsurantList.policyBeneficiaryList.account.payAccountId.description=It is necessary when updating the payer account information.
policyProductList.policyInsurantList.policyBeneficiaryList.account.bankCode.description=Bank code
policyProductList.policyInsurantList.policyBeneficiaryList.account.bankName.description=Bank name
policyProductList.policyInsurantList.policyBeneficiaryList.account.cardHolderName.description=Card holder name
policyProductList.policyInsurantList.policyBeneficiaryList.account.cardNumber.description=Card Number
policyProductList.policyInsurantList.policyBeneficiaryList.account.accountSubType.description=Account sub type
policyProductList.policyInsurantList.policyBeneficiaryList.account.expiryDate.description=Expiry date
policyProductList.policyInsurantList.policyBeneficiaryList.account.mobileNo.description=Mobile No
policyProductList.policyInsurantList.policyBeneficiaryList.account.thirdPartyPayVoucher.description=Third party pay voucher No. If using the third-party pay method.
policyProductList.policyInsurantList.attachmentList.attachmentUrl.description=Attachment URL
policyProductList.policyInsurantList.attachmentList.attachmentName.description=Attachment name
policyProductList.policyInsurantList.attachmentList.attachmentType.description=Attachment type
policyHolder.attachmentList.attachmentUrl.description=Attachment URL
policyHolder.attachmentList.attachmentName.description=Attachment name
policyHolder.attachmentList.attachmentType.description=Attachment type
policyHolder.account.payAccountId.description=It is necessary when updating the payer account information.
policyHolder.account.bankCode.description=Bank code
policyHolder.account.bankName.description=Bank name
policyHolder.account.cardHolderName.description=Card holder name
policyHolder.account.cardNumber.description=Card Number
policyHolder.account.accountSubType.description=Account sub type
policyHolder.account.expiryDate.description=Expiry date
policyHolder.account.mobileNo.description=Mobile No
policyHolder.account.thirdPartyPayVoucher.description=Third party pay voucher No. If using the third-party pay method.
policyBeneficialOwnerList.gender.description=Gender
policyBeneficialOwnerList.certiNo.description=Customer identity NO
policyBeneficialOwnerList.birthday.description=Birthday
policyBeneficialOwnerList.fullName.description=Full name
policyBeneficialOwnerList.userType.description=User type
policyBeneficialOwnerList.certiType.description=Customer identity type
policyBeneficialOwnerList.account.payAccountId.description=It is necessary when updating the payer account information.
policyBeneficialOwnerList.account.bankCode.description=Bank code
policyBeneficialOwnerList.account.bankName.description=Bank name
policyBeneficialOwnerList.account.cardHolderName.description=Card holder name
policyBeneficialOwnerList.account.cardNumber.description=Card Number
policyBeneficialOwnerList.account.accountSubType.description=Account sub type
policyBeneficialOwnerList.account.expiryDate.description=Expiry date
policyBeneficialOwnerList.account.mobileNo.description=Mobile No
policyBeneficialOwnerList.account.thirdPartyPayVoucher.description=Third party pay voucher No. If using the third-party pay method.
policySecondaryInsurantList.gender.description=Gender
policySecondaryInsurantList.certiNo.description=Customer identity NO
policySecondaryInsurantList.birthday.description=Birthday
policySecondaryInsurantList.fullName.description=Full name
policySecondaryInsurantList.userType.description=User type
policySecondaryInsurantList.certiType.description=Customer identity type
policySecondaryInsurantList.account.payAccountId.description=It is necessary when updating the payer account information.
policySecondaryInsurantList.account.bankCode.description=Bank code
policySecondaryInsurantList.account.bankName.description=Bank name
policySecondaryInsurantList.account.cardHolderName.description=Card holder name
policySecondaryInsurantList.account.cardNumber.description=Card Number
policySecondaryInsurantList.account.accountSubType.description=Account sub type
policySecondaryInsurantList.account.expiryDate.description=Expiry date
policySecondaryInsurantList.account.mobileNo.description=Mobile No
policySecondaryInsurantList.account.thirdPartyPayVoucher.description=Third party pay voucher No. If using the third-party pay method.
policyProductPremiumList.description=Policy premium list
policyProductPremiumList.investmentDetailList.description=Ilp premium detail list
policyProductPremiumList.investmentDetailList.productInvestmentId.description=Product investment ID
policyProductPremiumList.investmentDetailList.premiumType.description=Premium type
policyProductPremiumList.investmentDetailList.installPremium.description=Install premium
policyProductPremiumList.investmentDetailList.paymentFrequencyType.description=Payment frequency type
policyProductPremiumList.investmentDetailList.policyProductFundList.description=Policy product fund list
policyProductPremiumList.investmentDetailList.amount.description=The premium amount
policyProductPremiumList.investmentDetailList.policyProductFundList.fundCode.description=Code of fund
policyProductPremiumList.investmentDetailList.policyProductFundList.fundAllocation.description=Allocation of fund
policyProductPremiumList.investmentDetailList.policyProductFundList.fundName.description=Fund name
policyPayerList.payerType.description=Payer list
policyPayerList.payMethod.description=Pay method
policyPayerList.accounts.description=Customer account list
policyPayerList.gender.description=Gender
policyPayerList.certiNo.description=Customer identity NO
policyPayerList.birthday.description=Birthday
policyPayerList.fullName.description=Full name
policyPayerList.userType.description=User type
policyPayerList.certiType.description=Customer identity type
policyPayerList.account.payAccountId.description=It is necessary when updating the payer account information.
policyPayerList.account.bankCode.description=Bank code
policyPayerList.account.bankName.description=Bank name
policyPayerList.account.cardHolderName.description=Card holder name
policyPayerList.account.cardNumber.description=Card Number
policyPayerList.account.accountSubType.description=Account sub type
policyPayerList.account.expiryDate.description=Expiry date
policyPayerList.account.mobileNo.description=Mobile No
policyPayerList.account.thirdPartyPayVoucher.description=Third party pay voucher No. If using the third-party pay method.
payFeeType.length=50
feeAmount.length=20
errCode.length=64
withdrawalType.length=50
fundTradeType.title=fundTradeType
effectiveDateType.length=128
posFundUnits.title=posFundUnits
posFundUnits.length=20
fundTradeType.description=AMOUNT or UNIT.

message.length=1024
posFundCode.length=256
posFundAmount.length=20
currency.length=3
fundTradeType.enums.UNIT=UNIT

reasonCode.length=6
fundTradeType.enums.AMOUNT=AMOUNT
reason.length=128
fundTradeType.length=50
residenceCountry.length=64
E_ProposalUMEDecision.length=128
relationNo.length=64
temporary.length=1
agentCode.length=64
paymentFrequencyType.length=128
ckaIndicator.length=6
policyNo.length=50
type.length=4
relationshipWithPolicyholder.length=4
sumInsured.length=20
rejectReason.length=512
coveragePeriodType.length=8
fundCode.length=64
E_Declarations.length=10
height.length=16
writtenLanguage.length=16
E_SingaporePR.length=10
E_SourceOfFunds.length=10
E_UMEDecisionDate.length=128
certiType.length=8
secondNationality.length=16
nbConfigurationTypeCode.length=4
branchCode.length=64
singleTopUpType.length=4
distributionMethod.length=128
nationality.length=16
address11.length=256
address12.length=256
address13.length=256
minimumInvestmentPeriodType.length=10
address14.length=256
iban.length=256
fundAllocation.length=20
E_UWEnquiryID.length=128
E_PolicySource.length=64
beneficiaryOrder.length=10
E_StaffPurchaseCode.length=64
E_PremiumFinancing.length=512
cardNumber.length=90
E_MarketingConsent.length=512
kycStatus.length=6
gender.length=6
bankBranchName.length=256
agencyCode.length=64
phoneNo.length=90
channelRole.length=4
birthPlace.length=16
beneficiaryRatio.length=50
minimumInvestmentPeriodValue.length=10
premium.length=64
countryCode.length=10
taxNo.length=90
email.length=512
campaignCode.length=64
bankCode.length=90
phoneType.length=6
E_UMEIndicator.length=32
cardHolderName.length=90
E_TaxResidenceConflictReasonCode.length=10
periodPremium.length=20
userId.length=64
taxResidenceIndicator.length=6
ruleCode.length=32
productCode.length=128
partTimeJob.length=64
E_TaxNoNotAvailableReasonCode.length=90
relationshipToRelated.length=4
goodsCode.length=20
issuanceNo.length=50
paymentOrderNo.length=50
zipCode.length=8
E_MyInfo.length=64
E_TaxResidenceConflictReason.length=256
ruleDecisionCode.length=32
countryOfBirth.length=10
salesChannelCode.length=100
payMethod.length=4
taxResidentialCountry.length=90
zoneId.length=64
periodPlannedPremium.length=50
E_System_Source.length=32
numberOfIssuance.length=10
previousPolicyNo.length=64
issueWithoutPayment.length=4
E_Reinvestment.length=6
orderNo.length=64
organizationName.length=256
addressType.length=6
accountType.length=6
promotionCode.length=64
beneficialOwnerIndicator.length=128
weight.length=16
vestingAge.length=4
E_SourceOfWealth.length=10
liabilityCode.length=20
name.length=64
paymentMethod.length=4
certiNo.length=128
E_TaxResidenceIndicatorClarification.length=256
E_RelationshipDescription.length=256
policyDeliveryMethod.length=8
E_SourceOfWealthDetails.length=512
income.length=32
premiumPeriod.length=20
isRenewalPolicy.length=1
coveragePeriod.length=20
code.length=10
accountSubType.length=6
E_UMEReason.length=128
swiftCode.length=64
remark.length=512
bankName.length=90
title.length=64
E_SourceOfFundsDetails.length=512
rate.length=16
perferLanguage.length=16
paymentFrequencyValue.length=128
amount.length=50
E_IntroducerNric.length=64
bankBranchCode.length=50
race.length=16
premiumFrequencyType.length=8
typeOfPass.length=16
smoke.length=6
fullName.length=256
marriageStatus.length=6
planCode.length=20
premiumType.length=4
agencyName.length=64
bankCity.length=64
payerType.length=4
occupationCode.length=64
E_TaxNoNotAvailableReason.length=90
collectionOrRefundFlag.length=3
policyCurrency.length=3
policyCurrencyAmount.length=20
sourceCurrency.length=3
sourceTotalFee.length=20
feeLevel.length=50
feeType.length=50
policyTerminationFlag.length=8
