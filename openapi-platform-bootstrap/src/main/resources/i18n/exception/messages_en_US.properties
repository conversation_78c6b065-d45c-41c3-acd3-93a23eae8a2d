####
openapi.system.request.param.invalid={0}
openapi.system.unprocessable.entity=request is in progress
openapi.system.application.not.found=Application is not found.
openapi.system.application.not.permitted=Application not permitted.
openapi.system.api.url.not.permitted=API URL not permitted.
openapi.system.dashboard.name.not.found=Dashboard name not found.

###flow###
openapi.flow.idempotent.data.not.found=idempotent data is not found.

####callApi####
openapi.callapi.flow.schema.not.found=Flow Schema is not found for flow instance id: {0}.
openapi.callapi.validation.error=Validate error, the message is {0}.
openapi.callapi.authentication.type.not.support=Authentication type {0} is not support.
openapi.callapi.instance.in.header.is.invalid=Request header api-instance format is error.
openapi.callapi.api.token.unauthorized=Invalid or expired token.
openapi.callapi.api.access.forbidden= Api forbidden access
openapi.dictionary.enum.not.found=enum{0} not found


####application####
openapi.application.must.have.annotation.scenarioInstanceContextBuilder={0} must have @ScenarioInstanceContextBuilder.
openapi.application.must.have.annotation.scenarioInstanceJsonSchemaBuilder={0} must have annotation: @ScenarioInstanceJsonSchemaBuilder.
openapi.application.already.exists.for.builder={0} already exists for builder: {1}.
openapi.application.already.exists.for.duplicate={0} already exists, it's duplicate.
openapi.application.must.have.annotation.scenarioInstanceSampleBuilder={0} must have annotation: @ScenarioInstanceSampleBuilder.
openapi.application.must.have.tags.on.ScenarioInstanceSampleBuilder={0} must have tags on  @ScenarioInstanceSampleBuilder.
openapi.application.api.doc.template.error=Generate Api Doc for template: {0} error
openapi.application.sampleBuilder.namespace.not.found=SampleBuilder of namespace: {0}, schema: {1} is not found.
openapi.application.sample.template.doc.found=No sample template doc found.
openapi.application.scenario.instance.already.exists=Scenario instance already exists.

####dictionary####
openapi.dictionary.dictionary.loader.not.found=Cannot find dictionary loader of type: {0}.

####domain####
openapi.domain.auto.mapping.is.not.supported=Auto mapping is not supported for this model meta: {0}, because it's not IMappingable.
openapi.domain.domain.object.is.not.found=Domain object: {0} of id: {1} is not found.
openapi.domain.copy.modelMeta.error=Copy ModelMeta error!
openapi.domain.flowInstanceId.not.found=FlowDataRecordEntity of flowInstanceId: {0} is not found.
openapi.domain.children.can.only.one={0} 's children can only be 1.
openapi.domain.root.model.meta.error=The root model meta of scenario schema must be class, but find: [type: {0}, name: {1}]
openapi.domain.scenarioMeta.type.not.match=ScenarioMeta of type: {0} can not match {1}
openapi.domain.schema.is.not.array=this schema is not array(or list)
openapi.domain.scenarioInstanceAggregation.not.found=ScenarioInstanceAggregation is not found!
openapi.domain.scenario.api.config.exists=Scenario api config {0} exists
openapi.domain.namespaceEntity.not.found=NamespaceEntity of name: {0} is not found
openapi.domain.userName.not.found=User of userName: {0} is not found
openapi.domain.scenarioSchemaInstanceExample.not.found=ScenarioSchemaInstanceExample {0} not found
openapi.domain.workOrder.not.found=WorkOrder not found
openapi.domain.label.not.found=label not found
openapi.domain.menu.not.found=menu not found
openapi.domain.create.domain.error=Create domain error.
openapi.domain.delete.domain.error=Delete domain error.
openapi.domain.update.domain.error=Update domain error.
openapi.domain.scenario.instance.not.found=Scenario instance is not found.
openapi.domain.work.flow.not.exists=Default work flow not exists.
openapi.domain.scenario.instance.already.exists=Scenario instance already exists.
openapi.domain.model.meta.not.match=Model meta not match: {0}
openapi.domain.domain.not.found=Domain is not found
openapi.domain.scenario.schema.not.found=Scenario schema is not found.
openapi.domain.update.flow.schema.error=Update active flow schema error.
openapi.domain.schema.duplicated.error=Scenario schema's name & binding & version is duplicated, plesase check
openapi.domain.namespace.not.found=Namespace is not found.
openapi.domain.schema.group.not.found=Scenario schema group not found.
openapi.domain.api.version.not.found=APi version not found
openapi.api.version.not.found=api: {0} of version: {0} is not found.


####hooks####
openapi.hooks.event.category.aggregation.not.found=event category aggregation of name : {0} is not found
openapi.hooks.business.event.aggregation.not.found=WebHookBusinessEventAggregation of Id: {0} is not found.
openapi.hooks.is.duplicated={0} is duplicated.
openapi.hooks.api.hook.key.not.found=Api hook key is not found. channel code: {0}, url: {1}
openapi.hooks.trigger.event.error=Trigger event error.
openapi.hooks.example_not_found=Example is not found.

####infra####
openapi.infra.condition.not.in.whiteList=order by condition is not in white List
openapi.infra.model.meta.unknown=Model meta direction:{0} is unknown.

####integration####
openapi.integration.query.param.incorrect=WrapperQuery match orNested param incorrect
openapi.integration.error={0}
openapi.integration.validate.error=validate error, the message is {0}

####knowledge####
openapi.knowledge.modelValueElement.not.mapping=ModelValueElement: {0} can not mapping to {1}
openapi.knowledge.cast.error=Cast error
openapi.knowledge.inputModelValue.must.be.primaryValue=InputModelValue must be PrimaryValue.
openapi.knowledge.flow.schema.not.found=Flow Schema is not found for flow instance id: {0}
openapi.knowledge.require.param.is.missing=You do not send the param %s which is required {0}
openapi.knowledge.illegal.status.of.procedure=Illegal status of procedure in activity
openapi.knowledge.decryptMethod.is.not.match=the instance's decryptMethod is not match procedure's decryptMethod, instance's decryptMethod: {0}, procedure's decryptMethod: {1}
openapi.knowledge.flowActivityResultHandler.type.not.found=FlowActivityResultHandler for type: {0} is not found
openapi.knowledge.activity.type.flow.mismatch=Mismatch activity type flow config
openapi.knowledge.activity.name.not.found=FlowActivity: {0} depend on FlowActivity: {1}, but it's not found.
openapi.knowledge.modelValue.type.not.need=ModelValue type only support {0} here, but find {1}
openapi.knowledge.procedure.name.not.unique=System procedure name must be unique. but found: {0}
openapi.knowledge.procedure.not.in.scope=Procedure: {0} must be @Scope("prototype").
openapi.knowledge.procedure.not.have.content=RestfulService Procedure must have content.
openapi.knowledge.procedure.not.found=Procedure not found
openapi.knowledge.modelValueElement.not.found=ModelValueElement value of id: {0} is not found.
openapi.knowledge.param.not.found={0} is not found
openapi.knowledge.exclusive.activity.without.option=Exclusive activity without default option
openapi.knowledge.flowSchema.not.dag=FlowSchema: {0} is not a DAG

####migration####
openapi.migration.compare.not.support=Not support compare between {0} and {1}
openapi.migration.source.can.not.be.parsed=Swagger source can not be parsed
openapi.migration.source.is.wrong.format=Swagger source is in a wrong format
openapi.migration.un.support.export.type=Un support export type: {0}
openapi.migration.name.and.version.exist=exist same name and version instance, please modify
openapi.migration.export.size.larger.max.limit=The exported size is larger than the maximum limit : {0}

###
openapi.api-view.too.many.active.record=There is only one activated record under a channel
openapi.api-view.exist.same.name.data=Exist same name data, please modify
openapi.common.instance.context.must.binding.goods=context type must be binding goods scenario instance context
openapi.common.instance.context.must.binding.plans=context type must be binding plan scenario instance context

####security####
openapi.security.user.not.found=user not found: {0}
openapi.security.scenario.schema.not.found=Cannot found scenario schema group: {0} in namespace: {1}
openapi.security.scenario.schema.permission.already.exists=Scenario schema permission flter already exists: {0}
openapi.security.permissionFilter.not.have.annotation=PermissionFilter: {0} must have annotation: PermissionFilter.
openapi.security.scenario.instance.not.belong=ScenarioInstance:{0} not belong to this tenant
openapi.security.clientId.not.found=clientId is not found
openapi.security.apikey.invalid=ApiKey is invalid
openapi.security.clientid.not.found=Api ClientId not found
openapi.security.apikey.not.found=Apikey not found

####service####
openapi.service.binding.not.found="Can not find binding for {0}
openapi.service.scenarioSchemaGroup.not.found=ScenarioSchemaGroup {0} not found
openapi.service.model.meta.not.found=Model meta class : {0} is not found.
openapi.service.error={0}
openapi.service.model.meta.not.support=Model meta class : {0} is not support.

####share####
openapi.share.subscription.is.not.found=Subscription is not found.
openapi.share.channelCode.not.found=Channel code is not found.
openapi.share.json.schema.format.not.found=Json schema type: {0} , format: {1} is not found.
openapi.share.list.not.support=List is not supported for Map
openapi.share.datetime.format.not.support=We do not support datetime format: {0}
openapi.share.type.match.error={0} is not match for type: {1}

####Spec####
openapi.spec.api.not.found=Api is not found.
openapi.spec.api.instance.format.error=api-instance in header format error.
openapi.spec.api.instance.not.match=api-instance in header is not matched, definition is {0}, but you give: {1}
openapi.spec.api.timestamp.invalid=api-timestamp in header is invalid.
openapi.spec.api.instance.invalid=api-instance in header is invalid.
openapi.spec.api.timestamp.missing=api-timestamp in header is missing.

####web####
openapi.web.no.such.namespace=No such namespace , please check your id
openapi.web.file.not.found=no such file found
openapi.web.trigger.record.not.found=trigger record not found
openapi.web.no.example.available=Currently no example available
openapi.web.convert.File.failure=convert file to json map failure
openapi.web.import.version.not.support=import version is not support
openapi.web.flow.still.in.use=Flow still in use in {0}
openapi.web.request.object.is.null=you did not send request object for import operate"
openapi.web.data.not.found=data not found
openapi.web.upload.file.error=Upload file error.
openapi.web.no.such.domain=no such domain ,please check your id
openapi.web.procedure.is.exists=Procedure exists in current domain ,cannot delete
openapi.web.api.can.not.support=Api can`t support NoAuth with other authenticationType.
openapi.web.param.is.mismatch=param name : {0}, param type {1} is mismatch default value {2}
openapi.web.instance.not.initialize=instance doc for id {0} not initialize yet.
openapi.web.export.type.not.for.offline.exportation=export type {0} is not for offline exportation.
openapi.web.namespace.not.found=Namespace is not found.
openapi.web.flowSchema.not.found=FlowSchema is not found.
openapi.web.scenario.schema.not.found=Scenario schema is not found.
openapi.web.endpoint.not.exist=Endpoint not exist.
openapi.web.bind.flow.schema.error=Bind Flow Schema error.
openapi.web.flowInstance.not.found=FlowInstance is not found.
openapi.web.procedure.not.found=Procedure not found.
openapi.web.goods.not.found=Goods not found.
openapi.web.schema.group.not.found=Scenario schema group not found.
openapi.web.domain.not.found=Domain is not found
openapi.web.input.model.already.exists=InputModel already exists.
openapi.web.out.model.already.exists=OutputModel already exists.
openapi.web.parent.node.not.found=Parent node is not found.
openapi.web.tenant.in.username.incorrect= tenant in username is incorrect.

####common####
openapi.common.api.version.is.not.supported=Api version: {0} is not supported.
openapi.common.v2.not.supported=V2 is not supported

openapi.common.actual.premium.less.than.policy=Actual premium less than receivable premium.
openapi.common.policy.premium.cannot.be.null=Receivable premium is null.
openapi.common.relation.policy.premium.not.match=Family total premium not match with sum of premiums per person.
openapi.common.acquire.operate.lock.failed=Failed to acquire locks
openapi.common.procedure.content.can.not.be.empty=This procedure must configure content.
openapi.common.error=Open API Error: {0}
openapi.common.sub.group.polices.not.be.empty=subGroup polices can not be empty
openapi.common.groupPolices.not.be.empty=group polices can not be empty
openapi.common.policy.not.be.empty=policy can not be empty
openapi.common.issuance.transaction.type.not_support=the input  issuance transactionType must be UW_AND_CREATEISSUANCE
openapi.common.benefit.illustration.not.found=can not find benefit illustration formula code
openapi.common.collection.not.be.one=collection count must be one
openapi.common.collection.can.not.empty=collection can not be empty
openapi.common.factor.code.not.exists=factor code is not exists in meta service
openapi.common.get.party.failure=get Party info Failure
openapi.common.get.party.param.failure=get party info failure from {}
openapi.common.transaction.not.be.null=Request transaction data can`t be null
openapi.common.object.is.not.support=this sub Object is not support
openapi.common.user.type.not.support=user type : {0} is not support
openapi.common.code.not.found={0} not found code {1}
openapi.common.channel.user.not.match=Channel user no not match
openapi.common.rider.product.configure.error=If you configure rider product, you need set property 'productId'
openapi.common.property.get.specific.value.error=the property {0} do not get specific value, please send value
openapi.common.discriminator_key.not.found=discriminatorKey {0} , id {1} can not found in schema
openapi.common.scenario.instance.context.error=scenario instance context builder not found
openapi.common.validate.error=Validate error, the message is {0}
openapi.common.doc.not.found=Doc is not found
openapi.common.document.not.found=Document is not found
openapi.common.application.not.found=Application {0} is not found.
openapi.common.quotation.not.found=Quotation {0} is not found.
openapi.common.incomplete.person.info=The elements for inquiring personal information are incomplete, please check the search criteria.
openapi.common.check.cka.configuration.absent=CheckCkaStatus must have rule configuration
openapi.common.formula.code.not.found=Product benefit formula not configured.
openapi.common.collection.not.single=Collection has multi elements.
openapi.common.coverage.period.type.or.value.cannot.be.empty=Coverage period type or value cannot be empty.
openapi.common.channel.not.match=No level1 only channel predefined by request level1 channel code.
openapi.common.withdrawal.status.error=cannot be withdraw.
openapi.common.discriminator.key.not.found=Discriminator key not found.
openapi.common.email.duplicate=Duplicate email.
openapi.common.group.polices.not.be.empty=Group policy not be empty.
openapi.common.transaction.can.not.be.null=Validation error, transactionList must not null.
openapi.common.channel.customer.not.found=User not found.
openapi.common.claim.query.history.param.invalid=product code or liability code is invalid.


####questionnaire####
openapi.common.questionnaire.create.error=Create questionnaire failed.
openapi.common.query.questionnaire.error=Query questionnaire failed.
openapi.common.questionnaire.rule.not.configured=Questionnaire risk rule not configured.
openapi.common.questionnaire.not.found=Questionnaire not found.

####CLAIM####
openapi.claim.validate.error = Illegal parameter
openapi.claim.status.error = The claim case has been registered and the policy is under claim.
openapi.claim.customer.not.found = Customer missing.
openapi.claim.validate.mandatory = Param {0} is mandatory.
openapi.claim.param.data.duplicate = Duplicate {0} exists, please confirm.
openapi.claim.resource.not.found = Resource not found
openapi.claim.policy.not.found = Policy no not found. Please check it.
openapi.claim.policy.status.terminated = Policy status is terminated. Please check.
openapi.claim.policy.status.unsettled = Policy has unsettled claim. Please check.
openapi.claim.remote.error = Remote call error.
openapi.claim.no.permission = You do not have permission.
openapi.claim.report.repeat = The claim case has been accepted and the policy is under claim.
openapi.claim.resubmit = Please do not submit again!
openapi.claim.accident.detail.compensation.params.null = Compensation Information missing.
openapi.claim.no.claim.case.permission = The claim case ex-gratia settlement amount has exceeded your authority.
openapi.claim.create.duplicate.bill=Duplicate bills exist, please delete.
openapi.claim.create.case.duplicate.apply=Duplicate Apply By/Transaction The claim case has been accepted and the policy is under claim.
openapi.claim.error=System error
openapi.claim.application.not.found = Claim application info not found
openapi.claim.case.not.found = Claim Case info not found
openapi.claim.create.policy.not.found=Open API Error: No corresponding policy can be found
openapi.claim.type.not.match=LossParty claim type not match in the incident's claim types
openapi.claim.pay.method.incomplete=Payment method are incomplete.
openapi.claim.status.illegal=Illegal case status: {0}
openapi.claim.payee.pay.method.used=The information under payment method that has already been used for a transaction\
  \ cannot be changed.

####POS####
openapi.pos.workflow.configuration.error=Please check the workflow configuration of POS item in Product Center.
openapi.pos.out.effective.period=You cannot perform the operation because it is out of effective period.
openapi.pos.item.under.processing=The item is currently under processing on another POS application, please check before proceeding.
openapi.pos.policy.not.found=Policy is not found
openapi.pos.request.date.error=The Request Date can not be outside the range between the issue date and the current date.
openapi.pos.effective.date.error=POS effective date can not be earlier than policy effective date.
openapi.pos.effective.type.error=POS effective date type=[POS_IMMEDIATELY_DATE] is not available for this policy
openapi.pos.item.configuration.error=You must configure at least one POS item in Product Center before registration.
openapi.pos.exist.other.item=Mutually Exclusive item found against your selected item.
openapi.pos.policy.missing.field=Policy missing required fields.
openapi.pos.policy.status.not.effective=The policy status is not effective.
openapi.pos.item.not.registration=Please register the item you made
openapi.pos.address.info.missing=Please add a new address and fill in the mandatory fields.
openapi.pos.address.info.duplicate=There are duplicate records for [address]. Please help to check.
openapi.pos.holder.id.missing=Policyholder id is missing
openapi.pos.case.not.found = Pos case not found
openapi.pos.case.status.not.data.entry.in.progress = The status of pos case  is not data entry in progress
openapi.pos.auto_renewal_switch.error =The auto renewal switch flag after change should not the same as before change, please check
openapi.pos.auto_renewal_switch.config.error=Please needs config renew to enable in agreement.
openapi.pos.mutually_exclusive.error = Mutually Exclusive item found against your selected item
openapi.pos.transaction.data.is.empty=Transaction list can`t be empty.


####File####
openapi.common.file.name.cannot.be.empty=File name can`t be empty.
openapi.common.business.no.cannot.be.empty=Business no can`t be empty.
openapi.common.content.cannot.be.empty=Content can`t be empty.
openapi.common.reasoncode.cannot.be.empty=ReasonCode can`t be empty.
openapi.common.policyno.cannot.be.empty=PolicyNo can`t be empty.
openapi.common.size.cannot.be.empty=Size can`t be empty.
openapi.common.page.cannot.be.empty=Page can`t be empty.
openapi.common.upload.file.over.limit=Upload file limit size is {0} but actually size is {1}
openapi.common.file.not.found=no such file found
openapi.common.upload.file.limit.error=Upload file limit size is {0} but actually size is {1}
openapi.common.convert.file.error=transfer file to publicLink fail.business no:{0}
openapi.common.file.generate.document.error=Generate document failed
openapi.common.file.cannot.be.empty=File can`t be empty.
openapi.common.payment.amount.not.match=The actual payment amount does not match the database amount or payment not found
openapi.common.payment.not.found=Payment not found

#####Policy
openapi.policy.temporaryIssuance.not.found = temporary Issuance is not found
openapi.policy.renewal.history.not.found={0} not found renewal history.
openapi.policy.events.not.found={0} not found events.
openapi.policy.quotation.not.found=Quotation not found
openapi.policy.policy.over.limit=The normal policy limit size is {0}.
openapi.policy.application.date.cannot.be.empty=ApplicationDate can not be empty.
openapi.policy.the.issuance.not.exist=the issuance is not exist, issuanceNo is {0}
openapi.policy.fee.amount.not.be_empty=the fee amount can`t be empty when issue with payment
openapi.policy.the.issuance.is.effective=the issuance is effective,can`t change
openapi.policy.issuance.illegal.status=The issuance status is illegal.
openapi.policy.trade.no.can.not.be.empty=Trade No can`t be empty.
openapi.common.policy.not.found=Policy is not found.
openapi.common.policy.limit.size.error=the normal policy limit size is 10
openapi.common.policy.status.not.be.effective=The proposal cannot be canceled because it has issurance.
openapi.common.policies.can.not.be.empty=Policies can`t be empty.
openapi.policy.issuance.transaction.type.not.support=The input issuanceTransactionType must be UW_AND_CREATEISSUANCE.
openapi.common.param.invalid=Param is invalid

#######Market
openapi.market.planid.not.match.goodsid=planId {0} is not match goodsId {1}
openapi.market.goods.not.found=goods.code.not.found {0}
openapi.market.product.not.found=product not found
openapi.common.rule.engine.error=Open API rule engine error: {0}.
openapi.market.goodscode.can.not.be.empty=Goods code can`t be empty.
openapi.common.goodsid.can.not.be.empty=Goods id can`t be empty.
openapi.market.package.product.and.liability=Product liability not found.
openapi.common.plan.code.not.match.goodsid=Open API Error: Plan code not match goodsId.
openapi.market.liabilityId.not.match=lhs.getLiabilityId() {0} does not equals rhs.getLiabilityId() {1} Quotation failed
openapi.common.premium.discount.type.illegal=PremiumDiscountTypeEnum not found code: {0}
openapi.common.product.id.not.exist=product id {0} is not exist in configuration
openapi.market.liability.id.not.exist=liability id {0} is not exist in configuration
openapi.common.goods.info.is.not.present=goodsRelatingResponse is null ,planId is {0} , goodsId is {1}
openapi.market.application.elements.not.be.empty={0} is not be empty of application elements.
openapi.market.package.not.found=Package is not found.
openapi.market.promotion.not.found=Promotion code is not found.
market.common.quick.quotation.config.not.match=Parameters do not match quick quotation configuration.



#######metadata
openapi.metadata.config.not.found=Configuration not found.



####Customer####
openapi.customer.party.not.found=Party is not found.
openapi.customer.require.param.is.missing =The necessary elements of insurance are missing.
openapi.common.person.not.found=Person not found
openapi.customer.info.cannot.be.duplicate =There are duplicate records. Please help to check

#####Finance####
openapi.finance.collection.can.not.be.empty=Collection can`t be empty.
openapi.finance.confirm.source.bill.not.same=Confirm sourceAmount must same with bill.
openapi.finance.pay.account.not.found=pay account not found
openapi.finance.payment.amount.not.match=The actual payment amount does not match the database amount or payment not found
openapi.finance.payment.not.found=Payment not found
openapi.finance.bill.not.found=Bill not found
openapi.finance.confirm.bill.not.found=Pending confirm bill no found

openapi.finance.receivable.payable.not.found=policy no {0} receivable payable not found

####Report####
openapi.report.require.param.is.missing =The necessary elements of request are missing

####Agent####
openapi.agent.require.param.is.missing =The necessary elements of request are missing
openapi.common.agent.not.found =Agent not found

####Commission####
openapi.commission.param.is.illegal = The parameter is illegal


######Payment######
openapi.payment.pay.order.not.found = Pay order not found
openapi.payment.pay.order.already.success= Pay order already success


####PENDING CASE####
openapi.pending.case.not.found = Pending case not found
