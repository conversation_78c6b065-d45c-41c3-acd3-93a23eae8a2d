spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 105MB
  shardingsphere:
    sharding:
      default-data-source-name: genesis-mds-0
    datasource:
      refresh:
        enabled: true
        purge-delay: 5
      names: "genesis-mds-0"
      genesis-mds-0:
        type: "com.zaxxer.hikari.HikariDataSource"
        driver-class-name: "com.mysql.cj.jdbc.Driver"
        jdbc-url: "**********************************************************************************************************************************************************************"
        username: "root"
        password: "zatech"
        #jdbc-url: ************************************************************************************************************************************************************************************
        #username: genesis_openapi_platform_dev1
        #password: RPSPRx2hk7yc7b18
        minimum-idle: 1
        maximum-pool-size: 5
        idle-timeout: 540000
        connection-timeout: 60000
        connection-test-query: "select 1"
        pool-name: "genesis-mds-0"
  data:
    mongodb:
      #uri: *************************************************************************************************************************************************************************************************************
      uri: mongodb://localhost:27017/genesis_openapi_platform

octopus:
  mybatis:
    configuration:
      #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  cache:
    redis:
      port: 6379
      database: 8
      hostname: localhost
      #hostname: awschina-nx-redis-dev-app-01.dqimer.0001.cnw1.cache.amazonaws.com.cn
      name: ${spring.application.name}
  mq:
    kafka:
      producer:
        - name: procedureDataProducer
          topic: ${za.graphene.kafka.topic.openapi-platform}
          server-url: ${kafka.server.config}
      consumer:
        - name: procedureDateConsumer
          topic: ${za.graphene.kafka.topic.openapi-platform}
          server-url: ${kafka.server.config}
          group-id: procedure
          allow-consumer-tag: '*'

multitenancy:
  security:
    oauth2:
      resource-server:
        mgmt:
          check-token-url: http://genesis-xdev1-genesis-genesis-channel-auth.dev1.za-gj-aws.net/oauth/check_token
        api:
          check-token-url: http://genesis-xdev1-genesis-genesis-api-auth.dev1.za-gj-aws.net/oauth/check_token

logging:
  level:
    com.zatech.genesis.openapi.knowledge.flow.inbuilt: DEBUG

za:
  graphene:
    feign:
      policy: http://genesis-test1-genesis-genesis-policy.test1.za-gj-aws.net
      metadata: http://genesis-test1-genesis-genesis-metadata.test1.za-gj-aws.net
      channel-auth: http://gaia-poc-iam-channel-auth.dev.za-gj-aws.net
      product: http://genesis-test1-genesis-genesis-product.test1.za-gj-aws.net
      zeus: http://genesis-xdev1-genesis-genesis-zeus.dev1.za-gj-aws.net
      dp: http://genesis-dev1-genesis-genesis-gemini.dev1.za-gj-aws.net
log:
  provider:
    enabled: true
security:
  basic:
    enabled: false

openapi:
  tokenUrl: http://localhost:8080/common/oauth/token
  servers:
    - url: http://localhost:8080/openapi/v1
      description: Dev Enviroment
