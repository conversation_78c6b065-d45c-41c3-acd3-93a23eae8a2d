decorator.datasource.enabled: false
spring:
  application.name: zatech-openapi-platform
  profiles.active: ${ACTIVE_PROFILES:customized}
  cloud.nacos.config:
    server-addr: ${CONFIG_SERVER_URL}
    namespace: ${DEPLOY_ENV}
    group: ${NACOS_GROUP}
    username: ${NACOS_USER:genesis}
    password: ${NACOS_PWD:genesis}
    refresh-enabled: ${NACOS_REFRESH:false}
  config:
    import:
      - nacos:${SCHEMA_NAME:openapi_platform}.yml?group=${SS_NACOS_GROUP:ssmt.${NACOS_GROUP}}&refreshEnabled=true
      - nacos:${NACOS_ENV:openapi-env}.yml
      - nacos:zatech-openapi-platform.yml
      - optional:nacos:zatech-openapi-platform-customized.yml
springdoc.apiDocs.version: OPENAPI_3_0

