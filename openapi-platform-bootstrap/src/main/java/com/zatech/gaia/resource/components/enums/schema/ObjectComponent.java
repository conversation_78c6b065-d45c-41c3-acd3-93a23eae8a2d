package com.zatech.gaia.resource.components.enums.schema;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.gaia.resource.ZaEnumUtil;
import com.zatech.gaia.resource.components.enums.extend.AbstractExtendEnum;
import com.zatech.gaia.resource.components.enums.extend.ExtendEnumDeserializer;
import com.zatech.gaia.resource.components.enums.extend.ExtendEnumSerializer;
import com.zatech.gaia.resource.components.enums.extend.TenantAwareExtendEnumRegistry;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

import lombok.Getter;

/**
 * 扩展对象组件 - 支持预定义枚举值和动态扩展值
 * 线程安全，单例模式，兼容ObjectComponent枚举
 * JSON序列化采用BY_NAME策略，与标准enum行为一致
 */
@Getter
@JsonSerialize(using = ExtendEnumSerializer.class)
@JsonDeserialize(using = ObjectComponent.ExtendObjectComponentDeserializer.class)
@Schema(type = "string", implementation = String.class)
public final class ObjectComponent extends AbstractExtendEnum<ObjectComponent, Integer> {

    private static final TenantAwareExtendEnumRegistry<ObjectComponent, Integer> REGISTRY = new TenantAwareExtendEnumRegistry<>();

    // 预定义常量 - 与ObjectComponent保持一致的顺序
    public static final ObjectComponent DEVICE = of(BuildInObjectComponent.DEVICE);

    public static final ObjectComponent TRAVEL = of(BuildInObjectComponent.TRAVEL);

    public static final ObjectComponent ORDER = of(BuildInObjectComponent.ORDER);

    public static final ObjectComponent BUILDING = of(BuildInObjectComponent.BUILDING);

    public static final ObjectComponent PET = of(BuildInObjectComponent.PET);

    public static final ObjectComponent VEHICLE = of(BuildInObjectComponent.VEHICLE);

    public static final ObjectComponent CLAIM_EXPERIENCE = of(BuildInObjectComponent.CLAIM_EXPERIENCE);

    public static final ObjectComponent DRIVER = of(BuildInObjectComponent.DRIVER);

    public static final ObjectComponent CAR_OWNER = of(BuildInObjectComponent.CAR_OWNER);

    public static final ObjectComponent TRIP = of(BuildInObjectComponent.TRIP);

    public static final ObjectComponent FELLOW_TRAVELLER = of(BuildInObjectComponent.FELLOW_TRAVELLER);

    public static final ObjectComponent HOTEL = of(BuildInObjectComponent.HOTEL);

    public static final ObjectComponent TICKET = of(BuildInObjectComponent.TICKET);

    public static final ObjectComponent PARCEL = of(BuildInObjectComponent.PARCEL);

    public static final ObjectComponent CONTENT_FIXTURE_FITTING = of(BuildInObjectComponent.CONTENT_FIXTURE_FITTING);

    public static final ObjectComponent PLANT_MACHINERY = of(BuildInObjectComponent.PLANT_MACHINERY);

    public static final ObjectComponent STOCKS = of(BuildInObjectComponent.STOCKS);

    public static final ObjectComponent OTHER_PROPERTIES = of(BuildInObjectComponent.OTHER_PROPERTIES);

    public static final ObjectComponent EMPLOYER_LIABILITY = of(BuildInObjectComponent.EMPLOYER_LIABILITY);

    public static final ObjectComponent PUBLIC_LIABILITY = of(BuildInObjectComponent.PUBLIC_LIABILITY);

    public static final ObjectComponent FIDELITY_GUARANTEE = of(BuildInObjectComponent.FIDELITY_GUARANTEE);

    public static final ObjectComponent MACHINERY_EQUIPMENT = of(BuildInObjectComponent.MACHINERY_EQUIPMENT);

    public static final ObjectComponent ELECTRONIC_EQUIPMENT = of(BuildInObjectComponent.ELECTRONIC_EQUIPMENT);

    public static final ObjectComponent PERSONAL_ACCIDENT = of(BuildInObjectComponent.PERSONAL_ACCIDENT);

    public static final ObjectComponent CARGO_IN_TRANSIT = of(BuildInObjectComponent.CARGO_IN_TRANSIT);

    public static final ObjectComponent ADDRESS = of(BuildInObjectComponent.ADDRESS);

    public static final ObjectComponent VEHICLE_LOAN = of(BuildInObjectComponent.VEHICLE_LOAN);

    public static final ObjectComponent VEHICLE_ADDITIONAL_EQUIPMENT = of(BuildInObjectComponent.VEHICLE_ADDITIONAL_EQUIPMENT);

    public static final ObjectComponent LOAN_GUARANTEE = of(BuildInObjectComponent.LOAN_GUARANTEE);

    public static final ObjectComponent PRODUCT_LIABILITY = of(BuildInObjectComponent.PRODUCT_LIABILITY);

    public static final ObjectComponent BOOKING = of(BuildInObjectComponent.BOOKING);

    public static final ObjectComponent PROFESSIONAL_LIABILITY = of(BuildInObjectComponent.PROFESSIONAL_LIABILITY);

    public static final ObjectComponent INDIVIDUAL_RENTER = of(BuildInObjectComponent.INDIVIDUAL_RENTER);

    public static final ObjectComponent ORG_RENTER = of(BuildInObjectComponent.ORG_RENTER);

    public static final ObjectComponent CASH = of(BuildInObjectComponent.CASH);

    public static final ObjectComponent CROPS = of(BuildInObjectComponent.CROPS);

    public static final ObjectComponent DOCUMENTATION = of(BuildInObjectComponent.DOCUMENTATION);

    public static final ObjectComponent EDUCATION_INSTITUTION = of(BuildInObjectComponent.EDUCATION_INSTITUTION);

    public static final ObjectComponent PERSONAL_EFFECT = of(BuildInObjectComponent.PERSONAL_EFFECT);

    public static final ObjectComponent STRUCTURE = of(BuildInObjectComponent.STRUCTURE);

    public static final ObjectComponent FINE_ART = of(BuildInObjectComponent.FINE_ART);

    public static final ObjectComponent FOREST = of(BuildInObjectComponent.FOREST);

    public static final ObjectComponent GARAGE = of(BuildInObjectComponent.GARAGE);

    public static final ObjectComponent GROSS_PROFIT = of(BuildInObjectComponent.GROSS_PROFIT);

    public static final ObjectComponent HORTICULTURE = of(BuildInObjectComponent.HORTICULTURE);

    public static final ObjectComponent INVENTORY = of(BuildInObjectComponent.INVENTORY);

    public static final ObjectComponent LIVE_ANIMAL = of(BuildInObjectComponent.LIVE_ANIMAL);

    public static final ObjectComponent LOSS_OF_RENT = of(BuildInObjectComponent.LOSS_OF_RENT);

    public static final ObjectComponent OPERATION_LIABILITY = of(BuildInObjectComponent.OPERATION_LIABILITY);

    public static final ObjectComponent PRECIOUS_ITEM = of(BuildInObjectComponent.PRECIOUS_ITEM);

    public static final ObjectComponent PRODUCT = of(BuildInObjectComponent.PRODUCT);

    public static final ObjectComponent EMPLOYER = of(BuildInObjectComponent.EMPLOYER);

    public static final ObjectComponent EMPLOYEE_GROUP = of(BuildInObjectComponent.EMPLOYEE_GROUP);

    public static final ObjectComponent EMPLOYEE = of(BuildInObjectComponent.EMPLOYEE);

    public static final ObjectComponent REFRIGERATED_PRODUCT = of(BuildInObjectComponent.REFRIGERATED_PRODUCT);

    public static final ObjectComponent ROADS_AND_PATHWAYS = of(BuildInObjectComponent.ROADS_AND_PATHWAYS);

    public static final ObjectComponent STORAGE = of(BuildInObjectComponent.STORAGE);

    public static final ObjectComponent POOL = of(BuildInObjectComponent.POOL);

    public static final ObjectComponent EMPLOYEE_INFORMATION = of(BuildInObjectComponent.EMPLOYEE_INFORMATION);

    public static final ObjectComponent PROPERTY = of(BuildInObjectComponent.PROPERTY);

    public static final ObjectComponent DATA_STORAGE_DEVICE = of(BuildInObjectComponent.DATA_STORAGE_DEVICE);

    public static final ObjectComponent CONTENTS = of(BuildInObjectComponent.CONTENTS);

    public static final ObjectComponent RENOVATIONS = of(BuildInObjectComponent.RENOVATIONS);

    public static final ObjectComponent INSURED_CONTRACT = of(BuildInObjectComponent.INSURED_CONTRACT);

    public static final ObjectComponent INSURED_PERSON = of(BuildInObjectComponent.INSURED_PERSON);

    public static final ObjectComponent TAKEAWAY = of(BuildInObjectComponent.TAKEAWAY);

    public static final ObjectComponent FLIGHT = of(BuildInObjectComponent.FLIGHT);

    public static final ObjectComponent BUS = of(BuildInObjectComponent.BUS);

    public static final ObjectComponent VESSEL_SHIP = of(BuildInObjectComponent.VESSEL_SHIP);

    public static final ObjectComponent AIRCRAFT = of(BuildInObjectComponent.AIRCRAFT);

    public static final ObjectComponent BOND = of(BuildInObjectComponent.BOND);

    public static final ObjectComponent PRINCIPAL = of(BuildInObjectComponent.PRINCIPAL);

    public static final ObjectComponent OBLIGEE = of(BuildInObjectComponent.OBLIGEE);

    public static final ObjectComponent PERFORMANCE_BOND = of(BuildInObjectComponent.PERFORMANCE_BOND);

    public static final ObjectComponent BID_BOND = of(BuildInObjectComponent.BID_BOND);

    public static final ObjectComponent PAYMENT_BOND = of(BuildInObjectComponent.PAYMENT_BOND);

    public static final ObjectComponent PROJECT = of(BuildInObjectComponent.PROJECT);

    public static final ObjectComponent GENERAL_LIABILITY = of(BuildInObjectComponent.GENERAL_LIABILITY);

    private final String desc;

    private ObjectComponent(Integer code, String name, String desc, boolean extended) {
        super(code, name, extended);
        this.desc = desc;
    }

    public static ObjectComponent of(BuildInObjectComponent component) {
        if (component == null) {
            return null;
        }

        return REGISTRY.registerBuiltIn(new ObjectComponent(component.getCode(), component.name(), component.getDesc(), false));
    }

    public static ObjectComponent of(Integer code, String name, String desc) {
        if (code == null || name == null || desc == null) {
            return null;
        }

        return REGISTRY.registerTenantExtended(new ObjectComponent(code, name, desc, true));
    }

    public static ObjectComponent[] values() {
        return REGISTRY.values(ObjectComponent.class);
    }

    public int ordinal() {
        return REGISTRY.ordinalOf(this);
    }

    public static ObjectComponent valueOf(String name) {
        return REGISTRY.getByName(name);
    }

    public static ObjectComponent getByCode(Integer code) {
        return REGISTRY.getByCode(code);
    }

    public static ObjectComponent findByName(String name) {
        return REGISTRY.findByName(name);
    }

    public static ObjectComponent findByCode(Integer code) {
        return REGISTRY.findByCode(code);
    }

    /**
     * 兼容ObjectComponent的equals方法
     * 支持ExtendObjectComponent与ObjectComponent的相等性比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }

        if (obj instanceof ObjectComponent other) {
            return Objects.equals(this.getCode(), other.getCode());
        }

        if (obj instanceof BuildInObjectComponent other) {
            return Objects.equals(this.getCode(), other.getCode());
        }

        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(getCode());
    }

    @Deprecated(since = "3.10")
    public static ObjectComponent getByObjectCategoryAndSub(ObjectCategoryEnum objectCategory, ObjectSubCategoryEnum objectSubCategory) {
        if (objectCategory == null || objectSubCategory == null) {
            return null;
        }
        switch (objectCategory) {
            case DEVICE:
                return ObjectComponent.DEVICE;
            case TRAVEL:
                return switch (objectSubCategory) {
                    case TRAVEL -> ObjectComponent.TRAVEL;
                    case TRIP -> ObjectComponent.TRIP;
                    case FELLOW_TRAVELLER -> ObjectComponent.FELLOW_TRAVELLER;
                    case HOTEL -> ObjectComponent.HOTEL;
                    case TICKET -> ObjectComponent.TICKET;
                    default -> null;
                };
            case ORDER:
                return switch (objectSubCategory) {
                    case ORDER -> ObjectComponent.ORDER;
                    case BOOKING -> ObjectComponent.BOOKING;
                    default -> null;
                };
            case BUILDING:
                return ObjectComponent.BUILDING;
            case PET:
                return ObjectComponent.PET;
            case AUTO:
                return switch (objectSubCategory) {
                    case VEHICLE -> ObjectComponent.VEHICLE;
                    case CLAIM_EXPERIENCE -> ObjectComponent.CLAIM_EXPERIENCE;
                    case DRIVER -> ObjectComponent.DRIVER;
                    case CAR_OWNER -> ObjectComponent.CAR_OWNER;
                    case VEHICLE_LOAN -> ObjectComponent.VEHICLE_LOAN;
                    case VEHICLE_ADDITIONAL_EQUIPMENT -> ObjectComponent.VEHICLE_ADDITIONAL_EQUIPMENT;
                    case INDIVIDUAL_RENTER -> ObjectComponent.INDIVIDUAL_RENTER;
                    case ORG_RENTER -> ObjectComponent.ORG_RENTER;
                    default -> null;
                };
            case PARCEL:
                return ObjectComponent.PARCEL;
            case LOCATION_INSURED:
                return switch (objectSubCategory) {
                    case BUILDING -> ObjectComponent.BUILDING;
                    case CONTENTS_FIXTURES_FITTINGS -> ObjectComponent.CONTENT_FIXTURE_FITTING;
                    case PLANT_MACHINERY -> ObjectComponent.PLANT_MACHINERY;
                    case STOCKS -> ObjectComponent.STOCKS;
                    case OTHER_PROPERTIES -> ObjectComponent.OTHER_PROPERTIES;
                    case ADDRESS -> ObjectComponent.ADDRESS;
                    case EMPLOYEE_INFORMATION -> ObjectComponent.EMPLOYEE;
                    default -> null;
                };
            case EMPLOYER_LIABILITY:
                return switch (objectSubCategory) {
                    case ADDRESS -> ObjectComponent.ADDRESS;
                    default -> null;
                };
            case PUBLIC_LIABILITY:
                return switch (objectSubCategory) {
                    case BUILDING -> ObjectComponent.BUILDING;
                    case ADDRESS -> ObjectComponent.ADDRESS;
                    default -> null;
                };
            case CONTRACT_INSURED:
                return ObjectComponent.FIDELITY_GUARANTEE;
            case EQUIPMENT_INSURED:
                return switch (objectSubCategory) {
                    case MACHINERY_EQUIPMENTS -> ObjectComponent.MACHINERY_EQUIPMENT;
                    case ELECTRONIC_EQUIPMENTS -> ObjectComponent.ELECTRONIC_EQUIPMENT;
                    default -> null;
                };
            case PERSONAL_ACCIDENT:
                return ObjectComponent.PERSONAL_ACCIDENT;
            case MARINE_CARGO:
                return ObjectComponent.CARGO_IN_TRANSIT;
            case BOND_GUARANTEE:
                return switch (objectSubCategory) {
                    case BOND -> ObjectComponent.BOND;
                    case PRINCIPAL -> ObjectComponent.PRINCIPAL;
                    case OBLIGEE -> ObjectComponent.OBLIGEE;
                    case PERFORMANCE_BOND -> ObjectComponent.PERFORMANCE_BOND;
                    case BID_BOND -> ObjectComponent.BID_BOND;
                    case PAYMENT_BOND -> ObjectComponent.PAYMENT_BOND;
                    case LOAN_GUARANTEE -> ObjectComponent.LOAN_GUARANTEE;
                    default -> null;
                };
            case LIABILITY:
                return switch (objectSubCategory) {
                    case EMPLOYER_LIABILITY -> ObjectComponent.EMPLOYER_LIABILITY;
                    case PUBLIC_LIABILITY -> ObjectComponent.PUBLIC_LIABILITY;
                    case PRODUCT_LIABILITY -> ObjectComponent.PRODUCT_LIABILITY;
                    case PROFESSIONAL_LIABILITY -> ObjectComponent.PROFESSIONAL_LIABILITY;
                    default -> null;
                };
            case DATA_STORAGE_EXTERNAL_DEVICES:
                return ObjectComponent.DATA_STORAGE_DEVICE;
            case CYBER_RISK:
                return ObjectComponent.DEVICE;
            //SME 新增字段, objectCategory 直接对应 objectType, objectSubCategory 直接对应 objectComponent。
            case AUXILIARY_BUILDINGS:
            case BOOKS:
            case CASH_IN_SAFE:
            case CASH_IN_CASH_REGISTERS:
            case CASH_IN_TRANSIT:
            case CROPS:

            case DOCUMENTATION_AND_ARCHIVES:
            case EDUCATION_INSTITUTIONS:
            case EMPLOYEES_PERSONAL_EFFECTS:
            case ENGINEERING_STRUCTURES:
            case FINE_ARTS:
            case FOREST:
            case GARAGE:
            case GROSS_PROFIT:
            case HORTICULTURE:
            case INVENTORY:
            case LIVE_ANIMALS:
            case LOSS_OF_INTEREST:
            case LOSS_OF_RENT:
            case MARITIME_AND_RIVER_STRUCTURES:
            case MEDICAL_EQUIPMENTS:
            case MOBILE_ELECTRONIC_EQUIPMENT:
            case MODELS_MOULDS_SAMPLES_STENCILS:
            case MOVABLE_PROPERTY_AT_EXHIBITIONS:
            case MOVABLE_PROPERTY_STORED_IN_OPEN:
            case OPERATION_LIABILITY:
            case OUTDOOR_SUPPLIES:
            case PERSON_INSURED:
            case POOLS_AND_BATH_HOUSES:
            case PRECIOUS_ITEM:
            case PRODUCT_GOODS_EQUIPMENTS:
            case PRODUCTION_SERVICE_MACHINERY:
            case PROFESSIONAL_EMPLOYEES_PERSON_INFO:
            case PROPERTY_OF_ANOTHER_PERSON:
            case PUBLIC_PLACES:
            case RAW_MATERIALS_PRODUCTS_MERCHANDISES:
            case REFRIGERATED_GOODS:
            case ROADS_AND_PATHWAYS:
            case SPORTS_FIELDS:
            case STORING_OF_MATERIALS_FUEL_PROPERTY:
            case SWIMMING_POOLS:
            case TENANTS_OWNERS_BUILDINGS:
            case CONTENTS:
            case RENOVATIONS:
            case PROFESSIONAL_LIABILITY:
                return switch (objectSubCategory) {
                    case CASH -> ObjectComponent.CASH;
                    case CROPS -> ObjectComponent.CROPS;
                    case DOCUMENTATION -> ObjectComponent.DOCUMENTATION;
                    case EDUCATION_INSTITUTION -> ObjectComponent.EDUCATION_INSTITUTION;
                    case PERSONAL_EFFECT -> ObjectComponent.PERSONAL_EFFECT;
                    case STRUCTURE -> ObjectComponent.STRUCTURE;
                    case FINE_ART -> ObjectComponent.FINE_ART;
                    case FOREST -> ObjectComponent.FOREST;
                    case GARAGE -> ObjectComponent.GARAGE;
                    case GROSS_PROFIT -> ObjectComponent.GROSS_PROFIT;
                    case HORTICULTURE -> ObjectComponent.HORTICULTURE;
                    case INVENTORY -> ObjectComponent.INVENTORY;
                    case LIVE_ANIMAL -> ObjectComponent.LIVE_ANIMAL;
                    case LOSS_OF_RENT -> ObjectComponent.LOSS_OF_RENT;
                    case OPERATION_LIABILITY -> ObjectComponent.OPERATION_LIABILITY;
                    case PRECIOUS_ITEM -> ObjectComponent.PRECIOUS_ITEM;
                    case PRODUCT -> ObjectComponent.PRODUCT;
                    case EMPLOYER -> ObjectComponent.EMPLOYER;
                    case EMPLOYEE_GROUP -> ObjectComponent.EMPLOYEE_GROUP;
                    case EMPLOYEE -> ObjectComponent.EMPLOYEE;
                    case REFRIGERATED_PRODUCT -> ObjectComponent.REFRIGERATED_PRODUCT;
                    case ROADS_AND_PATHWAYS -> ObjectComponent.ROADS_AND_PATHWAYS;
                    case STORAGE -> ObjectComponent.STORAGE;
                    case POOL -> ObjectComponent.POOL;
                    case EMPLOYEE_INFORMATION -> ObjectComponent.EMPLOYEE;
                    case PROPERTY -> ObjectComponent.PROPERTY;
                    case PRODUCT_LIABILITY -> ObjectComponent.PRODUCT_LIABILITY;
                    case ADDRESS -> ObjectComponent.ADDRESS;
                    case BUILDING -> ObjectComponent.BUILDING;
                    case PUBLIC_LIABILITY -> ObjectComponent.PUBLIC_LIABILITY;
                    case MACHINERY_EQUIPMENTS -> ObjectComponent.MACHINERY_EQUIPMENT;
                    case DEVICE -> ObjectComponent.DEVICE;
                    case BOOKING -> ObjectComponent.BOOKING;
                    case ELECTRONIC_EQUIPMENTS -> ObjectComponent.ELECTRONIC_EQUIPMENT;
                    case CONTENTS -> ObjectComponent.CONTENTS;
                    case RENOVATIONS -> ObjectComponent.RENOVATIONS;
                    case PROJECT -> ObjectComponent.PROJECT;
                    default -> null;
                };
            case VESSEL_SHIP:
                return ObjectComponent.VESSEL_SHIP;
            case AIRCRAFT:
                return ObjectComponent.AIRCRAFT;
            default:
                return null;
        }
    }

    @Deprecated(since = "3.10")
    public static ObjectComponent getByObjectCategoryAndSub(Integer objectCategory, Integer objectSubCategory) {
        return getByObjectCategoryAndSub(ZaEnumUtil.getEnum(ObjectCategoryEnum.class, objectCategory), ZaEnumUtil.getEnum(ObjectSubCategoryEnum.class, objectSubCategory));
    }

    public static class ExtendObjectComponentDeserializer extends ExtendEnumDeserializer<ObjectComponent, Integer> {

        public ExtendObjectComponentDeserializer() {
            super(ObjectComponent::valueOf, ObjectComponent::values, "ExtendObjectComponent");
        }

    }

}