package com.zatech.genesis.openapi.platform;

import com.za.taylor.EnableTaylor;
import com.zatech.genesis.openapi.platform.annotation.EnableOpenApiPlugin;
import com.zatech.genesis.openapi.platform.application.scenario.instance.exception.ApplicationStartStageErrorCode;
import com.zatech.genesis.openapi.platform.dictionary.exception.DictionaryErrorCode;
import com.zatech.genesis.openapi.platform.domain.exception.DomainErrorCode;
import com.zatech.genesis.openapi.platform.hooks.api.exception.WebHookErrorCodes;
import com.zatech.genesis.openapi.platform.infra.exception.InfraErrorCode;
import com.zatech.genesis.openapi.platform.integration.IntegrationErrorCode;
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode;
import com.zatech.genesis.openapi.platform.migration.exception.MigrationErrorCode;
import com.zatech.genesis.openapi.platform.security.inbuilt.exception.SecurityErrorCode;
import com.zatech.genesis.openapi.platform.service.exception.ServiceErrorCode;
import com.zatech.genesis.openapi.platform.share.ErrorConstant;
import com.zatech.genesis.openapi.platform.share.exception.ApiViewErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.CommonBizErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.ShareErrorCode;
import com.zatech.genesis.openapi.platform.spec.ApiSpecErrorCodes;
import com.zatech.genesis.openapi.platform.web.exception.WebErrorCode;
import com.zatech.genesis.portal.toolbox.exception.EnablePortalToolboxException;
import com.zatech.genesis.portal.toolbox.i18n.EnablePortalToolboxI18n;
import com.zatech.genesis.portal.toolbox.util.EnablePortalToolboxUtil;
import com.zatech.octopus.module.mq.core.annotation.EnableProducers;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableOpenApiPlugin({"com.zatech.genesis", "com.zhongan.graphene", "com.zatech.fusion"})
@SpringBootApplication
@EnableTransactionManagement
@EnableCaching
@EnableProducers(basePackages = "com.zatech.genesis.openapi")
@MapperScan("com.zatech.genesis.openapi.platform.infra.**.mapper")
@EnablePortalToolboxI18n
@EnableFeignClients(basePackages = {"com.zhongan.graphene", "com.zatech.genesis", "com.zatech.fusion"})
@EnablePortalToolboxUtil
@EnablePortalToolboxException(
    serviceKey = ErrorConstant.KEY,
    showChain = false,
    errorCodes = {
        ApplicationStartStageErrorCode.class, DictionaryErrorCode.class,
        DomainErrorCode.class, WebHookErrorCodes.class, InfraErrorCode.class, IntegrationErrorCode.class,
        KnowledgeErrorCode.class, MigrationErrorCode.class, CommonBizErrorCode.class, SecurityErrorCode.class,
        ServiceErrorCode.class, ShareErrorCode.class, WebErrorCode.class, ApiViewErrorCode.class,
        ApiSpecErrorCodes.class
    })
@EnableTaylor
//@EnableDiscoveryClient
public class OpenapiApplication {

    public static void main(String[] args) {

        SpringApplication.run(OpenapiApplication.class, args);

    }

}
