/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.config;

import com.zatech.genesis.openapi.platform.share.service.impl.OpenAPIErrorCodeFilterImpl;
import com.zatech.genesis.portal.toolbox.exception.global.ErrorCodeTemplateFilter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/1/31 16:31
 **/
@Configuration
public class OpenApiConfig {

    @Bean
    ErrorCodeTemplateFilter errorCodeTemplateFilter() {
        return new OpenAPIErrorCodeFilterImpl();
    }

}
