package com.zatech.genesis.openapi.platform.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.annotation.AliasFor;

/**
 * <AUTHOR>
 * @Date 2022/1/10
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@ComponentScan
public @interface EnableOpenApiPlugin {

    @AliasFor(annotation = ComponentScan.class)
    String[] value() default {};

    @AliasFor(annotation = ComponentScan.class)
    String[] basePackages() default {};

}
