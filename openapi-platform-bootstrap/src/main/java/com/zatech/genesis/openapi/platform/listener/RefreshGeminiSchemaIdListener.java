/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.listener;

import brave.Span;
import brave.Tracer;

import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.instance.ScenarioInstanceEntity;
import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.schema.ScenarioSchemaEntity;
import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.schema.factory.ScenarioSchemaEntityFactory;
import com.zatech.genesis.openapi.platform.migration.event.CheckGeminiSchemaIdEvent;
import com.zatech.genesis.openapi.platform.service.RefreshGeminiSchemaIdService;
import com.zatech.genesis.openapi.platform.share.ThreadPoolContext;
import com.zatech.genesis.openapi.platform.share.enums.ScenarioSchemaBindingEnum;
import com.zatech.genesis.openapi.platform.share.enums.ScenarioSchemaOriginEnum;
import com.zatech.genesis.openapi.platform.share.lock.ILocker;
import com.zatech.octopus.component.cache.redis.support.RedisCacheProvider;
import com.zatech.octopus.component.sleuth.TraceOp;
import com.zhongan.multitenancy.spring.boot.util.DataSourceUtils;

import io.micrometer.observation.Observation;
import io.micrometer.observation.ObservationRegistry;
import io.micrometer.observation.transport.ReceiverContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/11/28 15:06
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "openapi.refresh.gemini_schema", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RefreshGeminiSchemaIdListener {

    @Autowired
    private Environment environment;

    @Autowired
    private ScenarioSchemaEntityFactory scenarioSchemaEntityFactory;

    @Autowired
    ObservationRegistry observationRegistry;

    @Autowired
    Tracer tracer;

    @Autowired
    private RefreshGeminiSchemaIdService refreshGeminiSchemaIdService;

    @Autowired
    private RedisCacheProvider redisCacheProvider;

    private final ThreadLocal<Tracer.SpanInScope> scopeContext = new ThreadLocal<>();

    private Map<String, String> geminiPhaseMap = new HashMap<>();

    private Map<String, String> geminiPhaseEBMap = new HashMap<>();

    private Map<String, String> geminiPhaseV2Map = new HashMap<>();

    private Map<String, String> geminiPhaseEBV2Map = new HashMap<>();

    private Map<String, String> geminiGoodsPhaseV2Map = new HashMap<>();

    private List<String> needRefreshSchemasBindingTenant = Arrays.asList("issuance", "underwriting", "quotation", "benefit_illustration",
        "master_policy_basic_info_create");

    private List<String> needRefreshSchemasBindingGoods = Arrays.asList("issuance", "underwriting", "quotation", "benefit_illustration",
        "verify_issuance", "calculate_scenario", "quotation_verify");

    @Autowired
    private ThreadPoolContext threadPoolContext;

    @Autowired
    private ILocker locker;

    private static final String REFRESH_SCHEMA_LOCK_KEY = "refresh_schema:scenario_instance:%d";

    @PostConstruct
    public void init() {

        geminiPhaseMap.putIfAbsent("issuance", "POLICY_API");
        geminiPhaseMap.putIfAbsent("underwriting", "UW_API");
        geminiPhaseMap.putIfAbsent("quotation", "QUOTATION_API");
        geminiPhaseMap.putIfAbsent("benefit_illustration", "BENEFIT_ILLUSTRATION_API");

        geminiPhaseEBMap.putIfAbsent("issuance", "POLICY_EB_API");
        geminiPhaseEBMap.putIfAbsent("quotation", "QUOTATION_EB_API");
        geminiPhaseEBMap.putIfAbsent("master_policy_basic_info_create", "MASTER_POLICY_BASIC_INFO");

        geminiPhaseV2Map.putIfAbsent("issuance", "POLICY_API_V2");
        geminiPhaseV2Map.putIfAbsent("underwriting", "UW_API_V2");
        geminiPhaseV2Map.putIfAbsent("quotation", "QUOTATION_API_V2");

        geminiPhaseEBV2Map.putIfAbsent("issuance", "POLICY_EB_API_V2");
        geminiPhaseEBV2Map.putIfAbsent("quotation", "QUOTATION_EB_API_V2");
        geminiPhaseEBV2Map.putIfAbsent("master_policy_basic_info_create", "MASTER_POLICY_BASIC_INFO_V2");

        geminiGoodsPhaseV2Map.putIfAbsent("issuance", "POLICY");
        geminiGoodsPhaseV2Map.putIfAbsent("underwriting", "UW");
        geminiGoodsPhaseV2Map.putIfAbsent("quotation", "QUOTATION");
        geminiGoodsPhaseV2Map.putIfAbsent("benefit_illustration", "BENEFIT_ILLUSTRATION");
        geminiGoodsPhaseV2Map.putIfAbsent("verify_issuance", "POLICY_VERIFY");
        geminiGoodsPhaseV2Map.putIfAbsent("calculate_scenario", "OTHER_CALCULATE");
        geminiGoodsPhaseV2Map.putIfAbsent("quotation_verify", "UW_QUOTATION");
    }

    @EventListener(value = {ApplicationReadyEvent.class, CheckGeminiSchemaIdEvent.class})
    public void onApplicationEvent() {

        Set<String> tenantList = getTenantList();
        tenantList.forEach(tenant -> {
            ReceiverContext<Map<String, String>> context = new ReceiverContext<>(Map::get);
            context.setCarrier(Map.of("x-za-tenant", tenant));
            Observation.createNotStarted("refresh_schema", () -> context, observationRegistry)
                .observe(() -> {
                    threadPoolContext.getRefreshSchemaExecutor().execute(() -> {
                        try {
                            //遍历该tenant下面所有的scenario instance
                            refreshGeminiSchemaForBindingTenant(tenant);
                            refreshGeminiSchemaForBindingGoods(tenant);
                        } catch (Exception e) {
                            log.warn("refresh gemini schema error: {0}", e);
                        }
                    });
                });
        });
    }

    private void refreshGeminiSchemaForBindingGoods(String tenant) {

        needRefreshSchemasBindingGoods.stream().forEach(schemaName -> scenarioSchemaEntityFactory.getDomainBy(ScenarioSchemaOriginEnum.JsonSchema,
            ScenarioSchemaOriginEnum.SampleTemplate,
            ScenarioSchemaBindingEnum.Goods, schemaName).stream().forEach(scenarioSchema ->
            refreshGeminiSchema(tenant, schemaName, geminiGoodsPhaseV2Map, ScenarioSchemaBindingEnum.Goods, scenarioSchema)
        ));
    }

    private void refreshGeminiSchemaForBindingTenant(String tenant) {

        needRefreshSchemasBindingTenant.forEach(schemaName -> scenarioSchemaEntityFactory.getDomainBy(ScenarioSchemaOriginEnum.JsonSchema,
            ScenarioSchemaOriginEnum.SampleTemplate,
            ScenarioSchemaBindingEnum.Tenant, schemaName).forEach(scenarioSchema -> {
                if (isEB(scenarioSchema)) {
                    if ("v1".equals(scenarioSchema.version())) {
                        refreshGeminiSchema(tenant, schemaName, geminiPhaseEBMap, ScenarioSchemaBindingEnum.Tenant, scenarioSchema);
                    }
                    if ("v2".equals(scenarioSchema.version())) {
                        refreshGeminiSchema(tenant, schemaName, geminiPhaseEBV2Map, ScenarioSchemaBindingEnum.Tenant, scenarioSchema);
                    }
                } else {
                    if ("v1".equals(scenarioSchema.version())) {
                        refreshGeminiSchema(tenant, schemaName, geminiPhaseMap, ScenarioSchemaBindingEnum.Tenant, scenarioSchema);
                    }
                    if ("v2".equals(scenarioSchema.version())) {
                        refreshGeminiSchema(tenant, schemaName, geminiPhaseV2Map, ScenarioSchemaBindingEnum.Tenant, scenarioSchema);
                    }
                }
            }));
    }

    private void refreshGeminiSchema(String tenant, String schemaName, Map<String, String> phaseMap,
                                     ScenarioSchemaBindingEnum binding, ScenarioSchemaEntity scenarioSchema) {
        Arrays.stream(scenarioSchema.scenarioInstanceEntities()).filter(ScenarioInstanceEntity::isActive).forEach(x -> {
            locker.operationWithLock(REFRESH_SCHEMA_LOCK_KEY.formatted(x.getId()), () -> {
                beginningTracing();
                TraceOp.setExtItem("x-za-tenant", tenant);
                refreshGeminiSchemaIdService.checkSchemaFromGemini(phaseMap.get(schemaName), binding, x);
                scopeContext.remove();
            });
        });
    }

    private boolean isEB(ScenarioSchemaEntity scenarioSchemaEntity) {

        return "group_benefit".equals(scenarioSchemaEntity.groupEntity().namespaceEntity().name());
    }

    private void beginningTracing() {

        Span newSpan = tracer.newTrace();
        scopeContext.set(tracer.withSpanInScope(newSpan));
    }

    private Set<String> getTenantList() {

        return DataSourceUtils.getDataSourceNames(environment).stream().map(tenant -> {
            if (tenant.contains("-")) {
                return tenant.substring(0, tenant.indexOf("-"));
            } else {
                return tenant;
            }
        }).collect(Collectors.toSet());
    }

}
