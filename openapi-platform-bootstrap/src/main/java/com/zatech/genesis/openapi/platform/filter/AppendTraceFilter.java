package com.zatech.genesis.openapi.platform.filter;

import brave.Span;
import brave.Tracer;
import brave.propagation.TraceContext;

import java.io.IOException;

import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import static java.util.Optional.ofNullable;

@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
@Component
public class AppendTraceFilter extends OncePerRequestFilter {

    @Resource
    private Tracer tracer;

    public static final String API_TRACE_ID = "api-trace-id";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            addTraceToResponse(response);
        } catch (Exception e) {
            log.warn("Failed to add trace info into response. ", e);
        }
        filterChain.doFilter(request, response);
    }

    private void addTraceToResponse(HttpServletResponse response) {
        ofNullable(tracer)
            .map(Tracer::currentSpan)
            .map(Span::context)
            .map(TraceContext::traceIdString)
            .ifPresent(traceId -> response.setHeader(API_TRACE_ID, traceId));
    }

}
