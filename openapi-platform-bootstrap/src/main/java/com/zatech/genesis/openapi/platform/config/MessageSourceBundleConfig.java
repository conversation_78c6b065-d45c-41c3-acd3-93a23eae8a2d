package com.zatech.genesis.openapi.platform.config;

import com.zatech.genesis.portal.toolbox.i18n.I18nBundler;
import com.zatech.genesis.portal.toolbox.i18n.messagesource.DefaultMessageSourceCoordinate;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class MessageSourceBundleConfig {

    @Autowired
    I18nBundler i18nBundler;

    @PostConstruct
    void init() {
        i18nBundler.addMessageSourceBundleByCoordinate(
            new DefaultMessageSourceCoordinate("test",
                new String[] {"classpath:i18n/tenant/test"}));
    }
}
