<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>openapi-platform-hooks</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>openapi-platform-hooks-inbuilt</artifactId>
    <name>openapi-platform-hooks-inbuilt</name>
    <description>The OpenApi Platform Hooks Inbuilt Module</description>

    <dependencies>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-hooks-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>zatech-octopus-component-cryptogram-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-hooks-plugin-nano</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-infra</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-common</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
    </build>
</project>
