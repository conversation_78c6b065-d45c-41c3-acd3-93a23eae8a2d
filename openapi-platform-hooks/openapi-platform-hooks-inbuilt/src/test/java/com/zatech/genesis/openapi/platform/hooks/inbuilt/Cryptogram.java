package com.zatech.genesis.openapi.platform.hooks.inbuilt;


import java.security.NoSuchAlgorithmException;

import jakarta.annotation.PostConstruct;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/6/2
 **/
public class Cryptogram {

    @PostConstruct
    void testCrypt() throws NoSuchAlgorithmException {

        /**WithSignatureEventModel eventModel = new WithSignatureEventModel();
         NanoEventCategoryEnum.ClaimEventCategoryEnum event = NanoEventCategoryEnum.ClaimEventCategoryEnum.CLAIM_RESULT;
         eventModel.setEvent(event.getEventCategory().getEventName());
         eventModel.setVersion("v1");
         eventModel.setTimestamp("2022-05-18T21:51:43.925+08");
         eventModel.setSubscription(SubsriptionConsts.Nano);
         eventModel.setRetryTimes(0);
         eventModel.setMessageId(82915387195513L);
         eventModel.setPagination(false);

         System.out.println(JsonParser.toJsonString(eventModel));
         BusinessData businessData = new BusinessData();
         System.out.println(JsonParser.toJsonString(businessData));

         // 生成密钥
         //KeyUtils.KeyModel keyModel = KeyUtils.generateKey(CryptogramEnum.AES.getName(), 256);

         //System.out.println(keyModel.getKey());

         String key = "HLETW3J2UUWTXIZ6wzFr2op0pnm4mDXvkHWomhSbQ34=";
         // 加密过程
         ICryptogram rsa = cryptogramFactory.getCryptogram(CryptogramEnum.AES);
         EncryptRequest encryptRequest = new EncryptRequest();
         encryptRequest.setKey(key);
         encryptRequest.setPlainText(JsonParser.toJsonString(businessData));
         EncryptResult encryptResult = rsa.encrypt(encryptRequest);
         System.out.println(encryptResult.getCipheredText());
         eventModel.setData(encryptResult.getCipheredText());

         //解密
         DecryptRequest decryptRequest = new DecryptRequest();
         decryptRequest.setKey(key);
         decryptRequest.setCipheredText(eventModel.getData());
         DecryptResult decryptResult = rsa.decrypt(decryptRequest);
         System.out.println(decryptResult.getPlainText());

         eventModel.setData(decryptResult.getPlainText());
         Map<String, Object> treeMap = new TreeMap<>(String::compareTo);
         JsonMap jsonMap = JsonParser.fromObjToJsonMap(eventModel);
         System.out.println(jsonMap.toJsonString());
         treeMap.putAll(jsonMap);
         System.out.println(JsonParser.toJsonString(treeMap));
         String str = treeMap.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).reduce((a,b) -> a + "&" + b).get();

         try {
         System.out.println(str);
         String md5 = DigestUtils.md5DigestAsHex(str.getBytes("utf-8"));
         System.out.println(md5);
         eventModel.setSignature(md5);
         } catch (UnsupportedEncodingException e) {
         e.printStackTrace();
         }**/
    }

    @Data
    static class BusinessData {

        private CaseInfo caseInfo = new CaseInfo();

        @Data
        public static class CaseInfo {

            private String paymentStatus = "WAITING_FOR_PAYMENT";

            private String payoutAmount = "payoutAmount";

            private String applicationNo = "901730000467005";

            private String caseNo = "CLMGC00437004";

            private String caseStatus = "ACCEPT_SETTLED";

            private String decision = "ACCEPT";

            private String assessmentCompleteStatus = "YES";

            private String baseCurrency = "SGD";

            private String incidentNo = "INC20220526925110957998083";

            private String materialCompleteStatus = "YES";

        }

    }

}
