package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory

import com.zatech.genesis.openapi.platform.api.resource.developercenter.mgmt.webhook.endpoint.request.CreateEndpointRequest
import com.zatech.genesis.openapi.platform.domain.DomainFactory
import com.zatech.genesis.openapi.platform.domain.exception.DomainObjectNotFoundException
import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.WebHookRegistryAggregation
import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookRegistry
import com.zatech.genesis.openapi.platform.infra.application.service.{IWebHookEventSubscriptionService, IWebHookKeyService, IWebHookRegistryService}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.octopus.component.sleuth.TraceOp
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * <AUTHOR>
 * @Date 2022/6/8
 * */
@Component
class WebHookRegistryAggregationFactory @Autowired()(
                                                      webHookEventSubscriptionService: IWebHookEventSubscriptionService,
                                                      webHookKeyService: IWebHookKeyService,
                                                      webHookRegistryService: IWebHookRegistryService
                                                    ) extends DomainFactory[WebHookRegistryAggregation, Long] {
  /**
   * registry 中绑定event，event中绑定registry，存在循环引用，延迟注入
   */
  @Autowired @Lazy private val eventDefinitionDomainFactory: WebHookEventAggregationFactory = null

  override def getDomain(domainId: Long): WebHookRegistryAggregation = {
    webHookRegistryService.getOptById(domainId).map(newDomain)
      .getOrElse(throw new DomainObjectNotFoundException(classOf[WebHookRegistryAggregation], domainId))
  }

  @Transactional
  def createDomain(request: CreateEndpointRequest): WebHookRegistryAggregation = {
    val registry = new WebHookRegistry
    registry.setCallbackUrl(request.getUrl)
    registry.setName(request.getName)
    registry.setDescription(request.getDescription)
    registry.setChannelCode(TraceSupport.getAppIdOrThrow)
    registry.setTenantCode(TraceOp.getTenant)
    registry.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    registry.setModifier(TraceSupport.getAuthenticationUserNameOrNull)
    webHookRegistryService.save(registry)
    val aggregation = getDomain(registry.getId)
    aggregation.createKey(request.getKey)
    if(CollectionUtils.isNotEmpty(request.getEvents)) {
      aggregation
        .subscribeEvents(request.getEvents.map(event => EventName(event)).toSet)
    }
    aggregation
  }

  def all: Seq[WebHookRegistryAggregation] = {
    webHookRegistryService.listByAuth().map(newDomain)
  }

  @Transactional
  override def deleteDomain(registryId: Long): Unit = {
    getDomain(registryId).delete()
  }

  def newDomain(po: WebHookRegistry): WebHookRegistryAggregation = {
    new WebHookRegistryAggregation(
      entity = po,
      webHookKeyService = webHookKeyService,
      webHookRegistryService = webHookRegistryService,
      eventAggregationFactory = eventDefinitionDomainFactory,
      webHookEventSubscriptionService = webHookEventSubscriptionService
    )
  }
}
