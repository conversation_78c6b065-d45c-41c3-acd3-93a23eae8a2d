package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, WithSignatureEventModel}
import com.zatech.genesis.openapi.platform.hooks.api.{EventName, IEventHookSlot, IEventHookSlotChain, IHookTriggerAggregation}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.EventDefinitionManager
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.security.api.application.IApplicationFactory
import com.zatech.genesis.openapi.platform.share.Loggable

/**
 * <AUTHOR>
 * @Date 2022/6/2
 * */
final class InbuiltEventHookSlotChain(
                                       slots: List[IEventHookSlot],
                                       eventDefinitionManager: EventDefinitionManager,
                                       applicationFactory: IApplicationFactory
                                     ) extends IEventHookSlotChain with Loggable {

  override def execute(hookAggregation: IHookTriggerAggregation, businessData: BaseBusinessEventData): EventHookContext = {
    val eventName = EventName(hookAggregation.getEvent.getEvent)
    val eventDefinition = eventDefinitionManager.getEventDefOrThrow(eventName)

    //v1.1 update: 多个回调接口在外部的bound表进行区分，这里对于单个组合进行处理（url-事件信息一对一），否则log的情况会混乱
    val context = new EventHookContext
    context.setEventDefinition(eventDefinition)
    context.setTriggerAggregation(hookAggregation)
    context.setRegistryAggregation(hookAggregation.getRegistry)
    context.setApplication(applicationFactory.getApplication(hookAggregation.getAppId))

    //chain不长，一般不会stack over flow，如果死循环，则更希望尽快stack overflow
    def recursiveExecuteSlot(slots: List[IEventHookSlot], eventModel: BaseEventModel): Boolean = {
      slots match {
        case head :: tail => {
          val result =
            if (head.shouldExecute(context, businessData, eventModel)) head.execute(context, businessData, eventModel) else true
          result && recursiveExecuteSlot(tail, eventModel)
        }
        case _ => true
      }
    }

    //不断执行，直到得到false
    while (recursiveExecuteSlot(slots, new WithSignatureEventModel)) {
      if (log.isDebugEnabled) {
        log.debug("Next round of executing slots chain")
      }
    }

    context
  }
}
