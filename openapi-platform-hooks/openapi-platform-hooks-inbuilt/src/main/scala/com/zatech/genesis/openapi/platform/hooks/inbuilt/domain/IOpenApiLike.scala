package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.openapi.platform.hooks.api.model.WithSignatureEventModel
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.pageable.{OnceAllPageableBusinessEventData, TruncatedPageableBusinessEventData}
import com.zatech.genesis.portal.toolbox.jsonschema.swagger.SwaggerSchemaFactory
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.media.{Content, MediaType, Schema, StringSchema}
import io.swagger.v3.oas.models.parameters.{Parameter, RequestBody}
import io.swagger.v3.oas.models.servers.Server
import io.swagger.v3.oas.models.{Components, OpenAPI, Operation, PathItem}

/**
 * <AUTHOR>
 * @create 2024/11/19 11:28
 * */
trait IOpenApiLike {this: WebHookEventAggregation =>

  private lazy val resolvedSchema =
    SwaggerSchemaFactory.newResolvedSchemaByClass(definition.getEventBusinessModelClass)

  //web hook event的标准OpenAPI文档
  lazy val eventOpenApi: OpenAPI = {
    val hintDesc =
      """
        |[Attention please!]
        | the data property in request body schema is the business model,
        | but in real call it is encrypted into a secret text,
        | for more details please read [WebHooks](/openx/docs/console/webhooks)
        |""".stripMargin

    new OpenAPI()
      .info(buildInfo)
      .servers(buildServers)
      .components(buildComponents)
      .path("/", new PathItem().post(
        new Operation()
          .description(description + hintDesc)
          .summary(description)
          .requestBody(buildRequestBody)
          .parameters(buildParameters)
      ))
  }

  private def buildInfo: Info = new Info().title(id.value).version(version)

  private def buildComponents: Components = new Components().schemas(resolvedSchema.referencedSchemas)

  private def buildRequestBody: RequestBody = {
    new RequestBody()
      .required(true)
      .content(
        new Content()
          .addMediaType(
            "application/json",
            new MediaType()
              .schema(buildRequestBodySchema)
              .examples(examplesOpt.map(_.toOpenApiExamples).orNull)))
  }

  private def buildRequestBodySchema: Schema[_] = {
    if(definition.getStrategy.isSingle) buildSingleDataBodySchema
    else if(definition.getStrategy.isTruncate) buildTruncatePageBodySchema
    else buildOnceAllPageableBodySchema
  }

  private def buildSingleDataBodySchema: Schema[_] = {
    val baseSchema = SwaggerSchemaFactory.newFlattenSchemaByClass(classOf[WithSignatureEventModel])
    //将data替换为业务model schema
    baseSchema.addProperty("data", resolvedSchema.schema)
  }

  private def buildOnceAllPageableBodySchema: Schema[_] = {
    val baseSchema = SwaggerSchemaFactory.newFlattenSchemaByClass(classOf[WithSignatureEventModel])
    val onceAllPageSchema = SwaggerSchemaFactory.newFlattenSchemaByClass(classOf[OnceAllPageableBusinessEventData])
    //将data替换为业务model schema
    onceAllPageSchema.addProperty("data", resolvedSchema.schema)
    baseSchema.addProperty("data", onceAllPageSchema)
  }

  private def buildTruncatePageBodySchema: Schema[_] = {
    val baseSchema = SwaggerSchemaFactory.newFlattenSchemaByClass(classOf[WithSignatureEventModel])
    val truncateSchema = SwaggerSchemaFactory.newFlattenSchemaByClass(classOf[TruncatedPageableBusinessEventData])
    //将data替换为业务model schema
    truncateSchema.addProperty("data", resolvedSchema.schema)
    baseSchema.addProperty("data", truncateSchema)
  }

  private def buildParameters: List[Parameter] = {
    List(
      new Parameter()
        .name("x-openapi-app-id")
        .description("the application registered for business.")
        .example("a_138a6425b838476ebd0ddd7ea42b0769")
        .in("header")
        .required(true)
        .schema(new StringSchema()),
      new Parameter()
        .name("x-openapi-channel")
        .description("which channel the message is related to.")
        .example("x-hyper")
        .in("header")
        .required(true)
        .schema(new StringSchema()),
      new Parameter()
        .name("x-openapi-tenant")
        .description("which tenant the message is related to.")
        .example("test")
        .in("header")
        .required(true)
        .schema(new StringSchema()),
    )
  }

  private def buildServers: List[Server] = {
    registries.map(registry => {
      new Server()
        .url(registry.getCallbackUrl)
        .description(registry.getDescription)
    }).toList
  }
}
