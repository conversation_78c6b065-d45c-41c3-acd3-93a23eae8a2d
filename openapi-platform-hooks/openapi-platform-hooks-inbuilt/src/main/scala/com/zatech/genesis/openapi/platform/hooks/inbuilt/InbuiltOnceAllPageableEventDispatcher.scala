package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.IHookTriggerAggregation
import com.zatech.genesis.openapi.platform.hooks.api.dispatch.IEventDispatcher
import com.zatech.genesis.openapi.platform.hooks.api.model.{EventHookContext, PageableDataModel}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.slot.EventHookSlotManager
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.pageable.OnceAllPageableBusinessEventData
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/1
 * */
@Component
class InbuiltOnceAllPageableEventDispatcher @Autowired()(
                                                          eventHookSlotManager: EventHookSlotManager
                                                        ) extends IEventDispatcher {

  override def dispatch(hookAggregation: IHookTriggerAggregation, data: BaseBusinessEventData): EventHookContext = {
    val slotChain = eventHookSlotManager.newSlotChain
    val pageableDataModel = fromOnceAllPageableBusinessData(data.asInstanceOf[OnceAllPageableBusinessEventData])
    slotChain.execute(hookAggregation, pageableDataModel)
  }

  private def fromOnceAllPageableBusinessData(data: OnceAllPageableBusinessEventData): PageableDataModel = {
    val pageableData = new PageableDataModel
    pageableData.setData(data.getData)
    //TODO 默认每页20条
    pageableData.setPageSize(data.getPageSizeOpt.orElse(20))
    pageableData.setTotal(data.getTotal)
    pageableData.setBusinessId(data.getBusinessId)
    pageableData.setExtraInfo(data.getExtraInfo)
    pageableData
  }
}
