package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.plugin.api.IWebHookEventDefinitionRegistry
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.BusinessTriggeredEventDefinition
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
/**
 * <AUTHOR>
 * @create 2024/11/13 10:03
 * */
@Component
class EventDefinitionManager @Autowired()(eventDefRegistries: java.util.List[IWebHookEventDefinitionRegistry]) {
  private val eventDefMap: Map[EventName, BusinessTriggeredEventDefinition] = {
    eventDefRegistries
      .flatMap(_.registerBusinessEventDefinitions())
      .map(definition => EventName(definition.getEventCategory.getEvent) -> definition)
      .toMap
  }

  def getEventDefOpt(eventName: EventName): Option[BusinessTriggeredEventDefinition] = {
    eventDefMap.get(eventName)
  }

  def getEventDefOrThrow(eventName: EventName): BusinessTriggeredEventDefinition = {
    getEventDefOpt(eventName)
      .getOrElse(throw new IllegalStateException(s"can not find event definition: $eventName"))
  }
}
