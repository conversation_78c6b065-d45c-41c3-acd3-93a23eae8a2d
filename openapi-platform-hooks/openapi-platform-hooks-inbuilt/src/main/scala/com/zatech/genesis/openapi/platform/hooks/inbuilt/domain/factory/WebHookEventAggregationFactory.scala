package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory

import com.zatech.genesis.openapi.platform.common.EnvironmentSupport
import com.zatech.genesis.openapi.platform.common.iddef.{NoTenant, TenantCode}
import com.zatech.genesis.openapi.platform.domain.exception.DomainObjectNotFoundException
import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.WebHookEventAggregation
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory.WebHookEventAggregationFactory.EventAggregationMap
import com.zatech.genesis.openapi.platform.hooks.inbuilt.{EventDefinitionManager, EventExampleManager}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.EventCategory
import com.zatech.genesis.openapi.platform.infra.application.service.IWebHookEventSubscriptionService
import com.zatech.genesis.openapi.platform.knowledge.procedure.api.IProcedureManager
import com.zatech.genesis.portal.toolbox.share.DomainFactory
import com.zatech.genesis.portal.toolbox.util.TraceSupport
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @create 2024/11/12 15:45
 * */
@Component
class WebHookEventAggregationFactory @Autowired()(
                                               procedureManager: IProcedureManager,
                                               webHookEventSubscriptionService: IWebHookEventSubscriptionService,
                                               webHookRegistryAggregationFactory: WebHookRegistryAggregationFactory,
                                               eventExampleManager: EventExampleManager,
                                               eventDefinitionManager: EventDefinitionManager,
                                               traceSupport: TraceSupport,
                                               environmentSupport: EnvironmentSupport
                                               ) extends DomainFactory[EventName, WebHookEventAggregation] {

  private val eventAggregationMap = new EventAggregationMap

  initSystemDefinition()
  initTenantsDefinition()

  private def initSystemDefinition(): Unit = {
    val allProcedureMetas = procedureManager.listAllAvailableProcedureMetaHolders(traceSupport.getTenantOrNull)
    val domains = buildDomains(allProcedureMetas.flatMap(_.eventCategories))
    eventAggregationMap.put(NoTenant, domains)
  }

  private def initTenantsDefinition(): Unit = {
    environmentSupport
      .allTenants
      .foreach(tenant => initTenantDomains(TenantCode(tenant)))
  }

  private def initTenantDomains(tenantCode: TenantCode): Unit = {
    def initTenantDefinitions(tenantCode: TenantCode) = {
      buildDomains(
        procedureManager
          .listAllAvailablePluginProcedureMetaHolders(tenantCode.value)
          .flatMap(_.eventCategories))
    }
    eventAggregationMap.computeIfAbsent(tenantCode, _ => initTenantDefinitions(tenantCode))
  }

  override def getDomain(id: EventName): WebHookEventAggregation = {
    getDomainOpt(id).getOrElse(throw new DomainObjectNotFoundException(classOf[WebHookEventAggregation], id))
  }

  def getDomainOpt(id: EventName): Option[WebHookEventAggregation] = {
    val tenantCode = TenantCode(traceSupport.getTenantOrNull)
    eventAggregationMap.getDomainOpt(tenantCode, id)
  }

  override def deleteDomain(id: EventName): Unit = ???

  def allDomains: Set[WebHookEventAggregation] = {
    val tenantCode = TenantCode(traceSupport.getTenantOrNull)
    eventAggregationMap.allDomains(tenantCode)
  }

  private def buildDomains(eventCategories: Set[EventCategory]): Map[EventName, WebHookEventAggregation] = {
    eventCategories.map(event => EventName(event.getEvent) -> newDomain(event)).toMap
  }

  private def newDomain(eventCategory: EventCategory): WebHookEventAggregation = {
    new WebHookEventAggregation(
      webHookEventSubscriptionService = webHookEventSubscriptionService,
      webHookRegistryAggregationFactory = webHookRegistryAggregationFactory,
      eventExampleManager = eventExampleManager,
      eventDefinitionManager = eventDefinitionManager,
      eventCategory = eventCategory
    )
  }
}

object WebHookEventAggregationFactory {

  private class EventAggregationMap extends ConcurrentHashMap[TenantCode, Map[EventName, WebHookEventAggregation]] {
    /**
     * 指定租户和没有租户的都能匹配，因为没有租户的是全局生效的
     */
    def getDomainOpt(tenantCode: TenantCode, eventName: EventName): Option[WebHookEventAggregation] = {
      if(tenantCode.isEmpty) return Option(get(NoTenant)).flatMap(_.get(eventName))
      Option(get(tenantCode)).flatMap(_.get(eventName)).orElse(getDomainOpt(NoTenant, eventName))
    }

    def allDomains(tenantCode: TenantCode): Set[WebHookEventAggregation] = {
      val systemDomains = get(NoTenant).values.toSet
      if(tenantCode.isEmpty) systemDomains
      else systemDomains ++ Option(get(tenantCode)).map(_.values).getOrElse(Seq.empty)
    }
  }
}
