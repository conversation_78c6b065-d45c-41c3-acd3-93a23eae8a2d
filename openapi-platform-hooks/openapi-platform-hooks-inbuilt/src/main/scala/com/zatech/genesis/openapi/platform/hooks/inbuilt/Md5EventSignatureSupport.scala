package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.IEventSignatureSupport
import com.zatech.genesis.openapi.platform.hooks.api.model.{EventHookContext, WithSignatureEventModel}
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import org.springframework.stereotype.Component
import org.springframework.util.DigestUtils

import java.util

/**
 * <AUTHOR>
 * @Date 2022/6/7
 * */
@Component
class Md5EventSignatureSupport extends IEventSignatureSupport with Loggable {

  override def signature(context: EventHookContext, eventModel: WithSignatureEventModel): Unit = {
    //先清空signature
    eventModel.setSignature(null)

    //Tricky, 为了加签，先把之前的数据保持下来，然后塞回去
    val oldData = eventModel.getData
    eventModel.setData(context.getData)

    val treeMap = new util.TreeMap[String, Object]((o1: String, o2: String) => o1.compareTo(o2))
    val jsonMap = JsonParser.fromObjToJsonMap(eventModel)
    treeMap.putAll(jsonMap)

    eventModel.setData(oldData)

    if (log.isDebugEnabled) {
      log.debug("Sorted event model to signature: {}", JsonParser.toJsonString(treeMap))
    }

    val strToSignature =
      treeMap.entrySet().stream().map[String](e => e.getKey() + "=" + e.getValue()).reduce((a, b) => a + "&" + b).get()

    if (log.isDebugEnabled) {
      log.debug("Data string to signature: {}", strToSignature)
    }

    val md5 = DigestUtils.md5DigestAsHex(strToSignature.getBytes("utf-8"));

    if (log.isDebugEnabled) {
      log.debug("Data string to signature: {}, md5: {}", strToSignature, md5)
    }

    eventModel.setSignature(md5)
  }
}
