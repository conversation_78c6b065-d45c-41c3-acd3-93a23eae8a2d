package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.IHookTriggerAggregation
import com.zatech.genesis.openapi.platform.hooks.api.dispatch.IEventDispatcher
import com.zatech.genesis.openapi.platform.hooks.api.model.{EventHookContext, SingleDataModel}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.slot.EventHookSlotManager
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.{BaseBusinessEventData, SingleBusinessEventData}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/1
 * */
@Component
class InbuiltSingleDataEventDispatcher @Autowired()(
                                                     eventHookSlotManager: EventHookSlotManager
                                                   ) extends IEventDispatcher {

  override def dispatch(hookAggregation: IHookTriggerAggregation, data: BaseBusinessEventData): EventHookContext = {
    val slotChain = eventHookSlotManager.newSlotChain
    val singleData = fromSingleBusinessData(data.asInstanceOf[SingleBusinessEventData])
    slotChain.execute(hookAggregation, singleData)
  }

  private def fromSingleBusinessData(data: SingleBusinessEventData): SingleDataModel = {
    val result = new SingleDataModel
    result.setData(data.getData)
    result.setBusinessId(data.getBusinessId)
    result.setExtraInfo(data.getExtraInfo)
    result;
  }
}
