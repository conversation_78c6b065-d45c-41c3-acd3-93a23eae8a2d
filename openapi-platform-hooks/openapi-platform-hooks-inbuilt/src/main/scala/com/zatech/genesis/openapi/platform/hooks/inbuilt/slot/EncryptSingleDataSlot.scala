package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.IEventHookSlot
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, SingleDataModel, WithEncryptedDataEventModel}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.octopus.module.cryptogram.core.CryptogramEnum
import com.zatech.octopus.module.cryptogram.core.factory.CryptogramFactory
import com.zatech.octopus.module.cryptogram.core.model.EncryptRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/2
 *       数据加密slot
 * */
@Component
class EncryptSingleDataSlot @Autowired()(
                                          cryptogramFactory: CryptogramFactory
                                        ) extends IEventHookSlot with Loggable {

  override def getOrder: Int = IEventHookSlot.ZERO_ORDER + 10 * IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    businessData.isInstanceOf[SingleDataModel] && eventModel.isInstanceOf[WithEncryptedDataEventModel]
  }

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    // 加密过程
    val aes = cryptogramFactory.getCryptogram(CryptogramEnum.AES_GCM);
    val encryptRequest = new EncryptRequest()
    val singleData = businessData.asInstanceOf[SingleDataModel]

    val key = context.getRegistryAggregation.getKey

    encryptRequest.setKey(key)
    val data = singleData.getData.toJsonString
    encryptRequest.setPlainText(data)
    //偏移量，类似加盐，去密钥前16个字符
    encryptRequest.setIv(key.substring(0, 16))
    //Tricky, 为了后续加签能够得到原始数据
    context.addData(data)
    val encryptResult = aes.encrypt(encryptRequest)
    eventModel.asInstanceOf[WithEncryptedDataEventModel].setData(encryptResult.getCipheredText)
    true
  }
}
