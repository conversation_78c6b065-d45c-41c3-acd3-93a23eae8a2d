package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext}
import com.zatech.genesis.openapi.platform.hooks.api.{IEventHookSlot, UrlValidator}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2023/2/14 17:57
 */

@Component
class ValidateUrlSlot @Autowired()(
                                    urlValidator: UrlValidator
                                  ) extends IEventHookSlot with Loggable {
  /**
   * 处理顺序，越小越靠前
   *
   * @return
   */
  override def getOrder: Int = IEventHookSlot.ZERO_ORDER - IEventHookSlot.JUMP_SIZE

  /**
   * 是否执行slot
   *
   * @return
   */
  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData,
                             eventModel: BaseEventModel): Boolean = true

  /**
   * 处理
   *
   * @return 如果是false则停止于该slot, 如果为true则继续执行其他slot
   */
  override def execute(context: EventHookContext,
                       businessData: BaseBusinessEventData,
                       eventModel: BaseEventModel): Boolean = {
    val url = context.getRegistryAggregation.getCallbackUrl
    urlValidator.validate(url)
    true
  }
}
