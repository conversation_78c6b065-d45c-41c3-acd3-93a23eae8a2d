package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookBusinessEventTriggerRecord

/**
 * <AUTHOR>
 * @create 2025/1/2 16:48
 * */
package object storage {

  implicit class RichTriggerRecord(record: WebHookBusinessEventTriggerRecord) {
    def getRawResponse: String = {
      if (!record.getFinishFlag || record.getOutput == null) return null
      if (record.getSuccessFlag) return SucceedOutput.from(record.getOutput).response
      if (record.getOutput.startsWith("{\"response\"")) {
        FailedOutput.from(record.getOutput).response
      } else ErrorOutput.from(record.getOutput).errorMsg
    }
  }
}
