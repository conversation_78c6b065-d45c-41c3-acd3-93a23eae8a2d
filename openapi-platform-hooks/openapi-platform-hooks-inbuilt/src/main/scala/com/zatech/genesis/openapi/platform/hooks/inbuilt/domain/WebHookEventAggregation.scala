package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.openapi.platform.api.resource.developercenter.mgmt.webhook.event.response.{WebHookEventDetail, WebHookEventSummary}
import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory.WebHookRegistryAggregationFactory
import com.zatech.genesis.openapi.platform.hooks.inbuilt.{EventDefinitionManager, EventExampleManager}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.{BusinessTriggeredEventDefinition, EventCategory}
import com.zatech.genesis.openapi.platform.infra.application.service.IWebHookEventSubscriptionService
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.openapi.platform.share.enums.ZATechDomainEnum
import com.zatech.genesis.portal.toolbox.share.DomainObject
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.genesis.portal.toolbox.util.jsonschema.SwaggerJsonSchemaHelper

/**
 * <AUTHOR>
 * @create 2024/11/12 15:44
 * */
class WebHookEventAggregation(webHookEventSubscriptionService: IWebHookEventSubscriptionService,
                              webHookRegistryAggregationFactory: WebHookRegistryAggregationFactory,
                              eventExampleManager: EventExampleManager,
                              eventDefinitionManager: EventDefinitionManager,
                              val eventCategory: EventCategory) extends DomainObject[EventName] with IOpenApiLike {

  override val id: EventName = EventName(eventCategory.getEvent)
  val version: String = eventCategory.getVersion
  val domain: ZATechDomainEnum = eventCategory.getDomainEnum
  val description: String = eventCategory.getDescription
  val examplesOpt: Option[EventExampleManager.WebHookExamples] = eventExampleManager.getExamplesOpt(id)
  val definition: BusinessTriggeredEventDefinition = eventDefinitionManager.getEventDefOrThrow(id)
  val subscriptions: String = definition.getSubscriptionsStr

  def registries: Set[WebHookRegistryAggregation] = {
    webHookEventSubscriptionService
      .listEndpointsByEvent(List(id.value), TraceSupport.getAppIdOrThrow)
      .map(webHookRegistryAggregationFactory.newDomain)
      .toSet
  }

  def toSummary: WebHookEventSummary = {
    val summary = new WebHookEventSummary
    summary
      .setName(id.value)
      .setDomain(domain.getCode)
      .setVersion(version)
      .setDescription(eventCategory.getDescription)
  }

  def toDetail: WebHookEventDetail = {
    val detail = new WebHookEventDetail
    detail
      .setName(id.value)
      .setDomain(domain.getCode)
      .setVersion(version)
      .setDescription(eventCategory.getDescription)
    detail
      .setSchema(SwaggerJsonSchemaHelper.fromOpenApiToJsonMap(eventOpenApi))
      .setEndpointsSubscribed(registries.map(_.toSummary).toList)
  }

  def subscribeEndpoints(registryIds: Set[Long]): Unit = {
    val currentRegistryIds = registries.map(_.getId)
    webHookEventSubscriptionService.unsubscribeEvents(currentRegistryIds.toSeq, List(id.value))
    registryIds.map(webHookRegistryAggregationFactory.getDomain).foreach(registry => {
      registry.subscribeEvents(Set(id))
    })
  }

  def unsubscribeEndpoints(registryIds: Set[Long]): Unit = {
    registryIds.foreach(id => webHookEventSubscriptionService.deleteByRegistryId(id))
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case other: WebHookEventAggregation => eventCategory.equals(other.eventCategory)
      case _ => false
    }
  }

  override def hashCode(): Int = eventCategory.hashCode()
}
