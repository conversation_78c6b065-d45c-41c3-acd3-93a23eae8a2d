package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.dispatch.{IEventDispatcher, IEventDispatcherFactory}
import com.zatech.genesis.openapi.platform.hooks.api.enums.BusinessEventDataTypeEnum
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/1
 * */
@Component
class InbuiltEventDispatcherFactory @Autowired()(
                                                  applicationContext: ApplicationContext
                                                ) extends IEventDispatcherFactory {

  override def getDispatcher(typeEnum: BusinessEventDataTypeEnum): IEventDispatcher = {
    typeEnum match {
      case BusinessEventDataTypeEnum.single => applicationContext.getBean(classOf[InbuiltSingleDataEventDispatcher])
      case BusinessEventDataTypeEnum.onceAllPageable => applicationContext.getBean(classOf[InbuiltOnceAllPageableEventDispatcher])
    }
  }
}
