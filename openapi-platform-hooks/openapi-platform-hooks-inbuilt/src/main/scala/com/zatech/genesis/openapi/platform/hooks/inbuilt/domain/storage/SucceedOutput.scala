package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.storage

import com.fasterxml.jackson.annotation.JsonProperty
import com.zatech.genesis.openapi.platform.share.json.{JsonParser, Jsonable}
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser

import scala.beans.BeanProperty

/**
 * <AUTHOR>
 * @create 2025/1/2 16:31
 * */
class SucceedOutput(@JsonProperty("response")
                    @BeanProperty
                    val response: String) extends Jsonable {
  override def toJsonString: String = JsonParser.toJsonString(this)
}

object SucceedOutput {
  def from(output: String): SucceedOutput =
    StaticJsonParser.fromJsonString(output, classOf[SucceedOutput])
}