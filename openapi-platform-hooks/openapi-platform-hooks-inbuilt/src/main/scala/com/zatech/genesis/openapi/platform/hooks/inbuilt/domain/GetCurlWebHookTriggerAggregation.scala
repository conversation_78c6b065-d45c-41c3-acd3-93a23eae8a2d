package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.portal.toolbox.share.Loggable

/**
 * <AUTHOR>
 * @create 2024/12/30 11:24
 * 不会真的调用，用于获取curl
 * */
class GetCurlWebHookTriggerAggregation(eventDefinition: WebHookEventAggregation,
                                       registry: WebHookRegistryAggregation)
  extends MockWebHookTriggerAggregation(eventDefinition, registry) with Loggable {

}
