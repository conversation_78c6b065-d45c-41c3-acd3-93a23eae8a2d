package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.common.storage.{RequestContext, ResponseContext}
import com.zatech.genesis.openapi.platform.share.json.JsonMap
import org.springframework.http.{HttpEntity, ResponseEntity}

/**
 * <AUTHOR>
 * @create 2024/12/31 14:39
 * */
package object domain {

  implicit class RichRequestContext(request: HttpEntity[String]) {
    def toRequestContext: RequestContext = {
      val headers = new JsonMap()
      request.getHeaders.toSingleValueMap.forEach((k, v) => headers.add(k, v))
      new RequestContext(headers, null)
    }
  }

  implicit class RichResponseContext(response: ResponseEntity[String]) {
    def toResponseContext: ResponseContext = {
      val headers = new JsonMap()
      response.getHeaders.toSingleValueMap.forEach((k, v) => headers.add(k, v))
      new ResponseContext(headers, response.getStatusCode.value())
    }
  }
}
