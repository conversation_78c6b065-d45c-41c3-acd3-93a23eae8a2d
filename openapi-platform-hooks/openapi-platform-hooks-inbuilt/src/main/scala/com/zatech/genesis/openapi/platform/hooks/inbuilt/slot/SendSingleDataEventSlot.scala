package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, SingleDataModel, WithSignatureEventModel}
import com.zatech.genesis.openapi.platform.hooks.api.{IEventHookSlot, IEventSignatureSupport, IHookRegistryAggregation}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.security.api.application.IApplicationFactory
import com.zatech.genesis.openapi.platform.share.{Loggable, TimeHelper}
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

import java.util.concurrent.ThreadLocalRandom

/**
 * <AUTHOR>
 * @Date 2022/6/7
 * */
@Component
class SendSingleDataEventSlot @Autowired()(
                                            @Qualifier("webHookSender") restTemplate: RestTemplate,
                                            signatureSupport: IEventSignatureSupport,
                                            applicationFactory: IApplicationFactory
                                          ) extends IEventHookSlot with Loggable with Javable {

  //TODO 先默认写死重试次数上限
  private val MAX_RETRY_TIMES = 3

  override def getOrder: Int = IEventHookSlot.MAX_ORDER - IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    businessData.isInstanceOf[SingleDataModel] && eventModel.isInstanceOf[WithSignatureEventModel]
  }

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    val signatureEventModel = eventModel.asInstanceOf[WithSignatureEventModel]
    val triggerAggregation = context.getTriggerAggregation
    val registryAggregation = context.getRegistryAggregation

    val webHookSender = new WebHookSender(context, eventModel, restTemplate)

    def retrySendData(registry: IHookRegistryAggregation, retryTimes: Int): Boolean = {
      if (retryTimes >= MAX_RETRY_TIMES) return false
      //因为每次重试signature都不一样，所以在这里生成signature
      signatureEventModel.setRetryTimes(retryTimes)
      signatureEventModel.setTimestamp(TimeHelper.getNowWithZone)
      signatureSupport.signature(context, signatureEventModel)
      val bodyString = signatureEventModel.toJsonString
      val isSuccess = webHookSender.send(bodyString, retryTimes)
      //梯度增加时长+随机数
      if(! isSuccess) {
        Thread.sleep(retryTimes * 2000 + getRandomInterval)
        retrySendData(registry, retryTimes + 1)
      }
      isSuccess
    }

    val finalSuccessful = retrySendData(registryAggregation, 0)
    if (!finalSuccessful) {
      triggerAggregation.onCallbackFinalFailed(registryAggregation)
    }

    //不继续进行后续操作了
    false
  }

  private def getRandomInterval: Int = ThreadLocalRandom.current.nextInt(500, 2000)
}
