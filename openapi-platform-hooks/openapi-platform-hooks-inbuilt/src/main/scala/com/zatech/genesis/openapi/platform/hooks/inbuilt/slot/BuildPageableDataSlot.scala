package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.IEventHookSlot
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, PageableDataModel}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.infra.IdUtil
import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/2
 *       构建分页数据slot
 * */
@Component
class BuildPageableDataSlot extends IEventHookSlot with Loggable {

  override def getOrder: Int = IEventHookSlot.ZERO_ORDER + 5 * IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    businessData.isInstanceOf[PageableDataModel]
  }

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {

    if (log.isDebugEnabled) {
      log.debug(s"Begin execute build page data event hook slot")
    }

    val pageableDataModel = businessData.asInstanceOf[PageableDataModel]
    //如果已经到达分页上限，则推出chain
    if (pageableDataModel.getPageSize * pageableDataModel.getPageIndex >= pageableDataModel.getTotal) {
      if (log.isDebugEnabled) {
        log.debug("Reach the last page, break slots chain")
      }
      return false
    }
    pageableDataModel.setPageIndex(pageableDataModel.getPageIndex + 1)

    val pageId = IdUtil.newHookPageId()
    pageableDataModel.setPageId(pageId)

    if (log.isDebugEnabled) {
      log.debug(s"End execute add page id event hook slot, new pageId: ${pageableDataModel.getPageId}")
    }

    true
  }
}
