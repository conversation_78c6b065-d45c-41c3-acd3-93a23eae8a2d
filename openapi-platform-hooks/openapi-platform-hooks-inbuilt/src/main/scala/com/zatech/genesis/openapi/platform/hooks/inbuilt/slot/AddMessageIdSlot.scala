package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.IEventHookSlot
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.infra.IdUtil
import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/2
 *       添加消息唯一id
 * */
@Component
class AddMessageIdSlot extends IEventHookSlot with Loggable {

  override def getOrder: Int = IEventHookSlot.ZERO_ORDER

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    //之所以有不为null的情况是因为分页推送可能会多次执行该slot
    eventModel.getMessageId == null
  }

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    if (log.isDebugEnabled) {
      log.debug(s"Begin execute add message id event hook slot")
    }
    val msgId = IdUtil.newHookMessageId()
    eventModel.setMessageId(msgId)
    context.setMessageId(msgId)
    if (log.isDebugEnabled) {
      log.debug(s"End execute add message id event hook slot, new messageId: ${eventModel.getMessageId}")
    }
    true
  }
}
