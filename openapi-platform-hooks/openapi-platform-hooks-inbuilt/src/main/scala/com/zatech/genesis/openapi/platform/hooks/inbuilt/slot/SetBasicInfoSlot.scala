package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.IEventHookSlot
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, PageableDataModel}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import org.springframework.stereotype.Component
/**
 * <AUTHOR>
 * @Date 2022/6/7
 *       添加基本信息
 * */
@Component
class SetBasicInfoSlot extends IEventHookSlot {

  override def getOrder: Int = IEventHookSlot.ZERO_ORDER + IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = true

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {

    val triggerAggregation = context.getTriggerAggregation
    /**
     * 因为可以订阅多个套件，所以还是选取第一个符合要求的。
     * @return
     */
    def getSubscription: String =
      triggerAggregation.getSubscriptions
        .intersect(context.getEventDefinition.getSubscriptions)
        .headOption.orNull

    eventModel.setEvent(context.getEventDefinition.getEventCategory.getEvent)
    eventModel.setSubscription(getSubscription)
    eventModel.setVersion(context.getEventDefinition.getEventCategory.getVersion)
    eventModel.setPagination(businessData.isInstanceOf[PageableDataModel])
    eventModel.setBizNo(triggerAggregation.getBizNo.orElse(null))
    eventModel.setExtraInfo(JsonParser.fromJsonToJsonMap(triggerAggregation.getExtraInfoPlainString.orElse(null)))
    true
  }
}
