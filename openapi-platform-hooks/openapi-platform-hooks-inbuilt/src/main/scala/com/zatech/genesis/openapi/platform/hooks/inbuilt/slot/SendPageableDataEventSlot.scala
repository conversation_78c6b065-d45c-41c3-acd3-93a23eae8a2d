package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.IEventHookSlot
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, PageableDataModel}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/2
 * */
@Component
class SendPageableDataEventSlot extends IEventHookSlot {

  override def getOrder: Int = IEventHookSlot.MAX_ORDER - IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    businessData.isInstanceOf[PageableDataModel]
  }

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = ???
}
