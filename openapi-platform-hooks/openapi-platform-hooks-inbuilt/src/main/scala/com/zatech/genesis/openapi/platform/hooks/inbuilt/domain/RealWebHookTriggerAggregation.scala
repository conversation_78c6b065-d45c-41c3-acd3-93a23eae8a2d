package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.openapi.platform.domain.DomainObject
import com.zatech.genesis.openapi.platform.hooks.api.{IHookRegistryAggregation, IHookTriggerAggregation, WebHookBusinessEventId}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory.WebHookRegistryAggregationFactory
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.storage.{ErrorOutput, FailedOutput, SucceedOutput}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.EventCategory
import com.zatech.genesis.openapi.platform.infra.application.entity.{WebHookBusinessEventBound, WebHookBusinessEventTriggerRecord}
import com.zatech.genesis.openapi.platform.infra.application.model.WebHookBusinessEventModel
import com.zatech.genesis.openapi.platform.infra.application.service.{IWebHookBusinessEventBoundService, IWebHookBusinessEventReceivedService, IWebHookBusinessEventTriggerRecordService}
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import io.netty.handler.codec.smtp.SmtpRequests.data
import org.springframework.http.{HttpEntity, ResponseEntity}

import java.util.{Date, Optional}
import java.{lang, util}
/**
 * <AUTHOR>
 * @Date 2022/5/30
 * */
class RealWebHookTriggerAggregation(
                                     id: WebHookBusinessEventId,
                                     webHookBusinessEventBound: WebHookBusinessEventBound,
                                     webHookBusinessEventReceivedService:IWebHookBusinessEventReceivedService,
                                     webHookBusinessEventBoundService: IWebHookBusinessEventBoundService,
                                     webHookBusinessEventTriggerRecordService: IWebHookBusinessEventTriggerRecordService,
                                     webHookRegistryAggregationFactory: WebHookRegistryAggregationFactory
                                   ) extends DomainObject[WebHookBusinessEventId] with IHookTriggerAggregation with Loggable {

  lazy val registry: WebHookRegistryAggregation = webHookRegistryAggregationFactory.getDomain(registryId)

  val isTriggered: lang.Boolean = webHookBusinessEventBound.getTriggeredFlag
  val subscriptions: Array[String] = webHookBusinessEventBound.getSubscriptions.split(",")

  private val bizNoOpt = Option(webHookBusinessEventBound.getBizNo).map(_.trim).filter(_.nonEmpty)

  private val extraInfoOpt = Option(webHookBusinessEventBound.getExtraInfo).map(_.trim).filter(_.nonEmpty)

  val registryId: Long = webHookBusinessEventBound.getWebHookRegistryId.toLong

  private var businessEventReceivedIdOpt: Option[Long] = None

  override def getId: WebHookBusinessEventId = id

  /**
   * 发现了有业务事件接收到
   *
   * @param id
   */
  def businessEventReceived(id: Long): Unit = businessEventReceivedIdOpt = Some(id)

  private def triggerEventFinalFailed(): Unit = {
    val bound = new WebHookBusinessEventBound
    bound.setId(webHookBusinessEventBound.getId)
    bound.setFinishFlag(true)
    bound.setSuccessFlag(false)
    bound.setGmtEnded(new Date())
    webHookBusinessEventBoundService.updateById(bound)
  }

  override def getSubscriptions: util.List[String] = subscriptions.toList

  override def getBizNo: Optional[String] = bizNoOpt

  override def getExtraInfoPlainString: Optional[String] = extraInfoOpt

  override def getEvent: EventCategory = id.event

  override def getRegistry: IHookRegistryAggregation = registry

  override def onCallbackStarted(registry: IHookRegistryAggregation,
                                 retryTimes: Int, messageId: String,
                                 request: HttpEntity[String]): Long = {
    log.info("Begin call back, url: {}, data: {}", registry.getCallbackUrl, data)
    businessEventReceivedIdOpt.map(id => {
      val record = new WebHookBusinessEventTriggerRecord
      record.setBusinessEventReceivedId(id)
      record.setBusinessEventBoundId(webHookBusinessEventBound.getId)
      record.setMessageId(messageId)
      record.setFinishFlag(false)
      record.setWebHookRegistryId(registry.getId.toString)
      record.setRequestContext(request.toRequestContext.toJson)
      record.setInput(request.getBody)
      record.setRetryTimes(retryTimes)
      record.setGmtStarted(new Date())
      webHookBusinessEventTriggerRecordService.save(record)
      record.getId
    }).get
  }

  override def onCallbackSucceed(registry: IHookRegistryAggregation,
                                 callbackId: Long, retryTimes: Int,
                                 request: HttpEntity[String], response: ResponseEntity[String]): Unit = {
    log.info("Call back: {} succeed, url: {}, retryTimes: {}",
      callbackId, registry.getCallbackUrl, retryTimes)
    //两个应该不需要事务
    val record = new WebHookBusinessEventTriggerRecord
    record.setId(callbackId)
    record.setResponseContext(response.toResponseContext.toJson)
    record.setOutput(new SucceedOutput(response.getBody).toJsonString)
    record.setFinishFlag(true)
    record.setSuccessFlag(true)
    record.setGmtEnded(new Date())
    webHookBusinessEventTriggerRecordService.updateById(record)

    val bound = new WebHookBusinessEventBound
    bound.setId(webHookBusinessEventBound.getId)
    bound.setFinishFlag(true)
    bound.setSuccessFlag(true)
    bound.setGmtEnded(new Date())
    webHookBusinessEventBoundService.updateById(bound)
  }

  override def onCallbackFailed(registry: IHookRegistryAggregation,
                                callbackId: Long, retryTimes: Int,
                                request: HttpEntity[String], response: ResponseEntity[String]): Unit = {
    val responseBody = response.getBody
    log.error(s"Call back: {} failed, url: {}, retryTimes: {}, response: [${response.getStatusCode.value()}]{}",
      callbackId, registry.getCallbackUrl, retryTimes, responseBody)
    val record = new WebHookBusinessEventTriggerRecord
    record.setId(callbackId)
    record.setResponseContext(response.toResponseContext.toJson)
    record.setOutput(new FailedOutput(response.getBody).toJsonString)
    record.setFinishFlag(true)
    record.setSuccessFlag(false)
    record.setGmtEnded(new Date())
    webHookBusinessEventTriggerRecordService.updateById(record)
  }

  override def onCallbackError(registry: IHookRegistryAggregation,
                               callbackId: Long, retryTimes: Int, ex: Exception): Unit = {
    log.error("Call back: {} error, url: {}, retryTimes: {}",
      callbackId, registry.getCallbackUrl, retryTimes, ex)
    val record = new WebHookBusinessEventTriggerRecord
    record.setId(callbackId)
    record.setOutput(new ErrorOutput(ex.getMessage).toJsonString)
    record.setFinishFlag(true)
    record.setSuccessFlag(false)
    record.setGmtEnded(new Date())
    webHookBusinessEventTriggerRecordService.updateById(record)
  }

  override def onCallbackFinalFailed(registry: IHookRegistryAggregation): Unit = {
    log.error("Call back final failed, url: {}", registry.getCallbackUrl)
    triggerEventFinalFailed()
  }

  override def onEventTriggeredError(eventModel: WebHookBusinessEventModel, ex: Exception): Unit = {
    businessEventReceivedIdOpt.map(id => {
      val record = new WebHookBusinessEventTriggerRecord
      record.setBusinessEventReceivedId(id)
      record.setBusinessEventBoundId(webHookBusinessEventBound.getId)
      record.setMessageId(eventModel.getMessageId)
      record.setFinishFlag(true)
      record.setSuccessFlag(false)
      record.setWebHookRegistryId(registryId.toString)
      record.setInput(eventModel.getEventData)
      record.setOutput(new ErrorOutput(ex.getMessage).toJsonString)
      record.setGmtStarted(new Date())
      record.setGmtEnded(new Date())
      webHookBusinessEventTriggerRecordService.save(record)
      record.getId
    })
    //bound表也需要更新
    val bound = new WebHookBusinessEventBound
    bound.setId(webHookBusinessEventBound.getId)
    bound.setFinishFlag(true)
    bound.setSuccessFlag(false)
    bound.setGmtEnded(new Date())
    webHookBusinessEventBoundService.updateById(bound)
  }

  /**
   * 获取调用记录
   *
   * @return
   */
  override def getRecords: util.List[WebHookBusinessEventTriggerRecord] = {
    businessEventReceivedIdOpt
      .map(webHookBusinessEventTriggerRecordService.listByBusinessEventReceivedId(_))
      .getOrElse(util.List.of())
  }

  override def tryLockTriggerEvent(): Boolean =
    webHookBusinessEventBoundService.tryUpdateTriggerFlagByCAS(webHookBusinessEventBound.getId)

  override def tryLockBusinessEventReceived(): Boolean =
    webHookBusinessEventReceivedService.tryUpdateTriggerFlagByCAS(businessEventReceivedIdOpt.get)
}