package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.openapi.platform.api.resource.developercenter.mgmt.webhook.event.response.WebHookTryItResponse
import com.zatech.genesis.openapi.platform.common.storage.ResponseContext
import com.zatech.genesis.openapi.platform.hooks.api.exception.WebHookErrorCodes
import com.zatech.genesis.openapi.platform.hooks.api.{IHookRegistryAggregation, IHookTriggerAggregation}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.storage.{ErrorOutput, FailedOutput, RichTriggerRecord, SucceedOutput}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.EventCategory
import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookBusinessEventTriggerRecord
import com.zatech.genesis.openapi.platform.infra.application.model.WebHookBusinessEventModel
import com.zatech.genesis.portal.toolbox.exception.CommonException
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.genesis.portal.toolbox.share.Loggable
import org.springframework.http.{HttpEntity, ResponseEntity}

import java.util
import java.util.concurrent.ConcurrentHashMap
import java.util.{Date, Random}

/**
 * <AUTHOR>
 * @create 2022/9/13 17:35
 * 模拟调用，但不会记录数据到DB中
 */
class MockWebHookTriggerAggregation(eventDefinition: WebHookEventAggregation,
                                    registry: IHookRegistryAggregation)
  extends IHookTriggerAggregation with Loggable {

  private val mockResults = new ConcurrentHashMap[Long, WebHookBusinessEventTriggerRecord]
  private val random = new Random

  override def getEvent: EventCategory = eventDefinition.eventCategory

  override def getRegistry: IHookRegistryAggregation = registry

  override def getRecords = new util.ArrayList[WebHookBusinessEventTriggerRecord](mockResults.values)

  override def onCallbackStarted(registry: IHookRegistryAggregation,
                                 retryTimes: Int, messageId: String,
                                 request: HttpEntity[String]): Long = {
    val id = System.currentTimeMillis + random.nextInt
    val record = new WebHookBusinessEventTriggerRecord
    record.setId(id)
    record.setBusinessEventReceivedId(0L)
    record.setMessageId(messageId)
    record.setFinishFlag(false)
    record.setWebHookRegistryId(registry.getId.toString)
    record.setRequestContext(request.toRequestContext.toJson)
    record.setInput(request.getBody)
    record.setRetryTimes(retryTimes)
    record.setGmtStarted(new Date)
    mockResults.put(id, record)
    id
  }

  override def onCallbackSucceed(registry: IHookRegistryAggregation,
                                 callbackId: Long, retryTimes: Int,
                                 request: HttpEntity[String], response: ResponseEntity[String]): Unit = {
    val record = mockResults.get(callbackId)
    record.setResponseContext(response.toResponseContext.toJson)
    record.setOutput(new SucceedOutput(response.getBody).toJsonString)
    record.setFinishFlag(true)
    record.setSuccessFlag(true)
    record.setGmtEnded(new Date)
  }

  override def onCallbackFailed(registry: IHookRegistryAggregation,
                                callbackId: Long, retryTimes: Int,
                                request: HttpEntity[String], response: ResponseEntity[String]): Unit = {
    val record = mockResults.get(callbackId)
    record.setResponseContext(response.toResponseContext.toJson)
    record.setOutput(new FailedOutput(response.getBody).toJsonString)
    record.setFinishFlag(true)
    record.setSuccessFlag(false)
    record.setGmtEnded(new Date)
  }

  override def onCallbackFinalFailed(registry: IHookRegistryAggregation): Unit = {
    log.error("Call back final failed.")
  }

  override def onEventTriggeredError(eventModel: WebHookBusinessEventModel, ex: Exception): Unit = {
    throw CommonException.byError(WebHookErrorCodes.trigger_event_error)
  }

  override def onCallbackError(registry: IHookRegistryAggregation,
                               callbackId: Long,
                               retryTimes: Int,
                               ex: Exception): Unit = {
    val record = mockResults.get(callbackId)
    record.setOutput(new ErrorOutput(ex.getMessage).toJsonString)
    record.setFinishFlag(true)
    record.setSuccessFlag(false)
    record.setGmtEnded(new Date)
  }

  override def tryLockTriggerEvent(): Boolean = true

  override def tryLockBusinessEventReceived(): Boolean = true

  def toResponse: WebHookTryItResponse = {
    val finalRecord = getRecords.maxBy(_.getRetryTimes)
    val context = ResponseContext.from(finalRecord.getResponseContext)

    new WebHookTryItResponse()
      .setBody(finalRecord.getRawResponse)
      .setHeaders(context.headers)
      .setHttpStatus(context.httpStatus)
  }
}