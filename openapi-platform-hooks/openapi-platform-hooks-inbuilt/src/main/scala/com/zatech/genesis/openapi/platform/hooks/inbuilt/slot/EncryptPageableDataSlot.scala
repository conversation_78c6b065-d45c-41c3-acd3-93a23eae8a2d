package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.IEventHookSlot
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, PageableDataModel}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/7
 * */
@Component
class EncryptPageableDataSlot extends IEventHookSlot {

  override def getOrder: Int = IEventHookSlot.ZERO_ORDER + 10 * IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    businessData.isInstanceOf[PageableDataModel]
  }

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = ???
}
