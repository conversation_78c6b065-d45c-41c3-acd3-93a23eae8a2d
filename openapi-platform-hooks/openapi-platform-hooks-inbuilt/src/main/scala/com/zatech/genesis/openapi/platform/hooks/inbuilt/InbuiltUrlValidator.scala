package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.hooks.api.UrlValidator
import com.zatech.genesis.openapi.platform.share.UrlMatcher
import com.zatech.genesis.portal.toolbox.share.Loggable
import org.springframework.stereotype.Component

import java.net.{InetAddress, URL}
import java.util
import java.util.function.Predicate
import java.util.regex.Pattern
import scala.annotation.tailrec

/**
 * <AUTHOR>
 * @create 2025/1/3 09:51
 * */
@Component
class InbuiltUrlValidator(props: OpenApiProps) extends UrlValidator with Loggable {

  private val permittedUrls: util.Set[String] =
    Option(props.getCallback.getPermitted.getUrls).getOrElse(util.Set.of())
  private val forbiddenIps: util.Set[String] =
    Option(props.getCallback.getForbidden.getIps).getOrElse(util.Set.of())

  private val rules = Array[Predicate[URL]](
    url => "https".equals(url.getProtocol) || UrlMatcher.`match`(url, permittedUrls),
    url => {
      if (UrlMatcher.`match`(url, permittedUrls)) true
      else {
        val allByName = InetAddress.getAllByName(url.getHost)
        allByName.forall(address =>
          !(internalIp(address.getHostAddress) && forbiddenIps.contains(address.getHostAddress)))
      }
    }
  )

  private def internalIp(ip: String) = {
    val reg = Pattern.compile(
      "^(127\\.0\\.0\\.1)|(localhost)|(10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|(172\\.((1[6-9])|(2\\d)|(3[01]))\\" +
      ".\\d{1,3}\\.\\d{1,3})|(192\\.168\\.\\d{1,3}\\.\\d{1,3})$")
    val `match` = reg.matcher(ip)
    `match`.find
  }

  override def validate(urlStr: String): Boolean = {
    @tailrec
    def validate(url: URL, rules: Array[Predicate[URL]]): Boolean = {
      if (rules.isEmpty) return true
      rules.head.test(url) && validate(url, rules.tail)
    }

    validate(new URL(urlStr), rules)
  }
}
