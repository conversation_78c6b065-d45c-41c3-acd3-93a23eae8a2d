/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.WebHookBusinessEventId
import com.zatech.genesis.openapi.platform.hooks.api.dispatch.IEventDispatcherFactory
import com.zatech.genesis.openapi.platform.hooks.api.enums.BusinessEventDataTypeEnum
import com.zatech.genesis.openapi.platform.hooks.api.model.BusinessTriggeredApplicationEvent
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory.WebHookTriggerAggregationFactory
import com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.EventDataStatusEnum
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.EventCategory
import com.zatech.genesis.openapi.platform.infra.application.service.IWebHookBusinessEventReceivedService
import com.zatech.genesis.openapi.platform.share.enums.ZATechDomainEnum
import com.zatech.genesis.openapi.platform.share.{Loggable, ThreadPoolContext}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.util.CollectionUtils

import scala.concurrent.{ExecutionContextExecutor, Future}

/**
 * <AUTHOR>
 * @Date 2022/6/8
 * */
@Component
class BusinessTriggeredEventListener @Autowired()(
                                                   eventDispatcherFactory: IEventDispatcherFactory,
                                                   webHookBusinessEventReceivedService: IWebHookBusinessEventReceivedService,
                                                   webHookBusinessEventAggregationFactory: WebHookTriggerAggregationFactory,
                                                   threadPoolContext: ThreadPoolContext
                                                 ) extends ApplicationListener[BusinessTriggeredApplicationEvent] with Loggable {

  @Async
  override def onApplicationEvent(event: BusinessTriggeredApplicationEvent): Unit = {

    implicit val executor: ExecutionContextExecutor = threadPoolContext.getHookExecutor

    /**
     * 为了避免处理因消息和procedure 的 event 绑定时序不确定的问题，一次性查询所有待处理的events全部查询处理
     */
    val allWaitingEvents = webHookBusinessEventReceivedService.listAllWaitingEvents()
    allWaitingEvents.forEach(waitingEvent => {
      val category =
        new EventCategory(
          ZATechDomainEnum.byCode(waitingEvent.getDomain),
          waitingEvent.getEvent, null, waitingEvent.getVersion)

      //所以subscription顺序也很重要，因为是字符串拼接
      val eventId = WebHookBusinessEventId(waitingEvent.getBusinessId, waitingEvent.getSubscriptions, category)
      val eventAggregations = webHookBusinessEventAggregationFactory.getDomains(eventId)

      if (!CollectionUtils.isEmpty(eventAggregations)) {
        eventAggregations.forEach(eventAggregation => {
          if (!eventAggregation.isTriggered) {
            if (EventDataStatusEnum.RESULT.name().equals(waitingEvent.getEventDataStatus)) {
              eventAggregation.tryLockTriggerEvent()
            }
            eventAggregation.businessEventReceived(waitingEvent.getBusinessEventReceivedId)
            //received表triggered_flag 在处理事件中间态时，能避免重复触发回调
            if (eventAggregation.tryLockBusinessEventReceived()) {
              Future {
                try {
                  val dataType = BusinessEventDataTypeEnum.valueOf(waitingEvent.getEventDataType)
                  val dispatcher = eventDispatcherFactory.getDispatcher(dataType)
                  val data = dataType.getReader.apply(waitingEvent.getEventData)
                  log.info("Begin dispatch event: {}, data: {} of business event received id: {}", category.toJsonString, waitingEvent.getEventData, waitingEvent.getBusinessEventReceivedId)
                  dispatcher.dispatch(eventAggregation, data)
                  log.info("End dispatch event.")
                } catch {
                  case e: Exception => {
                    log.error("Trigger business event error.", e)
                    eventAggregation.onEventTriggeredError(waitingEvent, e)
                  }
                }
              }
            }
          }
        })
      }
    })
  }
}
