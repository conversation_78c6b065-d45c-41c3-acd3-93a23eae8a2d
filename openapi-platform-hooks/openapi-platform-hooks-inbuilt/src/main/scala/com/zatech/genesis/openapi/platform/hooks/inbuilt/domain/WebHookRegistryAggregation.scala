package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain

import com.zatech.genesis.openapi.platform.api.resource.developercenter.mgmt.webhook.endpoint.common.{WebHookEndpointDetail, WebHookEndpointSummary}
import com.zatech.genesis.openapi.platform.api.resource.developercenter.mgmt.webhook.endpoint.request.UpdateEndpointRequest
import com.zatech.genesis.openapi.platform.hooks.api.{EventName, IHookRegistryAggregation}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory.WebHookEventAggregationFactory
import com.zatech.genesis.openapi.platform.infra.application.entity.{WebHookEventSubscription, WebHookKey, WebHookRegistry}
import com.zatech.genesis.openapi.platform.infra.application.service.{IWebHookEventSubscriptionService, IWebHookKeyService, IWebHookRegistryService}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import org.apache.commons.collections4.CollectionUtils
/**
 * <AUTHOR>
 * @Date 2022/6/8
 * */
//TODO 重构，解决n+1问题
class WebHookRegistryAggregation(
                                  val entity: WebHookRegistry,
                                  webHookKeyService: IWebHookKeyService,
                                  webHookRegistryService: IWebHookRegistryService,
                                  eventAggregationFactory: WebHookEventAggregationFactory,
                                  webHookEventSubscriptionService: IWebHookEventSubscriptionService
                                ) extends IHookRegistryAggregation {

  override def getId: java.lang.Long = entity.getId

  private lazy val key: WebHookKey = webHookKeyService.queryLastKeyByWebHookRegistryId(getId)

  private lazy val subscriptions: Seq[WebHookEventSubscription] =
    webHookEventSubscriptionService.listByWebHookRegistryId(getId).toSeq

  lazy val subscribedEvents: Set[WebHookEventAggregation] = {
    webHookEventSubscriptionService
      .listByWebHookRegistryId(getId)
      .map(subscription => eventAggregationFactory.getDomain(EventName(subscription.getEvent)))
      .toSet
  }

  override def getTenantCode: String = entity.getTenantCode

  override def getCallbackUrl: String = entity.getCallbackUrl

  override def getAppId: String = entity.getChannelCode

  override def getKey: String = key.getKey

  def getName: String = entity.getName

  def getDescription: String = entity.getDescription

  def createKey(key: String): Unit = {
    val webHookKey = new WebHookKey
    webHookKey.setKey(key)
    webHookKey.setTenantCode(getTenantCode)
    webHookKey.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    webHookKey.setWebHookRegistryId(getId.toString)
    webHookKeyService.save(webHookKey)
  }

  def subscribeEvents(eventNames: Set[EventName]): Unit = {
    val subscriptions = eventNames.map(name => {
      val eventDomain = eventAggregationFactory.getDomain(name)
      val subscription = new WebHookEventSubscription()
      subscription.setSubscriptions(eventDomain.subscriptions)
      subscription.setEvent(name.value)
      subscription.setDomain(eventDomain.domain.getCode)
      subscription.setVersion(eventDomain.eventCategory.getVersion)
      subscription.setWebHookRegistryId(getId.toString)
      subscription.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
      subscription
    })
    webHookEventSubscriptionService.saveBatch(subscriptions.toSeq)
  }

  def update(request: UpdateEndpointRequest): Unit = {
    entity.setName(request.getName)
    entity.setDescription(request.getDescription)
    webHookRegistryService.updateById(entity)
    if(CollectionUtils.isEmpty(request.getEvents)) {
      webHookEventSubscriptionService.deleteByRegistryId(getId)
    } else {
      val needDeleteSubscriptions =
        subscriptions.filter(subscription => !request.getEvents.contains(subscription.getEvent))
      webHookEventSubscriptionService.removeBatchByIds(needDeleteSubscriptions.map(_.getId))
      val newEvents = request.getEvents.diff(subscriptions.map(_.getEvent))
      subscribeEvents(newEvents.map(EventName).toSet)
    }
  }

  def delete(): Unit = {
    webHookRegistryService.removeById(getId)
    webHookEventSubscriptionService.deleteByRegistryId(getId)
    webHookKeyService.deleteByRegistryId(getId)
  }

  def toSummary: WebHookEndpointSummary = {
    val summary = new WebHookEndpointSummary
    summary
      .setId(getId)
      .setUrl(getCallbackUrl)
      .setName(getName)
      .setDescription(getDescription)
  }

  def toDetail: WebHookEndpointDetail = {
    val detail = new WebHookEndpointDetail()
    detail
      .setKey(getKey)
      .setEventsSubscribed(subscribedEvents.map(_.toSummary).toList)
      .setId(getId)
      .setUrl(getCallbackUrl)
      .setName(getName)
      .setDescription(getDescription)
    detail
  }
}
