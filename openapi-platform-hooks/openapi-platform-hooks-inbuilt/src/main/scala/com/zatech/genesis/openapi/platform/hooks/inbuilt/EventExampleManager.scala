package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.api.enums.BusinessEventDataTypeEnum
import com.zatech.genesis.openapi.platform.hooks.inbuilt.EventExampleManager.WebHookExamples
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.genesis.portal.toolbox.share.Loggable
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap
import com.zatech.genesis.portal.toolbox.util.scan.{IResourceScanner, RichResource}
import io.swagger.v3.oas.models.examples.Example
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @create 2024/11/15 16:51
 * */
@Component
class EventExampleManager @Autowired()(webHookExampleScanner: IResourceScanner){
  private val rootPath = "web-hooks/examples"

  private val exampleMap: Map[EventName, WebHookExamples] = {
    val map = webHookExampleScanner.scanAndMap(rootPath) { folder =>
      if (folder.isDirectory_?) {
        val examples = new WebHookExamples
        val exampleFolder = s"$rootPath/${folder.name}"
        webHookExampleScanner.scanAndMap(exampleFolder){examples.addExample}
        Some(EventName(folder.name) -> examples)
      } else None
    }
    map.flatten.toMap
  }

  def getExamplesOpt(eventName: EventName): Option[WebHookExamples] = exampleMap.get(eventName)
}

object EventExampleManager extends Loggable {
  final val TypeKey = "type"

  class ExampleHolder(rawData: JsonMap) {
    val dataType: BusinessEventDataTypeEnum = {
      val type_? = rawData.getOrElse(TypeKey,
        throw new IllegalStateException("webhook example must have 'type' config field."))
      BusinessEventDataTypeEnum.valueOf(type_?.toString)
    }
    val data: BaseBusinessEventData =
      dataType.getReader.apply(JsonMap.create(rawData.removed(TypeKey)).toJsonString)
  }

  class WebHookExamples extends java.util.HashMap[String, ExampleHolder] {
    def addExample(example: Resource): Unit = {
      val name = example.name
      if(example.isFile_? && name.endsWith(".json")) {
        val content = StaticJsonParser.fromJsonStringToMap(example.content)
        put(example.nameWithoutSuffix, new ExampleHolder(content))
      } else log.error(s"we find a file: $name, it is invalid web hook example, please check.")
    }

    def toOpenApiExamples: java.util.Map[String, Example] = {
      this.transform((k, v) => new Example().value(v.data))
    }
  }
}
