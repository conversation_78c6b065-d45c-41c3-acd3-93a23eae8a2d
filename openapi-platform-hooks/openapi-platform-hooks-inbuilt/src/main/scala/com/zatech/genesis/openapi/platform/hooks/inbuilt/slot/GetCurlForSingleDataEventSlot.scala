package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.common.model.CurlInfo
import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext, SingleDataModel, WithSignatureEventModel}
import com.zatech.genesis.openapi.platform.hooks.api.{IEventHookSlot, IEventSignatureSupport}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.GetCurlWebHookTriggerAggregation
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.BaseBusinessEventData
import com.zatech.genesis.openapi.platform.share.{Loggable, TimeHelper}
import io.swagger.v3.oas.models.PathItem.HttpMethod
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

/**
 * 用来获取mock发送的curl命令
 *
 * <AUTHOR>
 * @create 2022/9/5 14:38
 */
@Component
class GetCurlForSingleDataEventSlot @Autowired()(
                                                  @Qualifier("webHookSender") restTemplate: RestTemplate,
                                                  signatureSupport: IEventSignatureSupport
                                                ) extends IEventHookSlot with Loggable {

  override def getOrder: Int = IEventHookSlot.MAX_ORDER - 10 * IEventHookSlot.JUMP_SIZE

  override def shouldExecute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean =
    businessData.isInstanceOf[SingleDataModel] &&
      eventModel.isInstanceOf[WithSignatureEventModel] &&
      context.getTriggerAggregation.isInstanceOf[GetCurlWebHookTriggerAggregation]

  override def execute(context: EventHookContext, businessData: BaseBusinessEventData, eventModel: BaseEventModel): Boolean = {
    val signatureEventModel = eventModel.asInstanceOf[WithSignatureEventModel]
    signatureEventModel.setRetryTimes(0)
    signatureEventModel.setTimestamp(TimeHelper.getNowWithZone)
    signatureSupport.signature(context, signatureEventModel)
    val webHookSender = new WebHookSender(context, eventModel, restTemplate)
    context.setBaggage(buildCurl(webHookSender, signatureEventModel))
    false
  }

  private def buildCurl(webHookSender: WebHookSender,
                        signatureEventModel: WithSignatureEventModel): CurlInfo = {
    new CurlInfo()
      .setUrl(webHookSender.url)
      .setMethod(HttpMethod.POST)
      .setHeaders(webHookSender.headersMap.add("Content-Type", "application/json"))
      .setBody(signatureEventModel)
  }
}
