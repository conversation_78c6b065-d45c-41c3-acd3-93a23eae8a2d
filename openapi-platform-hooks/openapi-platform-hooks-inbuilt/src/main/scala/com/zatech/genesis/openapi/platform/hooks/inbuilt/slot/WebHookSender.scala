package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.model.{BaseEventModel, EventHookContext}
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.genesis.portal.toolbox.share.Loggable
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap
import org.springframework.http.{HttpEntity, HttpHeaders}
import org.springframework.web.client.RestTemplate

/**
 * <AUTHOR>
 * @create 2024/12/31 10:59
 * */
class WebHookSender(context: EventHookContext,
                    eventModel: BaseEventModel,
                    restTemplate: RestTemplate) extends Loggable {
  private val trigger = context.getTriggerAggregation
  private val registry = context.getRegistryAggregation
  private val application = context.getApplication
  val url: String = registry.getCallbackUrl

  val headers = new HttpHeaders()
  headers.add("x-openapi-app-id", trigger.getAppId)
  headers.add("x-openapi-channel", application.getPayload.channelCodeOpt().orNull)
  headers.add("x-openapi-tenant", trigger.getTenantCode)

  def headersMap: JsonMap = {
    JsonMap.create(headers.toSingleValueMap.asInstanceOf[java.util.Map[String, AnyRef]])
  }

  def send(bodyString: String, retryTimes: Int): Boolean = {
    val request = new HttpEntity[String](bodyString, headers)
    var callbackId: java.lang.Long = null
    try {
      callbackId = trigger.onCallbackStarted(registry, retryTimes, eventModel.getMessageId.toString, request)
      val response = restTemplate.postForEntity(url, request, classOf[String])
      if (response.getStatusCode.is2xxSuccessful()) {
        trigger.onCallbackSucceed(registry, callbackId, retryTimes, request, response)
        return true
      } else {
        trigger.onCallbackFailed(registry, callbackId, retryTimes, request, response)
      }
    } catch {
      case e: Exception =>
        log.error("try send data error", e)
        if (callbackId != null) trigger.onCallbackError(registry, callbackId, retryTimes, e)
    }
    false
  }
}