package com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory

import com.zatech.genesis.openapi.platform.domain.DomainFactory
import com.zatech.genesis.openapi.platform.domain.exception.DomainObjectNotFoundException
import com.zatech.genesis.openapi.platform.hooks.api.WebHookBusinessEventId
import com.zatech.genesis.openapi.platform.hooks.api.exception.WebHookErrorCodes
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.{RealWebHookTriggerAggregation, WebHookEventAggregation}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.BusinessEventBound
import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookBusinessEventBound
import com.zatech.genesis.openapi.platform.infra.application.service.{IWebHookBusinessEventBoundService, IWebHookBusinessEventReceivedService, IWebHookBusinessEventTriggerRecordService}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

import java.util.stream.Collectors
import scala.jdk.OptionConverters._

/**
 * <AUTHOR>
 * @Date 2022/6/6
 * */
@Component
class WebHookTriggerAggregationFactory @Autowired()(
                                                     webHookBusinessEventBoundService: IWebHookBusinessEventBoundService,
                                                     webHookBusinessEventReceivedService: IWebHookBusinessEventReceivedService,
                                                     webHookBusinessEventTriggerRecordService: IWebHookBusinessEventTriggerRecordService
                                                   ) extends DomainFactory[RealWebHookTriggerAggregation, WebHookBusinessEventId] {

  @Autowired
  private val webHookRegistryAggregationFactory: WebHookRegistryAggregationFactory = null

  //这个地方domain的语义有一点问题，但是这样子bound和log，才能和registry连起来,否则就不清楚消息发到哪里去了
  def getDomains(domainId: WebHookBusinessEventId): java.util.List[RealWebHookTriggerAggregation] =
    getDomainsOpt(domainId).getOrElse(throw new DomainObjectNotFoundException(WebHookErrorCodes.business_event_aggregation_not_found, domainId + ""))

  def getDomainsOpt(domainId: WebHookBusinessEventId): Option[java.util.List[RealWebHookTriggerAggregation]] = {
    val event = domainId.event
    webHookBusinessEventBoundService.queryBy(
        domainId.businessId, event.getDomainEnum.getCode,
        domainId.subscriptions, event.getVersion, event.getEvent)
      .map(bound => bound.stream().map(boundWithRegistry =>
        new RealWebHookTriggerAggregation(
          id = domainId,
          webHookBusinessEventBound = boundWithRegistry,
          webHookBusinessEventBoundService = webHookBusinessEventBoundService,
          webHookBusinessEventTriggerRecordService = webHookBusinessEventTriggerRecordService,
          webHookRegistryAggregationFactory = webHookRegistryAggregationFactory,
          webHookBusinessEventReceivedService = webHookBusinessEventReceivedService
        )).collect(Collectors.toList[RealWebHookTriggerAggregation])).toScala
  }

  @Transactional
  def bindBusinessHookEvent(eventBoundRes: BusinessEventBound,
                            eventAggregation: WebHookEventAggregation,
                            registryIds: Seq[java.lang.Long]): Unit = {
    registryIds.foreach(id => {
      val eventBound = new WebHookBusinessEventBound
      eventBound.setEvent(eventAggregation.id.value)
      eventBound.setDomain(eventAggregation.domain.getCode)
      eventBound.setSubscriptions(eventAggregation.subscriptions)
      eventBound.setVersion(eventAggregation.version)
      eventBound.setBusinessId(eventBoundRes.getBusinessId)
      eventBound.setChannelCode(TraceSupport.getAppIdOrThrow)
      eventBound.setBizNo(eventBoundRes.getBizNo)
      eventBound.setExtraInfo(JsonParser.toJsonString(eventBoundRes.getExtraInfo))
      eventBound.setWebHookRegistryId(id.toString)
      webHookBusinessEventBoundService.trySaveEventBound(eventBound)
    })
  }

  override def getDomain(domainId: WebHookBusinessEventId): RealWebHookTriggerAggregation = ???

  override def deleteDomain(domainId: WebHookBusinessEventId): Unit = ???
}
