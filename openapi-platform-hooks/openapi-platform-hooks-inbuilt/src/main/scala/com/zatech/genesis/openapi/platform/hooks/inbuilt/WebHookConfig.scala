package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.portal.toolbox.util.scan.{ClassPathResourceScanner, IResourceScanner}
import org.springframework.context.annotation.{Bean, Configuration}

/**
 * <AUTHOR>
 * @create 2024/11/15 16:47
 * */
@Configuration
class WebHookConfig {

  @Bean(Array("webHookExampleScanner"))
  def hookExampleScanner(): IResourceScanner = new ClassPathResourceScanner
}
