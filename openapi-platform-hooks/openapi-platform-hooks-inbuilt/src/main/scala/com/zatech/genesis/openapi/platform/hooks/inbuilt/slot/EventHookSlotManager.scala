package com.zatech.genesis.openapi.platform.hooks.inbuilt.slot

import com.zatech.genesis.openapi.platform.hooks.api.{IEventHookSlot, IEventHookSlotChain}
import com.zatech.genesis.openapi.platform.hooks.inbuilt.EventDefinitionManager
import com.zatech.genesis.openapi.platform.security.api.application.IApplicationFactory
import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Component

import scala.jdk.CollectionConverters._

/**
 * <AUTHOR>
 * @Date 2022/6/2
 * */
@Component
class EventHookSlotManager @Autowired()(
                                         applicationContext: ApplicationContext,
                                         eventDefinitionManager: EventDefinitionManager,
                                         applicationFactory: IApplicationFactory
                                       ) extends Loggable {

  private val slots: List[IEventHookSlot] =
    applicationContext.getBeansOfType(classOf[IEventHookSlot]).values().asScala.toList.sorted

  log.info("Event hook slots: {}", slots.map(slot => slot.getClass.getSimpleName + s"[order: ${slot.getOrder}]").mkString(" -> "))

  def newSlotChain: IEventHookSlotChain =
    new InbuiltEventHookSlotChain(slots, eventDefinitionManager, applicationFactory)
}
