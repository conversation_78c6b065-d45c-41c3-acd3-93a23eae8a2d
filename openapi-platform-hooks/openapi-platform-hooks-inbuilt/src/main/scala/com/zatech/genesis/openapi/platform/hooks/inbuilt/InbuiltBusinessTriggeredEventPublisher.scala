package com.zatech.genesis.openapi.platform.hooks.inbuilt

import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.api.enums.BusinessEventDataTypeEnum
import com.zatech.genesis.openapi.platform.hooks.api.model.BusinessTriggeredApplicationEvent
import com.zatech.genesis.openapi.platform.hooks.plugin.api.IBusinessTriggeredEventPublisher
import com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.EventDataStatusEnum
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.business.{BaseBusinessEventData, BusinessTriggeredEvent}
import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookBusinessEventReceived
import com.zatech.genesis.openapi.platform.infra.application.service.{IWebHookBusinessEventReceivedService, IWebHookEventSubscriptionService}
import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/8
 * */
@Component
class InbuiltBusinessTriggeredEventPublisher @Autowired()(
                                                           applicationEventPublisher: ApplicationEventPublisher,
                                                           webHookBusinessEventReceivedService: IWebHookBusinessEventReceivedService,
                                                           eventSubscriptionService: IWebHookEventSubscriptionService
                                                         ) extends IBusinessTriggeredEventPublisher with Loggable {
  /**
   * event definition manager需要扫描所有的consumer，
   * 而consumer中需要调用此类进行event publish，存在循环依赖，所以延迟注入
   */
  @Autowired @Lazy val eventDefinitionManager: EventDefinitionManager = null

  override def publishEvent(event: BusinessTriggeredEvent[_ <: BaseBusinessEventData]): Unit = {
    //先判断：租户下是否订阅了该记录，如果不记录那就直接返回了，不会往下继续走
    if (!eventSubscriptionService.eventIsSubscribed(event.getEvent.getEvent)) return
    //存储接收到的数据
    val eventDef = eventDefinitionManager.getEventDefOrThrow(EventName(event.getEvent.getEvent))
    val receivedData = new WebHookBusinessEventReceived
    receivedData.setBusinessId(event.getBusinessId)
    receivedData.setEvent(event.getEvent.getEvent)
    receivedData.setEventData(event.getEventData.toJsonString)
    receivedData.setRawData(event.getRawData.toJsonString)
    receivedData.setEventDataType(BusinessEventDataTypeEnum.fromDataClass(event.getEventData.getClass).name())
    receivedData.setDomain(event.getEvent.getDomainEnum.getCode)
    receivedData.setVersion(event.getEvent.getVersion)
    receivedData.setSubscriptions(eventDef.getSubscriptionsStr)
    receivedData.setMsgId(event.getIdempotentMsgId)
    receivedData.setEvenDataStatus(Option(event.getEventDataStatus).map(_.name()).getOrElse(EventDataStatusEnum.RESULT.name()))
    receivedData.setTriggeredFlag(false)
    webHookBusinessEventReceivedService.trySaveBusinessEvent(receivedData)

    log.info("WebHook BusinessEvent saved. businessId: {}", event.getBusinessId)

    //发送处理事件
    val applicationEvent = new BusinessTriggeredApplicationEvent
    applicationEventPublisher.publishEvent(applicationEvent)

    log.info("WebHook BusinessEvent published. businessId: {}", event.getBusinessId)
  }
}
