package com.zatech.genesis.openapi.platform.hooks.inbuilt;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Date 2022/6/7
 **/
@Configuration("hookConfig")
@ComponentScan
public class Config {

    @Value("${custom.rest.connect-timeout:3000}")
    private int connectTime;

    @Value("${custom.rest.read-timeout:30000}")
    private int readTime;

    @Value("${openapi.webhook.useProxies:false}")
    private boolean useProxies;

    @Value("${openapi.webhook.proxyPort:3128}")
    private int proxyPort;

    @Value("${openapi.webhook.proxyHost:''}")
    private String proxyHost;

    @Value("${openapi.webhook.forwardProxyDomains:''}")
    private String forwardProxyDomains;


    @Bean
    RestTemplate webHookSender(ClientHttpRequestFactory factory) {

        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(new HeaderRequestInterceptor("Content-Type", "application/json"));

        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }

    @Bean
    ClientHttpRequestFactory hookFactory() {

        //代理只作用于hookFactory
        OpenapiCustomProxyFactory openapiCustomProxyFactory = new OpenapiCustomProxyFactory();
        openapiCustomProxyFactory.setConnectTimeout(connectTime);
        openapiCustomProxyFactory.setReadTimeout(readTime);
        return openapiCustomProxyFactory;
    }

    static class HeaderRequestInterceptor implements ClientHttpRequestInterceptor {

        private final String headerName;

        private final String headerValue;

        public HeaderRequestInterceptor(String headerName, String headerValue) {

            this.headerName = headerName;
            this.headerValue = headerValue;
        }

        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

            request.getHeaders().set(headerName, headerValue);
            return execution.execute(request, body);
        }

    }

    public class OpenapiCustomProxyFactory extends SimpleClientHttpRequestFactory {

        private Proxy proxy;

        private final List<Pattern> proxyPatterns = new ArrayList<>();

        public OpenapiCustomProxyFactory() {
            if (useProxies) {
                proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
                // 将通配符转换为正则表达式
                Arrays.stream(forwardProxyDomains.split("\\|"))
                    .map(host -> host.replace(".", "\\.").replace("*", ".*"))
                    .map(Pattern::compile)
                    .forEach(proxyPatterns::add);
            }
        }

        @Override
        protected HttpURLConnection openConnection(URL url, Proxy proxy) throws IOException {
            //开关打开，才取代理配置
            if (useProxies) {
                String host = url.getHost();
                //匹配请求的host是否被允许代理
                boolean bypassProxy = proxyPatterns.stream()
                    .anyMatch(pattern -> pattern.matcher(host).matches());

                Proxy selectedProxy = bypassProxy ? this.proxy : Proxy.NO_PROXY;
                return super.openConnection(url, selectedProxy);
            } else {
                return super.openConnection(url, proxy);
            }
        }

    }

}
