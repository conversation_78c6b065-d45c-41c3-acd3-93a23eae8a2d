package com.zatech.genesis.openapi.platform.tools.modelmeta

import com.zatech.genesis.openapi.platform.infra.knowledge.schema.entity.ProcedureModelMeta
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.service.{IProcedureMetaService, IProcedureModelMetaService}
import com.zatech.genesis.openapi.platform.tools.model.ModelMetaParam
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

import scala.collection.mutable
import scala.jdk.CollectionConverters._

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
class ProcedureModelMetaGenerator(procedureModelMetaService: IProcedureModelMetaService,
                                  procedureMetaService: IProcedureMetaService,
                                  @Autowired applicationContext: ApplicationContext
                                 ) extends SourceCategoryModelMetaGenerator(applicationContext) {


  @Transactional
  def generateModelMeta(param: ModelMetaParam): Unit = {
    val generator = getGenerator(param.source)
    generateModelMeta(generator, param)
    procedureModelMetaService.saveBatch(generator.getModelMetas.asInstanceOf[mutable.Buffer[ProcedureModelMeta]].asJava)
    procedureMetaService.updateModelMetaId(generator.getId, generator.getRootId, generator.getDirection)
  }
}
