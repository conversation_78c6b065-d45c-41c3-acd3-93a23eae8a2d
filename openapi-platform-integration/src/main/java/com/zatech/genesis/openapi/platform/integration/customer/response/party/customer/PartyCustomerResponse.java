/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.customer.response.party.customer;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.customer.PolicyCustomerEnum;
import com.zatech.genesis.openapi.platform.integration.customer.base.BaseIndividualIncludeCertificateBasic;
import com.zatech.genesis.openapi.platform.integration.customer.response.SecondNationalityResponse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/8/4 18:03
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(description = "Party Customer Response Info", name = "PartyCustomerResponse")
public class PartyCustomerResponse extends BaseIndividualIncludeCertificateBasic implements Serializable {

    private static final long serialVersionUID = -1007968456959810957L;

    @Schema(description = "Person id")
    private Long personId;

    @Schema(description = "Party ID")
    private Long partyId;

    @Schema(description = "Role type")
    private PolicyCustomerEnum roleType;

    @Schema(description = "Accounts")
    private List<PartyCustomerAccountResponse> accounts;

    @Schema(description = "Phones")
    private List<PartyCustomerPhoneResponse> phones;

    @Schema(description = "Emails")
    private List<PartyCustomerEmailResponse> emails;

    @Schema(description = "Addresses")
    private List<PartyCustomerAddressResponse> addresses;

    @Schema(description = "Social accounts")
    private List<PartyCustomerSocialAccountResponse> socialAccounts;

    @Schema(description = "Taxes")
    private List<PartyCustomerTaxResponse> taxes;

    @Schema(description = "Labels")
    private List<PartyCustomerLabelResponse> labels;

    @Schema(description = "Attachments")
    private List<PartyCustomerAttachmentResponse> attachments;

    @Schema(description = "Channel user no")
    private String channelUserNo;

    @Schema(description = "Channel code")
    private String channelCode;

    @Schema(description = "second nationality")
    private List<SecondNationalityResponse> secondNationality;

    @Schema(description = "Missing status")
    private YesNoEnum missingStatus;

    @Schema(description = "Trans type")
    private TransTypeEnum transType;

    @Schema(description = "Trans no")
    private String transNo;

}
