/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.outer.fallback;

import com.zatech.genesis.cdc.es.client.search.builder.SearchSourceBuilder;
import com.zatech.genesis.openapi.platform.integration.cdc.request.PolicyRequestDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.EsPageResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyTaskResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.UwTaskResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.issuance.IssuanceResponseDTO;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterCdcService;
import com.zatech.genesis.openapi.platform.integration.outer.response.PolicyInsuredListResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceResponse;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.octopus.module.web.dto.ResultBase;

import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FeignCdcFallbackFactory extends AbstractFallbackFactory<IOuterCdcService> {

    @Override
    public IOuterCdcService create(Throwable throwable) {

        return new IOuterCdcService() {
            @Override
            public ResultBase<EsPageResponseDTO<PolicyResponseDTO>> searchPolicyNewDetail(PolicyRequestDTO req) {
                throw outerServiceException(throwable, JsonParser.toJsonString(req));
            }

            @Override
            public ResultBase<EsPageResponseDTO<PolicyResponseDTO>> searchPolicyNewList(PolicyRequestDTO req) {
                throw outerServiceException(throwable, JsonParser.toJsonString(req));
            }

            @Override
            public Page<PolicyResponseDTO> queryPolicy(SearchSourceBuilder builder) {
                throw outerServiceException(throwable, JsonParser.toJsonString(builder));
            }

            @Override
            public Page<IssuanceResponse> queryIssuance(SearchSourceBuilder builder) {
                throw outerServiceException(throwable, JsonParser.toJsonString(builder));
            }

            @Override
            public Page<IssuanceResponseDTO> queryIssuanceNew(SearchSourceBuilder builder) {
                throw outerServiceException(throwable, JsonParser.toJsonString(builder));
            }

            @Override
            public Page<PolicyTaskResponseDTO> queryPolicyTask(SearchSourceBuilder builder) {
                throw outerServiceException(throwable, JsonParser.toJsonString(builder));
            }

            @Override
            public Page<UwTaskResponseDTO> queryUwTask(SearchSourceBuilder builder) {
                throw outerServiceException(throwable, JsonParser.toJsonString(builder));
            }

            @Override
            public Page<PolicyInsuredListResponse> searchPolicyInsurantList(SearchSourceBuilder builder) {
                throw outerServiceException(throwable, JsonParser.toJsonString(builder));
            }
        };
    }
}
