package com.zatech.genesis.openapi.platform.integration.outer;


import com.zatech.genesis.openapi.platform.integration.calculate.request.SchemaFormulaCalculateRequest;
import com.zatech.genesis.openapi.platform.integration.calculate.response.SchemaFormulaCalculateResponse;
import com.zatech.genesis.openapi.platform.integration.outer.fallback.FeignCalculatorFallbackFactory;

import jakarta.validation.constraints.NotNull;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/12/23 14:19
 */
@FeignClient(value = "zatech-plugin-calculator", url = "${za.graphene.feign.calculator}", fallbackFactory = FeignCalculatorFallbackFactory.class)
public interface IOuterCalculatorService {

    @PostMapping(value = "/api/v2/calculator/calculate")
    SchemaFormulaCalculateResponse calculate(@RequestBody @NotNull SchemaFormulaCalculateRequest request);

}
