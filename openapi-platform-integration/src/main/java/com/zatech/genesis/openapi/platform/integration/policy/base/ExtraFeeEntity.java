package com.zatech.genesis.openapi.platform.integration.policy.base;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(name = "ExtraFeeEntity", title = "The base extra fee request")
public class ExtraFeeEntity implements Serializable {

    /**
     * 期缴-加费（不含税）
     */
    @Schema(title = "The period premium excluding tax", required = true)
    private String periodPremium;

    /**
     * 期缴-加费（含税）
     */
    @Schema(title = "The period premium included tax")
    private String periodPremiumTax;

    /**
     * 年化-加费（不含税）
     */
    @Schema(title = "The annual premium excluding tax")
    private String annualPremium;

    /**
     * 年化-加费（含税）
     */
    @Schema(title = "The annual premium included tax")
    private String annualPremiumTax;

}
