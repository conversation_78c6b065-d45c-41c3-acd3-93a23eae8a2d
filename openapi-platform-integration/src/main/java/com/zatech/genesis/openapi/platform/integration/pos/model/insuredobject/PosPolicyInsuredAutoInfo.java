/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policy.VehicleEngineTypeEnum;
import com.zatech.gaia.resource.components.enums.subject.SubjectTypeEnum;
import com.zatech.genesis.openapi.platform.integration.pos.model.base.BaseCommon;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: <PERSON>
 * @create: 2021-08-01 22:26
 */
@Setter
@Getter
@Schema(description = "Policy auto object change request class", name = "PosPolicyInsuredAutoInfo")
public class PosPolicyInsuredAutoInfo extends BaseCommon {

    @Schema(description = "ABS")
    @Size(max = 10, message = "ABS field length must be less 10.")
    private String abs;

    @Schema(description = "Deductible or not")
    @Size(max = 64, message = "Deductible or not field length must be less 64.")
    private String additionalExcess;

    @Schema(description = "Air bag")
    @Size(max = 10, message = "Air bag field length must be less 10.")
    private String airbags;

    @Schema(description = "No Claim Discount(NCD)")
    private PosPolicyInsuredAutoNcdInfo autoNcd;

    @Min(value = 0, message = "Average driving distance must be greater than or equal to 0.")
    @Size(max = 64, message = "Average driving distance must be less 64.")
    @Schema(description = "Average driving distance")
    private String averageDrivingDistance;

    @Schema(description = "Motorcycle type")
    @Size(max = 20, message = "Vehicle body type field length must be less 20.")
    private String vehicleBodyType;

    @Schema(description = "Owner")
    @Valid
    private PosPolicyInsuredAutoCarOwnerInfo carOwner;

    @Schema(description = "Load")
    @Size(max = 20, message = "Carring capacity field length must be less 20.")
    private String carringCapacity;

    @Schema(description = "Carrying goods type")
    @Size(max = 20, message = "Carrying goods type field length must be less 20.")
    private String carryingGoodsType;

    @Schema(description = "Chassis number")
    @Size(max = 30, message = "Chassis No field length must be less 30.")
    private String chassisNo;

    @Schema(description = "Number of claims")
    private Integer claimsNumber;

    @Schema(description = "Registration type")
    @Size(max = 20, message = "Registration type field length must be less 20.")
    private String classRegisteredAs;

    @Schema(description = "Color of plateNo")
    @Size(max = 64, message = "Color of plateNo field length must be less 64.")
    private String colorOfPlateNo;

    @Schema(description = "Vehicle color")
    @Size(max = 20, message = "Vehicle color field length must be less 20.")
    private String colourOfVehicle;

    @Schema(description = "Purchase date")
    private Date dateOfPurchase;

    @Schema(description = "Device ABS")
    @Size(max = 64, message = "Device ABS field length must be less 64.")
    private String deviceAbs;

    @Schema(description = "Device AEB")
    @Size(max = 64, message = "Device AEB field length must be less 64.")
    private String deviceAeb;

    @Schema(description = "Device airbag")
    @Size(max = 10, message = "Device air bag field length must be less 10.")
    private String deviceAirbag;

    @Schema(description = "Device alarm")
    @Size(max = 10, message = "Device alarm field length must be less 10.")
    private String deviceAlarm;

    @Schema(description = "Device gear or steering lock")
    //same with gear_or_steering_lock. length should be 20. confirmed with policy already.
    @Size(max = 20, message = "Device gear or steering lock length must be less 20.")
    private String deviceGearOrSteeringLock;

    @Schema(description = "Device GPS")
    @Size(max = 10, message = "Device GPS field length must be less 10.")
    private String deviceGps;

    @Schema(description = "Device immobiliser")
    @Size(max = 10, message = "Device immobiliser field length must be less 10.")
    private String deviceImmobiliser;

    @Schema(description = "Device installed")
    @Size(max = 64, message = "Device installed field length must be less 64.")
    private String deviceInstalled;

    @Schema(description = "Device tracking")
    @Size(max = 64, message = "Device tracking field length must be less 64.")
    private String deviceTracking;

    @Schema(description = "Installed equipment (Deprecated)")
    private String devicesInstalled;

    @Schema(description = "Distance confirmation date")
    private Date distanceConfirmationDate;

    @Min(value = 0, message = "Driving distance must be greater than or equal to 0.")
    @Size(max = 64, message = "Driving distance must be less 64.")
    @Schema(description = "Driving distance")
    private String drivingDistance;

    @Schema(description = "Engine capacity / displacement")
    @Size(max = 20, message = "Device gear or steering lock field length must be less 20.")
    private String engineCapacity;

    @Schema(description = "Engine number")
    @Size(max = 30, message = "Engine number field length must be less 30.")
    private String engineNo;

    @Schema(description = "Extended field")
    @Size(max = 1024, message = "ExtraInfo field length must be less 20.")
    private String extraInfo;

    @Schema(description = "Alarm")
    @Size(max = 10, message = "Alarm field length must be less 10.")
    private String factoryFittedAlarm;

    @Min(value = 0, message = "FF Weight must be greater than or equal to 0.")
    @Size(max = 64, message = "FF Weight must be less 64.")
    @Schema(description = "FF Weight")
    private String ffWeight;

    @Min(value = 0, message = "FR Weight must be greater than or equal to 0.")
    @Size(max = 64, message = "FR Weight must be less 64.")
    @Schema(description = "FR Weight")
    private String frWeight;

    @Min(value = 0, message = "G Weight must be greater than or equal to 0.")
    @Size(max = 64, message = "G Weight must be less 64.")
    @Schema(description = "G weight")
    @JsonProperty("gWeight")
    private String gWeight;

    @Schema(description = "Gear or steering lock")
    @Size(max = 20, message = "Device gear or steering lock length must be less 20.")
    private String gearOrSteeringLock;

    @Schema(description = "Place of ownership(Deprecated)")
    private String geographicalLocationWhereVehicleIsGaraged;

    @Schema(description = "Gps")
    @Size(max = 10, message = "GPS field length must be less 10.")
    private String gps;

    @Schema(description = "Number of haulage permit")
    @Size(max = 30, message = "Number of haulage permit field length must be less 30.")
    private String haulagePermitNo;

    @Min(value = 0, message = "Height of vehicle must be greater than or equal to 0.")
    @Size(max = 64, message = "Height of vehicle must be less 64.")
    @Schema(description = "Height of vehicle")
    private String heightOfVehicle;

    @Schema(description = "ID of holder")
    private Long holderCustomerId;

    @Schema(description = "If under the off peak car plan")
    @Size(max = 20, message = "If under the off peak car plan field length must be less 20.")
    private String ifUnderTheOffPeakCarScheme;

    @Schema(description = "Immobiliser")
    @Size(max = 10, message = "Immobiliser field length must be less 10.")
    private String immobiliser;

    @Schema(description = "Import type")
    @Size(max = 20, message = "Import type field length must be less 20.")
    private String importType;

    @Schema(description = "Inspection expiry date")
    private Date inspectionExpiryDate;

    @Schema(description = "The new renewal flag 'n stands for new insurance, R stands for renewal, and t stands for re insurance' will only be that new insurance will not be warehoused")
    //不入库
    private String insuraceFlag;

    @Schema(description = "Subject No")
    @Size(max = 100, message = "InsuredNo field length must be less 100.")
    private String insuredNo;

    @Schema(description = "Object type, refer to the API reference. [Detail](/openx/docs/common/guide/reference/subject-type)")
    private SubjectTypeEnum insuredType;

    @Schema(description = "Delete flag")
    private YesNoEnum isDeleted;

    @Schema(description = "Is loan vehicle")
    private YesNoEnum isLoanVehicle;

    @Schema(description = "Is new vehicle")
    private YesNoEnum isNewVehicle;

    @Schema(description = "Is not registered")
    private YesNoEnum isNotRegistered;

    @Schema(description = "Is special shape vehicle")
    private YesNoEnum isSpecialShapeVehicle;

    @Min(value = 0, message = "Last year driving distance must be greater than or equal to 0.")
    @Size(max = 64, message = "Last year driving distance must be less 64.")
    @Schema(description = "Last year driving distance")
    private String lastYearDrivingDistance;

    @Min(value = 0, message = "Length of vehicle must be greater than or equal to 0.")
    @Size(max = 64, message = "Length of vehicle must be less 64.")
    @Schema(description = "Length of vehicle")
    private String lengthOfVehicle;

    @Schema(description = "Finance company / loan provider")
    private String loanProvider;

    @Min(value = 0, message = "Loan years must be greater than or equal to 0.")
    @Size(max = 64, message = "Loan years must be less 64.")
    @Schema(description = "Loan years")
    private String loanYears;

    @Schema(description = "Main driving area")
    @Size(max = 64, message = "Main driving area field length must be less 64.")
    private String mainDrivingArea;

    @Schema(description = "Manufacturer")
    @Size(max = 50, message = "Manufacturer field length must be less 50.")
    private String make;

    @Min(value = 0, message = "Market price must be greater than or equal to 0.")
    @Size(max = 64, message = "Market price must be less 64.")
    @Schema(description = "Market price")
    private String marketPrice;

    @Schema(description = "Model")
    @Size(max = 50, message = "Model field length must be less 50.")
    private String model;

    @Min(value = 0, message = "Number of accidents in last year must be greater than or equal to 0.")
    @Schema(description = "Number of accidents in last year")
    private Integer numberOfAccidentInLastYear;

    @Min(value = 0, message = "Number of seat must be greater than or equal to 0.")
    @Size(max = 64, message = "Number of seat must be less 64.")
    @Schema(description = "Number of seat")
    private String numberOfSeat;

    @Schema(description = "Off peak car")
    @Size(max = 64, message = "Off peak car field length must be less 64.")
    private String offPeakCar;

    @Schema(description = "Plate No")
    @Size(max = 20, message = "PlateNo field length must be less 64.")
    private String plateNo;

    @Schema(description = "Policy ID")
    private Long policyId;

    @Schema(description = "Driver information")
    private List<PosPolicyInsuredAutoDriverInfo> policyInsuredAutoDriverList;

    @Schema(description = "Primary key of policy object")
    private Long policyInsuredAutoId;

    @Schema(description = "Primary key ID")
    private Long policyInsuredObjectId;

    @Schema(description = "Power type")
    @Size(max = 20, message = "Power type field length must be less 64.")
    private String powerType;

    @Schema(description = "Purchase date")
    private Date purchaseDate;

    @Min(value = 0, message = "Vehicle purchase amount must be greater than or equal to 0.")
    @Size(max = 64, message = "Vehicle purchase amount must be less 64.")
    @Schema(description = "Vehicle purchase amount")
    private String purchasePrice;

    @Schema(description = "Rate classes Bi")
    @Size(max = 64, message = "Rate classes Bi must be less 64.")
    private String rateClassesBi;

    @Schema(description = "Rate classes Od")
    @Size(max = 64, message = "Rate classes Bi must be less 64.")
    private String rateClassesOd;

    @Schema(description = "Rate classes Pd")
    @Size(max = 64, message = "Rate classes Pd must be less 64.")
    private String rateClassesPd;

    @Schema(description = "Rate classes Pic")
    @Size(max = 64, message = "Rate classes Pic must be less 64.")
    private String rateClassesPic;

    @Schema(description = "Registration area")
    @Size(max = 64, message = "Registration area must be less 64.")
    private String registrationArea;

    @Schema(description = "Registration Card No")
    @Size(max = 64, message = "Registration area must be less 64.")
    private String registrationCardNo;

    @Schema(description = "Registration category")
    @Size(max = 64, message = "Registration category must be less 64.")
    private String registrationCategory;

    @Schema(description = "Date of registration")
    private Date registrationDate;

    @Schema(description = "Registration hiragana")
    @Size(max = 64, message = "Registration hiragana must be less 64.")
    private String registrationHiragana;

    @Schema(description = "Registration No")
    @Size(max = 64, message = "Registration No must be less 64.")
    private String registrationNo;

    @Schema(description = "Registration serial No")
    @Size(max = 64, message = "Registration serial No must be less 64.")
    private String registrationSerialNo;

    @Min(value = 0, message = "RF Weight must be greater than or equal to 0.")
    @Size(max = 64, message = "RF Weight must be less 64.")
    @Schema(description = "RF weight")
    private String rfWeight;

    @Min(value = 0, message = "RR Weight must be greater than or equal to 0.")
    @Size(max = 64, message = "RR Weight must be less 64.")
    @Schema(description = "RR Weight")
    private String rrWeight;

    @Schema(description = "Carrying passengers")
    @Size(max = 20, message = "seating Capacity must be less 20.")
    private String seatingCapity;

    @Schema(description = "Insurance amount")
    @Size(max = 20, message = "Insurance amount must be less 20.")
    private String sumInsured;

    @Schema(description = "Suspension certificate")
    @Size(max = 64, message = "Suspension certificate must be less 64.")
    private String suspensionCertificate;

    @Min(value = 0, message = "Tonnage must be greater than or equal to 0.")
    @Size(max = 64, message = "Tonnage must be less 64.")
    @Schema(description = "Tonnage")
    private String tonnage;

    @Schema(description = "Tracking system")
    @Size(max = 10, message = "Tracking system must be less 10.")
    private String trackingSystem;

    @Schema(description = "Type of fuel used")
    @Size(max = 20, message = "Type of fuel used must be less 20.")
    private String typeOfFuelUsed;

    @Schema(description = "Type of goods transported")
    @Size(max = 20, message = "Type of goods transported must be less 20.")
    private String typesOfGoodsCarried;

    @Schema(description = "Use of vehicle")
    @Size(max = 20, message = "Use of vehicle used must be less 20.")
    private String useOfVehicle;

    @Schema(description = "Variant")
    @Size(max = 20, message = "Variant must be less 20.")
    private String variant;

    @Min(value = 0, message = "Vehicle age must be greater than or equal to 0.")
    @Schema(description = "Vehicle age")
    private Integer vehicleAge;

    @Schema(description = "Vehicle capacity")
    @Size(max = 20, message = "Vehicle capacity must be less 20.")
    private String vehicleCapacity;

    @Schema(description = "Vehicle color")
    @Size(max = 20, message = "Vehicle color must be less 20.")
    private String vehicleColor;

    @Schema(description = "Engine type EV / non ev")
    private VehicleEngineTypeEnum vehicleEngine;

    @Schema(description = "Vehicle make")
    @Size(max = 50, message = "Vehicle make must be less 50.")
    private String vehicleMake;

    @Schema(description = "Vehicle model")
    @Size(max = 50, message = "Vehicle model must be less 50.")
    private String vehicleModel;

    @Schema(description = "Vehicle No")
    @Size(max = 50, message = "Vehicle No must be less 50.")
    private String vehicleNo;

    @Schema(description = "Vehicle structure")
    @Size(max = 64, message = "Vehicle structure must be less 64.")
    private String vehicleStructure;

    @Schema(description = "Vehicle type")
    @Size(max = 64, message = "Vehicle type must be less 64.")
    private String vehicleType;

    @Schema(description = "Vehicle usage")
    @Size(max = 20, message = "Vehicle usage must be less 20.")
    private String vehicleUsage;

    @Schema(description = "Vin No")
    @Size(max = 64, message = "Vin No must be less 64.")
    private String vinNo;

    /**
     * schema中定义为vinNo，使用时直接用 vinNo
     */
    @Schema(description = "Vehicle identification number")
    @Size(max = 64, message = "Vehicle identification number must be less 64.")
    private String vehicleIdentificationNumber;

    @Schema(description = "Violation code(Deprecated)'")
    @Size(max = 64, message = "Vehicle identification number must be less 64.")
    private String violationCode;

    @Min(value = 0, message = "Weight must be greater than or equal to 0.")
    @Size(max = 64, message = "Weight must be less 64.")
    @Schema(description = "Weight")
    private String weight;

    @Min(value = 0, message = "Width of vehicle must be greater than or equal to 0.")
    @Size(max = 64, message = "Width of vehicle must be less 64.")
    @Schema(description = "Width of vehicle")
    private String widthOfVehicle;

    @Schema(description = "Manufacture date")
    private Date yearOfMake;

    @Schema(description = "Year of manufacturing")
    private Integer yearOfManufacturing;

    /**
     * 车辆组别
     */
    @Schema(description = "Vehicle group")
    @Size(max = 64, message = "Vehicle group must be less 64.")
    private String vehicleGroup;

    /**
     * 负载能力
     */
    @Schema(description = "Load capacity")
    @Size(max = 64, message = "Load capacity must be less 64.")
    private String loadCapacity;

    /**
     * 车辆类别
     */
    @Schema(description = "Vehicle category")
    @Size(max = 64, message = "Vehicle category must be less 64.")
    private String vehicleCategory;

    /**
     * 装甲车辆
     */
    @Schema(description = "Armored vehicle")
    @Size(max = 64, message = "Armored vehicle must be less 64.")
    private String armoredVehicle;

    /**
     * 车辆改装
     */
    @Schema(description = "Vehicle modification")
    @Size(max = 64, message = "Vehicle modification must be less 64.")
    private String vehicleModification;

    /**
     * 发动机功率
     */
    @Schema(description = "Engine power")
    @Size(max = 64, message = "Engine power must be less 64.")
    private String enginePower;

    /**
     * 发动机容积
     */
    @Schema(description = "Engine volume")
    @Size(max = 64, message = "Engine volume must be less 64.")
    private String engineVolume;

    /**
     * 车辆发票金额
     */
    @Schema(description = "Vehicle invoice value")
    @Size(max = 64, message = "Vehicle invoice value must be less 64.")
    private String vehicleInvoiceValue;

    /**
     * 车辆贷款信息
     */
    @Schema(description = "Vehicle loan")
    private PosPolicyInsuredAutoLoanInfo loan;

    /**
     * 车辆设备信息
     */
    @Schema(description = "Additional equipment list")
    private List<PosPolicyInsuredAutoAdditionalEquipmentInfo> policyAdditionalEquipmentList;

    /**
     * Vehicle brand
     */
    @Schema(description = "Vehicle brand")
    @Size(max = 64, message = "Vehicle brand must be less 64.")
    private String vehicleBrand;

    /**
     * Vehicle in advertising and branding
     */
    @Schema(description = "Vehicle in advertising and branding")
    @Size(max = 64, message = "Vehicle in advertising and branding must be less 64.")
    private String vehicleInAdvertisingAndBranding;

    /**
     * Registration end date
     */
    @Schema(description = "Registration end date")
    private Date registrationEndDate;

    /**
     * Car driven right hand
     */
    @Schema(description = "Car driven right hand")
    @Size(max = 64, message = "Car driven right hand must be less 64.")
    private String carDrivenRightHand;

    /**
     * MTPL No.
     */
    @Schema(description = "MTPL No.")
    @Size(max = 64, message = "MTPL No must be less 64.")
    private String mtplNo;

    /**
     * Auto Model
     */
    @Schema(description = "Auto model")
    @Size(max = 64, message = "Auto model must be less 64.")
    private String autoModel;

    /**
     * Auto body type
     */
    @Schema(description = "Auto body type")
    @Size(max = 64, message = "Auto body type must be less 64.")
    private String autoBodyType;

    @Schema(description = "Engine type")
    @Size(max = 64, message = "Engine type type must be less 64.")
    private String engineType;

}
