/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.claim;

import com.zatech.genesis.model.claim.Claim;
import com.zatech.genesis.openapi.platform.integration.claim.base.CustomerApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.DocumentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.IncidentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.IncidentContentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.LossPartyApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.request.LossPartyInsuredObjectRelationApiRequest;
import com.zatech.genesis.openapi.platform.integration.claim.request.RegistrationApiRequest;
import com.zatech.genesis.openapi.platform.integration.transform.claim.convert.UnifiedModelClaimConverter;
import com.zatech.genesis.openapi.platform.integration.transform.claim.convert.UnifiedModelClaimCustomerConverter;
import com.zatech.genesis.openapi.platform.share.TraceSupport;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;

public class UnifiedClaimTransform {

    private final Claim claim;

    private final List<UnifiedClaimCaseTransform> caseTransforms;

    public UnifiedClaimTransform(Claim claim) {
        this.claim = claim;
        this.caseTransforms = Optional.ofNullable(claim.getCaseList()).orElse(Collections.emptyList()).stream().map(UnifiedClaimCaseTransform::new).toList();
    }

    public RegistrationApiRequest toRegistrationApiRequest() {

        RegistrationApiRequest request = UnifiedModelClaimConverter.INSTANCE.convertBasic(claim);
        request.setPayees(toPayees());
        request.setClaimant(toClaimants());
        request.setLossParties(toLossParties());
        request.setDocuments(toDocuments());
        request.setIncidentContents(toIncidentContentApiBases());
        //todo 需要增加节点信息
        request.setIncident(toIncidentApiBase());
        request.setChannelCode(TraceSupport.getChannelCodeOrNull());
        request.setAuthorizedParticipants(toAuthorizedParticipants());
        request.setLossPartyInsuredObjectRelations(toLossPartyInsuredObjectRelations());
        return request;
    }

    public RegistrationApiRequest toRegistrationApiRequestForGroup() {

        RegistrationApiRequest request = UnifiedModelClaimConverter.INSTANCE.convertBasic(claim);
        request.setPayees(toPayees());
        request.setClaimant(toClaimants());
        request.setLossParties(toLossParties());
        request.setDocuments(toDocuments());
        if (CollectionUtils.isNotEmpty(claim.getInsuredList())) {
            request.setInsureds(claim.getInsuredList().stream()
                .map(UnifiedModelClaimCustomerConverter.INSTANCE::toInsured)
                .filter(Objects::nonNull)
                .toList());
        } else {
            request.setInsureds(toInsureds(request.getLossParties()));
        }
        request.setIncidentContents(toIncidentContentApiBases());
        request.setIncident(toIncidentApiBase());
        request.setChannelCode(TraceSupport.getChannelCodeOrNull());
        request.setAuthorizedParticipants(toAuthorizedParticipants());
        request.setLossPartyInsuredObjectRelations(toLossPartyInsuredObjectRelations());
        return request;
    }

    /**
     * gorup policy:如果是第三方责任险，同时理赔自己和其他方，则insureds需要传被保人，lossParty传第三放责任人。如果不涉及第三放则lossParty传被保人即可。
     *
     * @param lossPartyApiBaseList
     * @return
     */
    private List<CustomerApiBase> toInsureds(List<LossPartyApiBase> lossPartyApiBaseList) {

        return Optional.ofNullable(lossPartyApiBaseList)
            .stream()
            .flatMap(Collection::stream)
            .map(LossPartyApiBase::getCustomer)
            .filter(Objects::nonNull)
            .toList();
    }

    private List<LossPartyInsuredObjectRelationApiRequest> toLossPartyInsuredObjectRelations() {
        return caseTransforms.stream()
            .map(caseTransform -> Optional.ofNullable(caseTransform.getCase().getLossParties())
                .orElse(Collections.emptyList()).stream()
                .map(lossParty -> Optional.ofNullable(lossParty.getLossPartyInsuredObjectRelationList()).orElse(Collections.emptyList()))
                .flatMap(Collection::stream)
                .toList())
            .flatMap(Collection::stream)
            .map(UnifiedModelClaimConverter.INSTANCE::convertLossPartyInsuredObjectRelation)
            .filter(Objects::nonNull)
            .toList();
    }

    private List<CustomerApiBase> toAuthorizedParticipants() {
        return Optional.ofNullable(claim.getAuthorizedParticipantList())
            .orElse(Collections.emptyList())
            .stream()
            .map(UnifiedModelClaimCustomerConverter.INSTANCE::toAuthorizedParticipant)
            .filter(Objects::nonNull)
            .toList();
    }

    public CustomerApiBase toClaimants() {
        return Optional.ofNullable(claim.getClaimant()).map(UnifiedModelClaimCustomerConverter.INSTANCE::toClaimant).orElse(null);
    }

    public List<DocumentApiBase> toDocuments() {
        return caseTransforms.stream().map(UnifiedClaimCaseTransform::toDocument).filter(Objects::nonNull).flatMap(Collection::stream).toList();
    }

    public List<CustomerApiBase> toPayees() {
        return caseTransforms.stream().map(UnifiedClaimCaseTransform::toPayees).filter(Objects::nonNull).flatMap(Collection::stream).toList();
    }

    public List<LossPartyApiBase> toLossParties() {
        return caseTransforms.stream().map(UnifiedClaimCaseTransform::toLossParties).filter(Objects::nonNull).flatMap(Collection::stream).toList();
    }

    public List<IncidentContentApiBase> toIncidentContentApiBases() {
        return caseTransforms.stream().map(UnifiedClaimCaseTransform::toIncidentContent).filter(Objects::nonNull).toList();
    }

    public IncidentApiBase toIncidentApiBase() {
        return toIncidentContentApiBases().stream().findFirst().orElse(null);
    }


}
