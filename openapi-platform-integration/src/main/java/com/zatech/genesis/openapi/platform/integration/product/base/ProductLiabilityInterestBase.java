package com.zatech.genesis.openapi.platform.integration.product.base;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(title = "Product Liability Interest")
public class ProductLiabilityInterestBase {

    @Schema(description = "The unique identifier of the product liability interest record.")
    private Long productLiabilityInterestId;

    /**
     * interest id
     */
    @Schema(description = "The unique identifier generated by the system for tracking interest-related items in insurance operations, such as policy loans, premium payment plans, or investment returns. This ID is used to reference and manage interest calculations, accruals, or payouts associated with insurance products that have financial components like whole life policies or annuity products.")
    private Long interestId;

    /**
     * interest code
     */
    @Schema(description = "The unique code that identifies the interest calculation method or interest-related component in insurance products. This could represent different types of interest rates (fixed/variable), dividend calculation methods, or bonus allocation rules used in policies with investment/saving components.")
    private String interestCode;

    /**
     * interest name
     */
    @Schema(description = "The business name assigned to a specific insurance interest or party involved in the policy, such as beneficiary designation or insured entity. This name appears in policy documentation and transaction records related to the interest.")
    private String interestName;

    /**
     * Optional/required
     * {@link com.zatech.gaia.resource.components.enums.product.IsOptionalEnum}
     */
    @Schema(description = "Whether it is optional or not")
    private Integer isOptional;

}
