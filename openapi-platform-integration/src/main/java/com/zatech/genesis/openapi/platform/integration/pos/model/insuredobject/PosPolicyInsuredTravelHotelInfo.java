/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: za-wuyongzhi
 * @Date: Created in 2021/7/19 14:56
 **/
@Setter
@Getter
@Schema(description = "Policy hotel object change request class", name = "PosPolicyInsuredTravelHotelInfo")
public class PosPolicyInsuredTravelHotelInfo {

    @Schema(description = "Primary Key")
    private Long policyInsuredTravelId;

    @Schema(description = "Id of policy")
    private Long policyId;

    @Schema(description = "Number of insured")
    private String insuredNo;

    @Schema(description = "Name of hotel")
    private String hotelName;

    @Schema(description = "Id of holder")
    private Long holderCustomerId;

    @Schema(description = "Extension field")
    private String extraInfo;

}
