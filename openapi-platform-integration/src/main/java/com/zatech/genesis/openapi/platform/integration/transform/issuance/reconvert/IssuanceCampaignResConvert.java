/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.issuance.reconvert;

import com.zatech.genesis.model.policy.campaign.CampaignInfo;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCampaignInfoResResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCampaignResResponse;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;


@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface IssuanceCampaignResConvert {

    IssuanceCampaignResConvert INSTANCE = Mappers.getMapper(IssuanceCampaignResConvert.class);

    CampaignInfo convert(IssuanceCampaignInfoResResponse campaignInfoResResponse);

    default List<CampaignInfo> convertCampaignList(IssuanceCampaignResResponse campaignInfoResResponse) {
        if (campaignInfoResResponse == null) {
            return null;
        }
        return Optional.ofNullable(campaignInfoResResponse.getIssuanceCampaignInfoList()).orElse(Collections.emptyList())
            .stream().map(IssuanceCampaignResConvert.INSTANCE::convert).toList();
    }

}
