/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.outer.request;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/8/4 15:49
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BaseIndividualPhone extends BaseIndividualExtension {

    @Schema(title = "Phone type")
    private PhoneTypeEnum phoneType;

    @Schema(title = "Country code")
    private String countryCode;

    @Schema(title = "Phone number")
    private String phoneNo;

    @Schema(title = "Extension map")
    private Map<String, String> extensionMap;
}
