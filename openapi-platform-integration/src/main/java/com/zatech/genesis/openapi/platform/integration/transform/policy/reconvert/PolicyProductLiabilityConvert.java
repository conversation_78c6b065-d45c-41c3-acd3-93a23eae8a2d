package com.zatech.genesis.openapi.platform.integration.transform.policy.reconvert;

import com.zatech.genesis.model.policy.product.ProductLiability;
import com.zatech.genesis.model.policy.product.ProductLiabilityBenefit;
import com.zatech.genesis.model.policy.product.ProductLiabilityRiskSa;
import com.zatech.genesis.openapi.platform.integration.policy.base.LiabilityRiskSaCalcBase;
import com.zatech.genesis.openapi.platform.integration.policy.response.PolicyProductLiabilityBenefitResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.PolicyProductLiabilityResponse;
import com.zatech.genesis.openapi.platform.integration.transform.BaseConvert;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, imports = {BooleanToYesNoEnum.class})
public interface PolicyProductLiabilityConvert extends BaseConvert {

    PolicyProductLiabilityConvert INSTANCE = Mappers.getMapper(PolicyProductLiabilityConvert.class);

    ProductLiability convert(PolicyProductLiabilityResponse source);

    @AfterMapping
    default void afterMapping(PolicyProductLiabilityResponse source, @MappingTarget ProductLiability target) {

        List<ProductLiabilityBenefit> liabilityBenefitList = Optional.ofNullable(source.getPolicyProductLiabilityBenefitList()).orElse(Collections.emptyList()).stream().map(this::convert).toList();
        target.getLiabilityBenefitList().addAll(liabilityBenefitList);

        List<ProductLiabilityRiskSa> liabilityRisk = Optional.ofNullable(source.getLiabilityRisk()).orElse(Collections.emptyList()).stream().map(this::convert).toList();
        target.getLiabilityRiskList().addAll(liabilityRisk);

        Optional.ofNullable(source.getPolicyProductLiabilityPremiumList()).orElse(Collections.emptyList()).stream().map(PolicyLiabilityPremiumConvert.INSTANCE::convert).forEach(target.getPremiumDetailList()::add);

        Optional.ofNullable(source.getClaimStackCodeList()).ifPresent(target.getClaimStackCodeList()::addAll);
        Optional.ofNullable(source.getExtensions()).ifPresent(target.getExtensions()::putAll);
    }

    @Mapping(target = "liabilityId", expression = "java(source.getLiabilityId() != null ? Long.valueOf(source.getLiabilityId()) : null)")
    @Mapping(target = "guaranteePeriodType", expression = "java(BooleanToYesNoEnum.convert(source.getGuaranteePeriodType()))")
    @Mapping(target = "extensions", source = "extensions", qualifiedByName = "mapToObjectValues")
    ProductLiabilityBenefit convert(PolicyProductLiabilityBenefitResponse source);

    @Mapping(target = "riskSa", source = "risksa")
    @Mapping(target = "riskCategoryCode", expression = "java(convertOptionalToString(liabilityRiskSaCalcBase.getRiskCategory()))")
    @Mapping(target = "riskSubCategoryCode", expression = "java(convertOptionalToString(liabilityRiskSaCalcBase.getRiskSubCategory()))")
    ProductLiabilityRiskSa convert(LiabilityRiskSaCalcBase liabilityRiskSaCalcBase);

    default String convertOptionalToString(Integer value) {
        return Optional.ofNullable(value).map(Object::toString).orElse(null);
    }

}
