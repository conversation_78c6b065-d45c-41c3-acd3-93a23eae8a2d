/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.campaign.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @author: liyadong002
 * @Date: 2021/7/20 15:26
 * @Description: FreePolicyBase
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(title = "Campaign Cate Free Policy Base")
public class FreePolicyBase implements Serializable {

    private static final long serialVersionUID = -94267222647771718L;

    /**
     * associated good and plan
     */
    @Valid
    @NotNull(message = "freePolicyBasic can't be null")
    @Schema(description = "Basic information of the free policy, including the associated goods and plans.")
    private FreePolicyBasicBase freePolicyBasic;

    /**
     * campaignType and campaignValue
     */
    @Valid
    @NotNull(message = "freePolicyDetail can't be null")
    @Schema(description = "Detailed information of the free policy, such as the campaign type and the campaign value associated with it.")
    private FreePolicyDetailBase freePolicyDetail;
}
