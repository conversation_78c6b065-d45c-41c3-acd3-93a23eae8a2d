/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.request;

import com.zatech.genesis.openapi.platform.integration.policy.base.PolicyInsuredContractBase;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 合同<br/>
 *
 * <AUTHOR> 2017年7月6日 上午11:31:12
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "PolicyInsuredContractRequest", description = "Policy insured contract request")
public class PolicyInsuredContractRequest extends PolicyInsuredContractBase {

}
