package com.zatech.genesis.openapi.platform.integration.pos.model.customer;

import com.zatech.gaia.resource.components.enums.policybatch.RecordTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;
import com.zatech.genesis.openapi.platform.integration.pos.model.base.BaseCommon;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "Company account information", name = "CompanyAccount")
@EqualsAndHashCode(callSuper = false)
public class PosCompanyAccount extends BaseCommon {

    /**
     * pos_case 主键ID
     */
    @Schema(description = "The ID of POS case")
    private Long posCaseId;

    /**
     * pos_transaction_id
     */
    @Schema(description = "The ID of POS transaction")
    private Long posTransactionId;

    /**
     *
     */
    @Schema(description = "The type of record. Such as 1：CHANGE_BEFORE 2：CHANGE_AFTER ")
    private RecordTypeEnum recordType;

    /**
     * 企业支付账号id
     */
    @Schema(description = "The ID of account")
    private Long accountId;

    /**
     * 用户ID
     */
    @Schema(description = "Party ID")
    private Long partyId;

    @Schema(description = "The ID of company")
    private Long companyId;

    /**
     * 银行code
     */
    @Schema(description = "Bank code")
    private String bankCode;

    /**
     * 支行code
     */
    @Schema(description = "Bank branch code")
    private String bankBranchCode;

    /**
     * 银行名称
     */
    @Schema(description = "Bank name")
    private String bankName;

    /**
     * 支行名称
     */
    @Schema(description = "Bank branch name")
    private String bankBranchName;

    /**
     * 账户开户人名
     */
    @Schema(description = "Card holder name")
    private String cardHolderName;

    /**
     * 银行卡
     */
    @Schema(description = "Card number")
    private String cardNumber;

    /**
     *
     */
    @Schema(description = "Account type, refer to the API reference. [Detail](/openx/docs/common/guide/reference/account-type)")
    private AccountTypeEnum accountType;

    /**
     *
     */
    @Schema(description = "The sub type of account")
    private AccountSubTypeEnum accountSubType;

    @Schema(description = "The extension list of POS account")
    private Map<String, String> extensions;

}
