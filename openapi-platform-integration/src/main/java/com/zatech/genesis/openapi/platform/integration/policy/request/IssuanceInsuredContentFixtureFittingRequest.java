/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.request;

import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceInsuredObjectBase;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 建筑内物品标的
 * @date 2022/10/20 11:38
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "IssuanceInsuredContentFixtureFittingRequest", title = "Issuance insured content, fixture and fitting request")
public class IssuanceInsuredContentFixtureFittingRequest extends IssuanceInsuredObjectBase {

    private static final long serialVersionUID = -4569862118627565593L;

    @Schema(title = "The name of insured object")
    private String insuredObjectName;

    @Schema(title = "Estimated Value", description = "data range: (0,999999999999999.99] ")
    private String estimatedValue;

}
