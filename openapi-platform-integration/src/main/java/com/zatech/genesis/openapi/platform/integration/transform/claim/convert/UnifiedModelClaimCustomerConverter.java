/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.claim.convert;

import com.zatech.genesis.model.claim.base.Account;
import com.zatech.genesis.model.claim.base.Customer1;
import com.zatech.genesis.model.claim.customer.AuthorizedParticipant;
import com.zatech.genesis.model.claim.customer.Claimant;
import com.zatech.genesis.model.claim.customer.Payee1;
import com.zatech.genesis.model.claim.incident.VehicleDriver;
import com.zatech.genesis.model.claim.lossparty.LossPartyCustomer;
import com.zatech.genesis.model.claim.lossparty.PropertyOwner;
import com.zatech.genesis.model.claim.lossparty.VehicleOwner;
import com.zatech.genesis.model.policy.customer.Customer;
import com.zatech.genesis.openapi.platform.integration.claim.base.CustomerApiAccountBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.CustomerApiBase;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface UnifiedModelClaimCustomerConverter {

    UnifiedModelClaimCustomerConverter INSTANCE = Mappers.getMapper(UnifiedModelClaimCustomerConverter.class);

    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase convert(Customer1 customer1);

    @Mapping(target = "customerType", constant = "PAYEE_PERSONAL")
    @Mapping(source = "accountList", target = "customerAccountList")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toPayee1(Payee1 payee1);

    @Mapping(source = "paymentMethod", target = "payMethod")
    CustomerApiAccountBase convert(Account account1);

    @Mapping(target = "customerType", constant = "CLAIMANT")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toClaimant(Claimant claimant);

    @Mapping(target = "customerType", constant = "AUTHORIZED_PARTICIPANT")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toAuthorizedParticipant(AuthorizedParticipant authorizedParticipant);

    @Mapping(target = "customerType", constant = "LOSS_PARTY")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toLossPartyCustomer(LossPartyCustomer lossPartyCustomer);

    @Mapping(target = "customerType", constant = "PROPERTY_OWNER")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toPropertyOwner(PropertyOwner propertyOwner);

    @Mapping(target = "customerType", constant = "DRIVER")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toVehicleDriver(VehicleDriver vehicleDriver);

    @Mapping(target = "customerType", constant = "VEHICLE_OWNER")
    @Mapping(source = "certiNo", target = "idNo")
    @Mapping(source = "certiType", target = "idType")
    CustomerApiBase toVehicleOwner(VehicleOwner vehicleOwner);

    @Mapping(target = "customerType", constant = "INSURED")
    @Mapping(source = "name", target = "fullName")
    CustomerApiBase toInsured(Customer customer);

}
