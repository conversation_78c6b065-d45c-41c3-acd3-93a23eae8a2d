package com.zatech.genesis.openapi.platform.integration.market.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/9 14:59
 **/
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(
    name = "PackageBenefitLiabilityBase", description = "Package Benefit Liability Configuration"
)
@Data
public class PackageBenefitLiabilityBase implements Serializable {

    private static final long serialVersionUID = 152348254107223L;

    @Schema(
        description = "The unique identifier generated by the system for liability. A \"liability\" represents an indivisible independent insurance unit used to describe the indemnity liability that the insurer assumes towards the insured. The liabilityId is used to retrieve the business-related configuration of the liability to calculate premiums, refunds, and claim payouts at the liability level."
    )
    private Long liabilityId;

    @Schema(
        description = "The pre-configured sum insured for a liability within a package. Sum insured refers to the maximum amount an insurance company will pay in the event of a covered loss. It is a key factor in determining the premium and the extent of financial protection provided by the insurance policy."
    )
    private String sumInsured;

    @Schema(
        description = "The premium associated with a specific liability. Premium is the payment made by the policyholder to the insurance company for the insurance coverage. It is typically calculated based on the risk assessed for the liability and the coverage terms."
    )
    private String premium;

    @Schema(
        description = "The maximum amount that the insurance company will pay out for a covered claim under a specific liability. It sets the upper limit of the insurer's financial responsibility for a single claim or a series of claims within a defined period."
    )
    private String amountLimit;

    @Schema(
        description = "The sequence number or position of this liability within the package. This number is used to maintain the order of liabilities as they are configured or displayed in the system, especially when a package contains multiple liabilities."
    )
    private Integer orderNo;

    @Schema(
        description = "Additional notes or comments about the liability. This field allows users to add specific details, clarifications, or context related to the liability that might not be captured in other structured fields, providing more comprehensive information about the liability configuration."
    )
    private String liabilityRemark;

    @Schema(
        description = "Descriptive information about the coverage provided by this liability. This field elaborates on what risks are covered and the scope of protection offered under this specific liability, helping users understand the details of the insurance coverage."
    )
    private String liabilityCoverRemarks;

    @Schema(
        description = "Descriptive information or comments related to the premium of this liability. This field may include details on how the premium is calculated, any special premium conditions, or other relevant notes about the liability's premium."
    )
    private String liabilityPremiumRemarks;

    @Schema(
        description = "Descriptive information or comments related to the amount limit of this liability. This field can provide further explanation on how the limit is applied, any conditions affecting the limit, or other relevant details about the liability's coverage limit."
    )
    private String amountLimitRemarks;

}
