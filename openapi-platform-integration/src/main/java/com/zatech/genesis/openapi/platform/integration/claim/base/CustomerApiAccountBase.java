/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.claim.base;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 **/
@Data
@Schema(name = "CustomerApiAccountBase")
public class CustomerApiAccountBase implements Serializable {

    private static final long serialVersionUID = 741642669995319259L;

    /**
     * 支付方式(PayMethodEnum)
     */
    @Schema(description = "Pay method")
    @JsonAlias("paymentMethod")
    private PayMethodEnum payMethod;

    /**
     * /**
     * 支付方式备注
     */
    @Schema(description = "Pay method name")
    @JsonAlias("paymentMethodName")
    private String payMethodName;

    /**
     * 账户类型(AccountTypeEnum)
     */
    @Schema(description = "Account type, refer to the API reference. [Detail]" +
        "(/openx/docs/common/guide/reference/account-type)", type = "string")
    private AccountTypeEnum accountType;

    /**
     * 子账户类型(AccountSubTypeEnum)
     */
    @Schema(description = "Account sub type")
    private AccountSubTypeEnum accountSubType;

    /**
     * 银行CODE
     */
    @Schema(description = "Bank code", maxLength = 64)
    private String bankCode;

    /**
     * 银行名称
     */
    @Schema(description = "Bank name", maxLength = 128)
    private String bankName;

    /**
     * 支付地址
     */
    @Schema(description = "Bank branch address", maxLength = 128)
    private String bankBranchAddress;

    /**
     * 支行code
     */
    @Schema(description = "Store code", maxLength = 64)
    private String storeCode;

    /**
     * 支行名称
     */
    @Schema(description = "Store name", maxLength = 128)
    private String storeName;

    /**
     * 账户开户人名
     */
    @Schema(description = "Account person name", maxLength = 256)
    private String accountPersonName;

    /**
     * 支付账号
     */
    @Schema(description = "Account No.", maxLength = 128)
    private String accountNo;

    /**
     * 过期日期
     */
    @Schema(description = "Expire date")
    private LocalDate expireDate;

    /**
     * 银行所在城市
     */
    @Schema(description = "Bank city", maxLength = 32)
    private String bankCity;

    /**
     * 国际银行账号
     */
    @Schema(description = "IBAN", maxLength = 64)
    private String iban;


    /**
     * 银行地址
     */

    @Schema(description = "Bank address", maxLength = 128)
    private String bankAddress;

}
