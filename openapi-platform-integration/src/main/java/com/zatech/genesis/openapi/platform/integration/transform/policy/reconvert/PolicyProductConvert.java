/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.policy.reconvert;

import com.zatech.genesis.model.policy.product.Product;
import com.zatech.genesis.model.policy.product.ProductFundPlan;
import com.zatech.genesis.model.policy.product.ProductInvestmentPlan;
import com.zatech.genesis.model.policy.product.ProductLiability;
import com.zatech.genesis.openapi.platform.integration.policy.response.PolicyProductFundPlanResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.PolicyProductInvestmentPlanResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.PolicyProductResponse;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, imports = {BooleanToYesNoEnum.class})
public interface PolicyProductConvert {

    PolicyProductConvert INSTANCE = Mappers.getMapper(PolicyProductConvert.class);

    @Mapping(source = "mainId", target = "mainProductId")
    @Mapping(source = "premiumPeriodType", target = "paymentPeriodType")
    @Mapping(source = "premiumPeriod", target = "paymentPeriod")
    @Mapping(source = "premiumFrequency", target = "premiumFrequencyType")
    @Mapping(target = "includingTax", expression = "java(BooleanToYesNoEnum.convert(source.getIncludingTax()))")
    @Mapping(target = "isVirtual", expression = "java(BooleanToYesNoEnum.convert(source.getIsVirtual()))")
    @Mapping(source = "latestRenewalEffectiveDate", target = "latestRenewalEffectiveDate")
    Product convert(PolicyProductResponse source);

    @AfterMapping
    default void afterMapping(
        PolicyProductResponse source,
        @MappingTarget Product target) {

        List<ProductLiability> productLiabilityList = Optional.ofNullable(source.getPolicyProductLiabilityList()).orElse(Collections.emptyList())
            .stream().map(PolicyProductLiabilityConvert.INSTANCE::convert).toList();
        target.getProductLiabilityList().addAll(productLiabilityList);

        Optional.ofNullable(source.getPolicyProductInvestmentPlanList()).orElse(Collections.emptyList())
            .stream().map(this::convert).forEach(target.getProductInvestmentPlanList()::add);
        Optional.ofNullable(source.getPolicyProductPremiumList()).orElse(Collections.emptyList())
            .stream().map(PolicyProductPremiumConvert.INSTANCE::convert).forEach(target.getPremiumDetailList()::add);
    }

    default ProductInvestmentPlan convert(PolicyProductInvestmentPlanResponse policyProductInvestmentPlanResponse) {
        ProductInvestmentPlan productInvestmentPlan = new ProductInvestmentPlan();
        productInvestmentPlan.setProductId(policyProductInvestmentPlanResponse.getProductId());
        productInvestmentPlan.setAmount(policyProductInvestmentPlanResponse.getAmount());
        productInvestmentPlan.setPremiumType(policyProductInvestmentPlanResponse.getPremiumType());
        productInvestmentPlan.setPaymentFrequencyType(policyProductInvestmentPlanResponse.getPaymentFrequencyType());
        productInvestmentPlan.setPaymentFrequencyValue(policyProductInvestmentPlanResponse.getPaymentFrequencyValue());
        productInvestmentPlan.setSingleTopUpType(policyProductInvestmentPlanResponse.getSingleTopUpType());
        productInvestmentPlan.setStartDate(policyProductInvestmentPlanResponse.getStartDate());
        productInvestmentPlan.setEndDate(policyProductInvestmentPlanResponse.getEndDate());
        productInvestmentPlan.setInvestmentPlanStatus(policyProductInvestmentPlanResponse.getInvestmentPlanStatus());
        productInvestmentPlan.setFundAppointmentType(policyProductInvestmentPlanResponse.getFundAppointmentType());
        productInvestmentPlan.setModelPortfolioCode(policyProductInvestmentPlanResponse.getModelPortfolioCode());

        List<ProductFundPlan> productFundPlanList = Optional.ofNullable(policyProductInvestmentPlanResponse.getPolicyProductFundPlanList()).orElse(Collections.emptyList())
            .stream().map(this::convert).toList();
        productInvestmentPlan.getProductFundPlanList().addAll(productFundPlanList);

        Optional.ofNullable(policyProductInvestmentPlanResponse.getExtensions()).ifPresent(e -> productInvestmentPlan.getExtensions().putAll(e));
        return productInvestmentPlan;
    }

    default ProductFundPlan convert(PolicyProductFundPlanResponse policyProductFundPlanResponse) {
        ProductFundPlan productFundPlan = new ProductFundPlan();
        productFundPlan.setProductId(policyProductFundPlanResponse.getProductId());
        productFundPlan.setProductInvestmentPlanId(policyProductFundPlanResponse.getProductInvestmentPlanId());
        productFundPlan.setFundCode(policyProductFundPlanResponse.getFundCode());
        productFundPlan.setFundAllocation(policyProductFundPlanResponse.getFundAllocation());
        productFundPlan.setStartDate(policyProductFundPlanResponse.getStartDate());
        productFundPlan.setEndDate(policyProductFundPlanResponse.getEndDate());
        productFundPlan.setHolderCustomerId(policyProductFundPlanResponse.getHolderCustomerId());
        return productFundPlan;
    }

}
