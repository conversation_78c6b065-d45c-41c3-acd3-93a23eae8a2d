/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.response;

import com.zatech.gaia.resource.biz.MainInsurantRelationEnum;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.RelationshipWithMainInsuredEnum;
import com.zatech.gaia.resource.components.enums.common.UserTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.metadata.InsuredNatureEnum;
import com.zatech.gaia.resource.components.enums.policy.InsuredStatusEnum;
import com.zatech.gaia.resource.components.enums.policy.InsuredTerminationReasonEnum;
import com.zatech.gaia.resource.graphene.customer.CustomerTitleEnum;
import com.zatech.genesis.openapi.platform.integration.metadata.base.DynamicMetaField;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyCustomerAccountComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyCustomerAddressComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyCustomerPhoneComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyEmailComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.base.PolicyAttachmentComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.request.PolicyConsenteeRequest;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 被保险人人 DTO<br/>
 *
 * <AUTHOR> 2017年7月6日 上午11:31:12
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(description = "Policy insurant response")
public class PolicyInsurantResponse extends DynamicMetaField implements Serializable {

    @Serial
    private static final long serialVersionUID = 8071053339927756755L;

    @Schema(
        title = "Serial No",
        description = "A unique serial number assigned to an item or record."
    )
    private String serialNo;

    /**
     * 受益人
     */
    @Schema(title = "Policy beneficiary list")
    private List<PolicyBeneficiaryResponse> policyBeneficiaryList;

    /**
     * 受信人
     */
    @Schema(title = "Policy Trustee list")
    private List<PolicyTrusteeResponse> policyTrusteeList;

    @Schema(title = "Policy consentee list")
    private List<PolicyConsenteeRequest> policyConsenteeList;

    /**
     * 客户告知
     */
    @Schema(title = "Customer declaration list")
    private List<PolicyCustomerDeclarationResponse> customerDeclarationList;

    /**
     * 数据库主键id
     */
    @Schema(title = "Policy insurant ID")
    private Long policyInsurantId;

    /**
     * 投保人客户id
     */
    @Schema(title = "Holder customer ID")
    private Long holderCustomerId;

    /**
     * 与投保人关系
     */
    @Schema(title = "Relationship with policyholder")
    private RelationEnum relationshipWithPolicyholder;

    /**
     * 与投保人关系（兼容旧版本）
     */
    @Schema(title = "Relationship with policyholder")
    private RelationEnum holderRelation;

    /**
     * 被保险人顺序
     */
    @Schema(title = "Insured order")
    private Integer insuredOrder;

    /**
     * 虚拟小孩儿数量
     **/
    @Schema(title = "Virtual kid number")
    private Integer virtualKidNumber;

    /**
     * 虚拟成年人数量
     **/
    @Schema(title = "Virtual adult number")
    private Integer virtualAdultNumber;

    /**
     * 虚拟总数量
     **/
    @Schema(title = "Virtual total number")
    private Integer virtualTotalNumber;

    /**
     * 主被保人关系
     **/
    @Schema(title = "Main insurant relation")
    private MainInsurantRelationEnum mainInsurantRelation;

    /**
     * 保单id
     */
    @Schema(title = "Policy ID")
    private Long policyId;

    /**
     * 投保险种id
     */
    @Schema(title = "Policy product ID")
    private Long policyProductId;

    /**
     * 用户类型1：个人，2：公司
     */
    @Schema(title = "User type")

    private UserTypeEnum userType;

    /**
     * 客户id
     */
    @Schema(title = "Customer ID")
    private Long customerId;

    /**
     * 客户证件号
     */
    @Schema(title = "Certi no")
    private String certiNo;

    /**
     * 客户证件类型
     */
    @Schema(title = "Certi type")

    private CertiTypeEnum certiType;

    /**
     * 生日
     */
    @Schema(title = "Birthday")

    private Date birthday;

    /**
     * 性别
     */
    @Schema(title = "Gender")

    private GenderEnum gender;

    /**
     * 客户姓名
     */
    @Schema(title = "Name")
    private String name;

    /**
     * 姓氏(汉字)
     */
    @Schema(title = "Surname")
    private String surname;

    /**
     * 姓氏(拼音、片假名)
     */
    @Schema(title = "Surname2")
    private String surname2;

    /**
     * 名(汉字)
     */
    @Schema(title = "Given name")
    private String givenName;

    /**
     * 名(拼音、片假名)
     */
    @Schema(title = "Given name2")
    private String givenName2;

    /**
     * 姓(罗马字)
     */
    @Schema(title = "Last name3")
    private String lastName3;

    /**
     * 名(罗马字)
     */
    @Schema(title = "First name3")
    private String firstName3;

    /**
     * 中间名1
     */
    @Schema(title = "Middle name")
    private String middleName;

    /**
     * 中间名2
     */
    @Schema(title = "Middle name2")
    private String middleName2;

    /**
     * 中间名3
     */
    @Schema(title = "Middle name3")
    private String middleName3;

    /**
     * 姓名2
     */
    @Schema(title = "Full name2")
    private String fullName2;

    /**
     * 姓名3
     */
    @Schema(title = "Full name3")
    private String fullName3;

    /**
     * email
     */
    @Schema(title = "Email")

    private List<BasePolicyEmailComponentDTO> email;

    /**
     * 手机号
     */
    @Schema(title = "Mobile phone")
    private List<BasePolicyCustomerPhoneComponentDTO> mobilePhone;

    /**
     * 账户
     */
    @Schema(title = "Account")
    private List<BasePolicyCustomerAccountComponentDTO> account;

    /**
     * 附件列表
     */
    @Schema(title = "Attachment list")
    private List<PolicyAttachmentComponentDTO> attachmentList;

    /**
     * 被保开始时间
     */
    @Schema(title = "Insured effective date")
    private Date insuredEffectiveDate;

    /**
     * 被保结束时间
     */
    @Schema(title = "Insured expiry date")
    private Date insuredExpiryDate;

    /**
     * 被保终止时间
     */
    @Schema(title = "Insured termination date")
    private Date insuredTerminationDate;

    /**
     * 被保终止原因
     */
    @Schema(title = "Insured termination reason")
    private InsuredTerminationReasonEnum insuredTerminationReason;

    /**
     * 被保险人状态
     */
    @Schema(title = "Status")
    private InsuredStatusEnum status;

    @Schema(title = "Relationship with main insured")
    private RelationshipWithMainInsuredEnum relationshipWithMainInsured;

    /**
     * 保单event productid
     */
    @Schema(title = "Policy by event product ID")
    private Long policyByEventProductId;

    /**
     * 保单event ID
     */
    @Schema(title = "Policy by event ID")
    private Long policyByEventId;

    /**
     * 税号
     */
    @Schema(title = "Tax no")
    private String taxNo;

    /**
     * 客户title
     */
    @Schema(title = "Title")
    private CustomerTitleEnum title;

    /**
     * 是否允许被分析
     */
    @Schema(title = "Allow analysis")
    private YesNoEnum allowAnalysis;

    @Schema(title = "Occupation class")
    private String occupationClass;

    @Schema(title = "Insured nature")
    private InsuredNatureEnum insuredNature;

    @Schema(title = "Address")
    private List<BasePolicyCustomerAddressComponentDTO> addressList;

    @Schema(title = "Party response")
    private PartyInfoResponse party;
}
