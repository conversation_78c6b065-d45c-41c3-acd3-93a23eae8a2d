/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.subject.SubjectTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 保单 DTO<br/>
 *
 * <AUTHOR> 2017年7月6日 上午11:31:12
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "BasePolicyInsuredObjectComponentDTO", title = "", description = "保单信息请求参数")
public class BasePolicyInsuredObjectComponentDTO extends BasePolicyCommonComponentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long policyInsuredObjectId;

    /**
     * 标的No
     */
    private String insuredNo;

    /**
     * 标的类型
     */
    private SubjectTypeEnum insuredType;

    private YesNoEnum innerShareFlag;

    /**
     * 保单id
     */
    private Long policyId;

    /**
     * 附件
     */
    private List<PolicyAttachmentComponentDTO> policyInsureAttachList;

    /**
     * 附件
     */
    private List<PolicyUsageComponentDTO> policyUsageList;

    /**
     *
     */
    private List<BasePolicyInsuredDeviceComponentDTO> policyInsuredDeviceComponentList;

}
