/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.query.response;

import com.zatech.gaia.resource.components.enums.product.PremiumDiscountTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * za-wangshixing
 */
@Setter
@Getter
@ToString
@Schema(title = "保费折扣")
public class QueryPolicyProductPremiumDiscountResponse implements Serializable {

    @Schema(title = "保费折扣类型")
    private PremiumDiscountTypeEnum premiumDiscountType;

    @Schema(title = "期缴保费折扣")
    private String periodDiscountPremium;

    @Schema(title = "product discount rate")
    private String discountRate;

}
