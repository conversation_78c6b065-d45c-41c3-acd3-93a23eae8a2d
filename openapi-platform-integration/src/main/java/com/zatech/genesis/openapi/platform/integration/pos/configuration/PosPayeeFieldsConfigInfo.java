/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.pos.configuration;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.schema.LayoutEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PosPayeeFieldsConfigInfo implements Serializable {

    @Schema(title = "Property Code")
    private String propertyCode;

    @Schema(title = "Property Name")
    private String propertyName;

    @Schema(title = "The display Name")
    private String displayName;

    @Schema(title = "Order No")
    private Integer orderNo;

    @Schema(title = "Tenant Dict Key")
    private String bizDictKey;

    @Schema(title = "Editable")
    private YesNoEnum editable;

    @Schema(title = "Required")
    private YesNoEnum required;

    @Schema(title = "Has It Been Selected")
    private YesNoEnum selected = YesNoEnum.NO;

    @Schema(title = "Data Type")
    private LayoutEnum dataType;

    @Schema(title = "Is Extension")
    private YesNoEnum isExtension;

    @Schema(title = "Object Dynamic Schema Extension Items")
    private List<ObjectDynamicSchemaItem> objectDynamicSchemaItems;

}
