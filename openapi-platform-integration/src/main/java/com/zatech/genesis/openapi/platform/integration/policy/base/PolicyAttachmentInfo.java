/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.zatech.gaia.resource.biz.AttachmentRefTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.issuance.AttachmentSourceEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceDraftModuleEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@Schema(name = "PolicyAttachmentInfo", title = "Policy attachment")
public class PolicyAttachmentInfo extends BaseInfo {
    public PolicyAttachmentInfo() {
        this.module = IssuanceDraftModuleEnum.ATTACHMENT;
    }

    @Schema(title = "Attachment scope", description = "1-policy 2-holder 3-insurant")
    private String scope;

    /**
     * Issuance Attachment Primary ID
     */
    private Long issuanceAttachmentId;

    /**
     * ref_id
     */
    private Long refId;

    /**
     * ref_type
     */
    private AttachmentRefTypeEnum refType;

    /**
     * 投保人id
     */
    private Long holderCustomerId;

    /**
     * 附件url
     */
    private String attachmentUrl;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件类型
     */
    private String documentType;

    /**
     * Attachment Info
     */
    private String attachmentInfo;

    /**
     * 附件来源类型
     */
    private AttachmentSourceEnum attachmentSource = AttachmentSourceEnum.NEW_BUSINESS;

    @Schema(title = "Creator")
    private String creator;

    @Schema(title = "Gmt created")
    private Date gmtCreated;

    @Schema(title = "Modifier")
    private String modifier;

    @Schema(title = "Gmt modified")
    private Date gmtModified;

    @Schema(title = "Is deleted")
    private String isDeleted;

    @Schema(title = "Remark")
    private String remark;

    @Schema(title = "Extra info")
    private String extraInfo;

    /**
     * 是否需要更新
     */
    @Schema(title = "Need update")
    private YesNoEnum needUpdate;

    @Schema(title = "Dynamic ref ID")
    private Long dynamicRefId;
}
