/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.market.matrixtable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zatech.genesis.openapi.platform.integration.product.base.formula.SchemaTypeFormulaFactors;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(title = "customer schema fields")
public class CustomerFactorList extends SchemaTypeFormulaFactors {

    /**
     * {@link com.zatech.gaia.resource.components.enums.schema.CustomerTypeEnum}
     */
    @Schema(description = "The categorization of customers as either individual consumers or organizational entities. Valid values: 1-Individual, 2-Organization")
    private Integer customerType;

    /**
     * {@link com.zatech.gaia.resource.components.enums.schema.CustomerSubTypeEnum}
     */
    @Schema(description = "Classification indicating which section the customer information belongs to in customer details. Valid values: 1-Basic Info, 2-Account, 3-Address, 4-Email, 5-Phone, 6-Policyholder, 7-Insured, 8-Beneficiary, 9-Payer, 10-Payee, 11-Claimant")
    private Integer customerSubType;

}