package com.zatech.genesis.openapi.platform.integration.metadata.response;

import com.zatech.genesis.openapi.platform.integration.metadata.base.def.SchemaDef;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/12/28 14:37
 **/
@Schema(
    title = "Schema 类型定义"
)
@Data
@EqualsAndHashCode(callSuper = false)
public class SchemaDefResponse extends SchemaDef {

}
