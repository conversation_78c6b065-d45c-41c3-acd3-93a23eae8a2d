/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.market.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/19 15:02
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "sa premium calc initial period request", name = "SaPremiumCalcInitialPeriodRequest")
public class SaPremiumCalcInitialPeriodRequest extends SaPremiumCalcRequest {

    /**
     * 首单多期时，必填；单期时非必填
     * 首单多期：从第2期开始，根据具体期数获取对应续期相关信息&赋值计算因子中
     */
    @Schema(description = "initial period instalment calculate info")
    private List<InstallmentInfoRequest> initialPeriods;


}
