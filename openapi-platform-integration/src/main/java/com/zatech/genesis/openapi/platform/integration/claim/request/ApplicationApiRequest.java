/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.claim.request;

import com.zatech.gaia.resource.claim.PreAuthorizationCaseEnum;
import com.zatech.gaia.resource.components.enums.schema.ProductCategoryEnum;
import com.zatech.genesis.openapi.platform.integration.claim.base.CustomerApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.DocumentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.IncidentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.LossPartyApiBase;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

@Data
@Schema(name = "ApplicationApiRequest")
public class ApplicationApiRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = -5435277323490199215L;

    /**
     * Policy NO.
     */
    @Schema(description = "Policy No. Required when registration by policy", required = true, maxLength = 50)
    private String policyNo;

    @Schema(description = "Application product category list")
    private List<ProductCategoryEnum> productCategories;

    /**
     * appliedBy
     */
    @NotBlank
    @Schema(description = "Applied By", required = true, maxLength = 50)
    private String appliedBy;

    /**
     * channel code
     */
    @Schema(description = "Channel code", maxLength = 32)
    private String channelCode;

    /**
     * transaction no
     */
    @NotBlank
    @Schema(description = "Transaction No.", required = true, maxLength = 32)
    private String transactionNo;

    /**
     * Application date
     */
    @NotNull
    @Schema(description = "Application date", required = true)
    private Date applicationDate;

    @Schema(description = "The claim amount")
    private String claimAmount;

    /**
     * Application extraInfo
     */
    @Schema(description = "Extension field", maxLength = 500)
    private String extraInfo;

    /**
     * The incident information
     */
    @Valid
    @NotNull
    @Schema(description = "Incident")
    private IncidentApiBase incident;

    /**
     * The claimant particulars (全自动理赔时非必填，默认取保单中被保人信息)
     */
    @Valid
    @Schema(description = "Claimant,default get from policy. Required when registration by person")
    private CustomerApiBase claimant;

    /**
     * The loss parties (全自动理赔时非必填，默认取保单中被保人信息)
     */
    @Valid
    @Schema(description = "Loss parties,default get from policy. Required when registration by person")
    private List<LossPartyApiBase> lossParties;

    /**
     * The insured (全自动理赔时非必填，默认取保单中被保人信息)
     */
    @Valid
    @Schema(description = "Insured,default get from policy")
    @Deprecated(since = "V3.09")
    private CustomerApiBase insured;

    @Schema(description = "The insured list for insurance claim reporting submission")
    private List<CustomerApiBase> insureds;

    /**
     * The payee information (全自动理赔时非必填，默认取保单中受益人信息)
     */
    @Valid
    @Schema(description = "Payees,default get from policy")
    private List<CustomerApiBase> payees;

    /**
     * Authorized participants 授权代理人
     **/
    @Schema(description = "List of case authorized participant for insurance claim reporting submission")
    private List<CustomerApiBase> authorizedParticipants;

    /**
     * The upload documents (全自动理赔时非必填)
     */
    @Valid
    @Schema(description = "Documents")
    private List<DocumentApiBase> documents;

    @Schema(description = "The flag used to indicate whether the claim case is a pre-authorization case: Non-pre-authorized or Pre-authorized or Pre-authorization in Progress.")
    private PreAuthorizationCaseEnum preAuthorizationCase;

}
