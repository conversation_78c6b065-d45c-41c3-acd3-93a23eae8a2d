/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.response;

import com.zatech.gaia.resource.biz.TravelTypeEnum;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.genesis.openapi.platform.integration.policy.base.PolicyInsuredObjectBase;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * travel标的 DTO<br/>
 *
 * <AUTHOR> 2019年12月5日 上午11:31:12
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(title = "", description = "Policy insured travel response")
public class PolicyInsuredTravelResponse extends PolicyInsuredObjectBase {

    /**
     * 主键id
     */
    @Schema(title = "Policy insured travel ID")
    private Long policyInsuredTravelId;

    /**
     * 旅行类型(单次旅行/多次旅行)
     */
    @Schema(title = "Travel type")
    private TravelTypeEnum travelType;

    /**
     * 旅行开始时间
     */
    @Schema(title = "Travel start date")
    private Date travelStartDate;

    /**
     * 旅行结束时间
     */
    @Schema(title = "Travel end date")
    private Date travelEndDate;

    /**
     * 目的地国家
     */
    @Schema(title = "Destination region")
    private String destinationRegion;

    /**
     * 目的地国家
     */
    @Schema(title = "Destination country")
    private CountryNationalityEnum destinationCountry;

    /**
     * 目的地城市
     */
    @Schema(title = "Destination city")
    private String destinationCity;

    /**
     * 目的地（项目灵活自定义）
     */
    @Schema(title = "Destination")
    private String destination;

    /**
     * 目的地时区
     */
    @Schema(title = "Destination time zone")
    private String destinationTimeZone;

    /**
     * 出发国家
     */
    @Schema(title = "Departure country")
    private CountryNationalityEnum departureCountry;

    /**
     * 出发地城市
     */
    @Schema(title = "Departure city")
    private String departureCity;

    /**
     * 出发地（项目灵活自定义）
     */
    @Schema(title = "Departure")
    private String departure;


    /**
     * 出发地时区
     */
    @Schema(title = "Departure time zone")
    private String departureTimeZone;

    /**
     * 平台订单号
     */
    @Schema(title = "Booking number")
    private String bookingNumber;

    /**
     * 订单类型
     */
    @Schema(title = "Travel order type")
    private String travelOrderType;

    /**
     * 订单号
     */
    @Schema(title = "Travel order number")
    private String travelOrderNumber;

    /**
     * 旅行费用
     */
    @Schema(title = "Travel expense")
    private String travelExpense;

    /**
     * 旅行预约时间
     */
    @Schema(title = "Booking time")
    private Date bookingTime;

    /**
     * 旅行社
     */
    @Schema(title = "Travel agency")
    private String travelAgency;

    /**
     * 成人数量
     */
    @Schema(title = "Adult number")
    private Integer adultNumber;

    /**
     * 儿童数量
     */
    @Schema(title = "Children number")
    private Integer childrenNumber;

    /**
     * 老人数量
     */
    @Schema(title = "Senior number")
    private Integer seniorNumber;

    /**
     * 旅行同行者
     */
    @Schema(title = "Fellow traveller")
    private List<PolicyInsuredFellowTravellerResponse> fellowTraveller;

    /**
     * 旅馆
     */
    @Schema(title = "Hotel")
    private List<PolicyInsuredHotelResponse> hotel;

    /**
     * 门票
     */
    @Schema(title = "Ticket")
    private List<PolicyInsuredTicketResponse> ticket;

    /**
     * 交通
     */
    @Schema(title = "Trip")
    private List<PolicyInsuredTripResponse> trip;

    /**
     * 免赔额
     */
    @Schema(title = "Deductible amount")
    private String deductibleAmount;

    /**
     * 门票入园时间
     */
    @Schema(title = "Entry date")
    private Date entryDate;

    /**
     * 旅游门票类型
     */
    @Schema(title = "Ticket type")
    private String ticketType;

    /**
     * 降雨概率
     */
    @Schema(title = "Chance of rain")
    private String chanceOfRain;

    /**
     * 儿童数量
     */
    @Schema(title = "Child number")
    private Integer childNumber;

    /**
     * 提前天数
     */
    @Schema(title = "Days in advance")
    private Integer daysInAdvance;

    /**
     * 最高温度
     */
    @Schema(title = "Max temperature")
    private String maxTemperature;

    /**
     * 订单id
     */
    @Schema(title = "Order ID")
    private String orderId;

    /**
     * 目的地地址
     */
    @Schema(title = "Destination address")
    private String destinationAddress;

    /**
     * 出发地
     */
    @Schema(title = "Departure place")
    private String departurePlace;


    /**
     * 出发地时区
     */
    @Schema(title = "Departure zone ID")
    private String departureZoneId;

    /**
     * 到达地时区
     */
    @Schema(title = "Arrival zone ID")
    private String arrivalZoneId;

    /**
     * 投保人id
     */
    @Schema(title = "Holder customer ID")
    private Long holderCustomerId;

}
