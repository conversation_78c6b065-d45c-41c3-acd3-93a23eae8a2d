/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.query.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 保单应收账单基本信息返回实体
 */
@Getter
@Setter
public class QueryPolicyBcpReceivableBillResponse {

    /**
     * 应收账单明细
     */
    @Schema(title = "应收账单明细")
    private List<QueryPolicyBcpReceivableBillDetailResponse> receivableBillDetailList;

    /**
     * 应收账户信息
     */
    @Schema(title = "应收账户信息")
    private QueryPolicyBcpAccountResponse receivableAccountInfo;

}
