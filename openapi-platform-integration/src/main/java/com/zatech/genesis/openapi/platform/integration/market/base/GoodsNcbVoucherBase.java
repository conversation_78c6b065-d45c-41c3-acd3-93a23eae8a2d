package com.zatech.genesis.openapi.platform.integration.market.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/8 14:20
 **/
@Data
@Schema(name = "GoodsNcbVoucherBase", description = "Goods Ncb Voucher")
@JsonIgnoreProperties(
    ignoreUnknown = true
)
public class GoodsNcbVoucherBase implements Serializable {

    private static final long serialVersionUID = 17730123231L;

    @Schema(
        name = "goodsId",
        description = "The unique identifier generated by the system for insurance goods. A \"goods\" represents an insurance product sold by specifying marketing attributes based on a technical product to meet specific customer needs. It allows users to select a combination of particular coverage according to their business needs and is the smallest sales unit for customers to purchase insurance products."
    )
    private Long goodsId;

    @Schema(
        name = "ncbIssueType",
        description = "The type or method of No Claim Bonus (NCB) issuance. This field likely represents different ways NCB can be issued, such as a percentage discount, a fixed amount, or based on specific conditions. It could be represented by enumerated values, but the specific values are not provided in the current context."
    )
    private Integer ncbIssueType;

    @Schema(
        name = "voucherDefId",
        description = "The unique identifier for a voucher definition. A voucher definition specifies the parameters and rules for a particular type of voucher, such as discount amount, validity period, and target users. This ID links the NCB configuration to a specific voucher definition in the system."
    )
    private Long voucherDefId;

    @Schema(
        name = "targetUserOfVoucher",
        description = "Indicates the target user group for the voucher. This field specifies which customers are eligible to receive and use the voucher associated with the NCB. It could be represented by enumerated values, such as 'New Customers', 'Existing Customers', or specific customer segments. However, the exact enumerated values are not provided in the current context."
    )
    private Integer targetUserOfVoucher;

    @Schema(
        name = "effectivePeriodExtension",
        description = "The duration for which the effective period or validity of the voucher can be extended. This value likely represents the extension period in days, months, or another time unit, allowing for a flexible voucher expiration date based on specific NCB rules or conditions."
    )
    private Integer effectivePeriodExtension;

}
