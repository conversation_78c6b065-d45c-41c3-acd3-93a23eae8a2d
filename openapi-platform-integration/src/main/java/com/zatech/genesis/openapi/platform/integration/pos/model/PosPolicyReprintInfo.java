/*
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.pos.model;

import com.zatech.gaia.resource.components.enums.posonline.PosReprintCategoryEnum;
import com.zatech.genesis.metadata.masking.annotation.MaskingClass;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/25 18:07
 */
@Setter
@Getter
@MaskingClass
@Schema(title = "POS policy reprint information", description = "POS policy reprint information")
public class PosPolicyReprintInfo {

    @Schema(title = "policy reprint category", description = "The category classification for POS policy reprint operations.")
    private PosReprintCategoryEnum policyReprintCategory;

    @Schema(title = "address", description = "The full textual representation of the insured buildings location, including street number, street name, city, state/province, postal code, and country.")
    private String address;

    @Schema(title = "email", description = "An address associated with a policyholder or customer in the POS context, used by insurers for policy-related communications, updates, and service notifications.")
    private String email;
}
