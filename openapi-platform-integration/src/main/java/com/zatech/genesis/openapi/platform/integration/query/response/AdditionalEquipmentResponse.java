/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.query.response;

import com.zatech.genesis.openapi.platform.integration.metadata.base.DynamicMetaField;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * 保单车-附加设备标的返回实体
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Schema(title = "")
public class AdditionalEquipmentResponse extends DynamicMetaField {

    @Schema(title = "Equipment serial no ")
    private String equipmentSerialNo;

    @Schema(title = "Equipment name")
    private String equipmentName;

    /**
     * Equipment price
     */
    private String equipmentPrice;

    /**
     * Equipment description
     */
    private String equipmentDescription;

}
