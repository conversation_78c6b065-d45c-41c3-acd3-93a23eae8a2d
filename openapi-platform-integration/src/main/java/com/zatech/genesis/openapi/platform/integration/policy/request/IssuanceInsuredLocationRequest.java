/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.request;

import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceInsuredObjectBase;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Created by yaoxiaolong, 2022/10/18 12:01
 * <p>
 * location insured
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "IssuanceInsuredLocationRequest", title = "Issuance insured location request")
public class IssuanceInsuredLocationRequest extends IssuanceInsuredObjectBase implements Serializable {

    private static final long serialVersionUID = 4236827464838833763L;

    @Schema(title = "Zip code")
    private String zipCode;

    @Schema(title = "Address1")
    private String address1;

    @Schema(title = "Address2")
    private String address2;

    @Schema(title = "Address3")
    private String address3;

    /**
     * 经度坐标
     */
    @Schema(title = "Longitude")
    private String longitude;

    /**
     * 维度坐标
     */
    @Schema(title = "Latitude")
    private String latitude;

    @Schema(title = "Flood Zone")
    private String floodZone;

    @Schema(title = "Earthquake Zone")
    private String earthquakeZone;

    @Schema(title = "Object Relation List", description = "A list of serial numbers for referenced objects.")
    private List<String> refObjectSerialsNoList;

}
