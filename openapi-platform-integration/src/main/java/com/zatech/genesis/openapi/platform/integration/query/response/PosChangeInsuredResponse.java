/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.query.response;

import com.zatech.gaia.resource.components.enums.subject.SubjectTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * @Description 保全变更保单标的变更信息
 * <AUTHOR>
 * @Date 2020/2/19 19:01
 **/
@Getter
@Setter
@Schema(title = "保全变更保单标的变更信息")
public class PosChangeInsuredResponse implements Serializable {

    @Schema(title = "标的类型")
    private SubjectTypeEnum insuredType;

    @Schema(title = "航班信息变更列表")
    private List<PosChangeInsuredFlightDelayResponse> posChangeInsuredFlightDelayList;

    @Schema(title = "旅行信息变更列表")
    private List<PosChangeInsuredTravelResponse> posChangeInsuredTravelList;

}
