package com.zatech.genesis.openapi.platform.integration.outer;

import com.zatech.genesis.cdc.es.client.search.builder.SearchSourceBuilder;
import com.zatech.genesis.openapi.platform.integration.cdc.request.PolicyRequestDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.EsPageResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyTaskResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.UwTaskResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.issuance.IssuanceResponseDTO;
import com.zatech.genesis.openapi.platform.integration.outer.fallback.FeignCdcFallbackFactory;
import com.zatech.genesis.openapi.platform.integration.outer.response.PolicyInsuredListResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceResponse;
import com.zatech.octopus.module.web.dto.ResultBase;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/3/22 11:09
 **/
@FeignClient(value = "cdc", url = "${za.graphene.feign.cdc}", fallbackFactory = FeignCdcFallbackFactory.class)
public interface IOuterCdcService {

    @PostMapping("/cdc/searchPolicyNewDetail")
    ResultBase<EsPageResponseDTO<PolicyResponseDTO>> searchPolicyNewDetail(PolicyRequestDTO req);

    @PostMapping("/cdc/searchPolicyNewList")
    ResultBase<EsPageResponseDTO<PolicyResponseDTO>> searchPolicyNewList(PolicyRequestDTO req);

    @PostMapping(value = "/api/cdc/query/dsl/policies")
    Page<PolicyResponseDTO> queryPolicy(@RequestBody SearchSourceBuilder builder);

    @PostMapping(value = "/api/cdc/query/dsl/issuances")
    Page<IssuanceResponse> queryIssuance(@RequestBody SearchSourceBuilder builder);

    @PostMapping(value = "/api/cdc/query/dsl/issuances")
    Page<IssuanceResponseDTO> queryIssuanceNew(@RequestBody SearchSourceBuilder builder);

    @PostMapping(value = "/api/cdc/query/dsl/policy-tasks")
    Page<PolicyTaskResponseDTO> queryPolicyTask(@RequestBody SearchSourceBuilder builder);

    @PostMapping(value = "/api/cdc/query/dsl/uw-tasks")
    Page<UwTaskResponseDTO> queryUwTask(@RequestBody SearchSourceBuilder builder);

    @PostMapping(value = "/api/cdc/query/dsl/policy/insurant-list")
    Page<PolicyInsuredListResponse> searchPolicyInsurantList(@RequestBody SearchSourceBuilder req);

}
