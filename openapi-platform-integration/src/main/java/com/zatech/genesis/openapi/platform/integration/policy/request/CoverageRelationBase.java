/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.request;

import com.zatech.gaia.resource.components.enums.policy.CoverageRefTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "CoverageRelationBase")
public class CoverageRelationBase implements Serializable {

    @Schema(title = "Coverage RefType")
    private String serialNo;

    @Schema(title = "Coverage RefType")
    private CoverageRefTypeEnum refType;

    @Schema(title = "Product SerialNo")
    private String productSerialNo;

    @Schema(title = "Liability SerialNo list")
    private List<String> liabilitySerialNos;
}
