/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.zatech.gaia.resource.components.enums.policy.FeeRefTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "PolicyPremiumBase", title = "Policy premium base", description = "Policy premium base")
public class PolicyPremiumBase extends PolicyCommonBase implements Serializable {

    @Schema(title = "Policy premium id")
    private Long policyPremiumId;

    @Schema(title = "Policy id")
    private Long policyId;

    @Schema(title = "Policy event id")
    private Long policyByEventId;

    @Schema(title = "Ref type")
    private FeeRefTypeEnum refType;

    @Schema(title = "Ref id")
    private String refId;

    @Schema(title = "Premium period")
    private Integer periodNo;

    /**
     * ********************************************************************************************************************
     * stamp duty
     * ********************************************************************************************************************
     */
    @Schema(title = "Stamp duty")
    private String stampDuty;

}
