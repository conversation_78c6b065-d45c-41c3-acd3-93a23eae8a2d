/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.claim.base;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import lombok.Data;

@Data
@Schema(name = "VehicleApiBase")
public class VehicleApiBase implements Serializable {

    private static final long serialVersionUID = -5079432558778072786L;

    private Long vehicleId;

    @Schema(description = "Plate No.", maxLength = 64)
    @NotBlank
    private String plateNo;

    @Schema(description = "Vin No.", required = true, maxLength = 64)
    @NotBlank
    private String vinNo;

    @Schema(description = "Engine No.", maxLength = 64)
    @NotBlank
    private String engineNo;

    @Schema(description = "Registration No.", maxLength = 32)
    private String registrationNo;

    @Schema(description = "Car owner name", maxLength = 32)
    private String carOwnerName;

    @Schema(description = "Vehicle make", maxLength = 32)
    private String vehicleMake;

    @Schema(description = "Vehicle model", maxLength = 32)
    private String vehicleModel;

    @Schema(description = "Manufacturing year", maxLength = 16)
    private String manufacturingYear;

    @Schema(description = "Vehicle type", maxLength = 32)
    private String vehicleType;

    @Schema(description = "Vehicle capacity", maxLength = 16)
    private String vehicleCapacity;

    @Schema(description = "Model description", maxLength = 500)
    private String modelDescription;

    @Schema(description = "Accident responsibility ratio", maxLength = 6)
    private String accidentResponsibilityRatio;

    @Schema(description = "Vehicle usage", maxLength = 32)
    private String vehicleUsage;

    @Schema(description = "Purchase price", maxLength = 50)
    private String purchasePrice;

    @Schema(description = "Number of seat", maxLength = 32)
    private String seatingCapity;

    @Schema(description = "Chassis No.", maxLength = 32)
    private String chassisNo;

    @Schema(description = "Main driving area", maxLength = 64)
    private String mainDrivingArea;

    @Schema(description = "Market price", maxLength = 64)
    private String marketPrice;

    @Schema(description = "Vehicle registration area", maxLength = 64)
    private String registrationArea;

    @Schema(description = "Registration date")
    private LocalDate registrationDate;

    @Schema(description = "Extension field", maxLength = 500)
    private String extraInfo;

    @Schema(description = "Vehicle Total Loss")
    private YesNoEnum totalLoss;


    @Schema(description = "Currency")
    private String currency;


    @Schema(description = "Engine power")
    private String enginePower;

    @Schema(description = "Engine volume")
    private String engineVolume;

    @Schema(description = "Number of seat")
    private String numberOfSeat;


    @Schema(description = "Vehicle loss")
    private VehicleLossApiBase vehicleLoss;

    @Schema(description = "Total assessment amount")
    private String totalAssessmentAmount;

    @Schema(description = "VAT rate for vehicle")
    private String vatRate;

    @Schema(description = "Color of vehicle")
    private String color;

    @Schema(description = "Number of doors")
    private String numberOfDoors;

    @Schema(description = "Mileage")
    private String mileage;

    @Schema(description = "Engine type")
    private String engineType;

    @Schema(description = "Engine power unit")
    private String enginePowerUnit;


    @Schema(description = "Vehicle driver list")
    @Valid
    private List<VehicleDriverApiBase> vehicleDriverList;

    @Schema(description = "Vehicle loss list")
    @Valid
    private List<VehicleLossApiBase> vehicleLossList;
}
