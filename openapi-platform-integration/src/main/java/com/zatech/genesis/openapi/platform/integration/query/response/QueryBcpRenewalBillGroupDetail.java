/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.query.response;

import com.zatech.gaia.resource.components.enums.product.ProductTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QueryBcpRenewalBillGroupDetail {

    /**
     * 产品名称
     */
    @Schema(title = "产品名称")
    private String productName;

    /**
     * 主附险
     */
    @Schema(title = "主附险")
    private ProductTypeEnum productType;

    @Schema(title = "每个险种的总金额")
    private String productAmount;

    /**
     * 账单详情
     */
    @Schema(title = "账单详情")
    private List<QueryBcpRenewalBillDetail> bcpRenewalBillDetailList;

}
