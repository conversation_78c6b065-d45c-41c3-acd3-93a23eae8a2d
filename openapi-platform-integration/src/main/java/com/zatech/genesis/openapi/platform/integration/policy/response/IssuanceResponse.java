/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policy.CampaignRefTypeEnum;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.NotificationWithRuleCode;
import com.zatech.genesis.policy.api.response.IssuanceClaimStackConfigResponse;
import com.zatech.genesis.policy.api.response.IssuanceClaimStackTemplateResponse;
import com.zatech.genesis.policy.api.response.IssuanceCoverageRelationResponse;
import com.zatech.genesis.policy.api.response.IssuancePaymentPlanResponse;
import com.zatech.genesis.policy.api.response.IssuanceRelationDetailResponse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

/**
 *
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(title = "Issuance response")
public class IssuanceResponse extends IssuanceBase {

    /**
     * 投保人
     */
    @Schema(title = "Issuance holder")
    private IssuanceHolderResponse issuanceHolder;

    /**
     * 提名人
     */
    @Schema(title = "Issuance nominee")
    private IssuanceNomineeResponse issuanceNominee;

    /**
     * 投保险种
     */
    @Schema(title = "Issuance product list")
    private List<IssuanceProductResponse> issuanceProductList;

    @Schema(title = "Issuance premium list")
    private List<IssuancePremiumResponse> issuancePremiumList;

    /**
     * 标的信息
     */
    @Schema(title = "Issuance insured object")
    private List<IssuanceInsuredObjectResponse> issuanceInsuredObjectList;

    private List<IssuanceInsurantResponse> issuanceInsurantList;

    /**
     * 标的和险种的关联关系表
     */
    @Schema(title = "Issuance insured product rel list")
    private List<IssuanceInsuredProductRelResponse> issuanceInsuredProductRelList;

    /**
     * 付款人信息
     */
    @Schema(title = "Issuance payer list")
    private List<IssuancePayerResponse> issuancePayerList;

    /**
     * 投保单关系
     */
    @Schema(title = "Issuance relation list")
    private List<IssuanceRelationResponse> issuanceRelationList;

    /**
     * 客户是否主动参加计划 opt in 是主动参加 ，opt out 是默认参加、主动退出
     */
    @Schema(title = "Opt in out")
    private String optInOut;

    /**
     * 动态扩展字段
     */
    @Schema(title = "Ext field")
    private List<ExtendFiledResponse> extField;

    @Schema(title = "Notification code list")
    private List<NotificationWithRuleCode> notificationCodeList;

    private List<IssuanceClaimStackResponse> issuanceClaimStackList;

    @Schema(title = "Issuance special agreement list")
    private List<IssuanceSpecialAgreementResponse> issuanceSpecialAgreementList;

    /**
     * Voucher list
     */
    @Schema(title = "Voucher list.")
    private List<IssuanceVoucherResponse> voucherList;

    @Schema(title = "Issuance task info.", description = "The task associated with a policy or transaction.")
    private IssuanceTaskResponse task;

    @Schema(title = "Issuance campaign.")
    private IssuanceCampaignResResponse issuanceCampaign;

    @Schema(title = "Issuance rule decision.", description = "The list of rule decisions associated with the issuance of a policy.")
    private List<IssuanceRuleResponse> issuanceRuleDecisionList;

    private Date proposalDate;

    private Date quoteNeededDate;

    @Schema(title = "Issuance by event list")
    private List<IssuanceByEventResponse> issuanceByEventList;

    @Schema(title = "Issuance clause information")
    private List<IssuanceClauseResponse> clauseInfo;


    @Schema(title = "Workflow graph ID")
    private Long workflowGraphId;


    @Schema(title = "Whether it is renewal，Yes - Renew No - NB")
    private YesNoEnum isRenewalPolicy;

    private String agentCode;

    private String branchCode;

    private String agentPathCode;

    @Schema(title = "Multi currency")
    private Currency multiCurrency;

    @Schema(
        title = "Firsthand Premium Info"
    )
    private String firsthandPremiumInfo;

    @Schema(
        title = "Coverage Relation List"
    )
    private List<IssuanceCoverageRelationResponse> coverageRelationList;

    @Schema(title = "Issuance payment plan list", description = "The plan for premium payments associated with a policy.")
    private List<IssuancePaymentPlanResponse> paymentPlanList;

    @Schema(title = "Issue without payment", description = "Indicates whether the policy can be issued without receiving the premium payment. Valid values: True or False.")
    private YesNoEnum issueWithoutPayment;

    @Schema(title = "Issuance relation detail list.", description = "The list of relation details associated with the issuance of a policy.")
    private List<IssuanceRelationDetailResponse> issuanceRelationDetailList;

    @Schema(title = "User Auth Info", description = "The authentication information of the user associated with an insurance policy.")
    private UserAuthInfoResponse userAuthInfo;

    @Schema(title = "Issuance clause file list", description = "The list of clause files associated with an issuance.")
    private List<IssuanceClauseFileResponse> issuanceClauseFileList;

    @Schema(title = "Claim Stack Template list", description = "The list of claim stack templates in the system.")
    private List<IssuanceClaimStackTemplateResponse> claimStackTemplateList;

    @Schema(title = "Claim Stack Template list", description = "The list of claim stack templates in the system.")
    private List<IssuanceClaimStackConfigResponse> claimStackConfigList;

    /**
     * 获取投保单的保险起期
     */
    public Date queryIssuanceEffectiveDate() {
        if (Objects.isNull(getEffectiveDate()) && CollectionUtils.isNotEmpty(issuanceProductList)) {
            IssuanceProductResponse issuanceproduct = issuanceProductList.stream()
                .filter(x -> null == x.getMainId() || x.getMainId() == 0)
                .sorted(Comparator.comparing(IssuanceProductResponse::getEffectiveDate)).findFirst().orElse(null);
            if (null != issuanceproduct) {
                return issuanceproduct.getEffectiveDate();
            }
        }
        return getEffectiveDate();
    }

    /**
     * 获取投保单的保险止期
     */
    public Date queryIssuanceExpiryDate() {
        if (Objects.isNull(getExpiryDate()) && CollectionUtils.isNotEmpty(issuanceProductList)) {
            IssuanceProductResponse issuanceproduct = issuanceProductList.stream()
                .filter(x -> null == x.getMainId() || x.getMainId() == 0)
                .sorted(Comparator.comparing(IssuanceProductResponse::getExpiryDate).reversed()).findFirst()
                .orElse(null);
            if (null != issuanceproduct) {
                return issuanceproduct.getExpiryDate();
            }
        }
        return getExpiryDate();
    }

    /**
     * 获取投保单 总险种期交保费
     */
    public String queryIssuanceTotalInstallPremium() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalInstallPremium = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getTotalInstallPremium()))
                .map(x -> new BigDecimal(x.getTotalInstallPremium())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalInstallPremium) {
                return totalInstallPremium.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单税后保费
     */
    public String queryIssuanceLeviedPremium() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalLeviedPremium = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getLeviedPremium()))
                .map(x -> new BigDecimal(x.getLeviedPremium())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalLeviedPremium) {
                return totalLeviedPremium.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单保费税额
     */
    public String queryPremiumLevy() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalPremiumLevy = issuanceProductList.stream().filter(x -> StringUtils.isNotBlank(x.getLevy()))
                .map(x -> new BigDecimal(x.getLevy())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalPremiumLevy) {
                return totalPremiumLevy.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单 险种标准体期缴保费
     */
    public String queryIssuancePeriodPremium() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalPeriodPremium = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getPeriodPremium()))
                .map(x -> new BigDecimal(x.getPeriodPremium())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalPeriodPremium) {
                return totalPeriodPremium.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单 针对标准体期加保费
     */
    public String queryIssuanceExtraPremium() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalExtraPremium = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getExtraPremium()))
                .map(x -> new BigDecimal(x.getExtraPremium())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalExtraPremium) {
                return totalExtraPremium.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单 保单管理费
     */
    public String queryIssuancePolicyFee() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalPolicyFee = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getPolicyFee())).map(x -> new BigDecimal(x.getPolicyFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalPolicyFee) {
                return totalPolicyFee.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单 折扣后期缴保费
     */
    public String queryIssuanceDiscountPremium() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalDiscountPremium = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getDiscountPremium()))
                .map(x -> new BigDecimal(x.getDiscountPremium())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalDiscountPremium) {
                return totalDiscountPremium.toString();
            }
        }
        return null;
    }

    /**
     * 获取投保单 优惠的保费金额
     */
    public String queryIssuanceDiscountedPremium() {
        if (CollectionUtils.isNotEmpty(issuanceProductList)) {
            BigDecimal totalDiscountedPremium = issuanceProductList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getDiscountedPremium()))
                .map(x -> new BigDecimal(x.getDiscountedPremium())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (null != totalDiscountedPremium) {
                return totalDiscountedPremium.toString();
            }
        }
        return null;
    }


    /**
     * 当前应缴费金额
     *
     * @return
     */
    @JsonIgnore
    public BigDecimal getActualPremium() {
        BigDecimal periodFinalPremium = this.getIssuanceProductList().stream()
            .map(IssuanceProductResponse::getIssuanceProductPremiumList)
            .filter(Objects::nonNull).flatMap(List::stream)
            .map(com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceProductPremiumResponse::getPeriodFinalPremium)
            .filter(StringUtils::isNotBlank).map(BigDecimal::new)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal stampDutyAfterDiscount = Optional.ofNullable(this.getIssuanceCampaign())
            .map(IssuanceCampaignResResponse::getIssuanceCampaignInfoList)
            .map(l -> ListUtils.emptyIfNull(l).stream()
                .map(com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCampaignInfoResResponse::getIssuanceCampaignDetailList)
                .filter(Objects::nonNull).flatMap(List::stream)
                .map(com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCampaignDetailResResponse::getIssuanceCampaignDiscount)
                .filter(Objects::nonNull).flatMap(List::stream)
                .filter(x -> CampaignRefTypeEnum.POLICY.equals(x.getRefType()))
                .map(com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCampaignDiscountResponse::getStampDutyAfterDiscount)
                .filter(StringUtils::isNotBlank).map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
            )
            .orElse(BigDecimal.ZERO);
        return periodFinalPremium.add(stampDutyAfterDiscount);
    }

}
