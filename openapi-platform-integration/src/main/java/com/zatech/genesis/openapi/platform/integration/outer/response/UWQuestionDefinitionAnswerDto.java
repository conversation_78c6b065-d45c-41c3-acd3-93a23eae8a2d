package com.zatech.genesis.openapi.platform.integration.outer.response;

import com.zatech.genesis.openapi.platform.integration.outer.request.AnswerDefinition;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/10 19:49
 */
@Data
public class UWQuestionDefinitionAnswerDto {

    private QuestionDefinition uwQuestionDefinitionDto;

    private AnswerDefinition uwQuestionAnswerDto;

    private final List<UWQuestionDefinitionAnswerDto> nextQuestionList = new ArrayList<>();

}
