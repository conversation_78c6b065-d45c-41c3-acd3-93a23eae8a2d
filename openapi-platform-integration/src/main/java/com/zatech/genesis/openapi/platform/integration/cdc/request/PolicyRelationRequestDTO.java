/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.cdc.request;


import com.zatech.genesis.openapi.platform.integration.cdc.model.base.BasePolicyRelationDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: jiangyiyuan
 * @Date: 2020/12/4 10:56
 * @Description:
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
public class PolicyRelationRequestDTO extends BasePolicyRelationDTO {

    private static final long serialVersionUID = 3320010553354573834L;

}
