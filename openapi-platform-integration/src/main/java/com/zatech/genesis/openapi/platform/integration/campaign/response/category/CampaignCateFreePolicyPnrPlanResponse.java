/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.campaign.response.category;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zatech.gaia.resource.graphene.market.campaign.CampaignFreePolicyNotPurchaseTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021-07-21 11:46
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "CampaignCateFreePolicyPnrPlanResponse", title = "Free policy plan response")
public class CampaignCateFreePolicyPnrPlanResponse {

    @Schema(description = "The unique identifier generated by the system for a coverage plan. A 'coverage plan' comprises one or more packages (Tech Products) with some pre-defined marketing attributes and some of them can be redefined.")
    private Long planId;

    @Schema(description = "The type of free policy when a purchase is not made in a campaign. Values are defined in Configuration Center > Tenant Data Configuration > Marketing Enumerated Values > Campaign Free Policy Not Purchase Type. Valid values might include:  'Discount', 'Free Coverage', 'Gift', or others depending on the system configuration.")
    private CampaignFreePolicyNotPurchaseTypeEnum freeType;

    @Schema(description = "The list of products associated with the free policy campaign plan. 'Product' represents a specific type of insurance policy or coverage offered by an insurance company to protect individuals, businesses, or other entities against certain risks.")
    private List<CampaignCateFreePolicyPnrProductResponse> products;
}
