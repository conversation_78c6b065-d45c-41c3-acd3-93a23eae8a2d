/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.outer.response;


import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema
public class UwOutcomeResDTO {

    @Schema(title = "申请单号")
    private String applyNo;

    @Schema(title = "是否结束")
    private boolean isCompleted;

    @Schema(title = "结论标签，key:结论，value:标签")
    private Map<String, List<ReferenceLabelDTO>> conclusionLabel;

}
