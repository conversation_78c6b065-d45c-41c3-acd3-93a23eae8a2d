package com.zatech.genesis.openapi.platform.integration.calculate.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/28 14:16
 **/
@Schema(title = "Schema Formula Calculate Response")
@Data
public class SchemaFormulaCalculateResponse implements Serializable {

    private static final long serialVersionUID = -130053205205766L;

    @Schema(
        title = "uuid"
    )
    private String uid;

    @Schema(
        title = "formula code"
    )
    private String formulaCode;

    @Schema(
        title = "final result"
    )
    private String finalResult;

    @Schema(
        title = "steps' result"
    )
    private Map<String, Object> resultMap;

    @Schema(
        title = "final results"
    )
    private Map<String, Object> finalResultMap;

}
