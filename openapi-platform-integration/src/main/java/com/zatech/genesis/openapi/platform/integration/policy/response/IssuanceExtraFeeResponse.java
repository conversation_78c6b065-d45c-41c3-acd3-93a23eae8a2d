/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.response;

import com.zatech.gaia.resource.components.enums.policy.FeeRefTypeEnum;
import com.zatech.gaia.resource.components.enums.product.AdditionalTypeEnum;
import com.zatech.gaia.resource.components.enums.product.LoadingMethodEnum;
import com.zatech.gaia.resource.components.enums.underwriting.UwLoadingPeriodEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(title = "Issuance extra fee response")
public class IssuanceExtraFeeResponse implements Serializable {

    private static final long serialVersionUID = -6053316828202397474L;

    @Schema(title = "Issuance ID")
    private Long issuanceId;

    @Schema(title = "Product ID or liability ID")
    private Long refId;

    @Schema(title = "Holder customer ID")
    private Long holderCustomerId;

    @Schema(title = "Extra fee type")
    private FeeRefTypeEnum refType;

    @Schema(title = "Period premium")
    private String periodPremium;

    /**
     * 期缴-加费（含税）
     */
    @Schema(title = "Period premium(tax)")
    private String periodPremiumTax;

    /**
     * 年化-加费（不含税）
     */
    @Schema(title = "Annual premium")
    private String annualPremium;

    /**
     * 年化-加费（含税）
     */
    @Schema(title = "Annual premium tax")
    private String annualPremiumTax;

    /**
     * 加费年限
     */
    @Schema(title = "Loading period")
    private Integer loadingPeriod;


    /**
     * The type of loading
     */
    @Schema(title = "The type of loading")
    private AdditionalTypeEnum loadingType;

    /**
     * The method of loading
     */
    @Schema(title = "The method of loading")
    private LoadingMethodEnum loadingMethod;

    /**
     * The value to be calculated based on the loading method
     */
    @Schema(title = "The value to be calculated based on the loading method")
    private String loadingValue;

    /**
     * The type of loading period
     */
    @Schema(title = "The type of loading period")
    private UwLoadingPeriodEnum loadingPeriodType;

    @Schema(title = "Extra rate")
    private String extraRate;

}
