/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.market.matrixtable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zatech.genesis.openapi.platform.integration.product.base.formula.SchemaTypeFormulaFactors;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(title = "policy schema field List")
public class PolicyFactorList extends SchemaTypeFormulaFactors {

}
