package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.zatech.gaia.resource.customer.label.LabelActionType;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 投保人标签
 * <AUTHOR>
 * @Date Created in 16:35 上午 2022/02/28
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "IssuanceLabelEntity")
public class IssuanceLabelEntity extends BaseIssuanceCommonEntity {

    @Schema(title = "操作类型")
    private LabelActionType actionType;

    @Schema(title = "自然人/企业标签表主键ID，用于数据更新")
    private Long labelId;

    @Schema(title = "标签CODE")
    private String code;

    @Schema(title = "标签名")
    private String name;

    @Schema(title = "自然人ID或企业ID")
    private Long personId;

    @Schema(title = "party 类型: 1-个人; 2-组织")
    private Long partyId;
}
