/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.issuance;

import com.zatech.gaia.resource.components.enums.common.FeeTypeEnum;
import com.zatech.genesis.model.policy.Policy;
import com.zatech.genesis.model.policy.base.PolicyRelationDetail;
import com.zatech.genesis.model.policy.base.fee.Commission;
import com.zatech.genesis.model.policy.base.fee.PolicyTax;
import com.zatech.genesis.model.policy.base.fee.PremiumDetail;
import com.zatech.genesis.model.policy.base.fee.ServiceFee;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuancePremiumResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceResponse;
import com.zatech.genesis.openapi.platform.integration.transform.converts.ClauseFileConvert;
import com.zatech.genesis.openapi.platform.integration.transform.converts.CurrencyConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.ClaimStackConfigConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.ClaimStackTemplateConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.UserAuthInfoConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.reconvert.IssuanceCampaignResConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.reconvert.IssuancePaymentPlanConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.reconvert.IssuanceProductConvert;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;
import com.zatech.genesis.policy.api.response.IssuanceRelationDetailResponse;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, imports = {BooleanToYesNoEnum.class, CurrencyConvert.class, Optional.class, Collections.class, IssuanceProductConvert.class, IssuancePaymentPlanConvert.class, IssuanceCampaignResConvert.class, UserAuthInfoConvert.class},
    uses = {ClauseFileConvert.class, ClaimStackConfigConvert.class, ClaimStackTemplateConvert.class})
public interface IssuanceResponseConverter {

    IssuanceResponseConverter INSTANCE = Mappers.getMapper(IssuanceResponseConverter.class);

    @Mapping(target = "issueWithoutPayment", expression = "java(BooleanToYesNoEnum.convert(issuanceResponse.getIssueWithoutPayment()))")
    @Mapping(target = "issueDate", source = "proposalDate")
    @Mapping(target = "currency", expression = "java(CurrencyConvert.INSTANCE.convert(issuanceResponse.getMultiCurrency()))")
    @Mapping(target = "isRenewable", expression = "java(BooleanToYesNoEnum.convert(issuanceResponse.getIsRenewalPolicy()))")
    @Mapping(target = "zoneId", source = "zoneId")
    @Mapping(target = "goodsName", source = "goodsName")
    @Mapping(target = "effectiveDate", source = "effectiveDate")
    @Mapping(target = "expiryDate", source = "expiryDate")
    @Mapping(target = "taxExemption", expression = "java(BooleanToYesNoEnum.convert(issuanceResponse.getTaxExemption()))")
    @Mapping(target = "premium", source = "premium")
    @Mapping(target = "isRenewalPolicy", expression = "java(BooleanToYesNoEnum.convert(issuanceResponse.getIsRenewalPolicy()))")
    @Mapping(target = "applicationDate", source = "insureDate")
    @Mapping(target = "thirdPartyTransactionNo", source = "bizApplyNo")
    @Mapping(target = "planCode", source = "goodsPlanCode")
    @Mapping(target = "proposalStatus", source = "issuanceStatus")
    @Mapping(target = "proposalNo", source = "issuanceNo")
    @Mapping(target = "goodsPlanId", source = "goodsPlanId")
    @Mapping(target = "productList", expression = "java(Optional.ofNullable(issuanceResponse.getIssuanceProductList()).orElse(Collections.emptyList()).stream().map(IssuanceProductConvert.INSTANCE::convert).toList())")
    @Mapping(target = "premiumDetailList", expression = "java(IssuanceResponseConverter.INSTANCE.toPremiumDetailList(issuanceResponse.getIssuancePremiumList()))")
    @Mapping(target = "paymentPlan", expression = "java(IssuancePaymentPlanConvert.INSTANCE.convert(issuanceResponse.getPaymentPlanList()))")
    @Mapping(target = "campaignList", expression = "java(IssuanceCampaignResConvert.INSTANCE.convertCampaignList(issuanceResponse.getIssuanceCampaign()))")
    @Mapping(target = "relationDetailList", expression = "java(IssuanceResponseConverter.INSTANCE.toRelationDetailList(issuanceResponse.getIssuanceRelationDetailList()))")
    @Mapping(target = "clauseFileList", source = "issuanceClauseFileList")
    @Mapping(target = "issuanceRelationList", source = "issuanceRelationList")
    Policy toPolicy(IssuanceResponse issuanceResponse);

    default List<PremiumDetail> toPremiumDetailList(List<IssuancePremiumResponse> issuancePremiumList) {
        return Optional.ofNullable(issuancePremiumList)
            .orElse(Collections.emptyList())
            .stream()
            .map(this::convert)
            .toList();
    }

    default PremiumDetail convert(IssuancePremiumResponse issuancePremiumResponse) {
        PremiumDetail premiumDetail = new PremiumDetail();
        premiumDetail.setPeriodNo(issuancePremiumResponse.getPeriodNo());
        premiumDetail.setStampDuty(issuancePremiumResponse.getStampDuty());

        Optional.ofNullable(issuancePremiumResponse.getFeeDetailList())
            .orElse(Collections.emptyList())
            .stream()
            .filter(e -> FeeTypeEnum.BASIC_SERVICE_FEE_RECEIVABLE_AR.getCode().equals(e.getFeeType().getCode()))
            .map(e -> {
                ServiceFee serviceFee = new ServiceFee();
                serviceFee.setAmount(e.getFeeAmount());
                return serviceFee;
            })
            .forEach(premiumDetail.getServiceFeeList()::add);

        Optional.ofNullable(issuancePremiumResponse.getFeeDetailList())
            .orElse(Collections.emptyList())
            .stream()
            .filter(e -> FeeTypeEnum.BASIC_COMMISSION_RECEIVABLE_AR.getCode().equals(e.getFeeType().getCode()))
            .map(e -> {
                Commission commission = new Commission();
                commission.setAmount(e.getFeeAmount());
                return commission;
            })
            .forEach(premiumDetail.getCommissionList()::add);

        Optional.ofNullable(issuancePremiumResponse.getFeeDetailList())
            .orElse(Collections.emptyList())
            .stream()
            .filter(e -> FeeTypeEnum.STAMP_DUTY_AR.getCode().equals(e.getFeeType().getCode()) ||
                FeeTypeEnum.POLICY_LEVY.getCode().equals(e.getFeeType().getCode()))
            .map(e -> {
                PolicyTax policyTax = new PolicyTax();
                policyTax.setAmount(e.getFeeAmount());
                policyTax.setFeeType(e.getFeeType());
                return policyTax;
            })
            .forEach(premiumDetail.getTaxList()::add);

        return premiumDetail;
    }

    default List<PolicyRelationDetail> toRelationDetailList(List<IssuanceRelationDetailResponse> issuanceRelationDetailResponses) {
        return Optional.ofNullable(issuanceRelationDetailResponses)
            .orElse(Collections.emptyList())
            .stream()
            .map(this::convertRelationDetail)
            .toList();
    }

    default PolicyRelationDetail convertRelationDetail(IssuanceRelationDetailResponse issuanceRelationDetailResponse) {
        PolicyRelationDetail policyRelationDetail = new PolicyRelationDetail();
        policyRelationDetail.setRelationPolicyNo(issuanceRelationDetailResponse.getRelationPolicyNo());
        return policyRelationDetail;
    }
}
