/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.cdc.model.base;

import com.zatech.gaia.resource.components.enums.common.EmailTypeEnum;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: za-zhangwenlei
 * @Description:
 * @Date: Created in 10:41 2020/5/18
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
public class BasePolicyCompanyEmailDTO extends BasePolicyCommonDTO {

    /**
     * 邮箱id
     */
    private Long emailId;

    /**
     * 用户id
     */
    private Long partyId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话大类
     */
    private EmailTypeEnum emailType;

}
