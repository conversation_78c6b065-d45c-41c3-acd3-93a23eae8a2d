/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.cdc.response;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.genesis.openapi.platform.integration.cdc.model.base.BasePolicyInsuredObjectDTO;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by yaoxiaolong, 2022/2/21 18:08
 * <p>
 * description
 */
@Getter
@Setter
public class PolicyInsuredOrderSupportResponseDTO extends BasePolicyInsuredObjectDTO {

    private Long policyInsuredOrderId;

    /**
     * 平台订单号
     */
    private String bookingNumber;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 票类型
     */
    private String orderType;

    /**
     * 票数
     */
    private Integer numberOfOrder;

    /**
     * 下单时间
     */
    private Date orderDate;

    /**
     * 订单币种
     */
    private CurrencyEnum orderCurrency;

    /**
     * 订单金额
     */
    private String orderPrice;

    /**
     * 生效时间
     */
    private Date effectiveDate;

    /**
     * 生效时间时区
     */
    private String effectiveDateTimeZone;

    /**
     * 终止时间
     */
    private Date expiryDate;

    /**
     * 终止时间时区
     */
    private String expiryDateTimeZone;

}
