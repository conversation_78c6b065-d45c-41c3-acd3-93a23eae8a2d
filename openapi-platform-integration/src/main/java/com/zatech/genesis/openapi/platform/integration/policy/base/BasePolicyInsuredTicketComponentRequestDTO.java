/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.base;


import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "BasePolicyInsuredTicketComponentRequestDTO")
public class BasePolicyInsuredTicketComponentRequestDTO extends BasePolicyInsuredObjectComponentDTO implements Serializable {

    @Schema
    private Long policyInsuredTicketId;

    @Schema(title = "门票类型")
    private String ticketType;

    @Schema(title = "门票号")
    private String ticketNumber;

    @Schema(title = "进入景点时间")
    private Date entryTime;

    @Schema(title = "门票价格")
    private String ticketPrice;

    @Schema(title = "游玩地点")
    private String placeOfInterest;

}
