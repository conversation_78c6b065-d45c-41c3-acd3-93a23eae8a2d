package com.zatech.genesis.openapi.platform.integration.pos.model.customer;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policybatch.RecordTypeEnum;
import com.zatech.genesis.openapi.platform.integration.pos.model.base.BaseCommon;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "Company email information", name = "CompanyEmail")
@EqualsAndHashCode(callSuper = false)
public class PosCompanyEmail extends BaseCommon {

    /**
     * pos_case 主键ID
     */
    @Schema(description = "The identification of POS case")
    private Long posCaseId;

    /**
     * pos_transaction_id
     */
    @Schema(description = "The identification of POS transaction")
    private Long posTransactionId;

    /**
     *
     */
    @Schema(description = "The type of record. Such as 1：CHANGE_BEFORE 2：CHANGE_AFTER ")
    private RecordTypeEnum recordType;

    /**
     * 用户ID
     */
    @Schema(description = "User ID")
    private Long partyId;

    @Schema(description = "The ID of company")
    private Long companyId;

    /**
     * 客户邮箱ID
     */
    @Schema(description = "The identification of customer email")
    private Long emailId;

    /**
     * 电子邮箱
     */
    @Schema(description = "Email address")
    private String email;

    @Schema(description = "The extension list of POS email")
    private Map<String, String> extensions;

    @Schema(description = "Is default")
    private YesNoEnum isDefault;

}
