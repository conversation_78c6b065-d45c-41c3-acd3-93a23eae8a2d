/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.cdc.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policy.LiablilityStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: com.zhongan.graphene.cdc.scenario.share.dto.response.policy.PolicyProductLiabilityEsResponseDTO
 * @Description 保单信息返回对象--险种责任
 * <AUTHOR>
 * @Date 2018/11/5 17:04
 */
@Setter
@Getter
@Schema(title = "保单信息返回对象--险种责任")
@JsonIgnoreProperties(ignoreUnknown = true)
public class PolicyProductLiabilityEsResponseDTO implements Serializable {

    private static final long serialVersionUID = -6039601025469341438L;

    /**
     * 保单ID
     */
    @Schema(title = "保单ID")
    private Long policyId;

    /**
     * 险种ID
     */
    @Schema(title = "投保险种id")
    private Long policyProductId;

    /**
     * 保单险种责任ID
     */
    @Schema(title = "保单险种责任ID（数据库主键id）")
    private Long policyProductLiabilityId;

    /**
     * 责任ID
     */
    @Schema(title = "责任id")
    private Long liabilityId;

    /**
     * 投保份数
     */
    @Schema(title = "投保份数")
    private Long unit;

    /**
     * 保额
     */
    @Schema(title = "保额")
    private String sumInsured;

    /**
     * 责任标准体期缴保费
     */
    @Schema(title = "责任标准体期缴保费")
    private String periodPremium;

    /**
     * 责任年化保费
     */
    @Schema(title = "责任年化保费")
    private String annualPremium;

    /**
     * 投保人客户id
     */
    @Schema(title = "投保人客户id")
    private Long holderCustomerId;

    /**
     * 是否可选：1-是；2-否
     */
    @Schema(title = "是否可选 1:是 2：否")
    private YesNoEnum canOption;

    /**
     * 是否核保通过：1-是；2-否
     */
    @Schema(title = "是否核保通过 1:是 2：否 ")
    private YesNoEnum canUnderwrite;

    /**
     * 责任状态 详见 LiabilityStatusEnum
     */
    @Schema(title = "责任状态")
    private LiablilityStatusEnum status;

}
