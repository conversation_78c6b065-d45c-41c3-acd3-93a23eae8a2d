/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.notification.response.pendingcase;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by yaoxiaolong, 2023/10/17 18:41
 * <p>
 * description
 */
@Setter
@Getter
public class HospitalReceiver {

    @Schema(hidden = true, description = "The system-generated unique identifier for a hospital, used to uniquely identify and retrieve the details of the hospital within the system. Hospitals in insurance context typically refer to medical institutions collaborating with insurers to provide medical services for policyholders.")
    private Long hospital;

    /**
     * openapi 兼容v3字段命名规范
     */
    @Schema(description = "The system-generated unique identifier for a hospital, used to uniquely identify and retrieve the details of the hospital within the system. Hospitals in insurance context typically refer to medical institutions collaborating with insurers to provide medical services for policyholders.")
    private Long hospitalId;

    public void setHospital(Long hospital) {
        this.hospital = hospital;
        this.hospitalId = hospital;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
        this.hospital = hospitalId;
    }

}
