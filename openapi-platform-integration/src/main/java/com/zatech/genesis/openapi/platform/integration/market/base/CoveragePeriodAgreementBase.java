package com.zatech.genesis.openapi.platform.integration.market.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/9 14:58
 **/
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(
    name = "CoveragePeriodAgreementBase", description = "Coverage Period Agreement"
)
@Data
public class CoveragePeriodAgreementBase implements Serializable {

    private static final long serialVersionUID = 82348254107223L;

    @Schema(
        description = "Coverage period agreement type CoveragePeriodTypeEnum"
    )
    private Integer coveragePeriodAgreementType;

    @Schema(
        description = "Value type CoveragePeriodValueTypeEnum"
    )
    private Integer valueType;

    @Schema(
        description = "Minimum coverage period value"
    )
    private Integer minPeriod;

    @Schema(
        description = "Maximum coverage period value"
    )
    private Integer maxPeriod;

    @Schema(
        description = "Coverage cycle type"
    )
    private Integer coverageTimeAgreementType;

    @Schema(
        description = "Expiry date calculation method"
    )
    private Integer expiryDateCalMethod;

}
