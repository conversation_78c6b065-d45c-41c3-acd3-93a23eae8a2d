package com.zatech.genesis.openapi.platform.integration.market.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsSalesPartnerCombineResponse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/7 18:38
 **/
@Data
@JsonIgnoreProperties(
    ignoreUnknown = true
)
@Schema(
    name = "GoodsSalesAttributesBase", description = "Goods Sales Attributes"
)
public class GoodsSalesAttributesBase implements Serializable {

    private static final long serialVersionUID = -2234178254107228563L;

    @Deprecated
    @Schema(
        description = "Sales Channel Information", hidden = true
    )
    private List<GoodsSalesChannelBase> salesChannels;

    @Schema(
        name = "salesPartnerList",
        description = "The list of sales partners associated with the goods. Sales partners are entities or organizations that collaborate in the sales and distribution of insurance goods."
    )
    private List<GoodsSalesPartnerBase> salesPartnerList;

    @Schema(
        name = "salesPartnerFormulaList",
        description = "The list of formulas associated with sales partners for the goods. These formulas define specific calculations or rules that apply to sales partners, potentially for commission, incentives, or performance metrics."
    )
    private List<GoodsSalesPartnerFormulaBase> salesPartnerFormulaList;

    @Schema(
        name = "salesPosRefundVoucherList",
        description = "The list of POS (Point of Sale) refund vouchers applicable to the goods. These vouchers are used for processing refunds at the point of sale, potentially for returned goods or adjustments."
    )
    private List<GoodsPosRefundVoucherBase> goodsPosRefundVoucherList;

    @Schema(
        name = "goodsNcbVoucher",
        description = "The No Claim Bonus (NCB) voucher associated with the goods. NCB is a discount or reward offered to policyholders for not making claims during a policy period. This voucher likely manages or applies NCB benefits."
    )
    private GoodsNcbVoucherBase goodsNcbVoucher;

    @Schema(
        description = "The time zone for the goods, used to define time-related settings and operations in a specific geographical region. For example, Asia/Shanghai. It follows the IANA time zone database standard."
    )
    private String zoneId;

    @Schema(
        description = "The time when the goods start to be displayed or become visible in the sales system or frontend."
    )
    private Date showStartTime;

    @Schema(
        description = "The time when the goods stop being displayed or are no longer visible in the sales system or frontend."
    )
    private Date showEndTime;

    @Schema(
        description = "A flag indicating whether the goods are currently on sale. 1: Yes, the goods are available for sale; 2: No, the goods are not available for sale."
    )
    private Integer isOnSale;

    @Schema(
        description = "The time when the sale of the goods officially starts."
    )
    private Date saleStartTime;

    @Schema(
        description = "The time when the sale of the goods officially ends."
    )
    private Date saleEndTime;

    @Schema(
        description = "The maximum number of times these goods can be sold in total. It sets a limit on the overall sales quantity."
    )
    private Integer maxSalesCount;

    @Schema(
        description = "A flag indicating whether the wholesale warning function is enabled for these goods. 1: Yes, wholesale warning is enabled; 2: No, wholesale warning is disabled."
    )
    private Integer enableWholesaleWarning;

    @Schema(
        description = "The threshold number that triggers a wholesale warning. When sales reach or exceed this number, a warning is activated, potentially to alert about bulk purchases."
    )
    private Long wholesaleWarningNumber;

    @Schema(
        description = "A list of trigger points associated with the goods. Trigger points are specific events or conditions that initiate certain actions or processes related to the goods, such as price changes or promotional events."
    )
    private List<String> triggerPoints;

    @Schema(
        description = "The target audience for the goods, specified as a comma-separated string. It defines the demographic or customer segments that these goods are designed for."
    )
    private String suitCrowd;

    @Schema(
        description = "A flag indicating whether mutual assurance is supported for these goods. 1: Yes, mutual assurance is supported; 2: No, mutual assurance is not supported. Mutual assurance is a type of insurance where members pool their premiums to cover each other's losses."
    )
    private Integer isCalculationCycle;

    @Schema(
        description = "The cut-off date for mutual assurance related to these goods. This date might represent the deadline for enrollment or the expiry date for mutual assurance benefits."
    )
    private Integer cutOffDate;

    @Schema(
        description = "The cut-off time for mutual assurance related to these goods. This time, combined with the cut-off date, specifies the exact deadline for mutual assurance processes."
    )
    private String cutOffTime;

    @Schema(
        description = "The period during which the goods are publicly displayed or advertised. It defines the duration of visibility for marketing purposes."
    )
    private Integer publicDisplayPeriod;

    @Schema(
        description = "A flag indicating whether underwriting is allowed for these goods. 1: Yes, underwriting is allowed; 2: No, underwriting is not allowed. Underwriting is the process of assessing and assuming risk."
    )
    private YesNoEnum enableUnderWriting;

    @Schema(
        description = "The type of underwriting process applied to these goods. It specifies the methodology or category of underwriting used, such as manual or automated underwriting."
    )
    private String underWritingType;

    @Schema(
        description = "The platform or system used for underwriting issuance, defining the order or process flow for underwriting. UwIssuePlatformEnum refers to the enumerated values for Underwriting Issue Platform, such as system-based or manual platform."
    )
    private String underWritingOrder;

    @Schema(
        description = "The platform or system used for policy issuance, defining the order or process flow for issuing policies. UwIssuePlatformEnum refers to the enumerated values for Underwriting Issue Platform, indicating the policy issuance platform."
    )
    private String policyIssuanceOrder;

    @Schema(
        description = "A flag indicating whether payment is allowed for these goods. 1: Yes, payment is allowed; 2: No, payment is not allowed."
    )
    private YesNoEnum enablePayment;

    @Schema(
        description = "The type of payment method accepted for these goods. It categorizes the payment approach, such as online payment, offline payment, or installment payment."
    )
    private Integer paymentType;

    @Schema(
        description = "A list of payment methods accepted for purchasing these goods. It specifies the various ways customers can pay, such as credit card, bank transfer, or mobile payment."
    )
    private List<String> paymentMethodList;

    @Schema(
        description = "A flag indicating whether a non-premium policy can be issued and become effective without requiring immediate payment. 1: Yes, policy is effective without payment; 2: No, payment is required for policy effectiveness."
    )
    private YesNoEnum policyEffectiveWithoutPay;

    /**
     * policy effective without pay code
     * {@link com.zatech.gaia.resource.components.enums.market.PolicyEffectiveWithoutCollectionEnum}
     */
    @Schema(description = "Code specifying the reason for policy effectiveness without immediate payment.")
    private Integer policyEffectiveWithoutPayCode;

    @Schema(
        description = "A list of sales partners specifically for combined group sales of these goods. These partners are involved in selling the goods as part of a bundled or group offering."
    )
    private List<GoodsSalesPartnerCombineResponse> salesPartnerCombineResponses;

}
