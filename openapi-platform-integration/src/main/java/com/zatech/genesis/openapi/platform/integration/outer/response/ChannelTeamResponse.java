/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.outer.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zatech.genesis.openapi.platform.integration.outer.enums.TeamBusinessType;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2025/03/09
 */
@Data
@ToString(callSuper = true)
public class ChannelTeamResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "id", description = "The ID of team")
    private Long id;

    @Schema(title = "teamName", description = "The name of team")
    private String teamName;

    @Schema(title = "teamCode", description = "The code of team")
    private String teamCode;

    @Schema(title = "teamType", description = "The type of team")
    private String teamType;

    @Schema(title = "email", description = "The email of team")
    private String email;

    @Schema(title = "level", description = "The level of team")
    private Integer level;

    @Schema(title = "isVirtual", description = "The team is virtual or not")
    @JsonProperty("isVirtual")
    private Boolean virtual;

    @Schema(title = "teamBusinessType", description = "The business type of team")
    private TeamBusinessType teamBusinessType;

    @Schema(title = "hadBind", description = "The team han bind or not")
    private Boolean hadBind;

    @Schema(title = "createdTime", description = "The created time of team")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date createdTime;

    @Schema(title = "modifiedTime", description = "The modified time of team")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date modifiedTime;

    @Schema(title = "parentId", description = "The parent ID of team")
    private Long parentId;

    @Schema(title = "introduction", description = "The introduction of team")
    private String introduction;

    @Schema(title = "countryCode", description = "The country code of team")
    private String countryCode;

    @Schema(title = "phone number", description = "The phone No of team")
    @JsonProperty("phoneNum")
    private String phoneNo;

    @Schema(title = "children", description = "The children of team")
    private List<ChannelTeamResponse> children;

    @Schema(title = "isDirectBelong", description = "The agent belongs to this team or not")
    private Boolean isDirectBelong;

}
