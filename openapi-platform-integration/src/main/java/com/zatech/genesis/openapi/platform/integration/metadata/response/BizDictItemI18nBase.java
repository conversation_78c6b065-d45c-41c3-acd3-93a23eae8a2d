package com.zatech.genesis.openapi.platform.integration.metadata.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BizDictItemI18nBase {

    @Schema(description = "The actual data value associated with a particular field or configuration item in the system. This represents the stored value used in business operations and calculations.")
    private String value;

    @Schema(description = "The display name or label associated with the value, providing a human-readable representation of the stored value for user interfaces and reports.")
    private String valueName;

    @Schema(description = "Extended field 1 name - Customizable field for storing additional business-specific information or metadata related to the item.")
    private String itemExtend1Name;

    @Schema(description = "Extended field 2 name - Customizable field for storing additional business-specific information or metadata related to the item.")
    private String itemExtend2Name;

    @Schema(description = "Extended field 3 name - Customizable field for storing additional business-specific information or metadata related to the item.")
    private String itemExtend3Name;

    @Schema(description = "Extended field 4 name - Customizable field for storing additional business-specific information or metadata related to the item.")
    private String itemExtend4Name;

    @Schema(description = "Extended field 5 name - Customizable field for storing additional business-specific information or metadata related to the item.")
    private String itemExtend5Name;

    @Schema(description = "Extended field 6 name - Customizable field for storing additional business-specific information or metadata related to the item.")
    private String itemExtend6Name;

}
