package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.zatech.gaia.resource.components.enums.underwriting.UwDecisionEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description decision 产品层级信息
 * <AUTHOR>
 * @Date 2021/5/17 14:17
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "DecisionProductLevelResponse", title = "Decision product level response")
public class DecisionProductLevelResponse {

    @Schema(title = "Product ID", required = true)
    private Long productId;

    @Schema(title = "Decision category", required = true)
    private UwDecisionEnum uwDecision;
    /*    @Schema(title = "Down-sell detail", type = "List")
    private List<UwDownsellDetailResponse> downsellDetail;

    @Schema(title = "Loading detail", type = "List")
    private List<UwLoadingDetailResponse> loadingDetail;*/
}
