/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.claim;

import com.zatech.genesis.model.claim.caseinfo.Case;
import com.zatech.genesis.model.claim.documentation.Documentation;
import com.zatech.genesis.openapi.platform.integration.claim.base.CustomerApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.DocumentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.IncidentContentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.LossPartyApiBase;
import com.zatech.genesis.openapi.platform.integration.transform.claim.convert.UnifiedModelClaimCaseConverter;
import com.zatech.genesis.openapi.platform.integration.transform.claim.convert.UnifiedModelClaimCustomerConverter;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class UnifiedClaimCaseTransform {

    private final Case claimCase;

    public UnifiedClaimCaseTransform(Case claimCase) {
        this.claimCase = claimCase;
    }

    public List<CustomerApiBase> toPayees() {
        return Optional.ofNullable(claimCase.getPayeeList()).orElse(Collections.emptyList()).stream().map(UnifiedModelClaimCustomerConverter.INSTANCE::toPayee1).toList();
    }

    public List<LossPartyApiBase> toLossParties() {
        return Optional.ofNullable(claimCase.getLossParties()).orElse(Collections.emptyList()).stream().map(UnifiedModelClaimCaseConverter.INSTANCE::convert).toList();
    }

    public IncidentContentApiBase toIncidentContent() {
        return Optional.ofNullable(claimCase.getIncidentContent()).map(UnifiedModelClaimCaseConverter.INSTANCE::convert).orElse(null);
    }

    public List<DocumentApiBase> toDocument() {
        return Optional.ofNullable(claimCase.getDocumentation())
            .map(Documentation::getAttachmentList).orElse(Collections.emptyList())
            .stream()
            .map(UnifiedModelClaimCaseConverter.INSTANCE::convert).filter(Objects::nonNull).toList();
    }

    public Case getCase() {
        return claimCase;
    }

}
