/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.integration.policy.customer;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.openapi.platform.integration.customer.response.party.customer.PartyCustomerAttachmentResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.AccountBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.AddressBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.EmailBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.PartyCustomerBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.PartyCustomerDataSegregationCreateResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.PartyLabelBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.PhoneBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.SocialAccountBaseResponse;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.TaxBaseResponse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Party 客户基本信息 & 扩展信息
 *
 * <AUTHOR> 2023/4/10 15:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PartyCustomerResponse extends PartyCustomerBaseResponse {

    @Schema(title = "Trans type")
    private TransTypeEnum transType;

    @Schema(title = "Trans no")
    private String transNo;

    @Schema(title = "Data source: kafka/api")
    private String dataSource;

    @Schema(title = "Accounts")
    private List<AccountBaseResponse> accounts = Lists.newArrayList();

    @Schema(title = "Phones")
    private List<PhoneBaseResponse> phones = Lists.newArrayList();

    @Schema(title = "Emails")
    private List<EmailBaseResponse> emails = Lists.newArrayList();

    @Schema(title = "Addresses")
    private List<AddressBaseResponse> addresses = Lists.newArrayList();

    @Schema(title = "SocialAccounts")
    private List<SocialAccountBaseResponse> socialAccounts = Lists.newArrayList();

    @Schema(title = "Taxes")
    private List<TaxBaseResponse> taxes = Lists.newArrayList();

    @Schema(title = "Labels")
    private List<PartyLabelBaseResponse> labels;

    @Schema(title = "Attachments")
    private List<PartyCustomerAttachmentResponse> attachments = Lists.newArrayList();

    @Schema(title = "Generated person")
    private Boolean generatedPerson = Boolean.FALSE;

    @Schema(title = "Data segregation")
    private List<PartyCustomerDataSegregationCreateResponse> dataSegregation;
}
