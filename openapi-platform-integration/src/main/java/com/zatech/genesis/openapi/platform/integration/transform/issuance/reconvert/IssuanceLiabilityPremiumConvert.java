/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.issuance.reconvert;

import com.zatech.gaia.resource.components.enums.product.PremiumDiscountTypeEnum;
import com.zatech.genesis.model.policy.base.fee.PremiumDetail;
import com.zatech.genesis.model.policy.base.fee.PremiumDiscount;
import com.zatech.genesis.model.policy.base.fee.PremiumLoading;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceExtraFeeResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceProductLiabilityPremiumResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceProductPremiumDiscountDetailResponse;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.MultiCurrencyPremiumDetailConvert;
import com.zatech.genesis.openapi.platform.share.EnumHelper;

import java.util.Collections;
import java.util.Optional;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {MultiCurrencyPremiumDetailConvert.class})
public interface IssuanceLiabilityPremiumConvert {

    IssuanceLiabilityPremiumConvert INSTANCE = Mappers.getMapper(IssuanceLiabilityPremiumConvert.class);

    PremiumDetail convert(IssuanceProductLiabilityPremiumResponse liabilityPremiumResponse);


    @AfterMapping
    default void afterMapping(
        IssuanceProductLiabilityPremiumResponse source,
        @MappingTarget PremiumDetail target) {

        Optional.ofNullable(source.getDiscountPremiumDetailList()).orElse(Collections.emptyList()).stream().map(this::convert).forEach(target.getPremiumDiscountList()::add);
        Optional.ofNullable(source.getExtraFeeDetailList()).orElse(Collections.emptyList()).stream().map(this::convert).forEach(target.getPremiumLoadingList()::add);
        target.getMultiCurrencyPremiumList().addAll(MultiCurrencyPremiumDetailConvert.INSTANCE.convertListByIssuance(source.getIssuanceMultiCurrencyPremiumList()));
    }

    default PremiumDiscount convert(IssuanceProductPremiumDiscountDetailResponse issuanceProductPremiumDiscountDetailResponse) {
        PremiumDiscount premiumDiscount = new PremiumDiscount();
        premiumDiscount.setPremiumDiscountType(EnumHelper.codeOfIfExist(issuanceProductPremiumDiscountDetailResponse.getPremiumDiscountType(), PremiumDiscountTypeEnum.class));
        premiumDiscount.setPeriodDiscountPremium(issuanceProductPremiumDiscountDetailResponse.getPeriodDiscountPremium());
        premiumDiscount.setDiscountRate(issuanceProductPremiumDiscountDetailResponse.getDiscountRate());
        return premiumDiscount;
    }

    default PremiumLoading convert(IssuanceExtraFeeResponse issuanceExtraFeeResponse) {
        PremiumLoading premiumLoading = new PremiumLoading();
        premiumLoading.setPeriodPremium(issuanceExtraFeeResponse.getPeriodPremium());
        premiumLoading.setPeriodPremiumTax(issuanceExtraFeeResponse.getPeriodPremiumTax());
        premiumLoading.setLoadingType(issuanceExtraFeeResponse.getLoadingType());
        premiumLoading.setLoadingMethod(issuanceExtraFeeResponse.getLoadingMethod());
        premiumLoading.setLoadingValue(issuanceExtraFeeResponse.getLoadingValue());
        premiumLoading.setLoadingPeriod(issuanceExtraFeeResponse.getLoadingPeriod());
        premiumLoading.setLoadingPeriodType(issuanceExtraFeeResponse.getLoadingPeriodType());
        premiumLoading.setExtraRate(issuanceExtraFeeResponse.getExtraRate());
        return premiumLoading;
    }

}
