package com.zatech.genesis.openapi.platform.integration.calculate.request;

import com.zatech.genesis.openapi.platform.integration.calculate.base.SchemaCalculateFactor;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/12/28 14:14
 **/
@Schema(
    title = "schema calculate factor request"
)
@Data
@EqualsAndHashCode(callSuper = false)
public class SchemaCalculateFactorRequest extends SchemaCalculateFactor {

    @Schema(title = "factorValue")
    private Object factorValue;

}
