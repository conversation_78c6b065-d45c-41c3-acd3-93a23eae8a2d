/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.customer.party.base;

import com.zatech.gaia.resource.components.enums.common.DimensionTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 保单客户数据隔离信息类
 *
 * <AUTHOR> 2023/4/10 15:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "Party customer data segregation create response")
public class PartyCustomerDataSegregationCreateResponse {
    @Schema(title = "Dimension type for the data segregation")
    private DimensionTypeEnum dimensionType;

    @Schema(title = "Dimension code, eg. agent code")
    private String dimensionCode;

    @Schema(title = "Dimension value, eg. agent path code")
    private String dimensionValue;

    @Schema(title = "Dimension value")
    private String transactionNo;
}
