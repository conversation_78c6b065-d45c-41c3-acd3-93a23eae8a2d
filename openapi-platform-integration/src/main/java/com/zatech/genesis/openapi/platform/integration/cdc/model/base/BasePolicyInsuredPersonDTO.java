/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.cdc.model.base;

import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: za-zhangwenlei
 * @Description:
 * @Date: Created in 18:27 2020/5/13
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
public class BasePolicyInsuredPersonDTO extends BasePolicyInsuredObjectDTO {

    private static final long serialVersionUID = -8818395663338050762L;

    /**
     * 主键id
     */
    private Long policyInsuredPersonId;

    /**
     * 票号
     */
    private String ticketNumber;

    /**
     * 旅游门票类型
     */
    private String ticketType;

    /**
     * 票类型 详见 IssuancePaxTypeEnum
     */
    private Long noOfPax;

    /**
     * 门票入园时间
     */
    private Date entryDate;

    /**
     * 有效起期
     */
    private Date validationStartDate;

    /**
     * 有效止期
     */
    private Date validationEndDate;

    /**
     * 相关成人数量
     */
    private Integer adultNumber;

    /**
     * 相关儿童数量
     */
    private Integer childNumber;

    /**
     * 相关老人数量
     */
    private Integer seniorNumber;

    /**
     * 附件地址
     */
    private String attachmentUrl;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 降雨量
     */
    private String chanceOfRain;

    /**
     * 高温
     */
    private String maxTemperature;

    /**
     * 提前几天
     */
    private Integer daysInAdvance;

    //########################扩展字段###############


}
