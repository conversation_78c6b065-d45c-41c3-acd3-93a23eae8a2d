/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.pos.model.policy;

import com.zatech.genesis.openapi.platform.integration.pos.model.base.BaseCommon;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: za-duyufeng
 * @Description: 保单关系
 * @Date: Created in 09:30 2021/4/13 za-duyufeng
 */
@Setter
@Getter
@Schema(description = "Policy relation information", name = "PosPolicyRelation")
public class PosPolicyRelation extends BaseCommon {

    /**
     * 关系保单主键id
     */
    @Schema(description = "The identification of policy relation")
    private Long policyRelationId;

    /**
     * 关系单号
     */
    @Schema(description = "The number of relation")
    private String relationNo;

    /**
     * 第三方关系单号
     */
    @Schema(description = "The number of third party relation")
    private String thirdPartyRelationNo;

    /**
     * 下游关系单号
     */
    @Schema(description = "The number of host relation")
    private String hostRelationNo;

    /**
     * 关系单号扩展1
     */
    @Schema(description = "The relation no of extend 1")
    private String relationNoExtend1;

    /**
     * 关系单号扩展2
     */
    @Schema(description = "The relation no of extend 2")
    private String relationNoExtend2;

    /**
     * 关系单号扩展3
     */
    @Schema(description = "The relation no of extend 3")
    private String relationNoExtend3;

    /**
     * 投保人客户id
     */
    @Schema(description = "The identification of holder customer")
    private Long holderCustomerId;

}
