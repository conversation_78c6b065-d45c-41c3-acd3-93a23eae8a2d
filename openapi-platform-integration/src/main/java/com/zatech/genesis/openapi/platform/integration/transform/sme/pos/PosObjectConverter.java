/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.sme.pos;

import com.zatech.gaia.resource.components.enums.schema.ObjectComponent;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredBuildingInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredDeviceInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentBase;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentBuilding;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentDevice;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentFellowTraveller;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentHotel;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentOrder;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentPet;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentTicket;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentTravel;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectComponentTrip;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectTravelFellowTraveller;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectTravelHotel;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectTravelTicket;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectTravelTrip;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredOrderInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredPetInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredTravelFellowTravellerInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredTravelHotelInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredTravelInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredTravelTicketInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredTravelTripInfo;
import com.zatech.genesis.openapi.platform.integration.transform.policy.reconvert.AttachmentConvert;
import com.zatech.genesis.openapi.platform.share.tools.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;


@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {AttachmentConvert.class})
public interface PosObjectConverter {

    PosObjectConverter INSTANCE = Mappers.getMapper(PosObjectConverter.class);

    @Mapping(target = "componentType", constant = "TRAVEL")
    @Mapping(source = "policyInsuredTravelId", target = "insuredComponentId")
    PosPolicyInsuredObjectComponentTravel convertToBasis(PosPolicyInsuredTravelInfo travel);

    /**
     * 将建筑标的转换为建筑组件
     */
    @Mapping(target = "componentType", constant = "BUILDING")
    @Mapping(source = "policyInsuredBuildingId", target = "insuredComponentId")
    PosPolicyInsuredObjectComponentBuilding convert(PosPolicyInsuredBuildingInfo building);

    /**
     * 将设备标的转换为设备组件
     */
    @Mapping(target = "componentType", constant = "DEVICE")
    @Mapping(source = "policyInsuredDeviceId", target = "insuredComponentId")
    PosPolicyInsuredObjectComponentDevice convert(PosPolicyInsuredDeviceInfo device);

    /**
     * 将宠物标的转换为活体动物组件
     */
    @Mapping(target = "componentType", constant = "PET")
    @Mapping(source = "policyInsuredPetId", target = "insuredComponentId")
    PosPolicyInsuredObjectComponentPet convert(PosPolicyInsuredPetInfo pet);

    @Mapping(target = "componentType", constant = "ORDER")
    @Mapping(source = "policyInsuredOrderId", target = "insuredComponentId")
    PosPolicyInsuredObjectComponentOrder convert(PosPolicyInsuredOrderInfo order);

    default List<PosPolicyInsuredObjectComponentBase> travelToTravelComponentList(PosPolicyInsuredTravelInfo travel) {
        List<PosPolicyInsuredObjectComponentBase> componentList = new ArrayList<>();
        PosPolicyInsuredObjectComponentTravel posTravelComponent = PosObjectConverter.INSTANCE.convertToBasis(travel);
        if (null != posTravelComponent) {
            componentList.add(posTravelComponent);
        }
        //fellowTraveller hotel ticket trip
        PosPolicyInsuredObjectComponentTrip insuredObjectComponentTripBase = PosObjectConverter.INSTANCE.basisTripListToComponent(travel.getTrip());
        if (null != insuredObjectComponentTripBase) {
            componentList.add(insuredObjectComponentTripBase);
        }

        PosPolicyInsuredObjectComponentTicket insuredObjectComponentTicketBase = PosObjectConverter.INSTANCE.basisTicketListToComponent(travel.getTicket());
        if (null != insuredObjectComponentTicketBase) {
            componentList.add(insuredObjectComponentTicketBase);
        }
        PosPolicyInsuredObjectComponentHotel insuredObjectComponentHotelBase = PosObjectConverter.INSTANCE.basisHotelListToComponent(travel.getHotel());
        if (null != insuredObjectComponentHotelBase) {
            componentList.add(insuredObjectComponentHotelBase);
        }
        PosPolicyInsuredObjectComponentFellowTraveller insuredObjectComponentFellowTravellerBase = PosObjectConverter.INSTANCE.basisFellowListToComponent(travel.getFellowTraveller());
        if (null != insuredObjectComponentFellowTravellerBase) {
            componentList.add(insuredObjectComponentFellowTravellerBase);
        }
        return componentList;
    }

    default PosPolicyInsuredObjectComponentTrip basisTripListToComponent(List<PosPolicyInsuredTravelTripInfo> trips) {
        if (CollectionUtil.isEmpty(trips)) {
            return null;
        }
        PosPolicyInsuredObjectComponentTrip insuredObjectComponentTripBase = new PosPolicyInsuredObjectComponentTrip();
        insuredObjectComponentTripBase.setComponentType(ObjectComponent.TRIP);
        insuredObjectComponentTripBase.setComponentInfoList(PosObjectConverter.INSTANCE.basisTripListToTripComponentList(trips));
        return insuredObjectComponentTripBase;
    }

    List<PosPolicyInsuredObjectTravelTrip> basisTripListToTripComponentList(List<PosPolicyInsuredTravelTripInfo> trips);

    @Mapping(target = "componentType", constant = "TRIP")
    @Mapping(source = "policyInsuredTripId", target = "insuredComponentId")
    PosPolicyInsuredObjectTravelTrip convert(PosPolicyInsuredTravelTripInfo trip);

    default PosPolicyInsuredObjectComponentTicket basisTicketListToComponent(List<PosPolicyInsuredTravelTicketInfo> ticketInfos) {
        if (CollectionUtil.isEmpty(ticketInfos)) {
            return null;
        }
        PosPolicyInsuredObjectComponentTicket insuredObjectComponentTicket = new PosPolicyInsuredObjectComponentTicket();
        insuredObjectComponentTicket.setComponentType(ObjectComponent.TICKET);
        insuredObjectComponentTicket.setComponentInfoList(PosObjectConverter.INSTANCE.basisTicketListToTicketComponentList(ticketInfos));
        return insuredObjectComponentTicket;
    }

    List<PosPolicyInsuredObjectTravelTicket> basisTicketListToTicketComponentList(List<PosPolicyInsuredTravelTicketInfo> ticketInfos);

    @Mapping(target = "componentType", constant = "TICKET")
    @Mapping(source = "policyInsuredTicketId", target = "insuredComponentId")
    PosPolicyInsuredObjectTravelTicket convert(PosPolicyInsuredTravelTicketInfo trip);


    default PosPolicyInsuredObjectComponentHotel basisHotelListToComponent(List<PosPolicyInsuredTravelHotelInfo> hotelInfos) {
        if (CollectionUtil.isEmpty(hotelInfos)) {
            return null;
        }
        PosPolicyInsuredObjectComponentHotel insuredObjectComponentHotel = new PosPolicyInsuredObjectComponentHotel();
        insuredObjectComponentHotel.setComponentType(ObjectComponent.HOTEL);
        insuredObjectComponentHotel.setComponentInfoList(PosObjectConverter.INSTANCE.basisHoletListToHoletComponentList(hotelInfos));
        return insuredObjectComponentHotel;
    }

    List<PosPolicyInsuredObjectTravelHotel> basisHoletListToHoletComponentList(List<PosPolicyInsuredTravelHotelInfo> hotelInfos);

    @Mapping(target = "componentType", constant = "HOTEL")
    @Mapping(source = "policyInsuredHotelId", target = "insuredComponentId")
    PosPolicyInsuredObjectTravelHotel convert(PosPolicyInsuredTravelHotelInfo trip);

    default PosPolicyInsuredObjectComponentFellowTraveller basisFellowListToComponent(List<PosPolicyInsuredTravelFellowTravellerInfo> fellowTravellerInfos) {
        if (CollectionUtil.isEmpty(fellowTravellerInfos)) {
            return null;
        }
        PosPolicyInsuredObjectComponentFellowTraveller insuredObjectComponentFellowTraveller = new PosPolicyInsuredObjectComponentFellowTraveller();
        insuredObjectComponentFellowTraveller.setComponentType(ObjectComponent.FELLOW_TRAVELLER);
        insuredObjectComponentFellowTraveller.setComponentInfoList(PosObjectConverter.INSTANCE.basisFellowTravellerListToFellowTravellerComponentList(fellowTravellerInfos));
        return insuredObjectComponentFellowTraveller;
    }

    List<PosPolicyInsuredObjectTravelFellowTraveller> basisFellowTravellerListToFellowTravellerComponentList(List<PosPolicyInsuredTravelFellowTravellerInfo> hotelInfos);

    @Mapping(target = "componentType", constant = "FELLOW_TRAVELLER")
    @Mapping(source = "policyInsuredFellowTravellerId", target = "insuredComponentId")
    PosPolicyInsuredObjectTravelFellowTraveller convert(PosPolicyInsuredTravelFellowTravellerInfo trip);

}
