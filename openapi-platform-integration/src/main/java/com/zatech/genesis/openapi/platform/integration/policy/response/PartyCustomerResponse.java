package com.zatech.genesis.openapi.platform.integration.policy.response;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.genesis.model.policy.base.Attachment;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyCustomerAddressComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyCustomerPhoneComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.base.BasePolicyEmailComponentDTO;
import com.zatech.genesis.openapi.platform.integration.policy.customer.party.base.AccountBaseResponse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PartyCustomerResponse {

    @Schema(
        title = "Party ID",
        description = "The system-generated unique code used to identify the party and retrieve the party's information."
    )
    private Long partyId;

    @Schema(title = "Certi No.")
    private String certiNo;

    @Schema(title = "Certi type")
    private CertiTypeEnum certiType;

    @Schema(title = "Birthday")
    private LocalDate birthday;

    @Schema(title = "Full name")
    private String fullName;

    private String firstName;

    private String lastName;

    private GenderEnum sex;

    private GenderEnum gender;

    @Schema(title = "CountryNationalityEnum")
    private CountryNationalityEnum residenceCountry;

    @Schema(title = "Email")
    private List<BasePolicyEmailComponentDTO> emails;

    /**
     * 手机号
     */
    @Schema(title = "Mobile phone")
    private List<BasePolicyCustomerPhoneComponentDTO> phones;

    @Schema(title = "Address")
    private List<BasePolicyCustomerAddressComponentDTO> addresses;

    @Schema(title = "Accounts")
    private List<AccountBaseResponse> accounts = Lists.newArrayList();


    /**
     * attachment list
     */
    @Schema(description = "List of policy documents")
    private List<Attachment> attachmentList = Lists.newArrayList();
}
