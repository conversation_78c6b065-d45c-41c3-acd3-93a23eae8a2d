package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.zatech.gaia.resource.components.enums.market.inform.InformCategoryEnum;
import com.zatech.gaia.resource.components.enums.order.CustomerTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 投保单客户告知
 * <AUTHOR>
 * @Date Created in 11:05 上午 2019/11/19
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "BaseIssuanceCustomerDeclarationEntity")
public class BaseIssuanceCustomerDeclarationEntity extends BaseIssuanceCommonEntity {
    private static final long serialVersionUID = 36385651734003654L;

    /**
     * 主键id
     */
    private Long issuanceCustomerDeclarationId;

    /**
     * 投保单id
     */
    private Long issuanceId;

    /**
     * 分库分表键，投保人customerID
     */
    private Long holderCustomerId;

    /**
     * 告知类型1:健康告知 2：财务告知
     */
    private InformCategoryEnum declarationType;

    /**
     * 需要存储的key
     */
    private String declarationKey;

    /**
     * 投保险人customerID
     */
    private Long customerId;

    /**
     * 1:投保人,2:被投保人,3:受益人
     */
    private CustomerTypeEnum customerType;
}
