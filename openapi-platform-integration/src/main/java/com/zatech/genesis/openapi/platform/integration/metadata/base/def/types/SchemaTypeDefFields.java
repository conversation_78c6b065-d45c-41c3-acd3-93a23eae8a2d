package com.zatech.genesis.openapi.platform.integration.metadata.base.def.types;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zatech.genesis.openapi.platform.integration.metadata.base.SchemaFieldFullProperty;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SchemaTypeDefFields extends SchemaTypeDef {

    /**
     * 具体字段列表， 包含静态字段和扩展字段
     */
    @Schema(description = "The list of specific fields, including both static fields and extended fields. This represents all the fields defined within this schema type, encompassing both core, pre-defined attributes (static fields) and custom or dynamically added attributes (extended fields).")
    private List<SchemaFieldFullProperty> fields;

}
