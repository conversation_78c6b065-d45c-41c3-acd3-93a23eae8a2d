/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.pos;

import com.zatech.gaia.resource.components.enums.posonline.PosAutoRenewalSwitchEnum;
import com.zatech.genesis.model.policy.Policy;
import com.zatech.genesis.model.policy.customer.PolicyHolder;
import com.zatech.genesis.model.pos.PosTransaction;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosPolicyChangeInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosPolicyHolderInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.insuredobject.PosPolicyInsuredObjectInfo;
import com.zatech.genesis.openapi.platform.integration.pos.request.PosTransactionRequest;
import com.zatech.genesis.openapi.platform.integration.transform.pos.convert.PosHolderConvert;
import com.zatech.genesis.openapi.platform.share.tools.Convert;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class UnifiedPosTransactionTransform {

    private final PosTransaction posTransaction;

    private final UnifiedPosProductTransform unifiedPosProductTransform;

    public UnifiedPosTransactionTransform(PosTransaction posTransaction) {
        this.posTransaction = posTransaction;
        this.unifiedPosProductTransform = new UnifiedPosProductTransform(posTransaction);
    }

    public PosTransactionRequest toPosTransactionRequest() {
        PosTransactionRequest posTransactionRequest = new PosTransactionRequest();
        posTransactionRequest.setPolicyChangeInfoAfter(toPosPolicyChangeInfo());
        posTransactionRequest.setPosTransStatus(posTransaction.getPosTransStatus());
        posTransactionRequest.setPosTransType(posTransaction.getTransTypeEnum());
        posTransactionRequest.setEffectiveDate(posTransaction.getEffectiveDate());
        posTransactionRequest.setEffectiveDateType(posTransaction.getEffectiveDateType());
        posTransactionRequest.setReasonCode(Convert.convert(String.class, posTransaction.getReasonCode()));
        posTransactionRequest.setReason(posTransaction.getReason());
        posTransactionRequest.setCoverageDateChangeType(posTransaction.getCoverageDateChangeType());
        return posTransactionRequest;
    }

    private PosPolicyChangeInfo toPosPolicyChangeInfo() {
        PosPolicyChangeInfo posPolicyChangeInfo = convertBasic();
        posPolicyChangeInfo.setPolicyHolder(toPosPolicyHolderInfo());
        posPolicyChangeInfo.setPolicyInsuredObjectList(toObjectList());
        posPolicyChangeInfo.setPolicyProductList(unifiedPosProductTransform.toPosPolicyProductInfoList());
        return posPolicyChangeInfo;
    }

    private List<PosPolicyInsuredObjectInfo> toObjectList() {
        return Optional.ofNullable(posTransaction.getAfterPolicy()).map(Policy::getInsuredObjectList).orElse(Collections.emptyList())
            .stream().map(PosObjectService::convertPosObject).toList();
    }

    private PosPolicyChangeInfo convertBasic() {
        PosPolicyChangeInfo posPolicyChangeInfo = new PosPolicyChangeInfo();
        Policy policy = posTransaction.getAfterPolicy();
        if (policy != null) {
            posPolicyChangeInfo.setEffectiveDate(policy.getEffectiveDate());
            posPolicyChangeInfo.setExpiryDate(policy.getExpiryDate());
            posPolicyChangeInfo.setGoodsPlanId(policy.getGoodsPlanId());
            posPolicyChangeInfo.setGoodsPlanCode(policy.getPlanCode());
            posPolicyChangeInfo.setPackageDefId(policy.getPackageId());
            posPolicyChangeInfo.setPackageCode(policy.getPackageCode());
            posPolicyChangeInfo.setIssueDate(policy.getIssueDate());
            posPolicyChangeInfo.setSignOffDate(policy.getSignOffDate());
            posPolicyChangeInfo.setPolicyType(policy.getPolicyType() != null ? String.valueOf(policy.getPolicyType()) : null);
            posPolicyChangeInfo.setExtensions(policy.getExtensions());
            Optional.of(policy).map(Policy::getAutoRenewal).map(autoRenewal -> autoRenewal ? PosAutoRenewalSwitchEnum.SWITCH_ON : PosAutoRenewalSwitchEnum.SWITCH_OFF).ifPresent(posPolicyChangeInfo::setAutoRenewal);
        }
        return posPolicyChangeInfo;
    }

    public PosPolicyHolderInfo toPosPolicyHolderInfo() {
        PolicyHolder policyHolder = Optional.ofNullable(posTransaction).map(PosTransaction::getAfterPolicy).map(Policy::getPolicyHolder).orElse(null);
        if (policyHolder == null) {
            return null;
        }
        return PosHolderConvert.INSTANCE.toPosPolicyHolderInfo(policyHolder);
    }

}
