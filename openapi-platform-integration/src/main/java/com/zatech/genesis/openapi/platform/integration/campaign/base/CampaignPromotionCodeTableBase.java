/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.campaign.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2021-07-07 11:34
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(title = "Campaign Rule Base")
public class CampaignPromotionCodeTableBase {

    @Schema(description = "The unique code of the promotion code table configured by users. It serves as an identifier for the promotion code table within the system.")
    private String code;

    @Schema(description = "The user-defined name of the promotion code table. This name provides a descriptive label for the promotion code table for easy identification and management.")
    private String name;

    @Schema(description = "The current status of the promotion code table. It indicates the operational state of the table, such as active, inactive, or draft. Valid values are usually defined in a dictionary or enumeration within the system.")
    private Integer status;

    @Schema(description = "The version number of the promotion code table. It tracks changes and updates made to the table over time, allowing for version control and historical tracking of configurations.")
    private String version;

    @Schema(description = "The unique code assigned to the file containing the promotion code inventory. This code is used to retrieve and manage the specific inventory file associated with the promotion code table.")
    private String fileUniqueCode;

    @Schema(description = "The name of the file containing the promotion code inventory. This is the user-friendly name of the file, making it easier to identify and manage the inventory file.")
    private String fileName;

}
