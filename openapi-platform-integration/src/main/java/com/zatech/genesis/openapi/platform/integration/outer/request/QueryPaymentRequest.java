/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.integration.outer.request;

import com.zatech.gaia.resource.components.enums.bcp.FeeStatusEnum;
import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.bcp.SettleFlagEnum;
import com.zatech.gaia.resource.components.enums.bcp.TransStatusEnum;
import com.zatech.gaia.resource.components.enums.common.FeeTypeEnum;
import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.openapi.platform.integration.bcp.base.BaseQuery;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/8/1 11:05
 */
@Getter
@Setter
@Schema(title = "Query payment request", description = "Query payment request")
public class QueryPaymentRequest extends BaseQuery {

    @Schema(title = "businessTransactionNo", description = "Business transaction no")
    private String businessTransactionNo;

    @Schema(title = "payeeIds", description = "Payee id list")
    private List<Long> payeeIds;

    /**
     * 批处理查询数据时使用
     */
    @Schema(title = "postSyncFlag", description = "Post sync flag")
    private YesNoEnum postSyncFlag;

    @Schema(title = "feeStatus", description = "Fee status")
    private FeeStatusEnum feeStatus;

    @Schema(title = "transStatus", description = "Trans status")
    private TransStatusEnum transStatus;

    @Schema(title = "settleFlag", description = "Settle flag")
    private SettleFlagEnum settleFlag;

    @Schema(title = "transType", description = "Trans type")
    private TransTypeEnum transType;

    @Schema(title = "feeType", description = "Fee type")
    private FeeTypeEnum feeType;

    @Schema(title = "scrollId", description = "Scroll id")
    private Long scrollId;

    @Schema(title = "orderByIdAsc", description = "Order by id asc")
    private Boolean orderByIdAsc;

    @Schema(title = "payMethod", description = "Pay method")
    private PayMethodEnum payMethod;

    @Schema(title = "paymentReferenceNo2", description = "Payment reference no 2")
    private String paymentReferenceNo2;

    @Schema(title = "accountDateStart", description = "Account date start")
    private Date accountDateStart;

    @Schema(title = "accountDateEnd", description = "Account date end")
    private Date accountDateEnd;

    @Schema(title = "transactionDateStart", description = "Transaction date start")
    private Date transactionDateStart;

    @Schema(title = "transactionDateEnd", description = "Transaction date end")
    private Date transactionDateEnd;

    @Schema(title = "confirmDateStart", description = "Confirm date start")
    private Date confirmDateStart;

    @Schema(title = "confirmDateEnd", description = "Confirm date end")
    private Date confirmDateEnd;

    @Schema(title = "channelCode", description = "Channel code")
    private String channelCode;

    @Schema(title = "transTypeList", description = "Trans type enum list")
    private List<TransTypeEnum> transTypeList;

    @Schema(title = "payMethodList", description = "Pay method enum list")
    private List<PayMethodEnum> payMethodList;

    @Schema(title = "feeStatusList", description = "Fee status enum list")
    private List<FeeStatusEnum> feeStatusList;
}
