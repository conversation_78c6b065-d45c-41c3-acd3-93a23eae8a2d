/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.response;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.openapi.platform.integration.policy.base.PolicyInsuredObjectBase;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 标的建筑物<br/>
 *
 * <AUTHOR> 2017年7月6日 上午11:31:12
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(title = "", description = "Policy insured building response")
@SuppressWarnings("all")
public class PolicyInsuredBuildingResponse extends PolicyInsuredObjectBase {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(title = "Policy insured building ID")
    private Long policyInsuredBuildingId;

    /**
     * 国家
     */
    @Schema(title = "Country")
    private String country;

    /**
     * 邮政编码
     */
    @Schema(title = "Zip code")
    private String zipCode;

    /**
     * 经度
     */
    @Schema(title = "Longitude")
    private String longitude;

    /**
     * 纬度
     */
    @Schema(title = "Latitude")
    private String latitude;

    /**
     * 建筑年份
     */
    @Schema(title = "Built year")
    private Date builtYear;

    /**
     * 建筑结构
     */
    @Schema(title = "Structure")
    private String structure;

    /**
     * 独栋house或者是公寓
     */
    @Schema(title = "Building type")
    private String buildingType;

    /**
     * 房屋面积
     */
    @Schema(title = "Building size")
    private String buildingSize;

    /**
     * 是否有理赔记录
     */
    @Schema(title = "Claim history")
    private String claimHistory;

    /**
     * 是否有火警设备
     */
    @Schema(title = "Fire alarm")
    private String fireAlarm;

    /**
     * 是否有防盗设备
     */
    @Schema(title = "Burglar alarm")
    private String burglarAlarm;

    /**
     * 省
     */
    @Schema(title = "Address1")
    private String address1;

    /**
     * 市
     */
    @Schema(title = "Address2")
    private String address2;

    /**
     * 区
     */
    @Schema(title = "Address3")
    private String address3;

    /**
     * 镇
     */
    @Schema(title = "Address4")
    private String address4;

    /**
     * 地址
     */
    @Schema(title = "Address5")
    private String address5;

    /**
     * 标的所属企业的行业类别
     */
    @Schema(title = "Building industry category")
    private String buildingIndustryCategory;

    /**
     * 标的所处的洪水区域
     */
    @Schema(title = "Flood zone")
    private String floodZone;

    /**
     * 地震区域
     */
    @Schema(title = "Earthquake zone")
    private String earthquakeZone;

    /**
     * GPS度
     */
    @Schema(title = "GPS coordinate degree")
    private String gpsCoordinateDegree;

    /**
     * GPS分
     */
    @Schema(title = "GPS coordinate minute")
    private String gpsCoordinateMinute;

    /**
     * GPS秒
     */
    @Schema(title = "GPS coordinate second")
    private String gpsCoordinateSecond;

    /**
     * 楼层数
     */
    @Schema(title = "Number of floor(s)")
    private String numberOfFloor;

    /**
     * 建筑面积
     */
    @Schema(title = "Building area")
    private String buildingArea;

    /**
     * 建筑使用年数
     */
    @Schema(title = "Building has been occupied (years)")
    private String buildingOccupiedYears;

    /**
     * 建筑用途
     */
    @Schema(title = "Building occupation")
    private String buildingOccupation;

    /**
     * 安保系统
     */
    @Schema(title = "Security system")
    private String securitySystem;

    /**
     * 营业时间以外是否会被使用
     */
    @Schema(title = "Occupied after business hours")
    private YesNoEnum occupiedAfterBusinessHours;

    /**
     * 建筑构架
     */
    @Schema(title = "Building frame")
    private String buildingFrame;

    /**
     * 屋顶结构
     */
    @Schema(title = "Roof structure")
    private String roofStructure;

    /**
     * 墙体结构
     */
    @Schema(title = "Wall structure")
    private String wallStructure;

    /**
     * 地板结构
     */
    @Schema(title = "Floor structure")
    private String floorStructure;

    /**
     * 左侧相邻建筑用途
     */
    @Schema(title = "Adjoining building occupation(left)")
    private String adjoiningLeftBuildingOccupation;

    /**
     * 左侧相邻建筑距离（米）
     */
    @Schema(title = "Adjoining building distance(left, meter)")
    private String adjoiningLeftBuildingDistance;

    /**
     * 右侧相邻建筑用途
     */
    @Schema(title = "Adjoining building occupation(right)")
    private String adjoiningRightBuildingOccupation;

    /**
     * 右侧相邻建筑距离（米）
     */
    @Schema(title = "Adjoining building distance(right, meter)")
    private String adjoiningRightBuildingDistance;

    /**
     * 前侧相邻建筑用途
     */
    @Schema(title = "Adjoining building occupation(front)")
    private String adjoiningFrontBuildingOccupation;

    /**
     * 前侧相邻建筑距离（米）
     */
    @Schema(title = "Adjoining building distance(front, meter)")
    private String adjoiningFrontBuildingDistance;

    /**
     * 后侧相邻建筑用途
     */
    @Schema(title = "Adjoining building occupation(rear)")
    private String adjoiningRearBuildingOccupation;

    /**
     * 后侧相邻建筑距离（米）
     */
    @Schema(title = "Adjoining building distance(rear, meter)")
    private String adjoiningRearBuildingDistance;

    /**
     * 所有权状态
     */
    @Schema(title = "Owner or tenant")
    private String ownershipStatus;

    /**
     * 与building_type(HDB or PRIVATE)联动，
     */
    @Schema(title = "Sub building type(HDB1,HDB2...or PRV01,PRV02...)")
    private String subBuildingType;

    /**
     * Policy Holder Role of Building
     */
    @Schema(title = "Policy holder role of building")
    private String phHolderRoleOfBuilding;

    /**
     * Construction Type
     */
    @Schema(title = "Construction type")
    private String constructionType;

    /**
     * Housing Loan
     */
    @Schema(title = "Housing loan")
    private String housingLoan;

    /**
     * Bank
     */
    @Schema(title = "Bank")
    private String bank;

    /**
     * Loan No
     */
    @Schema(title = "Loan no")
    private String loanNo;

    /**
     * Sum Insured for Building
     */
    @Schema(title = "Sum insured for building")
    private String sumInsuredForBuilding;

    /**
     * Sum Insured for Contents
     */
    @Schema(title = "Sum insured for contents")
    private String sumInsuredForContents;

    //---------NB Quote 新增--------//
    @Schema(title = "The nature of Business")
    private String natureOfBusiness;

    @Schema(title = "Main Use of Building")
    private String mainUseOfBuilding;

    @Schema(title = "The name of insured object")
    private String insuredObjectName;

    @Schema(title = "Estimated Value")
    private String estimatedValue;

    @Schema(title = "The height of Building")
    private String heightOfBuilding;

    @Schema(title = "ire Protection and Security")
    private List<String> fireProtectionAndSecurity;

    @Schema(title = "Insured in Basement")
    private YesNoEnum insuredInBasement;

}
