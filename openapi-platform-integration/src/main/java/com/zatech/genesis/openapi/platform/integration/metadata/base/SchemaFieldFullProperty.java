package com.zatech.genesis.openapi.platform.integration.metadata.base;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.market.BizTopicEnum;
import com.zatech.gaia.resource.graphene.customer.MaskTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SchemaFieldFullProperty extends SchemaFieldProperty {

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "Indicates whether the field is mandatory. Valid values are: 1 for 'Yes' (required), and 2 for 'No' (not required).")
    private YesNoEnum required;

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "Indicates whether the field is displayed in the customer center user interface. Valid values are: 1 for 'Yes' (displayed), and 2 for 'No' (not displayed).")
    private YesNoEnum displayOnCustomerCenter;

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "Indicates whether the field is displayed at the tenant level configuration. Valid values are: 1 for 'Yes' (displayed), and 2 for 'No' (not displayed).")
    private YesNoEnum displayOnTanentLevel;

    @Schema(description = "The order in which the field is displayed in the user interface. Lower numbers indicate higher priority in display order.")
    private Integer orderNo;

    /**
     * Mask Type
     * {@link MaskTypeEnum}
     */
    @Schema(description = "The type of masking applied to the field value for security or display purposes, such as masking sensitive information like passwords or personal identification numbers. Refer to MaskTypeEnum for specific masking types.")
    private MaskTypeEnum maskType;

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "Indicates whether the field is related to calculations within the system, such as in formulas or ratetables. Valid values are: 1 for 'Yes' (calculation-related), and 2 for 'No' (not calculation-related).")
    private YesNoEnum calculationRelated;

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "Indicates whether the field represents a static factor, which is a fixed and unchanging factor used in configurations or calculations. Valid values are: 1 for 'Yes' (static field), and 2 for 'No' (dynamic field).")
    private YesNoEnum staticField;

    @Schema(description = "The categories of formulas where this field can be used or applied. It helps to categorize the field based on its relevance to different formula types, such as premium calculation formulas, claim payout formulas, etc. Refer to BizTopicEnum for specific formula categories.")
    private List<BizTopicEnum> applicableFormulaCategories;


    @Schema(description = "The user-friendly name displayed for this field in the user interface. This name is intended to be easily understood by users and is used for display purposes in forms, reports, and other UI elements.")
    private String displayName;

    @Schema(description = "Indicates whether the field represents a currency amount. Valid values are typically 1 for 'Yes' (currency amount) and 0 for 'No' (not a currency amount).")
    private Integer isCurrencyAmount;

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "Indicates whether this field can be used as part of a unique key to identify data records. Unique keys are used to ensure data integrity and uniqueness within the system. Valid values are: 1 for 'Yes' (available for unique key), and 2 for 'No' (not available for unique key).")
    private Integer availableForUniqueKey;

}
