package com.zatech.genesis.openapi.platform.integration.policy.base;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date Created in 2:48 下午 2019/11/19
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "IssuanceInsuredPersonEntity")
public class IssuanceInsuredPersonEntity extends BaseIssuanceInsuredPersonEntity {
    private static final long serialVersionUID = -5915801115498442242L;
}
