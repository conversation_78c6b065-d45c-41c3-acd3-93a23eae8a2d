package com.zatech.genesis.openapi.platform.integration.outer.response;

import com.zatech.genesis.openapi.platform.integration.cdc.model.base.issuance.BaseIssuanceInsuredEbSupportDTO;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Generated;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/10/29 18:06
 */
public class PolicyInsuredListResponse extends BaseIssuanceInsuredEbSupportDTO {
    @Schema(
        description = "The unique business number assigned to an insurance policy, which is generated based on a pre-defined rule at the tenant level in Configuration Center > Business No. Generation Rule."
    )
    private String policyNo;

    @Generated
    public String getPolicyNo() {
        return this.policyNo;
    }

    @Generated
    public void setPolicyNo(final String policyNo) {
        this.policyNo = policyNo;
    }
}

