/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.claim.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zatech.gaia.resource.claim.CaseDecisionEnum;
import com.zatech.gaia.resource.claim.ClaimStatusEnum;
import com.zatech.gaia.resource.claim.PaymentStatusEnum;
import com.zatech.gaia.resource.components.enums.claim.ClaimTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.openapi.platform.integration.claim.base.IncidentContentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.PolicyMatchApiBase;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
@Schema(name = "CaseApiResponse")
public class CaseApiResponse implements Serializable {

    private static final long serialVersionUID = 6114457611322186554L;

    /**
     * 赔案号
     **/
    @Schema(description = "Case No.")
    private String caseNo;

    /**
     * claim incident info
     */
    @Schema(description = "Incident content")
    private IncidentContentApiBase incidentContent;

    @Schema(description = "Matched policy list")
    private List<PolicyMatchApiBase> policyMatches;

    /**
     * 申请号
     **/
    @Schema(description = "Application No.")
    private String applicationNo;

    @Schema(description = "Transaction No.")
    private String transactionNo;

    /**
     * 报案时间
     **/
    @Schema(description = "Application date")
    private Date applicationDate;

    /**
     * 报案渠道来源
     **/
    @Schema(description = "Channel code")
    @JsonInclude()
    private String channelCode;

    /**
     * 理赔状态
     **/
    @Schema(description = "Case status")
    private ClaimStatusEnum caseStatus;

    /**
     * 理赔决定
     **/
    @Schema(description = "Decision")
    private CaseDecisionEnum decision;

    /**
     * 描述
     **/
    @Schema(description = "Comments")
    private String comments;

    /**
     * 赔付金额
     **/
    @Schema(description = "Payout amount")
    private String payoutAmount;

    /**
     *
     **/
    @Schema(description = "Payment status")
    private PaymentStatusEnum paymentStatus;

    /**
     * 币种
     **/
    @Schema(description = "Base currency")
    private CurrencyEnum baseCurrency;

    /**
     * 定损完成标识
     **/
    @Schema(description = "Assessment complete status")
    private YesNoEnum assessmentCompleteStatus;

    /**
     * 必传文件完成标识
     **/
    @Schema(description = "Material complete status")
    private YesNoEnum materialCompleteStatus;

    /**
     * 理赔类型
     **/
    @Schema(description = "Claim type list")
    private List<ClaimTypeEnum> claimTypes;

}
