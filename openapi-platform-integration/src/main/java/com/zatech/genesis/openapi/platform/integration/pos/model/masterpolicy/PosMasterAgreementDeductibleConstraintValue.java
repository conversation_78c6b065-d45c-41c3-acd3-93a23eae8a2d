/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.pos.model.masterpolicy;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

/**
 * Master Agreement Deductible Constraint Value
 */
@Getter
@Setter
@Schema(title = "POS deductible constraint value", description = "POS deductible constraint value")
public class PosMasterAgreementDeductibleConstraintValue {

    @Schema(title = "POS Agreement Deductible Serial Version ID", description = "Unique identifier for version control of serialized IssuanceCustomerDeclarationAnswerBase objects in POS operations.")
    private static final long serialVersionUID = 1L;

    /**
     * The Amount value
     */
    @Schema(title = "POS Deductible Constraint Amount", description = "The investment for the POS product, represented as a string value.")
    private String amount;

    /**
     * The Rate value
     */
    @Schema(title = "POS Deductible Constraint Rate", description = "The applied to the POS transaction, typically representing a percentage or factor used in policy calculations.")
    private String rate;
}
