package com.zatech.genesis.openapi.platform.integration.pos.model.customer;

import com.zatech.gaia.resource.components.enums.customer.CompanyAddressTypeEnum;
import com.zatech.gaia.resource.components.enums.policybatch.RecordTypeEnum;
import com.zatech.genesis.openapi.platform.integration.pos.model.base.BaseCommon;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "Company address information", name = "PosCompanyAddress")
@EqualsAndHashCode(callSuper = false)
public class PosCompanyAddress extends BaseCommon {

    /**
     * pos_case 主键ID
     */
    @Schema(description = "The identification of POS case")
    private Long posCaseId;

    /**
     * pos_transaction_id
     */
    @Schema(description = "The identification of POS transaction")
    private Long posTransactionId;

    /**
     * 记录类型
     */
    @Schema(description = "The type of record. Such as 1：CHANGE_BEFORE 2：CHANGE_AFTER ")
    private RecordTypeEnum recordType;

    /**
     * 客户中心地址ID
     */
    @Schema(description = "The identification of customer address")
    private Long addressId;

    /**
     * 地址类型
     */
    @Schema(description = "Address type, e.g. 1 - REGISTER, 2 - OFFICE, 3 - WAREHOUSE. For more detail, please refer to CompanyAddressTypeEnum.")
    private CompanyAddressTypeEnum organizationAddressType;

    /**
     * 地址11
     */
    @Schema(description = "Line 1 of first address")
    private String address11;

    /**
     * 地址12
     */
    @Schema(description = "Line 2 of first address")
    private String address12;

    /**
     * 地址13
     */
    @Schema(description = "Line 3 of first address")
    private String address13;

    /**
     * 地址14
     */
    @Schema(description = "Line 4 of first address")
    private String address14;

    /**
     * 地址15
     */
    @Schema(description = "Line 5 of first address")
    private String address15;

    /**
     * 地址21
     */
    @Schema(description = "Line 1 of second address")
    private String address21;

    /**
     * 地址22
     */
    @Schema(description = "Line 2 of second address")
    private String address22;

    /**
     * 地址23
     */
    @Schema(description = "Line 3 of second address")
    private String address23;

    /**
     * 地址24
     */
    @Schema(description = "Line 4 of second address")
    private String address24;

    /**
     * 地址25
     */
    @Schema(description = "Line 5 of second address")
    private String address25;

    /**
     * 邮编
     */
    @Schema(description = "The code of zip")
    private String zipCode;

    /**
     * 用户ID
     */
    @Schema(description = "Party ID")
    private Long partyId;

    @Schema(description = "The ID of company")
    private Long companyId;

    @Schema(description = "The extension list of POS address")
    private Map<String, String> extensions;

}
