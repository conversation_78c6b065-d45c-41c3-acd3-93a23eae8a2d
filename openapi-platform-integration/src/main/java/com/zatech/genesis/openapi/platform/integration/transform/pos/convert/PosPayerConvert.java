/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.pos.convert;

import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.genesis.model.policy.customer.PartyIndividual;
import com.zatech.genesis.model.policy.customer.party.PartyAccount;
import com.zatech.genesis.model.policy.customer.party.PartyAddress;
import com.zatech.genesis.model.policy.customer.party.PartyEmail;
import com.zatech.genesis.model.policy.customer.party.PartyPhone;
import com.zatech.genesis.model.pos.PosCase;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosBillInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosCaseCollectionAccountInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosCasePaymentAccountInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosPayerInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerAccount;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerAddress;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerEmail;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerPhone;
import com.zatech.genesis.openapi.platform.integration.transform.BaseConvert;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;
import com.zatech.octopus.common.util.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PosPayerConvert extends BaseConvert {

    PosPayerConvert INSTANCE = Mappers.getMapper(PosPayerConvert.class);

    default List<PosPayerInfo> toPosPayerList(List<com.zatech.genesis.model.pos.PosPayerInfo> source, PosCase posCase) {
        return Optional.ofNullable(source).orElse(Collections.emptyList()).stream()
            .map(item -> this.toPosPayer(item, posCase)).filter(Objects::nonNull).toList();
    }

    default PosPayerInfo toPosPayer(com.zatech.genesis.model.pos.PosPayerInfo source, PosCase posCase) {
        if (source == null) {
            return null;
        }
        PosPayerInfo posPayerInfo;
        if (source.getIndividual() != null && source.getCustomerType() == PartyTypeEnum.INDIVIDUAL) {
            posPayerInfo = PosPayerConvert.INSTANCE.convert(source.getIndividual());
        } else {
            posPayerInfo = new PosPayerInfo();
        }
        posPayerInfo.setPosBillInfoList(source.getPosBillInfoList().stream().map(item -> BeanUtils.copyProperties(item, PosBillInfo.class)).toList());
        posPayerInfo.setCollectionAccount(BeanUtils.copyProperties(source.getCollectionAccount(), PosCaseCollectionAccountInfo.class));
        posPayerInfo.setPolicyNo(posCase.getPolicyNo());
        posPayerInfo.setCustomerId(source.getCustomerId());
        posPayerInfo.setPosPremiumPayerType(source.getPosPremiumPayerType());
        posPayerInfo.setOriginalPremiumPayerType(source.getOriginalPremiumPayerType());
        posPayerInfo.setPremiumPayerType(source.getPremiumPayerType());
        posPayerInfo.setOriginalRoleType(source.getOriginalRoleType());
        posPayerInfo.setPosPolicyCommonCustomerId(source.getPosPolicyCommonCustomerId());
        posPayerInfo.setOrganizationIdNo(source.getOrganizationIdNo());
        posPayerInfo.setOrganizationName(source.getOrganizationName());
        posPayerInfo.setOrganizationIdType(source.getOrganizationIdType());
        posPayerInfo.setPartyType(source.getCustomerType());
        posPayerInfo.setAllowDelete(BooleanToYesNoEnum.convert(source.getAllowDelete()));
        posPayerInfo.setCustomerId(source.getCustomerId());
        posPayerInfo.setOrganizationIdNo(source.getOrganizationIdNo());
        posPayerInfo.setOrganizationName(source.getOrganizationName());
        posPayerInfo.setOrganizationIdType(source.getOrganizationIdType());
        return posPayerInfo;
    }

    @Mapping(target = "posBillSummaryInfos", source = "posBillSummaryInfoList")
    @Mapping(source = "bankName", target = "bank")
    @Mapping(source = "cardNumber", target = "accountNumber")
    PosCasePaymentAccountInfo convert(com.zatech.genesis.model.pos.PosCasePaymentAccountInfo source);

    @Mapping(target = "isPromotional", expression = "java(mapBooleanToEnum(source.getIsPromotional()))")
    @Mapping(target = "socialSecurity", expression = "java(mapBooleanToEnum(source.getSocialSecurity()))")
    @Mapping(target = "smoke", expression = "java(mapBooleanToEnum(source.getSmoke()))")
    @Mapping(target = "isCustomer", expression = "java(mapBooleanToEnum(source.getIsCustomer()))")
    @Mapping(target = "deathOrNot", expression = "java(mapBooleanToEnum(source.getIsCustomer()))")
    @Mapping(source = "emailList", target = "emails")
    @Mapping(source = "phoneList", target = "phones")
    @Mapping(source = "addressList", target = "addresses")
    @Mapping(source = "accountList", target = "accounts")
    @Mapping(target = "extensions", source = "extensions", qualifiedByName = "mapToStringValues")
    PosPayerInfo convert(PartyIndividual source);

    PosCustomerAddress convertAddress(PartyAddress partyAddress);

    PosCustomerPhone convertPhone(PartyPhone partyPhone);

    PosCustomerEmail convertEmail(PartyEmail partyEmail);

    PosCustomerAccount convertAccount(PartyAccount partyAccount);
}
