/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.transform.pos.convert;

import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.genesis.model.policy.customer.PartyIndividual;
import com.zatech.genesis.model.policy.customer.Payer;
import com.zatech.genesis.model.policy.customer.party.PartyAccount;
import com.zatech.genesis.model.policy.customer.party.PartyAddress;
import com.zatech.genesis.model.policy.customer.party.PartyEmail;
import com.zatech.genesis.model.policy.customer.party.PartyPhone;
import com.zatech.genesis.model.policy.customer.party.PartySocialAccount;
import com.zatech.genesis.model.pos.PosCase;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosBillInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosCaseCollectionAccountInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosCasePaymentAccountInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosPayerInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.PosPolicyPayerInfo;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerAccount;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerAddress;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerEmail;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerPhone;
import com.zatech.genesis.openapi.platform.integration.pos.model.customer.PosCustomerSocialAccount;
import com.zatech.genesis.openapi.platform.integration.transform.BaseConvert;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;
import com.zatech.octopus.common.util.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PosPayerConvert extends BaseConvert {

    PosPayerConvert INSTANCE = Mappers.getMapper(PosPayerConvert.class);

    default List<PosPayerInfo> toPosPayerList(List<com.zatech.genesis.model.pos.PosPayerInfo> source, PosCase posCase) {
        return Optional.ofNullable(source).orElse(Collections.emptyList()).stream()
            .map(item -> this.toPosPayer(item, posCase)).filter(Objects::nonNull).toList();
    }

    default PosPayerInfo toPosPayer(com.zatech.genesis.model.pos.PosPayerInfo source, PosCase posCase) {
        if (source == null) {
            return null;
        }
        PosPayerInfo posPayerInfo;
        if (source.getIndividual() != null && source.getCustomerType() == PartyTypeEnum.INDIVIDUAL) {
            posPayerInfo = PosPayerConvert.INSTANCE.convert(source.getIndividual());
        } else {
            posPayerInfo = new PosPayerInfo();
        }
        posPayerInfo.setPosBillInfoList(source.getPosBillInfoList().stream().map(item -> BeanUtils.copyProperties(item, PosBillInfo.class)).toList());
        posPayerInfo.setCollectionAccount(BeanUtils.copyProperties(source.getCollectionAccount(), PosCaseCollectionAccountInfo.class));
        posPayerInfo.setPolicyNo(posCase.getPolicyNo());
        posPayerInfo.setCustomerId(source.getCustomerId());
        posPayerInfo.setPosPremiumPayerType(source.getPosPremiumPayerType());
        posPayerInfo.setOriginalPremiumPayerType(source.getOriginalPremiumPayerType());
        posPayerInfo.setPremiumPayerType(source.getPremiumPayerType());
        posPayerInfo.setOriginalRoleType(source.getOriginalRoleType());
        posPayerInfo.setPosPolicyCommonCustomerId(source.getPosPolicyCommonCustomerId());
        posPayerInfo.setOrganizationIdNo(source.getOrganizationIdNo());
        posPayerInfo.setOrganizationName(source.getOrganizationName());
        posPayerInfo.setOrganizationIdType(source.getOrganizationIdType());
        posPayerInfo.setPartyType(source.getCustomerType());
        posPayerInfo.setAllowDelete(BooleanToYesNoEnum.convert(source.getAllowDelete()));
        posPayerInfo.setCustomerId(source.getCustomerId());
        posPayerInfo.setOrganizationIdNo(source.getOrganizationIdNo());
        posPayerInfo.setOrganizationName(source.getOrganizationName());
        posPayerInfo.setOrganizationIdType(source.getOrganizationIdType());
        return posPayerInfo;
    }

    @Mapping(target = "posBillSummaryInfos", source = "posBillSummaryInfoList")
    @Mapping(source = "bankName", target = "bank")
    @Mapping(source = "cardNumber", target = "accountNumber")
    PosCasePaymentAccountInfo convert(com.zatech.genesis.model.pos.PosCasePaymentAccountInfo source);

    @Mapping(target = "isPromotional", expression = "java(mapBooleanToEnum(source.getIsPromotional()))")
    @Mapping(target = "socialSecurity", expression = "java(mapBooleanToEnum(source.getSocialSecurity()))")
    @Mapping(target = "smoke", expression = "java(mapBooleanToEnum(source.getSmoke()))")
    @Mapping(target = "isCustomer", expression = "java(mapBooleanToEnum(source.getIsCustomer()))")
    @Mapping(target = "deathOrNot", expression = "java(mapBooleanToEnum(source.getIsCustomer()))")
    @Mapping(source = "emailList", target = "emails")
    @Mapping(source = "phoneList", target = "phones")
    @Mapping(source = "addressList", target = "addresses")
    @Mapping(source = "accountList", target = "accounts")
    @Mapping(target = "extensions", source = "extensions", qualifiedByName = "mapToStringValues")
    PosPayerInfo convert(PartyIndividual source);

    PosCustomerAddress convertAddress(PartyAddress partyAddress);

    PosCustomerPhone convertPhone(PartyPhone partyPhone);

    PosCustomerEmail convertEmail(PartyEmail partyEmail);

    PosCustomerAccount convertAccount(PartyAccount partyAccount);

    /**
     * 将integration的PosPayerInfo转换为unified-model的PosPayerInfo
     */
    default com.zatech.genesis.model.pos.PosPayerInfo integration2ModelConvert(PosPayerInfo source) {
        if (source == null) {
            return null;
        }

        com.zatech.genesis.model.pos.PosPayerInfo target = new com.zatech.genesis.model.pos.PosPayerInfo();

        // 转换PosPayerInfo特有字段
        target.setPolicyNo(source.getPolicyNo());
        target.setCustomerId(source.getCustomerId());
        target.setPosPremiumPayerType(source.getPosPremiumPayerType());
        target.setOriginalPremiumPayerType(source.getOriginalPremiumPayerType());
        target.setPremiumPayerType(source.getPremiumPayerType());
        target.setOriginalRoleType(source.getOriginalRoleType());
        target.setPosPolicyCommonCustomerId(source.getPosPolicyCommonCustomerId());
        target.setOrganizationIdNo(source.getOrganizationIdNo());
        target.setOrganizationName(source.getOrganizationName());
        target.setOrganizationIdType(source.getOrganizationIdType());
        target.setPartyType(source.getPartyType());

        // 转换Boolean字段
        if (source.getAllowDelete() != null) {
            target.setAllowDelete(BooleanToYesNoEnum.convert(source.getAllowDelete()));
        }

        // 将PosCustomer中的字段转换到Customer (继承的)
        convertPosCustomerToCustomer(source, target);

        // 转换嵌套对象
        if (source.getCollectionAccount() != null) {
            target.setCollectionAccount(convertCollectionAccount(source.getCollectionAccount()));
        }

        // 转换列表字段
        if (source.getPosBillInfoList() != null) {
            target.getPosBillInfoList().addAll(convertBillInfoList(source.getPosBillInfoList()));
        }

        return target;
    }

    /**
     * 将PosCustomer中与Customer相同的字段转换到Customer
     */
    default void convertPosCustomerToCustomer(PosPayerInfo source, 
                                              com.zatech.genesis.model.pos.PosPayerInfo target) {
        // Customer基础字段
        target.setCustomerId(source.getCustomerId());
        target.setCustomerType(source.getPartyType());
        target.setRoleType(source.getRoleType());

        // extensions字段转换
        if (source.getExtensions() != null) {
            target.getExtensions().putAll(source.getExtensions());
        }

        // 由于Customer主要依赖PartyIndividual和PartyOrganization，
        // 而PosCustomer包含扁平化的字段，需要构建对应的Individual或Organization对象
        if (com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum.INDIVIDUAL.equals(source.getPartyType())) {
            target.setIndividual(convertToPartyIndividual(source));
        } else if (com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum.COMPANY.equals(source.getPartyType())) {
            target.setOrganization(convertToPartyOrganization(source));
        }
    }

    /**
     * 将PosCustomer转换为PartyIndividual
     */
    default com.zatech.genesis.model.policy.customer.PartyIndividual convertToPartyIndividual(PosPayerInfo source) {
        com.zatech.genesis.model.policy.customer.PartyIndividual individual = 
            new com.zatech.genesis.model.policy.customer.PartyIndividual();

        // 基本信息
        individual.setPartyId(source.getCustomerId());
        individual.setFullName(source.getFullName());
        individual.setLastName(source.getLastName());
        individual.setMiddleName(source.getMiddleName());
        individual.setFirstName(source.getFirstName());
        individual.setSex(source.getSex());
        individual.setBirthday(source.getBirthday());
        individual.setCertiNo(source.getCertiNo());
        individual.setCertiType(source.getCertiType());
        individual.setBirthPlace(source.getBirthPlace());
        individual.setNationality(source.getNationality());
        individual.setEducation(source.getEducation());
        individual.setOccupationStatus(source.getOccupationStatus());
        individual.setOccupationCode(source.getOccupationCode());
        individual.setIndustryCode(source.getIndustryCode());
        individual.setRace(source.getRace());
        individual.setResidenceCountry(source.getResidenceCountry());
        individual.setMarriageStatus(source.getMarriageStatus());
        individual.setHolderRelation(source.getHolderRelation());
        individual.setIncome(source.getIncome());
        individual.setHeight(source.getHeight());
        individual.setWeight(source.getWeight());

        // Boolean字段转换
        individual.setIsPromotional(BooleanToYesNoEnum.convert(source.getIsPromotional()));
        individual.setSocialSecurity(BooleanToYesNoEnum.convert(source.getSocialSecurity()));
        individual.setSmoke(BooleanToYesNoEnum.convert(source.getSmoke()));
        individual.setIsCustomer(BooleanToYesNoEnum.convert(source.getIsCustomer()));
        individual.setDeathOrNot(BooleanToYesNoEnum.convert(source.getDeathOrNot()));

        // 转换地址、邮箱、电话等列表
        if (source.getAddresses() != null) {
            individual.getAddressList().addAll(source.getAddresses().stream()
                .map(this::integration2ModelConvertAddress).toList());
        }
        if (source.getEmails() != null) {
            individual.getEmailList().addAll(source.getEmails().stream()
                .map(this::integration2ModelConvertEmail).toList());
        }
        if (source.getPhones() != null) {
            individual.getPhoneList().addAll(source.getPhones().stream()
                .map(this::integration2ModelConvertPhone).toList());
        }
        if (source.getAccounts() != null) {
            individual.getAccountList().addAll(source.getAccounts().stream()
                .map(this::integration2ModelConvertAccount).toList());
        }

        return individual;
    }

    /**
     * 将PosCustomer转换为PartyOrganization
     */
    default com.zatech.genesis.model.policy.customer.PartyOrganization convertToPartyOrganization(PosPayerInfo source) {
        com.zatech.genesis.model.policy.customer.PartyOrganization organization = 
            new com.zatech.genesis.model.policy.customer.PartyOrganization();

        // 基本信息
        organization.setPartyId(source.getCustomerId());
        organization.setCompanyName(source.getOrganizationName());
        organization.setBusinessLicenseNumber(source.getOrganizationIdNo());
        organization.setIdType(source.getOrganizationIdType());

        return organization;
    }

    /**
     * 转换PosCaseCollectionAccountInfo
     */
    default com.zatech.genesis.model.pos.PosCaseCollectionAccountInfo convertCollectionAccount(PosCaseCollectionAccountInfo source) {
        if (source == null) {
            return null;
        }
        // 使用BeanUtils进行基本转换
        return BeanUtils.copyProperties(source, com.zatech.genesis.model.pos.PosCaseCollectionAccountInfo.class);
    }

    /**
     * 转换PosBillInfo列表
     */
    default List<com.zatech.genesis.model.pos.PosBillInfo> convertBillInfoList(List<PosBillInfo> source) {
        if (source == null) {
            return new java.util.ArrayList<>();
        }
        return source.stream()
            .map(this::convertBillInfo)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 转换单个PosBillInfo
     */
    default com.zatech.genesis.model.pos.PosBillInfo convertBillInfo(PosBillInfo source) {
        if (source == null) {
            return null;
        }
        // 使用BeanUtils进行转换
        return BeanUtils.copyProperties(source, com.zatech.genesis.model.pos.PosBillInfo.class);
    }

    List<com.zatech.genesis.model.pos.PosPayerInfo> integration2ModelConvertList(List<PosPayerInfo> sourceList);


    PartyAddress integration2ModelConvertAddress(PosCustomerAddress posCustomerAddress);

    PartyPhone integration2ModelConvertPhone(PosCustomerPhone posCustomerPhone);

    PartyEmail integration2ModelConvertEmail(PosCustomerEmail posCustomerEmail);

    PartyAccount integration2ModelConvertAccount(PosCustomerAccount posCustomerAccount);

    PartySocialAccount integration2ModelConvertSocialAccount(PosCustomerSocialAccount posCustomerSocialAccount);

    default List<PosPolicyPayerInfo> convertPosPolicyPayerInfoList(List<Payer> source) {
        return Optional.ofNullable(source).orElse(Collections.emptyList()).stream()
            .map(item -> this.convertPosPolicyPayerInfo(item)).filter(Objects::nonNull).toList();
    }

    @Mapping(target = "posCompanyInfo", expression = "java(PosCustomerConvert.INSTANCE.convert(source.getOrganization()))")
    @Mapping(target = ".", source = "individual")
    @Mapping(target = "addresses", source = "individual.addressList")
    @Mapping(target = "emails", source = "individual.emailList")
    @Mapping(target = "phones", source = "individual.phoneList")
    @Mapping(target = "accounts", source = "individual.accountList")
    @Mapping(target = "socialAccounts", source = "individual.socialAccountList")
    @Mapping(target = "partyType", source = "customerType")
    PosPolicyPayerInfo convertPosPolicyPayerInfo(Payer source);

    default List<Payer> convertToPayerList(List<PosPolicyPayerInfo> policyPayerList) {
        return Optional.ofNullable(policyPayerList).orElse(Collections.emptyList()).stream()
            .map(item -> this.convertToPayer(item)).filter(Objects::nonNull).toList();
    }

    @Mapping(target = "organization", expression = "java(PosCustomerConvert.INSTANCE.integration2ModelConvert(source.getPosCompanyInfo()))")
    @Mapping(target = "customerType", source = "partyType")
    @Mapping(target = "individual", source = ".")
    Payer convertToPayer(PosPolicyPayerInfo source);

    @AfterMapping
    default void mapToIndividual(@MappingTarget Payer target, PosPolicyPayerInfo source) {
        if (target.getIndividual() != null) {
            // 转换地址、邮箱、电话等列表
            PartyIndividual individual = target.getIndividual();
            if (CollectionUtils.isNotEmpty(source.getAddresses())) {
                individual.getAddressList().addAll(source.getAddresses().stream()
                    .map(this::integration2ModelConvertAddress).toList());
            }
            if (CollectionUtils.isNotEmpty(source.getEmails())) {
                individual.getEmailList().addAll(source.getEmails().stream()
                    .map(this::integration2ModelConvertEmail).toList());
            }
            if (CollectionUtils.isNotEmpty(source.getPhones())) {
                individual.getPhoneList().addAll(source.getPhones().stream()
                    .map(this::integration2ModelConvertPhone).toList());
            }
            if (CollectionUtils.isNotEmpty(source.getAccounts())) {
                individual.getAccountList().addAll(source.getAccounts().stream()
                    .map(this::integration2ModelConvertAccount).toList());
            }
            if (CollectionUtils.isNotEmpty(source.getSocialAccounts())) {
                individual.getSocialAccountList().addAll(source.getSocialAccounts().stream()
                    .map(this::integration2ModelConvertSocialAccount).toList());
            }
        }
    }

}
