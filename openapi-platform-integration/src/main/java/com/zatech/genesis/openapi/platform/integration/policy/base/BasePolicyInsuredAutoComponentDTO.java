/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.policy.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.policy.VehicleEngineTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 标的车的信息<br/>
 *
 * <AUTHOR> 2017年7月6日 上午11:31:12
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(name = "BasePolicyInsuredAutoComponentDTO", title = "", description = "保单信息请求参数")
public class BasePolicyInsuredAutoComponentDTO extends BasePolicyInsuredObjectComponentDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 分库分表id
     */
    private Long holderCustomerId;

    /**
     * 车险标的主键
     */
    @Schema(title = "车险标的主键")
    private Long policyInsuredAutoId;

    /**
     * 车号
     */
    @Schema(title = "车号/车牌号", required = true)
    private String vehicleNo;

    /**
     * 制造商
     */
    @Schema(title = "制造商", required = true)
    private String make;

    /**
     * 型号
     */
    @Schema(title = "型号", required = true)
    private String model;

    /**
     * 版本
     */
    @Schema(title = "版本")
    private String variant;

    /**
     * 出厂日期
     */
    @Schema(title = "出厂日期", required = true)
    private Date yearOfMake;

    /**
     * 车型
     */
    @Schema(title = "车型")
    private String bodyType;

    /**
     * 载重(KG)
     */
    @Schema(title = "载重(KG)")
    private String carringCapacity;

    /**
     * 载客
     */
    @Schema(title = "载客", required = true)
    private String seatingCapity;

    /**
     * 保险金额
     */
    @Schema(title = "保险金额", required = true)
    private String sumInsured;

    /**
     * 车辆购买金额
     */
    @Schema(title = "车辆购买金额", required = true)
    private String purchasePrice;

    /**
     * 购买日期
     */
    @Schema(title = "购买日期", required = true)
    private Date dateOfPurchase;

    /**
     * 登记日期
     */
    @Schema(title = "登记日期", required = true)
    private Date registrationDate;

    /**
     * 归属地
     */
    @Schema(title = "归属地")
    private String geographicalLocationWhereVehicleIsGaraged;

    /**
     * 导入类型
     */
    @Schema(title = "导入类型")
    private String importType;

    /**
     * 登记卡号
     */
    @Schema(title = "登记卡号")
    private String registrationCardNo;

    /**
     * 发动机号
     */
    @Schema(title = "发动机号")
    private String engineNo;

    /**
     * 底盘号
     */
    @Schema(title = "底盘号", required = true)
    private String chassisNo;

    /**
     * 发动机容量/排量
     */
    @Schema(title = "发动机容量/排量", required = true)
    private String engineCapacity;

    /**
     * 车辆颜色
     */
    @Schema(title = "车辆颜色")
    private String colourOfVehicle;

    /**
     * 使用的燃料类型
     */
    @Schema(title = "使用的燃料类型")
    private String typeOfFuelUsed;

    /**
     * 安装的设备
     */
    @Schema(title = "安装的设备")
    private String devicesInstalled;

    /**
     * 齿轮或转向锁
     */
    @Schema(title = "齿轮或转向锁")
    private String gearOrSteeringLock;

    /**
     * gps
     */
    @Schema(title = "gps")
    private String gps;

    /**
     * 安全气囊
     */
    @Schema(title = "安全气囊")
    private String airbags;

    /**
     * 跟踪系统
     */
    @Schema(title = "跟踪系统")
    private String trackingSystem;

    /**
     * 锁定
     */
    @Schema(title = "锁定")
    private String immobiliser;

    /**
     * 报警器
     */
    @Schema(title = "报警器")
    private String factoryFittedAlarm;

    /**
     * abs
     */
    @Schema(title = "abs")
    private String abs;

    /**
     * 财务公司/贷款提供商
     */
    @Schema(title = "财务公司/贷款提供商")
    private String loanProvider;

    /**
     * 注册类型
     */
    @Schema(title = "注册类型")
    private String classRegisteredAs;

    /**
     * 用途
     */
    @Schema(title = "用途", required = true)
    private String useOfVehicle;

    /**
     * 运输许可证编号
     */
    @Schema(title = "运输许可证编号")
    private String haulagePermitNo;

    /**
     * 如果在非高峰车计划下
     */
    @Schema(title = "如果在非高峰车计划下")
    private String ifUnderTheOffPeakCarScheme;

    /**
     * 运输货物类型
     */
    @Schema(title = "运输货物类型")
    private String typesOfGoodsCarried;

    /**
     * 车龄
     */
    @Schema(title = "车龄")
    private Integer vehicleAge;

    /**
     * 上年出险次数
     */
    @Schema(title = "上年出险次数")
    private Integer numberOfAccidentInLastYear;

    /**
     * 新转续的标志
     * 'N代表新保，R代表续保，T代表转保' 现在只会是新保
     * 不入库
     */
    @Setter(value = AccessLevel.NONE)
    private String insuraceFlag = "N";

    /**
     * 是否免赔
     */
    private String additionalExcess;

    /**
     * 车架号 Vehicle Identification Number
     */
    private String vehicleIdentificationNumber;

    /*********************** below since v2 new add **************************/

    /**
     * 车辆结构（两轮、三轮、四轮、大于四轮）
     **/
    private String vehicleStructure;

    /**
     * 车辆类型
     */
    private String vehicleType;

    /**
     * 引擎类型
     */
    private VehicleEngineTypeEnum vehicleEngine;

    /**
     * 是否未上牌
     */
    private YesNoEnum isNotRegistered;

    /**
     * 车辆注册号
     */
    private String registrationNo;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private String colorOfPlateNo;

    /**
     * 新车标志
     */
    private YesNoEnum isNewVehicle;

    /**
     * 异形车标志
     */
    private YesNoEnum isSpecialShapeVehicle;

    /**
     * 车贷车标志
     */
    private YesNoEnum isLoanVehicle;

    /**
     * 车贷贷款年数
     */
    private String loanYears;

    /**
     * 制造品牌（丰田）
     */
    private String vehicleMake;

    /**
     * 车辆型号（卡罗拉）
     */
    private String vehicleModel;

    private Date yearOfManufacturing;

    /**
     * 车辆用途（商用，家用，通勤，通学）
     */
    private String vehicleUsage;

    /**
     * 车排放量
     */
    private String vehicleCapacity;

    /**
     * 座位数
     */
    private String numberOfSeat;

    /**
     * 载重量
     */
    private String tonnage;

    /**
     * 购买日期 Date of Purchase
     */
    private Date purchaseDate;

    /**
     * 车架号
     */
    private String vinNo;

    /**
     * 车辆颜色
     */
    private String vehicleColor;

    /**
     * 非高峰时段使用
     */
    private String offPeakCar;

    /**
     * 中断证明书
     */
    private String suspensionCertificate;

    /**
     * 车长
     */
    private String lengthOfVehicle;

    /**
     * 车宽
     */
    private String widthOfVehicle;

    /**
     * 车高
     */
    private String heightOfVehicle;

    /**
     * 前前轴重
     */
    private String ffWeight;

    /**
     * 前后轴重
     */
    private String frWeight;

    /**
     * 后前轴重
     */
    private String rfWeight;

    /**
     * 后后轴重
     */
    private String rrWeight;

    /**
     * 车检有效期
     */
    private Date inspectionExpiryDate;

    /**
     * 主要行驶区域
     */
    private String mainDrivingArea;

    /**
     * 去年行驶距离
     */
    private String lastYearDrivingDistance;

    /**
     * 平均行驶距离
     */
    private String averageDrivingDistance;

    /**
     * 历史行驶距离
     */
    private String drivingDistance;

    /**
     * 距离的确认日期
     */
    private Date distanceConfirmationDate;

    /**
     * 实际价值
     */
    private String marketPrice;

    /**
     * 使用的燃料类型
     */
    private String powerType;

    /**
     * 车牌地域名
     */
    private String registrationArea;

    /**
     * 车牌分类番号
     */
    private String registrationCategory;

    /**
     * 车牌假名区别
     */
    private String registrationHiragana;

    /**
     * 车牌一连指定番号
     */
    private String registrationSerialNo;

    /**
     * 车辆重量
     */
    private String weight;

    /**
     * 车辆总重量
     */
    @JsonProperty("gWeight")
    private String gWeight;

    /**
     * 安装安全设备可以减低保费
     */
    private String deviceInstalled;

    /**
     * 是否安装GPS
     */
    private String deviceGps;

    /**
     * 是否安装安全气囊
     */
    private String deviceAirbag;

    /**
     * 是否安装追踪设备
     */
    private String deviceTracking;

    /**
     * 是否安装锁定设备
     */
    private String deviceImmobiliser;

    /**
     * 是否安装报警设备
     */
    private String deviceAlarm;

    /**
     * 是否安装ABS制动装置
     */
    private String deviceAbs;

    /**
     * 是否安装齿轮和转向锁
     */
    private String deviceGearOrSteeringLock;

    /**
     * 是否安装AEB
     */
    private String deviceAeb;

    /**
     * 运输货物类型
     */
    private String carryingGoodsType;

    /**
     * 车辆费率等级
     */
    private String rateClassesOd;

    /**
     * 物品费率等级
     */
    private String rateClassesPd;

    /**
     * 人物费率等级
     */
    private String rateClassesBi;

    /**
     * 伤害费率等级
     */
    private String rateClassesPic;

    private String violationCode;

    private String vehicleMarketValue;

    private String engineType;

    private String vehicleSeries;

    private String vehicleGroup;

    private String loadCapacity;

    private String vehicleCategory;

    private String armoredVehicle;

    private String vehicleModification;

    private String enginePower;

    private String engineVolume;

    private String vehicleInvoiceValue;

    @Schema(title = "Vehicle brand")
    private String vehicleBrand;

    @Schema(title = "Auto model")
    private String autoModel;

    @Schema(title = "Auto body type")
    private String autoBodyType;

    @Schema(title = "Vehicle in advertising and branding")
    private String vehicleInAdvertisingAndBranding;

    @Schema(title = "Registration end date")
    private Date registrationEndDate;

    @Schema(title = "Car driven right hand")
    private String carDrivenRightHand;

    @Schema(title = "MTPL no.")
    private String mtplNo;

    @Schema(title = "Vehicle Loan")
    private YesNoEnum vehicleLoan;

    @Schema(title = "Vehicle equipment package")
    private String vehicleEquipmentPackage;

}
