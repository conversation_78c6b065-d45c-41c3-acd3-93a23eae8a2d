/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.claim.base;

import com.zatech.gaia.resource.claim.VehiclePriceTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "VehicleLossSundryApiBase")
public class VehicleLossSundryApiBase implements Serializable {

    private static final long serialVersionUID = 2366054522060528807L;

    private Long vehicleLossId;

    @Schema(description = "Item", required = true)
    private String item;

    @Schema(description = "Item code")
    private String code;

    @Schema(description = "Price type")
    private VehiclePriceTypeEnum priceType;

    @Schema(description = "Unit price")
    private String unitPrice;

    @Schema(description = "Quantity")
    private String quantity;

    @Schema(description = "Amount")
    private String amount;

    @Schema(description = "Assessment amount", required = true)
    private String assessmentAmount;

    @Schema(description = "Verification amount")
    private String verificationAmount;

    @Schema(description = "Salvage recovery")
    private YesNoEnum salvageRecovery;

    @Schema(description = "Salvage value")
    private String salvageValue;

    @Schema(description = "Comments")
    private String comments;

    @Schema(description = "Extension field")
    private String extraInfo;


    @Schema(description = "Estimation Amount")
    private String estimationAmount;


}
