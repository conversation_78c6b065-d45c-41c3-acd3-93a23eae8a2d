/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.integration.cdc.model.base;

import com.zatech.gaia.resource.biz.MainInsurantRelationEnum;
import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.RelationshipWithMainInsuredEnum;
import com.zatech.gaia.resource.components.enums.common.UserTypeEnum;
import com.zatech.gaia.resource.components.enums.metadata.InsuredNatureEnum;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 被保险人 DTO
 *
 * <AUTHOR>
 * @Date 2020/5/13 10:52
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
public class BasePolicyInsurantSupportDTO extends BasePolicyCommonSupportDTO {

    private static final long serialVersionUID = 8071053339927756755L;

    /**
     * 数据库主键id
     */
    private Long policyInsurantId;

    /**
     * 用户类型1：个人，2：公司
     */
    private UserTypeEnum userType;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 邮箱id
     */
    private Long emailId;

    /**
     * 手机id
     */
    private Long mobileId;

    /**
     * 保单险种id
     */
    private Long policyProductId;

    /**
     * 保单id
     */
    private Long policyId;

    /**
     * 与投保人关系
     */
    private RelationEnum holderRelation;

    /**
     * 被保险人顺序
     */
    private Integer insuredOrder;

    /**
     * 投保人客户id
     */
    private Long holderCustomerId;

    /**
     * 虚拟小孩儿数量
     **/
    private Integer virtualKidNumber;

    /**
     * 虚拟成年人数量
     **/
    private Integer virtualAdultNumber;

    /**
     * 虚拟总数量
     **/
    private Integer virtualTotalNumber;

    /**
     * 主被保人关系
     **/
    private MainInsurantRelationEnum mainInsurantRelation;

    /**
     * 与主被保人关系
     **/
    private RelationshipWithMainInsuredEnum relationshipWithMainInsured;

    /*
     *("occupationClass")
     */
    private String occupationClass;
    //#######################扩展字段##############################

    private InsuredNatureEnum insuredNature;

}
