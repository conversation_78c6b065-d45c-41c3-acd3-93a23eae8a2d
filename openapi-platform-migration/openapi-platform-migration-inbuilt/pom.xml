<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>openapi-platform-migration</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>openapi-platform-migration-inbuilt</artifactId>
    <name>openapi-platform-migration-inbuilt</name>
    <packaging>jar</packaging>
    <description>The OpenApi Platform migration inbuilt Module</description>

    <dependencies>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-spec-inbuilt</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-migration-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-infra</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

</project>
