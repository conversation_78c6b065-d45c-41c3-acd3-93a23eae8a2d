{"temporary": 0, "isRenewalPolicy": 1, "kycStatus": "0", "goodsId": "12345", "remark": "remark", "policyNo": "not required", "channelRole": 0, "premium": 1234, "productDecision": [{"decisions": [{"uwDecision": {"ruleCode": "rule code", "rejectReason": "reject reason", "ruleDecisionCode": "rule decision", "nbConfigurationTypeCode": "2"}}], "productId": "productId"}], "campaigns": [{"rate": 16, "campaignCode": "campaignCode", "promotionCode": "promotionCode"}], "salesChannelCode": "not required", "insureDate": "2023-07-21T20:27:01.269+08", "zoneId": "Asia/singapore", "plan": {"holder": {"person": {"PEP": [{"name": "test1name", "title": "ttile", "relationshipToRelated": "1"}], "elements": {"race": "race", "smoke": "YES", "taxes": [{"taxNo": "10109", "taxResidentialCountry": "Singapore", "E_TaxNoNotAvailableReason": "reason desc", "E_TaxNoNotAvailableReasonCode": "B"}], "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "Singapore", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "ckaIndicator": "YES", "E_SingaporePR": "YES", "E_UWEnquiryID": "101", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "E_SourceOfWealth": "1", "ckaEffectiveDate": "2023-07-14T11:01:09.564+08:00", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "taxResidenceIndicator": "1", "E_SourceOfWealthDetails": "E_SourceOfWealthDetails", "expiryDateOfCertificate": "2030-02-01", "E_TaxResidenceConflictReason": "decription", "E_TaxResidenceConflictReasonCode": "3", "E_TaxResidenceIndicatorClarification": "desc"}, "attachments": [{"attachmentUrl": "********-b819-4987-94d0-0fea08b23b1e", "attachmentName": "12345.jpg", "attachmentType": "1"}]}}, "payers": {"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "accounts": [{"iban": "iban", "bankCity": "city", "bankCode": "bankCode", "bankName": "bankName", "swiftCode": "swiftCode", "cardNumber": "cardNumber", "expiryDate": "2023-07-14T11:01:09.564+08:00", "accountType": "2", "accountSubType": "1", "bankBranchCode": "branchCode", "bankBranchName": "branchName", "cardHolderName": "<PERSON><PERSON><PERSON>"}], "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "payerType": "1", "birthPlace": "birthPlace", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "paymentMethod": "1", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "E_SourceOfWealth": "1", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "E_SourceOfWealthDetails": "E_SourceOfWealthDetails", "expiryDateOfCertificate": "2030-02-01", "E_RelationshipDescription": "description", "relationshipWithPolicyholder": "1"}}]}, "planId": "***********", "elements": {"expireDate": "2023-07-14T11:01:09.564+08:00", "effectiveDate": "2023-07-14T11:01:09.564+08:00", "salesCurrency": "SGD", "calculateMethod": 1}, "insureds": {"person": [{"elements": {"race": "race", "tags": [{"code": "tagCode", "name": "tagName"}], "taxes": [{"taxNo": "10109", "taxResidentialCountry": "Singapore", "E_TaxNoNotAvailableReason": "reason desc", "E_TaxNoNotAvailableReasonCode": "B"}], "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "Singapore", "typeOfPass": "typeOfPass", "attachments": [{"attachmentUrl": "********-b819-4987-94d0-0fea08b23b1e", "attachmentName": "12345.jpg", "attachmentType": "1"}], "nationality": "Singapore", "partTimeJob": "bc", "ckaIndicator": "YES", "E_SingaporePR": "YES", "E_UWEnquiryID": "101", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "ckaEffectiveDate": "2023-07-14T11:01:09.564+08:00", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}, "attachments": [{"attachmentUrl": "********-b819-4987-94d0-0fea08b23b1e", "attachmentName": "12345.jpg", "attachmentType": "1"}], "beneficiaries": {"elements": {"race": "race", "tags": [{"code": "tagCode", "name": "tagName"}], "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "Singapore", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "beneficiaryRatio": 12, "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}}}]}, "products": [{"elements": {"expireDate": "2023-07-14T11:01:09.564+08:00", "sumInsured": 10.25, "effectiveDate": "2023-07-14T11:01:09.564+08:00", "premiumPeriod": 1, "coveragePeriod": 10, "calculateMethod": 1, "premiumPeriodType": 3, "coveragePeriodType": 1, "periodPlannedPremium": 5000, "premiumFrequencyType": 2, "minimumInvestmentPeriodType": "1", "minimumInvestmentPeriodValue": 1}, "insureds": {"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "Singapore", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "E_UWEnquiryID": "101", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "code", "perferLanguage": "1", "writtenLanguage": "1", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}, "trustees": {"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "Singapore", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "code", "perferLanguage": "1", "writtenLanguage": "1", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}}]}, "beneficiaries": {"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "Singapore", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "code", "perferLanguage": "1", "writtenLanguage": "1", "beneficiaryRatio": 10, "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}}]}}]}, "productId": "***********", "investments": [{"funds": [{"fundCode": "Fund_Asset_Balanced", "fundAllocation": 100, "distributionMethod": 1, "distributionAccount": {"iban": "iban", "bankCity": "city", "bankCode": "bankCode", "bankName": "bankName", "swiftCode": "swiftCode", "cardNumber": "cardNumber", "expiryDate": "2023-07-14T11:01:09.564+08:00", "accountType": 2, "accountSubType": 1, "bankBranchCode": "branchCode", "bankBranchName": "branchName", "cardHolderName": "<PERSON><PERSON><PERSON>"}}], "amount": 10000, "premiumType": 1, "singleTopUpType": 1, "paymentFrequencyType": 1, "paymentFrequencyValue": "12345"}], "liabilities": [{"sumInsured": 100.25, "liabilityCode": "liability code", "periodPremium": 20.05}]}], "attachments": [{"attachmentUrl": "********-b819-4987-94d0-0fea08b23b1e", "attachmentName": "12345.jpg", "attachmentType": "1"}], "beneficiaries": {"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "birthPlace": "birthPlace", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "beneficiaryOrder": 1234, "beneficiaryRatio": 0.5, "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}}]}, "premiumFunder": [{"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "accounts": [{"iban": "iban", "bankCity": "city", "bankCode": "bankCode", "bankName": "bankName", "swiftCode": "swiftCode", "cardNumber": "cardNumber", "expiryDate": "2023-07-14T11:01:09.564+08:00", "accountType": "2", "accountSubType": "1", "bankBranchCode": "branchCode", "bankBranchName": "branchName", "cardHolderName": "<PERSON><PERSON><PERSON>"}], "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "payMethod": "1", "payerType": "1", "birthPlace": "birthPlace", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "E_SourceOfFunds": "1", "writtenLanguage": "1", "E_SourceOfWealth": "1", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "E_SourceOfFundsDetails": "E_SourceOfFundsDetails", "E_SourceOfWealthDetails": "E_SourceOfWealthDetails", "expiryDateOfCertificate": "2030-02-01", "beneficialOwnerIndicator": "YES", "E_RelationshipDescription": "description", "relationshipWithPolicyholder": "1"}}]}], "beneficialOwners": {"person": [{"PEP": [{"name": "test1name", "title": "ttile", "relationshipToRelated": "1"}], "elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "accounts": [{"iban": "iban", "bankCity": "city", "bankCode": "bankCode", "bankName": "bankName", "swiftCode": "swiftCode", "cardNumber": "cardNumber", "expiryDate": "2023-07-14T11:01:09.564+08:00", "accountType": "2", "accountSubType": "1", "bankBranchCode": "branchCode", "bankBranchName": "branchName", "cardHolderName": "<PERSON><PERSON><PERSON>"}], "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "payMethod": "1", "payerType": "1", "birthPlace": "birthPlace", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "E_SourceOfWealth": "1", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "E_SourceOfWealthDetails": "E_SourceOfWealthDetails", "expiryDateOfCertificate": "2030-02-01", "beneficialOwnerIndicator": "YES", "E_RelationshipDescription": "description", "relationshipWithPolicyholder": "1"}}]}, "secondaryInsureds": {"person": [{"elements": {"race": "race", "smoke": "YES", "emails": [{"email": "<EMAIL>"}], "gender": "1", "height": "175", "income": "1000", "labels": [{"code": "tagCode", "name": "tagName"}], "phones": [{"phoneNo": "***********", "phoneType": "1", "countryCode": "65"}], "weight": "60", "certiNo": "492199", "E_MyInfo": "myInfo", "birthday": "1990-02-01", "fullName": "tester1234", "addresses": [{"zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "addressType": "1"}], "certiType": "1", "education": "1", "typeOfPass": "typeOfPass", "nationality": "Singapore", "partTimeJob": "bc", "E_SingaporePR": "YES", "countryOfBirth": "Singapore", "marriageStatus": "1", "occupationCode": "occupationCode", "perferLanguage": "1", "writtenLanguage": "1", "organizationName": "income", "residenceCountry": "Singapore", "secondNationality": ["secondNationality"], "expiryDateOfCertificate": "2030-02-01", "relationshipWithPolicyholder": "1"}}]}}, "previousPolicyNo": "not required", "issuanceRelations": [{"type": 1, "relationNo": "relation number", "numberOfIssuance": 10}], "issueWithoutPayment": 1, "orderNo": "not required", "promotionCode": "not required", "userId": 12345678901112, "agents": [{"agentCode": "101", "agentName": "101 name", "agencyCode": "100", "agencyName": "100 name", "salesAgreementCode": "100", "commissionShareRate": "1"}], "branchCode": "not required", "vestingAge": "100", "issuanceTransactionType": 1, "elements": {"E_UMEReason": "E_UMEReason", "E_Declarations": "E_Declarations", "E_PolicySource": "Singapore Policy", "E_Reinvestment": "1", "E_UMEIndicator": "E_UMEIndicator", "E_System_Source": "E_System_Source", "E_IntroducerNric": "F222328", "E_UMEDecisionDate": "E_UMEDecisionDate", "E_MarketingConsent": "E_MarketingConsent", "E_PremiumFinancing": "E_PremiumFinancing", "E_StaffPurchaseCode": "23", "E_ProposalUMEDecision": "E_ProposalUMEDecision"}, "paymentMethod": 1, "issuanceNo": "not required", "paymentOrderNo": "not required", "policyDeliveryMethod": 1}