[{"name": "api-instance", "in": "header", "description": "The instance identity of API.", "required": true, "schema": {"type": "string", "default": "Goods:1278100746813440"}}, {"name": "api-timestamp", "in": "header", "description": "The timestamp of sending the request, format: 'yyyy-MM-dd'T'HH:mm:ss.SSSX', example: '2021-07-17T11:01:09.564+08'.", "required": true, "schema": {"type": "string", "default": "2023-10-26T16:53:59.170+08"}}, {"name": "api-version", "in": "header", "description": "The version of API, not required, default is 'v1'.", "required": false, "schema": {"type": "string", "default": "v1"}}]