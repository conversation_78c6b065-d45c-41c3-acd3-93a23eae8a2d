[{"errorCode": "BIZ_ISS_100215", "errorMessage": " Customer/object  unique identification element must be required", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100062", "errorMessage": "Abnormal verification of basic information of the policyholder", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100063", "errorMessage": "Abnormal verification of basic information of the insured", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100051", "errorMessage": "The policyholder is on the blacklist", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100052", "errorMessage": "The insured is on the blacklist", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100053", "errorMessage": "The beneficiary is on the blacklist", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100054", "errorMessage": "The payer is on the blacklist", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100072", "errorMessage": "Abnormal premium", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100120", "errorMessage": "Abnormal bonus", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100226", "errorMessage": "The assignee cannot be the policyholder or the insured", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100222", "errorMessage": "Missing attachment", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100126", "errorMessage": "no collection transaction found", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100047", "errorMessage": "Insurance time, policy effective time can not be blank", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100110", "errorMessage": "Incorrect fund errorCode", "httpStatus": 500}, {"errorCode": "BIZ_ISS_100111", "errorMessage": "Incorrect fund allocation ratio", "httpStatus": 500}]