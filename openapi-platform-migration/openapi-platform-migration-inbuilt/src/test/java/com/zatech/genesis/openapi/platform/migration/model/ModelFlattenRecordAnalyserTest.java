package com.zatech.genesis.openapi.platform.migration.model;

import com.zatech.genesis.openapi.platform.integration.metadata.service.MetadataService;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.openapi.platform.share.jsonschema.SwaggerJsonSchemaHelper;
import com.zatech.genesis.openapi.platform.share.tools.ResourceUtil;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.media.Schema;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;

class ModelFlattenRecordAnalyserTest {

    @Mock
    MetadataService service;

    @Test
    void testAnalyse() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(service.queryBizDictTenant(any())).thenReturn(List.of());
        var api = api();
        Map<String, Schema> schemas = api.getPaths().entrySet().stream().findFirst().get().getValue().getPost()
            .getRequestBody().getContent().get("application/json").getSchema().getProperties();
        Map<String, Object> example = example();
        ModelFlattenRecordAnalyser.ModelFlattenRecordAnalyseResult modelFlattenRecordAnalyseResult = new ModelFlattenRecordAnalyser(schemas, service).analyseSchema();
        assertThat(modelFlattenRecordAnalyseResult).isNotNull();
    }

    private OpenAPI api() {
        String s = ResourceUtil.readUtf8Str("excel/issuance_schema.json");
        return SwaggerJsonSchemaHelper.fromJsonStringToOpenApi(s);
    }

    private Map<String, Object> example() {
        return JsonParser.fromJsonToMap(ResourceUtil.readUtf8Str("excel/issuance_request.json"));
    }
}
