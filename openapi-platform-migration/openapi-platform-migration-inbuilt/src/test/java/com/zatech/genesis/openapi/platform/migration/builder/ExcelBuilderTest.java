package com.zatech.genesis.openapi.platform.migration.builder;

import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.instance.ScenarioInstanceAggregation;
import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.instance.factory.ScenarioInstanceEntityFactory;
import com.zatech.genesis.openapi.platform.infra.application.entity.ApiErrorCode;
import com.zatech.genesis.openapi.platform.integration.metadata.service.MetadataService;
import com.zatech.genesis.openapi.platform.migration.model.InstanceDataWrapper;
import com.zatech.genesis.openapi.platform.share.enums.MetaCategoryEnum;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.openapi.platform.share.tools.ResourceUtil;

import java.io.IOException;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

class ExcelBuilderTest {

    @Mock
    ScenarioInstanceAggregation aggregation;

    @Mock
    ScenarioInstanceEntityFactory factory;

    @Mock
    MetadataService service;

    @Test
    void genExcel() throws IOException {
        MockitoAnnotations.openMocks(this);
        Mockito.when(aggregation.i18nDoc(any())).thenReturn(mockSchema());
        Mockito.when(factory.getApiErrorCodeList(anyLong())).thenReturn(List.of());
        var wrapper = new InstanceDataWrapper(aggregation, factory);
        ExcelBuilder builder = new ExcelBuilder(wrapper, service, errorCode());
/*        var file = new File("D:\\projectRepo\\genesis-openapi-platform\\openapi-platform-migration\\openapi-platform-migration-inbuilt\\src\\test\\resources\\excel\\text.xlsx");
        if (!file.exists()) file.createNewFile();
        FileUtil.writeBytes(builder.genExcel(), file);*/
        assertNotNull(builder.genExcel(MetaCategoryEnum.ScenarioSchema));
    }

    JsonMap mockSchema() {
        String s = ResourceUtil.readUtf8Str("excel/issuance_schema.json");
        return JsonParser.fromJsonToJsonMap(s);
    }

    List<ApiErrorCode> errorCode() {
        return JsonParser.toList(ResourceUtil.readUtf8Str("excel/errorCodes.json"), ApiErrorCode.class);
    }

}
