package com.zatech.genesis.openapi.platform.migration.utils;

import com.zatech.genesis.openapi.platform.migration.model.OpenAPIPathOperation;

import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * <AUTHOR>
 * @date 2023/3/30 18:43
 **/
public class PathUtils {

    public static List<OpenAPIPathOperation> toPathOperationsList(Map<String, PathItem> paths) {
        List<OpenAPIPathOperation> pathOperations = new ArrayList<>();

        paths.forEach((relativePath, path) ->
            pathOperations.addAll(toPathOperation(relativePath, path)));
        return pathOperations;
    }

    public static List<OpenAPIPathOperation> toPathOperation(String path, PathItem pathModel) {
        List<OpenAPIPathOperation> pathOperations = new ArrayList<>();
        getOperationMap(pathModel).forEach((httpMethod, operation) -> {
            String id = operation.getOperationId();
            if (id == null)
                id = path + " " + httpMethod.toString().toLowerCase();

            String operationName = operation.getSummary();
            if (isBlank(operationName)) {
                operationName = httpMethod.toString() + " " + path;
            }
            pathOperations.add(new OpenAPIPathOperation(httpMethod.name(), path, id, operationName, operation));
        });
        return pathOperations;
    }

    private static Map<PathItem.HttpMethod, Operation> getOperationMap(PathItem path) {
        Map<PathItem.HttpMethod, Operation> result = new LinkedHashMap<>();

        if (path.getGet() != null) {
            result.put(PathItem.HttpMethod.GET, path.getGet());
        }
        if (path.getPut() != null) {
            result.put(PathItem.HttpMethod.PUT, path.getPut());
        }
        if (path.getPost() != null) {
            result.put(PathItem.HttpMethod.POST, path.getPost());
        }
        if (path.getDelete() != null) {
            result.put(PathItem.HttpMethod.DELETE, path.getDelete());
        }
        if (path.getPatch() != null) {
            result.put(PathItem.HttpMethod.PATCH, path.getPatch());
        }
        if (path.getHead() != null) {
            result.put(PathItem.HttpMethod.HEAD, path.getHead());
        }
        if (path.getOptions() != null) {
            result.put(PathItem.HttpMethod.OPTIONS, path.getOptions());
        }

        return result;
    }

}
