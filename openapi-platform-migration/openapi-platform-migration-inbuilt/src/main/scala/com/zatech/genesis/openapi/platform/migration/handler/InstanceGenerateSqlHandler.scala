package com.zatech.genesis.openapi.platform.migration.handler

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioInstance
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioInstanceMapper
import com.zatech.genesis.openapi.platform.migration._
import com.zatech.genesis.openapi.platform.migration.instance.{InstanceDeleteSqlGenerator, InstanceInsertSqlGenerator, InstanceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import org.apache.ibatis.mapping.BoundSql

import java.util
import scala.jdk.CollectionConverters.{CollectionHasAsScala, IterableHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class InstanceGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newMapVal: MapVal, oldMapVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newMapVal).map(_.asInstanceOf[ScenarioInstanceMapVal].value).orNull
    val oldInfo = Option(oldMapVal).map(_.asInstanceOf[ScenarioInstanceMapVal].value).orNull
    if (newInfo != null && oldInfo != null && newInfo.getId != oldInfo.getId) {
      //删除老的
      (CompareHelper.compare(classOf[ScenarioInstance])(null, oldInfo) match {
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
      //插入新的
      (CompareHelper.compare(classOf[ScenarioInstance])(newInfo, null) match {
        case insert: InsertDslCompareResult => Some(new InstanceInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }

    } else {
      (CompareHelper.compare(classOf[ScenarioInstance])(newInfo, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new InstanceCustomInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new InstanceUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    }
    sqlList
  }



  private class InstanceCustomInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
    extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult) {

    override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioInstanceMapper]

    lazy val scenarioSchemaId = classOf[ScenarioInstance].getDeclaredField("scenarioSchemaId")


    lazy val name = classOf[ScenarioInstance].getDeclaredField("name")

    lazy val version = classOf[ScenarioInstance].getDeclaredField("version")

    /**
     * 特殊处理了delete的，因此只需要insert的
     */
    override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
      val insertionMappedStatementId = s"${mapperCls.getName}.insert"
      val insertionMappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(insertionMappedStatementId)
      Seq(insertionMappedStatement.getBoundSql(table))
    }

    override def generate: Seq[String] = {
      val sqlList = new util.LinkedList[String]()
      val sql = new StringBuilder(s"DELETE FROM $tableName WHERE 1=1")
      compareResult.getResult.stream().forEach(result => {
        if (result.getField.equals(scenarioSchemaId) && result.getNewValue != null) {
          sql.append(s" and scenario_schema_id = ${result.getNewValue}")
        }
        if (result.getField.equals(name) && result.getNewValue != null) {
          sql.append(s" and name = '${result.getNewValue}'")
        }
        if (result.getField.equals(version) && result.getNewValue != null) {
          sql.append(s" and version = '${result.getNewValue}'")
        }
      })
      if (sql.toString().contains("scenario_schema_id") && sql.toString().contains("name") && sql.toString().contains("version")) {
        sqlList.add(sql.toString())
      }
      sqlList.addAll(super.generate.asJavaCollection)
      sqlList.asScala.toSeq
    }
  }


}
