package com.zatech.genesis.openapi.platform.migration

import com.zatech.genesis.openapi.platform.migration.model.DeleteDslCompareResult
import org.apache.ibatis.mapping.BoundSql

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
abstract class AbstractDeleteSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: DeleteDslCompareResult)
  extends AbstractSqlGenerator[DeleteDslCompareResult](sqlGenerateTools, compareResult) {

  override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
    val mappedStatementId = s"${mapperCls.getName}.deleteById"
    val mappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(mappedStatementId)
    Seq(mappedStatement.getBoundSql(table))
  }

  override protected def rebuildSqlParams(params: Iterable[Any]): Array[Any] = {
    params.toArray :+ compareResult.getRecordId
  }
}
