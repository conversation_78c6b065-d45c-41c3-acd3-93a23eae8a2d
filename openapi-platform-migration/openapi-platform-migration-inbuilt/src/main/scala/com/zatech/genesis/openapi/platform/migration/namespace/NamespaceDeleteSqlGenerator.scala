package com.zatech.genesis.openapi.platform.migration.namespace

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.global.mapper.{DomainMapper, NamespaceMapper}
import com.zatech.genesis.openapi.platform.migration.model.DeleteDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractDeleteSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
class NamespaceDeleteSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: DeleteDslCompareResult)
  extends AbstractDeleteSqlGenerator(sqlGenerateTools, compareResult) {

  override protected val mapperCls: Class[_ <: BaseMapper[_]] = tableName match {
    case "namespace" => classOf[NamespaceMapper]
    case "domain" => classOf[DomainMapper]
  }
}
