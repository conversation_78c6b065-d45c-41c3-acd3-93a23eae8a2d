package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioSchemaGroup
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.schemagroup.{SchemaGroupDeleteSqlGenerator, SchemaGroupInsertSqlGenerator, SchemaGroupUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.SeqHasAsJava

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class ScenarioSchemaGroupGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newMapVal: MapVal, oldMapVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newMapVal).map(_.asInstanceOf[ScenarioSchemaGroupMapVal].value).orNull
    val oldInfo = Option(oldMapVal).map(_.asInstanceOf[ScenarioSchemaGroupMapVal].value).orNull

    (CompareHelper.compare(classOf[ScenarioSchemaGroup])(newInfo, oldInfo) match {
      case insert: InsertDslCompareResult => Some(new SchemaGroupInsertSqlGenerator(tools.sqlGenerateTools, insert))
      case update: UpdateDslCompareResult => Some(new SchemaGroupUpdateSqlGenerator(tools.sqlGenerateTools, update))
      case delete: DeleteDslCompareResult => Some(new SchemaGroupDeleteSqlGenerator(tools.sqlGenerateTools, delete))
      case _ => None
    }) map { generator => sqlList.addAll(generator.generate.asJava) }

    sqlList
  }

}
