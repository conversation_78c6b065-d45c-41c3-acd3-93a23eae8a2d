package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.migration.{DslAnalyzeTools, MapVal}
import com.zatech.genesis.openapi.platform.share.enums.MetaCategoryEnum

import java.util

/**
 * <AUTHOR>
 * @date 2022/10/19 18:08
 * */
trait IGenerateSqlHandler {

  def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String]

}

object IGenerateSqlHandler {

  def apply(kind: MetaCategoryEnum) = kind match {
    case MetaCategoryEnum.Namespace => new NameSpaceGenerateSqlHandler
    case MetaCategoryEnum.ScenarioSchemaGroup => new ScenarioSchemaGroupGenerateSqlHandler
    case MetaCategoryEnum.ScenarioSchema => new SchemaGenerateSqlHandler
    case MetaCategoryEnum.SampleTemplate => new SchemaSampleTemplateGenerateSqlHandler
    case MetaCategoryEnum.ModelMeta => new SchemaModelMetaGenerateSqlHandler
    case MetaCategoryEnum.FlowSchema => new FlowSchemaGenerateSqlHandler
    case MetaCategoryEnum.Dictionary => new DictionaryGenerateSqlHandler
    case MetaCategoryEnum.Procedure => new ProcedureGenerateSqlHandler
    case MetaCategoryEnum.ScenarioInstance => new InstanceGenerateSqlHandler
    case MetaCategoryEnum.ScenarioInstanceDoc => new InstanceDocGenerateSqlHandler
    case MetaCategoryEnum.ScenarioInstanceGoods => new InstanceGoodsGenerateSqlHandler
    case MetaCategoryEnum.ScenarioInstancePlan => new InstancePlanGenerateSqlHandler
    case MetaCategoryEnum.RScenarioInstanceFlowSchema => new RInstanceFlowGenerateSqlHandler
    case MetaCategoryEnum.OpenApiDoc => new OpenApiDocGenerateSqlHandler
    case MetaCategoryEnum.MetaDoc => new MetaDocGenerateSqlHandler
    case MetaCategoryEnum.ScenarioInstanceInsurer => new InstanceInsurerGenerateSqlHandler
    case MetaCategoryEnum.CallbackKey => new CallbackKeyGenerateSqlHandler
    case MetaCategoryEnum.Domain => new DomainGenerateSqlHandler
    case MetaCategoryEnum.CaseExamples => new CaseExamplesSqlGenerateHandler
    case MetaCategoryEnum.CaseI18nExamples => new CaseExamplesI18nSqlGenerateHandler
    case MetaCategoryEnum.ScenarioInstanceDocI18n => new InstanceDocI18nGenerateSqlHandler
    case MetaCategoryEnum.OpenApiDocI18n => new OpenApiDocI18nGenerateSqlHandler
    case MetaCategoryEnum.MetaDocI18n => new MetaDocI18nGenerateSqlHandler
    case MetaCategoryEnum.ScenarioApiConfig => new ApiConfigGenerateSqlHandler
    case MetaCategoryEnum.Record => new ApiViewRecordGenerateSqlHandler
    case MetaCategoryEnum.Group => new ApiViewGroupGenerateSqlHandler
    case MetaCategoryEnum.Schema => new ApiViewSchemaGenerateSqlHandler
    case MetaCategoryEnum.Instruction => new ApiViewDocumentGenerateSqlHandler
    case MetaCategoryEnum.RViewDocument => new RViewDocumentGenerateSqlHandler

  }

}
