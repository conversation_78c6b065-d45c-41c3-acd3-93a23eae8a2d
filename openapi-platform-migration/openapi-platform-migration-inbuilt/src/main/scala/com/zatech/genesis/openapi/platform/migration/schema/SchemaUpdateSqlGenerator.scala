package com.zatech.genesis.openapi.platform.migration.schema

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.mapper.{ScenarioApiConfig<PERSON><PERSON><PERSON>, ScenarioSchemaMapper, ScenarioSchemaModelMetaMapper, ScenarioSchemaSampleTemplateMapper}
import com.zatech.genesis.openapi.platform.infra.global.mapper.DomainMapper
import com.zatech.genesis.openapi.platform.infra.knowledge.flow.inbuilt.mapper.FlowSchemaMapper
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.mapper.{ModelValueDictionaryMapper, ProcedureMetaMapper}
import com.zatech.genesis.openapi.platform.migration.model.UpdateDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractUpdateSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @date 2022/10/13 18:37
 * */
class SchemaUpdateSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: UpdateDslCompareResult)
  extends AbstractUpdateSqlGenerator(sqlGenerateTools, compareResult) {

  override protected val mapperCls: Class[_ <: BaseMapper[_]] = tableName match {
    case "scenario_schema" => classOf[ScenarioSchemaMapper]
    case "scenario_schema_sample_template" => classOf[ScenarioSchemaSampleTemplateMapper]
    case "scenario_schema_model_meta" => classOf[ScenarioSchemaModelMetaMapper]
    case "model_value_dictionary" => classOf[ModelValueDictionaryMapper]
    case "flow_schema" => classOf[FlowSchemaMapper]
    case "procedure_meta" => classOf[ProcedureMetaMapper]
    case "domain" => classOf[DomainMapper]
    case "scenario_api_config" => classOf[ScenarioApiConfigMapper]
  }
}
