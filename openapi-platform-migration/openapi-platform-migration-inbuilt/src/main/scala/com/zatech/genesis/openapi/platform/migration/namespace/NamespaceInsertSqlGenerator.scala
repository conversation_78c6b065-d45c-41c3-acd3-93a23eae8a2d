package com.zatech.genesis.openapi.platform.migration.namespace

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.global.mapper.{DomainMapper, NamespaceMapper}
import com.zatech.genesis.openapi.platform.migration.model.InsertDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractInsertSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
class NamespaceInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
  extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult){

  override protected val mapperCls: Class[_ <: BaseMapper[_]] = tableName match {
    case "namespace" => classOf[NamespaceMapper]
    case "domain" => classOf[DomainMapper]
  }

}
