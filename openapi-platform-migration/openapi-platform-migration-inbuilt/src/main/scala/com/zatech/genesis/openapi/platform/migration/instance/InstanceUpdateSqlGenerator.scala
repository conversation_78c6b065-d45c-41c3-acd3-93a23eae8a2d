package com.zatech.genesis.openapi.platform.migration.instance

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.mapper._
import com.zatech.genesis.openapi.platform.migration.model.UpdateDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractUpdateSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @date 2022/10/13 18:37
 * */
class InstanceUpdateSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: UpdateDslCompareResult)
  extends AbstractUpdateSqlGenerator(sqlGenerateTools, compareResult) {

  override protected val mapperCls: Class[_ <: BaseMapper[_]] = tableName match {
    case "scenario_instance" => classOf[ScenarioInstanceMapper]
    case "scenario_instance_goods" => classOf[ScenarioInstanceGoodsMapper]
    case "scenario_instance_plan" => classOf[ScenarioInstancePlanMapper]
    case "scenario_instance_doc" => classOf[ScenarioInstanceDocMapper]
    case "r_scenario_instance_flow_schema" => classOf[RScenarioInstanceFlowSchemaMapper]
    case "open_api_doc" => classOf[OpenApiDocMapper]
    case "meta_doc" => classOf[MetaDocMapper]
    case "scenario_instance_insurer" => classOf[ScenarioInstanceInsurerMapper]
    case "callback_key" => classOf[CallbackKeyMapper]
    case "scenario_instance_case_example" => classOf[ScenarioInstanceCaseExampleMapper]
    case "scenario_instance_case_example_i18n" => classOf[ScenarioInstanceCaseExampleI18nMapper]
    case "scenario_instance_doc_i18n" => classOf[ScenarioInstanceDocI18nMapper]

  }
}
