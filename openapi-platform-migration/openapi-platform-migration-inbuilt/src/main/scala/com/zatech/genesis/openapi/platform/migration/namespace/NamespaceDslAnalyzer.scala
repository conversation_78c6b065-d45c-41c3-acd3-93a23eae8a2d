package com.zatech.genesis.openapi.platform.migration.namespace

import com.zatech.gaia.resource.components.enums.common.YesNoEnum
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.namespace.NamespaceDslTemplate
import com.zatech.genesis.openapi.platform.infra.global.entity.Namespace
import com.zatech.genesis.openapi.platform.migration.constant.MigrationConstant
import com.zatech.genesis.openapi.platform.migration.handler.IGenerateSqlHandler
import com.zatech.genesis.openapi.platform.migration.model._
import com.zatech.genesis.openapi.platform.migration.{AbstractDsl<PERSON>nalyzer, DslAnalyzeTools, MapVal}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.openapi.platform.share.enums.MetaCategoryEnum
import com.zatech.octopus.component.sleuth.TraceOp

import java.util.Objects

/**
 * <AUTHOR>
 * @Date 2022/8/25
 * */

class NamespaceDslAnalyzer(namespaceDslTemplate: NamespaceDslTemplate)
  extends AbstractDslAnalyzer[NamespaceDslTemplate](namespaceDslTemplate) with MapVal {

  override val parentOpt: Option[AbstractDslAnalyzer[_]] = None

  override protected def doSelfAnalyze(tools: DslAnalyzeTools): AnalyzeResult[NamespaceDslTemplate] = {
    val entityOpt = tools.namespaceEntityFactory.getDomainOpt(namespaceDslTemplate.getMetadata.getId)
    val result = new AnalyzeResult[NamespaceDslTemplate]()
    if (entityOpt.nonEmpty && Objects.equals(tools.cacheUtil.getString(MigrationConstant.NEED_EXPORT_OLD_DSL), YesNoEnum.YES.getDbView)) {
      result.setOldDsl(entityOpt.map(_.`export`()).orNull)
    }
    result.setNewDsl(namespaceDslTemplate)

    val oldDslMap = getNamespaceMap(result.getOldDsl)
    val newDslMap = getNamespaceMap(result.getNewDsl)

    MetaCategoryEnum.values().foreach(metaCategoryEnum => {
      if (newDslMap.contains(metaCategoryEnum) || oldDslMap.contains(metaCategoryEnum)) {
        result.setMigrationSqls(
          IGenerateSqlHandler.apply(metaCategoryEnum).getGenerateSql(
            newDslMap.get(metaCategoryEnum).orNull,
            oldDslMap.get(metaCategoryEnum).orNull,
            tools
          )
        )
      }
    })
    result
  }


  def getNamespaceMap(namespaceDslTemplate: NamespaceDslTemplate): Map[MetaCategoryEnum, MapVal] = {
    var map: Map[MetaCategoryEnum, MapVal] = Map()
    if (namespaceDslTemplate != null && namespaceDslTemplate.getMetadata != null) {

      val namespaceMeta = namespaceDslTemplate.getMetadata
      val namespace = new Namespace
      namespace.setId(namespaceMeta.getId)
      namespace.setName(namespaceMeta.getName)
      namespace.setTitle(namespaceMeta.getTitle)
      namespace.setDescription(namespaceMeta.getDescription)
      namespace.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
      namespace.setTenantCode(TraceOp.getTenant)
      namespace.setWeight(namespaceMeta.getWeight)

      map += (MetaCategoryEnum.Namespace -> NamespaceMapVal(namespace))
    }
    map
  }

}




