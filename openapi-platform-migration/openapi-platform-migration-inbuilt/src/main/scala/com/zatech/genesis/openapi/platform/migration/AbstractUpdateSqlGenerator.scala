package com.zatech.genesis.openapi.platform.migration

import com.zatech.genesis.openapi.platform.migration.model.UpdateDslCompareResult
import org.apache.ibatis.mapping.{BoundSql, ParameterMapping}

import scala.jdk.CollectionConverters.MapHasAsJava

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
abstract class AbstractUpdateSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: UpdateDslCompareResult)
  extends AbstractSqlGenerator[UpdateDslCompareResult](sqlGenerateTools, compareResult)  {

  override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
    val mappedStatementId = s"${mapperCls.getName}.updateById"
    val mappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(mappedStatementId)
    val paramMap = Map("param1" -> table, "et" -> table).asJava
    Seq(mappedStatement.getBoundSql(paramMap))
  }

  override protected def rebuildSqlParams(params: Iterable[Any]): Array[Any] = {
    params.toArray :+ compareResult.getRecordId
  }

  override protected def getTargetPropertyNameFromParameterMapping(parameterMapping: ParameterMapping): String = {
    parameterMapping.getProperty.replace("et.","")
  }
}
