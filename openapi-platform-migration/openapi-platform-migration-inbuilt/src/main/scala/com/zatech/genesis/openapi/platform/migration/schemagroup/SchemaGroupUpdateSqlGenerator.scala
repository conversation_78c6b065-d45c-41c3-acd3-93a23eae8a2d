package com.zatech.genesis.openapi.platform.migration.schemagroup

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioSchemaGroupMapper
import com.zatech.genesis.openapi.platform.migration.model.UpdateDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractUpdateSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
class SchemaGroupUpdateSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: UpdateDslCompareResult)
  extends AbstractUpdateSqlGenerator(sqlGenerateTools, compareResult) {
  override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioSchemaGroupMapper]
}
