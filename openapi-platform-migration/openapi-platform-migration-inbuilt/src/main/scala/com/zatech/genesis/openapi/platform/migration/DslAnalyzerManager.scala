package com.zatech.genesis.openapi.platform.migration

import com.fasterxml.jackson.core.`type`.TypeReference
import com.zatech.gaia.resource.components.enums.common.YesNoEnum
import com.zatech.genesis.openapi.platform.api.resource.apiview.model.ApiViewRecordDslTemplate
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.DslTemplate
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.instance.InstanceDslTemplate
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.namespace.NamespaceDslTemplate
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.schema.SchemaDslTemplate
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.schemegroup.SchemaGroupDslTemplate
import com.zatech.genesis.openapi.platform.domain.dsl.factory.MigrationOperateRecordEntityFactory
import com.zatech.genesis.openapi.platform.migration.constant.MigrationConstant
import com.zatech.genesis.openapi.platform.migration.instance.InstanceDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.model.AnalyzeResult
import com.zatech.genesis.openapi.platform.migration.namespace.NamespaceDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.record.RecordDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.schema.SchemaDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.schemagroup.SchemaGroupDslAnalyzer
import com.zatech.genesis.openapi.platform.share.enums.MetaCategoryEnum
import com.zatech.genesis.openapi.platform.share.json.{JsonMap, JsonParser}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Component
import org.springframework.transaction.support.TransactionTemplate

import java.util
import java.util.concurrent.TimeUnit


/**
 * <AUTHOR>
 * @Date 2022/8/26
 * */
@Component
class DslAnalyzerManager @Autowired()(tools: DslAnalyzeTools,
                                      migrationOperateRecordEntityFactory: MigrationOperateRecordEntityFactory,
                                      transactionTemplate: TransactionTemplate,
                                      jdbcTemplate: JdbcTemplate) {

  case class DslAnalyzerContainer(typeRef: TypeReference[_], newFunc: PartialFunction[DslTemplate[_, _], AbstractDslAnalyzer[_]])

  val AnalyzerMap: Map[MetaCategoryEnum, DslAnalyzerContainer] = Map(
    MetaCategoryEnum.Namespace -> DslAnalyzerContainer(new TypeReference[NamespaceDslTemplate] {}, { case template: NamespaceDslTemplate => new NamespaceDslAnalyzer(template) }),
    MetaCategoryEnum.ScenarioSchemaGroup -> DslAnalyzerContainer(new TypeReference[SchemaGroupDslTemplate] {}, { case template: SchemaGroupDslTemplate => new SchemaGroupDslAnalyzer(template) }),
    MetaCategoryEnum.ScenarioSchema -> DslAnalyzerContainer(new TypeReference[SchemaDslTemplate] {}, { case template: SchemaDslTemplate => new SchemaDslAnalyzer(template) }),
    MetaCategoryEnum.ScenarioInstance -> DslAnalyzerContainer(new TypeReference[InstanceDslTemplate] {}, { case template: InstanceDslTemplate => new InstanceDslAnalyzer(template) }),
    MetaCategoryEnum.Record -> DslAnalyzerContainer(new TypeReference[ApiViewRecordDslTemplate] {}, { case template: ApiViewRecordDslTemplate => new RecordDslAnalyzer(template) })

  )

  type ExporterFunc = Long => IExportable[_ <: DslTemplate[_,_]]

  val ExporterMap: Map[MetaCategoryEnum, ExporterFunc] = Map[MetaCategoryEnum, ExporterFunc](
    MetaCategoryEnum.Namespace -> {
      tools.namespaceEntityFactory.getDomain(_)
    },
    MetaCategoryEnum.ScenarioSchemaGroup -> {
      tools.scenarioSchemaGroupEntityFactory.getDomain(_)
    },
    MetaCategoryEnum.ScenarioSchema -> {
      tools.scenarioSchemaAggregationFactory.getDomain(_)
    },
    MetaCategoryEnum.ScenarioInstance -> {
      tools.scenarioInstanceAggregationFactory.getDomain(_)
    },
    MetaCategoryEnum.Record -> {
      tools.apiViewRecordEntityFactory.getDomain(_)
    }
  )

  def analyze(categoryEnum: MetaCategoryEnum)(jsonMap: JsonMap): AnalyzeResult[_] = {
    val container = AnalyzerMap(categoryEnum)
    val template = JsonParser.fromJsonMapToObjInApiSync(jsonMap, container.typeRef)
    tools.cacheUtil.setObject(MigrationConstant.NEED_EXPORT_OLD_DSL, YesNoEnum.YES.getDbView, 100, TimeUnit.SECONDS)
    container.newFunc(template.asInstanceOf[DslTemplate[_, _]]).analyze(tools)
  }

  def exportDsl(categoryEnum: MetaCategoryEnum)(domainId: Long): DslTemplate[_, _] = {
    val exporter = ExporterMap(categoryEnum)(domainId)
    val template = exporter.`export`()
    exporter.fillBasicInfo(template)
    template
  }


  def exportSql(categoryEnum: MetaCategoryEnum)(domainId: Long): String = {

    val sqlBuilder = new StringBuilder
    exportSqlList(categoryEnum)(domainId)
      .forEach(sql => sqlBuilder.append(sql).append(";").append("\n"))
    sqlBuilder.toString()

  }

  def exportSqlList(categoryEnum: MetaCategoryEnum)(domainId: Long): util.ArrayList[String] = {
    tools.cacheUtil.setObject(MigrationConstant.NEED_EXPORT_OLD_DSL, YesNoEnum.NO.getDbView, 100, TimeUnit.SECONDS)
    val exporter = ExporterMap(categoryEnum)(domainId)
    val template = exporter.`export`()
    val container = AnalyzerMap(categoryEnum)
    val totalSql = container.newFunc(template).analyze(tools).getMigrationSqls
    new util.ArrayList[String](totalSql)
  }

  def exportSqlWithoutTenant(categoryEnum: MetaCategoryEnum)(domainId: Long): String = {
    val sqlBuilder = new StringBuilder
    exportSqlList(categoryEnum)(domainId)
      .forEach(sql => sqlBuilder.append(removeTenantCode(sql)).append(";").append("\n"))
    sqlBuilder.toString()

  }

  def removeTenantCode(sql: String): String = {
    if (sql.startsWith("INSERT") && sql.contains("tenant_code")) {
      val columnStartIndex = sql.indexOf("(") + 1
      val columnEndIndex = sql.indexOf(")")
      val valueStartIndex = sql.indexOf("(", columnStartIndex) + 1
      val valueEndIndex = sql.lastIndexOf(")")

      // 获取SQL语句的前缀
      val prePart = sql.substring(0, columnStartIndex - 1)

      // 获取SQL语句的列名部分和值列表部分
      val columnPart = sql.substring(columnStartIndex, columnEndIndex).trim()  // 获取列名部分
      val valuePart = sql.substring(valueStartIndex, valueEndIndex).trim() // 获取值列表部分

      // 将列名部分和值列表部分转换为可变的List
      var columns = columnPart.split(",").map(_.trim).toList
      var values = valuePart.split(",").map(_.trim).toList
      val index = columns.indexOf("tenant_code")
      // 移除列名和值列表中的tenant_code
      if (columns.length != values.length) {
        //临时这么写，为了解决以逗号分隔value的数据，存在value内部含义其他逗号问题。目前此方式暂不存在问题，但是后面若出现极端情况会有问题
        values = values.patch(index + (values.length - columns.length), Nil, 1)
        columns = columns.patch(index, Nil, 1)
      } else {
        columns = columns.patch(index, Nil, 1)
        values = values.patch(index, Nil, 1)
      }
      // 重新生成SQL语句
      val newColumnPart = columns.mkString(",")
      val newValuePart = values.mkString(",")
      val newSql = prePart + "(" + newColumnPart + ") VALUES (" + newValuePart + ")"
      newSql
    } else sql
  }

  def importDsl(recordId: Long) = {
    val entity = migrationOperateRecordEntityFactory.getDomain(recordId);
    //幂等的一个操作，判断是否需要再次执行
    if (!entity.getImportFlag) {
      val sqlList = JsonParser.fromJson(entity.sqlData, classOf[java.util.List[String]]);
      transactionTemplate.execute(status => {
        try {
          sqlList.forEach(sql => jdbcTemplate.update(sql))
        } catch {
          case e: Exception => {
            status.setRollbackOnly()
            throw new RuntimeException(e.getCause.getMessage)
          }
        }
      })
    }
  }

}
