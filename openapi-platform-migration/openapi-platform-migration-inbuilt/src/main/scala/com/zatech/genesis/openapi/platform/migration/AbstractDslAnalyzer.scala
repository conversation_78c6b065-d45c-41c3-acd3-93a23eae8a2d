package com.zatech.genesis.openapi.platform.migration

import com.zatech.genesis.openapi.platform.api.resource.dsl.model.{DslTemplate, MetadataBaseInfo, SpecBaseInfo}
import com.zatech.genesis.openapi.platform.migration.model.AnalyzeResult

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, Future}
import scala.language.postfixOps

/**
 * <AUTHOR>
 * @Date 2022/8/26
 * */
abstract class AbstractDslAnalyzer[T <: DslTemplate[_ <: MetadataBaseInfo, _ <: SpecBaseInfo]](val dslTemplate: T)
  extends IDslAnalyzer[T]{

  val parentOpt: Option[AbstractDslAnalyzer[_]]

  override final def analyze(tools: DslAnalyzeTools): AnalyzeResult[T] = {
    implicit val executor = tools.threadPoolContext.getDslAnalyzeExecutor
    val parentFuture: Option[Future[AnalyzeResult[_]]] =
      parentOpt.map(parent => Future[AnalyzeResult[_]](parent.analyze(tools)))
    val selfResult = doSelfAnalyze(tools)
    //TODO back to 10s !
    parentFuture.fold(selfResult)(pf => Await.result(pf, 100000 seconds).merge(selfResult).asInstanceOf[AnalyzeResult[T]])
  }

  protected def doSelfAnalyze(tools: DslAnalyzeTools): AnalyzeResult[T]
}
