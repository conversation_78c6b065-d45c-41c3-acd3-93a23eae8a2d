package com.zatech.genesis.openapi.platform.migration.schema

import com.zatech.gaia.resource.components.enums.common.YesNoEnum
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.common.{DomainInfo, FlowMetadataInfo, ProcedureMetadataInfo}
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.schema._
import com.zatech.genesis.openapi.platform.domain.modelmeta.factory.ModelValueDictionaryEntityFactory
import com.zatech.genesis.openapi.platform.infra.application.entity.{ScenarioApiConfig, ScenarioSchema, ScenarioSchemaModelMeta, ScenarioSchemaSampleTemplate}
import com.zatech.genesis.openapi.platform.infra.global.entity.Domain
import com.zatech.genesis.openapi.platform.infra.knowledge.flow.inbuilt.entity.FlowSchema
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.entity.{ModelValueDictionary, ProcedureMeta}
import com.zatech.genesis.openapi.platform.migration.constant.MigrationConstant
import com.zatech.genesis.openapi.platform.migration.handler.IGenerateSqlHandler
import com.zatech.genesis.openapi.platform.migration.model.AnalyzeResult
import com.zatech.genesis.openapi.platform.migration.schemagroup.SchemaGroupDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.{AbstractDslAnalyzer, DslAnalyzeTools, MapVal}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.openapi.platform.share.enums.MetaCategoryEnum
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import com.zatech.octopus.component.sleuth.TraceOp
import org.apache.commons.collections4.{CollectionUtils, MapUtils}

import java.lang
import java.util.Objects
import scala.collection.mutable
import scala.language.postfixOps

/**
 * <AUTHOR>
 * @Date 2022/8/26
 * */
class SchemaDslAnalyzer(schemaDslTemplate: SchemaDslTemplate) extends AbstractDslAnalyzer[SchemaDslTemplate](schemaDslTemplate) with MapVal {

  override val parentOpt: Option[AbstractDslAnalyzer[_]] = Some(new SchemaGroupDslAnalyzer(schemaDslTemplate.getSpec.getSchemaGroup))

  var dictionaryEntityFactory: ModelValueDictionaryEntityFactory = null

  override protected def doSelfAnalyze(tools: DslAnalyzeTools): AnalyzeResult[SchemaDslTemplate] = {
    dictionaryEntityFactory = tools.dictionaryEntityFactory
    val entityOpt = tools.scenarioSchemaAggregationFactory.getDomainOpt(schemaDslTemplate.getMetadata.getId)
    val result = new AnalyzeResult[SchemaDslTemplate]()
    if (entityOpt.nonEmpty && Objects.equals(tools.cacheUtil.getString(MigrationConstant.NEED_EXPORT_OLD_DSL), YesNoEnum.YES.getDbView)) {
      result.setOldDsl(entityOpt.map(_.`export`()).orNull)
    }
    result.setNewDsl(schemaDslTemplate)

    //各个层级对象映射
    val oldDslMap = getSchemaInfoMap(result.getOldDsl)
    val newDslMap = getSchemaInfoMap(result.getNewDsl)
    MetaCategoryEnum.values().foreach(metaCategoryEnum => {
      if (newDslMap.contains(metaCategoryEnum) || oldDslMap.contains(metaCategoryEnum)) {
        result.setMigrationSqls(
          IGenerateSqlHandler.apply(metaCategoryEnum).getGenerateSql(
            newDslMap.get(metaCategoryEnum).orNull,
            oldDslMap.get(metaCategoryEnum).orNull,
            tools
          )
        )
      }
    })
    result

  }

  def getSchemaInfoMap(schemaDslTemplate: SchemaDslTemplate): Map[MetaCategoryEnum, MapVal] = {
    var map: Map[MetaCategoryEnum, MapVal] = Map()
    if (schemaDslTemplate != null && schemaDslTemplate.getMetadata != null) {
      //schemaModelMeta 收集器
      val schemaModelMetas = new mutable.HashSet[ScenarioSchemaModelMeta]
      //modelValueDictionary 收集器
      val modelValueDictionarys = new mutable.HashSet[ModelValueDictionary]
      //flowSchema 收集器
      val flowSchemas = new mutable.HashSet[FlowSchema]
      //procedure 收集器
      val procedures = new mutable.HashSet[ProcedureMeta]
      //apiConfig 收集器
      val apiConfigs = new mutable.HashSet[ScenarioApiConfig]


      val schemaMetadataInfo = schemaDslTemplate.getMetadata
      val scenarioSchema = new ScenarioSchema
      scenarioSchema.setId(schemaMetadataInfo.getId)
      scenarioSchema.setName(schemaMetadataInfo.getName)
      scenarioSchema.setTitle(schemaMetadataInfo.getTitle)
      scenarioSchema.setDescription(schemaMetadataInfo.getDescription)
      scenarioSchema.setConfig(schemaMetadataInfo.getConfig)
      scenarioSchema.setBinding(schemaMetadataInfo.getBinding.name())
      scenarioSchema.setActiveFlag(schemaMetadataInfo.isActiveFlag)
      scenarioSchema.setVersion(schemaMetadataInfo.getVersion)
      scenarioSchema.setProductVersion(schemaMetadataInfo.getProductVersion)
      scenarioSchema.setTenantCode(TraceOp.getTenant)
      scenarioSchema.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
      scenarioSchema.setWeight(schemaMetadataInfo.getWeight)

      val inputSampleOpt = Option(schemaMetadataInfo.getInput).map(meta => {
        scenarioSchema.setInputOrigin(meta.getOrigin.name())
        if (meta.getModelMeta != null) {
          scenarioSchema.setInputModelMetaId(meta.getModelMeta.getRoot.getId)
          setSchemaModelMeta(meta.getModelMeta.getRoot, schemaModelMetas, modelValueDictionarys)
          if (MapUtils.isNotEmpty(meta.getModelMeta.getReference)) {
            meta.getModelMeta.getReference.values().forEach(item => setSchemaModelMeta(item, schemaModelMetas, modelValueDictionarys))
          }
        }
        meta.getSampleTemplate
      })

      val outputSampleOpt = Option(schemaMetadataInfo.getOutput).map(meta => {
        scenarioSchema.setOutputOrigin(meta.getOrigin.name())
        if (meta.getModelMeta != null) {
          scenarioSchema.setOutputModelMetaId(meta.getModelMeta.getRoot.getId)
          setSchemaModelMeta(meta.getModelMeta.getRoot, schemaModelMetas, modelValueDictionarys)
          if (MapUtils.isNotEmpty(meta.getModelMeta.getReference)) {
            meta.getModelMeta.getReference.values().forEach(item => setSchemaModelMeta(item, schemaModelMetas, modelValueDictionarys))
          }
        }
        meta.getSampleTemplate
      })

      scenarioSchema.setDefaultFlowSchemaId(schemaMetadataInfo.getDefaultFlowId)
      scenarioSchema.setGroupId(schemaDslTemplate.getSpec.getSchemaGroup.getMetadata.getId)

      if (CollectionUtils.isNotEmpty(schemaMetadataInfo.getFlows)) {
        schemaMetadataInfo.getFlows.forEach(flow => {
          flowSchemas.add(toSchemaFlow(flow, scenarioSchema.getId))

          val procedureClaims = flow.getProcedureClaims
          if (CollectionUtils.isNotEmpty(procedureClaims)) {
            //过滤掉数据库初始化系统默认的
            procedureClaims.stream().filter(e => e.getDomainId != null).forEach(procedureClaim => {
              procedures.add(toProcedureMeta(procedureClaim))
            })
          }
        })

      }

      if (CollectionUtils.isNotEmpty(schemaMetadataInfo.getApiConfigs)) {
        schemaMetadataInfo.getApiConfigs.forEach(config => {
          apiConfigs.add(toScenarioApiConfig(config))
        })
      }


      map += (MetaCategoryEnum.ScenarioSchema -> ScenarioSchemaMapVal(scenarioSchema))
      map += (MetaCategoryEnum.ModelMeta -> ScenarioSchemaModelMetaMapVal(schemaModelMetas.map(e => (e.getId.toLong, e)).toMap))
      map += (MetaCategoryEnum.SampleTemplate -> ScenarioSchemaSampleTemplateMapVal(toSchemaSampleTemplate(inputSampleOpt, outputSampleOpt, schemaMetadataInfo.getId)))
      map += (MetaCategoryEnum.Dictionary -> modelValueDictionaryMapVal(modelValueDictionarys.map(e => (e.getId.toLong, e)).toMap))
      map += (MetaCategoryEnum.FlowSchema -> FlowSchemaMapVal(flowSchemas.map(e => (e.getId.toLong, e)).toMap))
      map += (MetaCategoryEnum.Procedure -> ProcedureMetaMapVal(procedures.map(e => (e.getId.toLong, e)).toMap))
      map += (MetaCategoryEnum.ScenarioApiConfig -> ScenarioApiConfigMapVal(apiConfigs.map(e => (e.getId.toLong, e)).toMap))

    }
    map
  }

  def toScenarioApiConfig(config: ApiConfig): ScenarioApiConfig = {
    val scenarioApiConfig = new ScenarioApiConfig
    scenarioApiConfig.setId(config.getId)
    scenarioApiConfig.setScenarioSchemaId(config.getScenarioSchemaId)
    scenarioApiConfig.setScenarioInstanceId(config.getScenarioInstanceId)
    scenarioApiConfig.setConfigType(config.getConfigType)
    scenarioApiConfig.setType(config.getType)
    scenarioApiConfig.setName(config.getName)
    scenarioApiConfig.setCategory(config.getCategory)
    scenarioApiConfig.setDefaultValue(config.getDefaultValue)
    scenarioApiConfig.setDescription(config.getDescription)
    scenarioApiConfig.setRequired(config.isRequired)
    scenarioApiConfig.setTenantCode(TraceOp.getTenant)
    scenarioApiConfig.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    scenarioApiConfig
  }


  def toDomain(domainInfo: DomainInfo): Domain = {
    val domain = new Domain
    domain.setId(domainInfo.getId)
    domain.setName(domainInfo.getName)
    domain.setTitle(domainInfo.getTitle)
    domain.setDescription(domainInfo.getDescription)
    domain.setNamespaceId(domainInfo.getNamespaceId)
    domain.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    domain
  }

  def toProcedureMeta(procedureClaim: ProcedureMetadataInfo): ProcedureMeta = {
    val procedureMeta = new ProcedureMeta
    procedureMeta.setId(procedureClaim.getId)
    procedureMeta.setName(procedureClaim.getName)
    procedureMeta.setTitle(procedureClaim.getTitle)
    procedureMeta.setDescription(procedureClaim.getDescription)
    procedureMeta.setType(procedureClaim.getType)
    procedureMeta.setTag(procedureClaim.getTag)
    procedureMeta.setContent(procedureClaim.getContent)
    procedureMeta.setDomainId(procedureClaim.getDomainId)
    procedureMeta.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    procedureMeta.setTenantCode(TraceOp.getTenant)
    //TODO 目前不使用procedure的metaData, 暂时不设置
    //    procedureMeta.setInputModelMetaId()
    //    procedureMeta.setOutputModelMetaId()
    procedureMeta.setProductVersion(procedureClaim.getProductVersion)
    procedureMeta
  }

  def toSchemaFlow(flow: FlowMetadataInfo, schemaId: lang.Long): FlowSchema = {
    val flowSchema = new FlowSchema
    flowSchema.setId(flow.getId)
    flowSchema.setName(flow.getName)
    flowSchema.setScenarioSchemaId(schemaId)
    flowSchema.setDescription(flow.getDescription)
    flowSchema.setVersion(flow.getVersion)
    flowSchema.setContent(flow.getContent)
    flowSchema.setTenantCode(TraceOp.getTenant)
    flowSchema.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    flowSchema
  }


  def toSchemaSampleTemplate(inputSampleOpt: Option[SampleTemplate], outputSampleOpt: Option[SampleTemplate], schemaId: lang.Long): ScenarioSchemaSampleTemplate = {
    val scenarioSchemaSampleTemplate = new ScenarioSchemaSampleTemplate

    inputSampleOpt.map(inputSampleOpt => {
      scenarioSchemaSampleTemplate.setInputTemplate(JsonParser.toJsonString(inputSampleOpt.getSampleTemplate))
      scenarioSchemaSampleTemplate.setId(inputSampleOpt.getId)
    })

    outputSampleOpt.map(outputSampleOpt => {
      scenarioSchemaSampleTemplate.setOutputTemplate(JsonParser.toJsonString(outputSampleOpt.getSampleTemplate))
      scenarioSchemaSampleTemplate.setId(outputSampleOpt.getId)
    })
    if (inputSampleOpt.isEmpty && outputSampleOpt.isEmpty) {
      return null
    }
    scenarioSchemaSampleTemplate.setScenarioSchemaId(schemaId)
    scenarioSchemaSampleTemplate.setTenantCode(TraceOp.getTenant)
    scenarioSchemaSampleTemplate.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    scenarioSchemaSampleTemplate
  }

  def setSchemaModelMeta(metaItem: ModelMetaItem, schemaModelMetas: mutable.HashSet[ScenarioSchemaModelMeta], modelValueDictionarys: mutable.HashSet[ModelValueDictionary]): Unit = {
    schemaModelMetas.add(toSchemaModelMeta(metaItem, modelValueDictionarys))
    if (metaItem.getItem != null) {
      setSchemaModelMeta(metaItem.getItem, schemaModelMetas, modelValueDictionarys)
    }
    if (MapUtils.isNotEmpty(metaItem.getChildren)) {
      metaItem.getChildren.values().forEach(item => setSchemaModelMeta(item, schemaModelMetas, modelValueDictionarys))
    }
  }


  def toSchemaModelMeta(metaItem: ModelMetaItem, modelValueDictionarys: mutable.HashSet[ModelValueDictionary]): ScenarioSchemaModelMeta = {
    val schemaModelMeta = new ScenarioSchemaModelMeta
    schemaModelMeta.setId(metaItem.getId)
    schemaModelMeta.setName(metaItem.getName)
    schemaModelMeta.setTitle(metaItem.getTitle)
    schemaModelMeta.setDescription(metaItem.getDescription)
    schemaModelMeta.setExample(metaItem.getExample)
    schemaModelMeta.setDefaultValue(metaItem.getDefaultValue)
    schemaModelMeta.setType(metaItem.getType)
    schemaModelMeta.setFormat(metaItem.getFormat)
    schemaModelMeta.setDirection(metaItem.getDirection)
    schemaModelMeta.setRequired(metaItem.isRequired)
    schemaModelMeta.setConfig(JsonParser.fromObjToJsonMap(metaItem.getConfig))
    schemaModelMeta.setExtension(metaItem.getExtension)
    schemaModelMeta.setParentId(metaItem.getParentId)
    schemaModelMeta.setRootId(metaItem.getRootId)
    schemaModelMeta.setRefId(metaItem.getRefId)
    schemaModelMeta.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    schemaModelMeta.setTenantCode(TraceOp.getTenant)

    if (metaItem.getDictionary != null) {
      schemaModelMeta.setModelValueDictionaryId(metaItem.getDictionary.getId)
      modelValueDictionarys.add(toModelValueDictionary(metaItem.getDictionary))
    }
    schemaModelMeta
  }

  def toModelValueDictionary(dictionary: DictionaryInfo): ModelValueDictionary = {
    val modelValueDictionary = new ModelValueDictionary
    modelValueDictionary.setId(dictionary.getId)
    modelValueDictionary.setContent(dictionary.getContent)
    modelValueDictionary.setSignature(dictionary.getSignature)
    modelValueDictionary.setLoaderType(dictionary.getLoaderType)
    modelValueDictionary.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    modelValueDictionary.setTenantCode(TraceOp.getTenant)

    modelValueDictionary
  }
}

