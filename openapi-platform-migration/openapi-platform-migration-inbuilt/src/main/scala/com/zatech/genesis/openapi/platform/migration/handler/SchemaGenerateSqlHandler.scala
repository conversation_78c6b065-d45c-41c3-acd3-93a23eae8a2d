package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioSchema
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareR<PERSON>ult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.schema.{SchemaDeleteSqlGenerator, SchemaInsertSqlGenerator, SchemaUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.{IterableHasAsJava, SeqHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class SchemaGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newMapVal: MapVal, oldMapVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newMapVal).map(_.asInstanceOf[ScenarioSchemaMapVal].value).orNull
    val oldInfo = Option(oldMapVal).map(_.asInstanceOf[ScenarioSchemaMapVal].value).orNull
    if (newInfo != null && oldInfo != null && newInfo.getId != oldInfo.getId) {
      //删除老的
      (CompareHelper.compare(classOf[ScenarioSchema])(null, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new SchemaInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
      //插入新的
      (CompareHelper.compare(classOf[ScenarioSchema])(newInfo, null) match {
        case delete: DeleteDslCompareResult => Some(new SchemaDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    } else {
      (CompareHelper.compare(classOf[ScenarioSchema])(newInfo, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new SchemaInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new SchemaUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new SchemaDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    }
    sqlList
  }

}
