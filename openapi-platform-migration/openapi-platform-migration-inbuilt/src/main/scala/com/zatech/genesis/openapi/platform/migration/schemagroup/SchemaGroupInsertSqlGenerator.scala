package com.zatech.genesis.openapi.platform.migration.schemagroup

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioSchemaGroupMapper
import com.zatech.genesis.openapi.platform.migration.model.InsertDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractInsertSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
class SchemaGroupInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
  extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult) {
  override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioSchemaGroupMapper]
}
