package com.zatech.genesis.openapi.platform.migration.handler

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioSchemaModelMeta
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioSchemaModelMetaMapper
import com.zatech.genesis.openapi.platform.infra.knowledge.modelmeta.ModelMeta
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.schema.SchemaUpdateSqlGenerator
import com.zatech.genesis.openapi.platform.migration._
import org.apache.ibatis.mapping.BoundSql

import java.util
import scala.jdk.CollectionConverters.{CollectionHasAsScala, IterableHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class SchemaModelMetaGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newMapVal = Option(newVal).map(_.asInstanceOf[ScenarioSchemaModelMetaMapVal].value).getOrElse(Map())
    val oldMapVal = Option(oldVal).map(_.asInstanceOf[ScenarioSchemaModelMetaMapVal].value).getOrElse(Map())
    val unionKeys = newMapVal.keySet.union(oldMapVal.keySet)
    //有则插入，无则删除，同有则更新
    unionKeys.foreach(key =>
      (CompareHelper.compare(classOf[ScenarioSchemaModelMeta])(newMapVal.get(key).orNull, oldMapVal.get(key).orNull) match {
        case insert: InsertDslCompareResult => Some(new modelMetaInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new SchemaUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new modelMetaDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    )
    sqlList.stream().distinct().toList
  }

  private class modelMetaInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
    extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult) {

    override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioSchemaModelMetaMapper]

    lazy val rootIdField = classOf[ModelMeta].getDeclaredField("rootId")

    /**
     * 特殊处理了delete的，因此只需要insert的
     */
    override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
      val insertionMappedStatementId = s"${mapperCls.getName}.insert"
      val insertionMappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(insertionMappedStatementId)
      Seq(insertionMappedStatement.getBoundSql(table))
    }

    override def generate: Seq[String] = {
      val sqlList = new util.LinkedList[String]()
      compareResult.getResult.stream().filter(e => e.getField.equals(rootIdField) && e.getNewValue!=null).findFirst()
        .ifPresent(result => sqlList.add(s"DELETE FROM $tableName WHERE root_id = ${result.getNewValue} or id = ${result.getNewValue}"))
      sqlList.addAll(super.generate.asJavaCollection)
      sqlList.asScala.toSeq
    }
  }


  private class modelMetaDeleteSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: DeleteDslCompareResult)
    extends AbstractDeleteSqlGenerator(sqlGenerateTools, compareResult) {

    override protected def getBoundSqls(table: Any): Seq[BoundSql] = null

    override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioSchemaModelMetaMapper]

    lazy val rootIdField = classOf[ModelMeta].getDeclaredField("rootId")

    override def generate: Seq[String] = {
      val sqlList = new util.LinkedList[String]()
      compareResult.getResult.stream().filter(e => e.getField.equals(rootIdField) && e.getNewValue != null).findFirst()
        .ifPresent(result => sqlList.add(s"DELETE FROM $tableName WHERE root_id = ${result.getNewValue} or id = ${result.getNewValue}"))
      sqlList.asScala.toSeq
    }
  }

}
