package com.zatech.genesis.openapi.platform.migration

import com.baomidou.mybatisplus.annotation.{TableField, TableId}
import com.zatech.genesis.openapi.platform.migration.exception.MigrationErrorCode
import com.zatech.genesis.openapi.platform.migration.model._
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException
import com.zatech.genesis.openapi.platform.share.tools.ReflectUtil

import java.lang.reflect.Field
import scala.jdk.CollectionConverters._

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
object CompareHelper {

  @throws[IllegalAccessException]
  def compare(tableCls: Class[_])(newObj: Object, oldObj: Object): DslCompareResult = {
    if (oldObj != null && newObj != null && (newObj.getClass != oldObj.getClass)) {
      throw OpenApiException.by(MigrationErrorCode.compare_not_support).params(this.getClass.getName, oldObj.getClass.getName).build()
    }
    var objId = 0L
    var compareItems: Array[DslCompareResult.DslCompareItem] = Array()
    if (newObj != null) {
      val newFields = ReflectUtil.getFields(newObj.getClass, isTableField)
      compareItems = newFields.flatMap(field => {
        val newValue = ReflectUtil.getFieldValue(newObj, field)
        val oldValue = if (oldObj == null) null else ReflectUtil.getFieldValue(oldObj, field)
        if (((newValue == null || oldValue == null) && (newValue != oldValue)) ||
          (newValue != null && !(newValue == oldValue))
        ) Some(new DslCompareResult.DslCompareItem(field, oldValue, newValue))
        else None
      })
    }
    if (newObj == null && oldObj != null) {
      objId = ReflectUtil.getFieldValue(oldObj, ReflectUtil.getField(oldObj.getClass, "id")).toString.toLong
      new DeleteDslCompareResult(objId, tableCls)
    } else if (oldObj == null && newObj != null) {
      objId = ReflectUtil.getFieldValue(newObj, ReflectUtil.getField(newObj.getClass, "id")).toString.toLong
      new InsertDslCompareResult(objId, tableCls, compareItems.toSeq.asJava)
    } else if (compareItems.nonEmpty) {
      objId = ReflectUtil.getFieldValue(newObj, ReflectUtil.getField(newObj.getClass, "id")).toString.toLong
      new UpdateDslCompareResult(objId, tableCls, compareItems.toSeq.asJava)
    } else new NoDiffDslCompareResult(objId, tableCls)
  }

  def isTableField(field: Field): Boolean = field.getAnnotation(classOf[TableField]) != null || field.getAnnotation(classOf[TableId]) != null
}
