package com.zatech.genesis.openapi.platform.migration.instance

import com.zatech.gaia.resource.components.enums.common.YesNoEnum
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.instance._
import com.zatech.genesis.openapi.platform.infra.application.entity._
import com.zatech.genesis.openapi.platform.infra.application.model.{OpenApi3DocModel, ScenarioInstanceMetaDocModel}
import com.zatech.genesis.openapi.platform.migration.constant.MigrationConstant
import com.zatech.genesis.openapi.platform.migration.handler.IGenerateSqlHandler
import com.zatech.genesis.openapi.platform.migration.model.AnalyzeResult
import com.zatech.genesis.openapi.platform.migration.schema.SchemaDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.{Abstract<PERSON>l<PERSON><PERSON><PERSON><PERSON>, DslAnalyzeTools, MapVal}
import com.zatech.genesis.openapi.platform.share.enums.{MetaCategoryEnum, ScenarioSchemaBindingEnum}
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import com.zatech.genesis.openapi.platform.share.{Javable, TraceSupport}
import com.zatech.octopus.component.sleuth.TraceOp
import org.apache.commons.collections4.CollectionUtils

import java.util.Objects
import scala.collection.mutable
import scala.jdk.CollectionConverters.CollectionHasAsScala

/**
 * <AUTHOR>
 * @date 2022/10/18 16:56
 * */
class InstanceDslAnalyzer(instanceDslTemplate: InstanceDslTemplate) extends AbstractDslAnalyzer[InstanceDslTemplate](instanceDslTemplate) with MapVal with Javable{

  override val parentOpt: Option[AbstractDslAnalyzer[_]] = Some(new SchemaDslAnalyzer(instanceDslTemplate.getSpec.getSchema))


  override protected def doSelfAnalyze(tools: DslAnalyzeTools): AnalyzeResult[InstanceDslTemplate] = {
    val entityOpt = tools.scenarioInstanceAggregationFactory.getDomainOpt(instanceDslTemplate.getMetadata.getId)
    val result = new AnalyzeResult[InstanceDslTemplate]()
    if (entityOpt.nonEmpty && Objects.equals(tools.cacheUtil.getString(MigrationConstant.NEED_EXPORT_OLD_DSL), YesNoEnum.YES.getDbView)) {
      result.setOldDsl(entityOpt.map(entity => {
        val instanceDslTemplate = entity.`export`()
        //添加基础信息
        entity.fillBasicInfo(instanceDslTemplate)
        instanceDslTemplate
      }).orNull)
    }

    result.setNewDsl(instanceDslTemplate)

    //各个层级对象映射
    val oldDslMap = getInstanceInfoMap(result.getOldDsl)
    val newDslMap = getInstanceInfoMap(result.getNewDsl)
    MetaCategoryEnum.values().foreach(metaCategoryEnum => {
      if (newDslMap.contains(metaCategoryEnum) || oldDslMap.contains(metaCategoryEnum)) {
        result.setMigrationSqls(
          IGenerateSqlHandler(metaCategoryEnum).getGenerateSql(
            newDslMap.get(metaCategoryEnum).orNull,
            oldDslMap.get(metaCategoryEnum).orNull,
            tools
          )
        )
      }
    })
    result
  }


  def getInstanceInfoMap(instanceDslTemplate: InstanceDslTemplate): Map[MetaCategoryEnum, MapVal] = {
    var map: Map[MetaCategoryEnum, MapVal] = Map()
    if (instanceDslTemplate != null && instanceDslTemplate.getMetadata != null) {
      val instance = new ScenarioInstance

      val metaInfo = instanceDslTemplate.getMetadata
      instance.setId(metaInfo.getId)
      instance.setName(metaInfo.getName)
      instance.setTitle(metaInfo.getTitle)
      instance.setActiveFlag(metaInfo.isActiveFlag)
      instance.setValidateFlag(metaInfo.isValidateFlag)
      instance.setVersion(metaInfo.getVersion)
      instance.setContent(metaInfo.getContent)
      instance.setScenarioSchemaId(instanceDslTemplate.getSpec.getSchema.getMetadata.getId)
      instance.setDeleted(metaInfo.getDeleted)
      instance.setTenantCode(TraceOp.getTenant)
      instance.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
      instance.setIsCallback(if (metaInfo.isCallback) "Y" else "N")
      instance.setWeight(metaInfo.getWeight)
      if (CollectionUtils.isNotEmpty(metaInfo.getAuthenticationTypes)) {
        instance.setAuthenticationType(metaInfo.getAuthenticationTypes.asScala.mkString(","))
      }


      Option(metaInfo.getExtension).map(_.getType match {
        case ScenarioSchemaBindingEnum.Goods => map += (MetaCategoryEnum.ScenarioInstanceGoods -> ScenarioInstanceGoodsMapVal(toInstanceGoods(metaInfo.getExtension)))
        case ScenarioSchemaBindingEnum.Plan => map += (MetaCategoryEnum.ScenarioInstancePlan -> ScenarioInstancePlanMapVal(toInstancePlan(metaInfo.getExtension)))
        case ScenarioSchemaBindingEnum.Insurer => map += (MetaCategoryEnum.ScenarioInstanceInsurer -> ScenarioInstanceInsurerMapVal(toInstanceInsurer(metaInfo.getExtension)))
      })

      Option(metaInfo.getCallbackInfo).map(
        info => map += (MetaCategoryEnum.CallbackKey -> CallbackKeyMapVal(toCallbackKey(info))))

      map += (MetaCategoryEnum.ScenarioInstance -> ScenarioInstanceMapVal(instance))

      map += (MetaCategoryEnum.RScenarioInstanceFlowSchema -> RScenarioInstanceFlowSchemaMapVal(toRScenarioInstanceFlowSchema(metaInfo.getBindFlow, metaInfo.getId).orNull))

      map = map ++ getDocInfo(metaInfo.getDoc, metaInfo.getId)

      map = map ++ getDocInfoI18n(metaInfo.getDocInfoI18ns, metaInfo.getId)

    }
    map
  }

  def getDocInfoI18n(docInfos: JList[DocInfoI18n], instanceId: Long): Map[MetaCategoryEnum, MapVal] = {
    var map: Map[MetaCategoryEnum, MapVal] = Map()
    //instanceDocI18n 收集器
    val scenarioInstanceDocI18ns = new mutable.HashSet[ScenarioInstanceDocI18n]
    // i18n的doc 收集器
    val openApiDocs = new mutable.HashSet[OpenApiDoc]
    // i18n的metaDoc 收集器
    val metaDocs = new mutable.HashSet[MetaDoc]
    if (CollectionUtils.isNotEmpty(docInfos)) {
      docInfos.forEach(doc => {
        val scenarioInstanceDocI18n = new ScenarioInstanceDocI18n
        scenarioInstanceDocI18n.setId(doc.getId)
        scenarioInstanceDocI18n.setLang(doc.getLang)
        scenarioInstanceDocI18n.setScenarioInstanceId(instanceId)
        scenarioInstanceDocI18n.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
        scenarioInstanceDocI18n.setTenantCode(TraceOp.getTenant)

        val openApiDocOpt = Option(doc.getOpenapiDoc).map(content => JsonParser.fromJsonMapToObj(content, classOf[OpenApi3DocModel]))
        val metaDocOpt = Option(doc.getMetaDoc).map(content => JsonParser.fromJsonMapToObj(content, classOf[ScenarioInstanceMetaDocModel]))

        openApiDocs.add(openApiDocOpt.map(apiDoc => {
          scenarioInstanceDocI18n.setOpenapi3DocId(apiDoc.getId)
          val docDO = new OpenApiDoc
          docDO.setId(apiDoc.getId)
          docDO.setOpenapi3Doc(doc.getOpenapiDoc)
          docDO.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
          docDO.setTenantCode(TraceOp.getTenant)
          docDO
        }).getOrElse(new OpenApiDoc))

        metaDocs.add(metaDocOpt.map(apiDoc => {
          scenarioInstanceDocI18n.setMetaDocId(apiDoc.getId)
          val docDO = new MetaDoc
          docDO.setId(apiDoc.getId)
          docDO.setMetaDoc(doc.getMetaDoc)
          docDO.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
          docDO.setTenantCode(TraceOp.getTenant)
          docDO
        }).getOrElse(new MetaDoc))
        scenarioInstanceDocI18ns.add(scenarioInstanceDocI18n)
      })
    }

    map += (MetaCategoryEnum.ScenarioInstanceDocI18n -> ScenarioInstanceDocI18nMapVal(scenarioInstanceDocI18ns.map(e => (e.getId.toLong, e)).toMap))
    map += (MetaCategoryEnum.OpenApiDocI18n -> OpenApiDocI18nMapVal(openApiDocs.map(e => (e.getId.toLong, e)).toMap))
    map += (MetaCategoryEnum.MetaDocI18n -> MetaDocI18nMapVal(metaDocs.map(e => (e.getId.toLong, e)).toMap))
    map
  }

  def toRScenarioInstanceFlowSchema(bindFlow: BindFlow, instanceId: Long): Option[RScenarioInstanceFlowSchema] = {
    Option(bindFlow).map(flow => {
      val relation = new RScenarioInstanceFlowSchema
      relation.setId(flow.getId)
      relation.setFlowSchemaId(flow.getFlow.getId)
      relation.setScenarioInstanceId(instanceId)
      relation.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
      relation.setTenantCode(TraceOp.getTenant)
      relation
    })
  }

  def getDocInfo(docInfo: DocInfo, instanceId: Long): Map[MetaCategoryEnum, MapVal] = {
    var map: Map[MetaCategoryEnum, MapVal] = Map()
    if (docInfo != null) {
      val scenarioInstanceDoc = new ScenarioInstanceDoc
      scenarioInstanceDoc.setId(docInfo.getId)
      scenarioInstanceDoc.setScenarioInstanceId(instanceId)
      scenarioInstanceDoc.setInputExample(docInfo.getInputExample)
      scenarioInstanceDoc.setOutputExample(docInfo.getOutputExample)
      scenarioInstanceDoc.setTenantCode(TraceOp.getTenant)
      scenarioInstanceDoc.setCreator(TraceSupport.getAuthenticationUserNameOrNull)

      val openApiDocOpt = Option(docInfo.getOpenapiDoc).map(content => JsonParser.fromJsonMapToObj(content, classOf[OpenApi3DocModel]))
      val metaDocOpt = Option(docInfo.getMetaDoc).map(content => JsonParser.fromJsonMapToObj(content, classOf[ScenarioInstanceMetaDocModel]))

      val openApiDoc = openApiDocOpt.map(apiDoc => {
        scenarioInstanceDoc.setOpenapi3DocId(apiDoc.getId)
        val docDO = new OpenApiDoc
        docDO.setId(apiDoc.getId)
        docDO.setOpenapi3Doc(docInfo.getOpenapiDoc)
        docDO.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
        docDO.setTenantCode(TraceOp.getTenant)
        docDO
      })

      val metaDoc = metaDocOpt.map(apiDoc => {
        scenarioInstanceDoc.setMetaDocId(apiDoc.getId)
        val docDO = new MetaDoc
        docDO.setId(apiDoc.getId)
        docDO.setMetaDoc(docInfo.getMetaDoc)
        docDO.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
        docDO.setTenantCode(TraceOp.getTenant)
        docDO
      })

      //instanceCaseExampleI18ns 收集器
      val instanceCaseExampleI18ns = new mutable.HashSet[ScenarioInstanceCaseExampleI18n]

      val caseExamples = Option(docInfo.getCaseExamples)
        .map(list =>
          list.asScala
            .map(example => (example.getId.asInstanceOf[Long], toCaseExample(example, instanceId, instanceCaseExampleI18ns)))
            .toMap)

      map += (MetaCategoryEnum.MetaDoc -> MetaDocMapVal(metaDoc.orNull))
      map += (MetaCategoryEnum.OpenApiDoc -> OpenApiDocMapVal(openApiDoc.orNull))
      map += (MetaCategoryEnum.ScenarioInstanceDoc -> ScenarioInstanceDocMapVal(scenarioInstanceDoc))
      map += (MetaCategoryEnum.CaseExamples -> ScenarioInstanceCaseExampleMapVal(caseExamples.getOrElse(Map())))
      map += (MetaCategoryEnum.CaseI18nExamples -> ScenarioInstanceCaseExampleI18nMapVal(instanceCaseExampleI18ns.map(e => (e.getId.toLong, e)).toMap))
    }
    map
  }


  def toInstanceGoods(extension: InstanceExtension): ScenarioInstanceGoods = {
    val scenarioInstanceGoods = new ScenarioInstanceGoods
    scenarioInstanceGoods.setId(extension.getId)
    scenarioInstanceGoods.setGoodsId(extension.getGoodsId)
    scenarioInstanceGoods.setGoodsCode(extension.getGoodsCode)
    scenarioInstanceGoods.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    scenarioInstanceGoods.setTenantCode(TraceOp.getTenant)
    scenarioInstanceGoods
  }

  def toInstancePlan(extension: InstanceExtension): ScenarioInstancePlan = {
    val scenarioInstancePlan = new ScenarioInstancePlan
    scenarioInstancePlan.setId(extension.getId)
    scenarioInstancePlan.setGoodsId(extension.getGoodsId)
    scenarioInstancePlan.setPlanId(extension.getPlanId)
    scenarioInstancePlan.setPlanCode(extension.getPlanCode)
    scenarioInstancePlan.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    scenarioInstancePlan.setTenantCode(TraceOp.getTenant)
    scenarioInstancePlan
  }

  def toInstanceInsurer(extension: InstanceExtension): ScenarioInstanceInsurer = {
    val scenarioInstanceInsurer = new ScenarioInstanceInsurer
    scenarioInstanceInsurer.setId(extension.getId)
    scenarioInstanceInsurer.setInsurerCode(extension.getInsurerCode)
    scenarioInstanceInsurer.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    scenarioInstanceInsurer.setTenantCode(TraceOp.getTenant)
    scenarioInstanceInsurer
  }

  def toCallbackKey(info: CallbackInfo): CallbackKey = {
    val callbackKey = new CallbackKey
    callbackKey.setId(info.getId)
    callbackKey.setDecryptKey(info.getDecryptKey)
    callbackKey.setDecryptMethod(info.getDecryptMethod)
    callbackKey.setScenarioInstanceId(info.getInstanceId)
    callbackKey.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    callbackKey.setTenantCode(TraceOp.getTenant)
    callbackKey
  }



  def toCaseExample(info: CaseExampleInfo, instanceId: Long, caseExampleI18ns : mutable.HashSet[ScenarioInstanceCaseExampleI18n]) : ScenarioInstanceCaseExample = {
    val example = new ScenarioInstanceCaseExample
    example.setId(info.getId)
    example.setScenarioInstanceId(instanceId)
    example.setOutputExample(info.getOutputExample)
    example.setInputExample(info.getInputExample)
    example.setInputExampleSchema(info.getInputExampleSchema)
    example.setOutputExampleSchema(info.getOutputExampleSchema)
    example.setExampleCase(info.getExampleCase)
    example.setCaseDescription(info.getCaseDescription)
    example.setTenantCode(TraceOp.getTenant)
    example.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    if (CollectionUtils.isNotEmpty(info.getCaseExampleInfoI18ns)) {
      info.getCaseExampleInfoI18ns.forEach(data =>
        caseExampleI18ns.add(toCaseExampleI18n(data, info.getId))
      )
    }
    example
  }

  def toCaseExampleI18n(data: CaseExampleInfoI18n, caseExampleId: Long): ScenarioInstanceCaseExampleI18n = {
    val scenarioInstanceDocI18n = new ScenarioInstanceCaseExampleI18n
    scenarioInstanceDocI18n.setId(data.getId)
    scenarioInstanceDocI18n.setLang(data.getLang)
    scenarioInstanceDocI18n.setCaseExampleId(caseExampleId)
    scenarioInstanceDocI18n.setOutputExampleSchema(data.getOutputExampleSchema)
    scenarioInstanceDocI18n.setInputExampleSchema(data.getInputExampleSchema)
    scenarioInstanceDocI18n.setTenantCode(TraceOp.getTenant)
    scenarioInstanceDocI18n.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
    scenarioInstanceDocI18n
  }
}

