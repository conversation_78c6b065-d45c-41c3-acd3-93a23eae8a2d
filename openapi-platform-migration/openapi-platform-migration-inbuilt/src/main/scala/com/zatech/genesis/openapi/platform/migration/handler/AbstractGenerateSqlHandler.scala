package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.migration.{DslAnalyzeTools, MapVal}

import java.util

/**
 * <AUTHOR>
 * @date 2022/10/19 18:18
 * */
abstract class AbstractGenerateSqlHandler extends IGenerateSqlHandler with MapVal {


  def getGenerateSql(newMapVal: MapVal, oldMapVal: MapVal, tools: DslAnalyzeTools): util.List[String] = ???


}
