package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioInstanceCaseExampleI18n
import com.zatech.genesis.openapi.platform.migration.instance.{InstanceDeleteSqlGenerator, InstanceInsertSqlGenerator, InstanceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.IterableHasAsJava

class CaseExamplesI18nSqlGenerateHandler extends AbstractGenerateSqlHandler {


  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newMapVal = Option(newVal).map(_.asInstanceOf[ScenarioInstanceCaseExampleI18nMapVal].value).getOrElse(Map())
    val oldMapVal = Option(oldVal).map(_.asInstanceOf[ScenarioInstanceCaseExampleI18nMapVal].value).getOrElse(Map())
    val unionKeys = newMapVal.keySet.union(oldMapVal.keySet)
    //有则插入，无则删除，同有则更新
    unionKeys.foreach(key =>
      (CompareHelper.compare(classOf[ScenarioInstanceCaseExampleI18n])(newMapVal.get(key).orNull, oldMapVal.get(key).orNull) match {
        case insert: InsertDslCompareResult => Some(new InstanceInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new InstanceUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    )
    sqlList
  }

}
