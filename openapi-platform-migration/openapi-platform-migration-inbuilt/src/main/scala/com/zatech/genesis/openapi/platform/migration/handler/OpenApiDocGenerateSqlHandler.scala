package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.application.entity.OpenApiDoc
import com.zatech.genesis.openapi.platform.migration.instance.{InstanceDeleteSqlGenerator, InstanceInsertSqlGenerator, InstanceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.{IterableHasAsJava, SeqHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class OpenApiDocGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newVal).map(_.asInstanceOf[OpenApiDocMapVal].value).orNull
    val oldInfo = Option(oldVal).map(_.asInstanceOf[OpenApiDocMapVal].value).orNull
    if (newInfo != null && oldInfo != null && newInfo.getId != oldInfo.getId) {
      //删除老的
      (CompareHelper.compare(classOf[OpenApiDoc])(null, oldInfo) match {
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
      //插入新的
      (CompareHelper.compare(classOf[OpenApiDoc])(newInfo, null) match {
        case insert: InsertDslCompareResult => Some(new InstanceInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }

    } else {
      (CompareHelper.compare(classOf[OpenApiDoc])(newInfo, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new InstanceInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new InstanceUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    }

    sqlList
  }

}
