package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.global.entity.Namespace
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.namespace.{NamespaceDeleteSqlGenerator, NamespaceInsertSqlGenerator, NamespaceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.{IterableHasAsJava, SeqHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class NameSpaceGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newMapVal: MapVal, oldMapVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newMapVal).map(_.asInstanceOf[NamespaceMapVal].value).orNull
    val oldInfo = Option(oldMapVal).map(_.asInstanceOf[NamespaceMapVal].value).orNull
    (CompareHelper.compare(classOf[Namespace])(newInfo, oldInfo) match {
      case insert: InsertDslCompareResult => Some(new NamespaceInsertSqlGenerator(tools.sqlGenerateTools, insert))
      case update: UpdateDslCompareResult => Some(new NamespaceUpdateSqlGenerator(tools.sqlGenerateTools, update))
      case delete: DeleteDslCompareResult => Some(new NamespaceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
      case _ => None
    }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    sqlList
  }

}
