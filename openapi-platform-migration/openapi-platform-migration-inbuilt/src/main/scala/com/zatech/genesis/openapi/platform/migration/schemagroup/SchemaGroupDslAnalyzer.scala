package com.zatech.genesis.openapi.platform.migration.schemagroup

import com.zatech.gaia.resource.components.enums.common.YesNoEnum
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.schemegroup.SchemaGroupDslTemplate
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioSchemaGroup
import com.zatech.genesis.openapi.platform.migration.constant.MigrationConstant
import com.zatech.genesis.openapi.platform.migration.handler.IGenerateSqlHandler
import com.zatech.genesis.openapi.platform.migration.model.AnalyzeResult
import com.zatech.genesis.openapi.platform.migration.namespace.NamespaceDslAnalyzer
import com.zatech.genesis.openapi.platform.migration.{AbstractDslA<PERSON>yzer, DslAnalyzeTools, MapVal}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.openapi.platform.share.enums.MetaCategoryEnum
import com.zatech.octopus.component.sleuth.TraceOp

import java.util.Objects

/**
 * <AUTHOR>
 * @Date 2022/8/26
 * */
class SchemaGroupDslAnalyzer(schemaGroupDslTemplate: SchemaGroupDslTemplate)
  extends AbstractDslAnalyzer[SchemaGroupDslTemplate](schemaGroupDslTemplate) with MapVal {

  override val parentOpt: Option[AbstractDslAnalyzer[_]] = Some(new NamespaceDslAnalyzer(schemaGroupDslTemplate.getSpec.getNamespace))

  override protected def doSelfAnalyze(tools: DslAnalyzeTools): AnalyzeResult[SchemaGroupDslTemplate] = {
    val entityOpt = tools.scenarioSchemaGroupEntityFactory.getDomainOpt(schemaGroupDslTemplate.getMetadata.getId)
    val result = new AnalyzeResult[SchemaGroupDslTemplate]()
    if (entityOpt.nonEmpty && Objects.equals(tools.cacheUtil.getString(MigrationConstant.NEED_EXPORT_OLD_DSL), YesNoEnum.YES.getDbView)) {
      result.setOldDsl(entityOpt.map(_.`export`()).orNull)
    }
    result.setNewDsl(schemaGroupDslTemplate)

    val oldDslMap = getSchemaGroup(result.getOldDsl)
    val newDslMap = getSchemaGroup(result.getNewDsl)

    result.setMigrationSqls(
      IGenerateSqlHandler.apply(MetaCategoryEnum.ScenarioSchemaGroup).getGenerateSql(
        newDslMap.get(MetaCategoryEnum.ScenarioSchemaGroup).orNull,
        oldDslMap.get(MetaCategoryEnum.ScenarioSchemaGroup).orNull,
        tools
      )
    )
    result
  }

  def getSchemaGroup(schemaGroupDslTemplate: SchemaGroupDslTemplate): Map[MetaCategoryEnum, MapVal] = {
    var map: Map[MetaCategoryEnum, MapVal] = Map()
    if (schemaGroupDslTemplate != null && schemaGroupDslTemplate.getMetadata != null) {
      val meta = schemaGroupDslTemplate.getMetadata
      val scenarioSchemaGroup = new ScenarioSchemaGroup
      scenarioSchemaGroup.setId(meta.getId)
      scenarioSchemaGroup.setName(meta.getName)
      scenarioSchemaGroup.setTitle(meta.getTitle)
      scenarioSchemaGroup.setConfig(meta.getConfig)
      scenarioSchemaGroup.setActiveFlag(meta.isActiveFlag)
      scenarioSchemaGroup.setNamespaceId(schemaGroupDslTemplate.getSpec.getNamespace.getMetadata.getId)
      scenarioSchemaGroup.setDescription(meta.getDescription)
      scenarioSchemaGroup.setCreator(TraceSupport.getAuthenticationUserNameOrNull)
      scenarioSchemaGroup.setTenantCode(TraceOp.getTenant)
      map += (MetaCategoryEnum.ScenarioSchemaGroup -> ScenarioSchemaGroupMapVal(scenarioSchemaGroup))
    }
    map
  }

}

