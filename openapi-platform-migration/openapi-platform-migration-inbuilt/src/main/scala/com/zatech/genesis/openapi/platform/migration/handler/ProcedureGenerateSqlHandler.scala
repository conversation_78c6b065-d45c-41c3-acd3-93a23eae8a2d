package com.zatech.genesis.openapi.platform.migration.handler

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.entity.ProcedureMeta
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.mapper.ProcedureMetaMapper
import com.zatech.genesis.openapi.platform.migration._
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.schema.{SchemaDeleteSqlGenerator, SchemaUpdateSqlGenerator}
import org.apache.ibatis.mapping.BoundSql

import java.util
import scala.jdk.CollectionConverters.{CollectionHasAsScala, IterableHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class ProcedureGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newMapVal = Option(newVal).map(_.asInstanceOf[ProcedureMetaMapVal].value).getOrElse(Map())
    val oldMapVal = Option(oldVal).map(_.asInstanceOf[ProcedureMetaMapVal].value).getOrElse(Map())
    val unionKeys = newMapVal.keySet.union(oldMapVal.keySet)
    //有则插入，无则删除，同有则更新
    unionKeys.foreach(key =>
      (CompareHelper.compare(classOf[ProcedureMeta])(newMapVal.get(key).orNull, oldMapVal.get(key).orNull) match {
        case insert: InsertDslCompareResult => Some(new ProcedureCustomerInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new SchemaUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new SchemaDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    )
    sqlList

  }

  private class ProcedureCustomerInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
    extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult) {

    override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ProcedureMetaMapper]

    lazy val name = classOf[ProcedureMeta].getDeclaredField("name")

    lazy val category = classOf[ProcedureMeta].getDeclaredField("type")

    lazy val tag = classOf[ProcedureMeta].getDeclaredField("tag")

    lazy val domainId = classOf[ProcedureMeta].getDeclaredField("domainId")

    /**
     * 特殊处理了delete的，因此只需要insert的
     */
    override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
      val insertionMappedStatementId = s"${mapperCls.getName}.insert"
      val insertionMappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(insertionMappedStatementId)
      Seq(insertionMappedStatement.getBoundSql(table))
    }

    override def generate: Seq[String] = {
      val sqlList = new util.LinkedList[String]()
      val sql = new StringBuilder(s"DELETE FROM $tableName WHERE 1=1")
      compareResult.getResult.stream().forEach(result => {
        if (result.getField.equals(name) && result.getNewValue != null) {
          sql.append(s" and name = '${result.getNewValue}'")
        }
        if (result.getField.equals(category) && result.getNewValue != null) {
          sql.append(s" and type = '${result.getNewValue}'")
        }
        if (result.getField.equals(tag) && result.getNewValue != null) {
          sql.append(s" and tag = '${result.getNewValue}'")
        }
        if (result.getField.equals(domainId) && result.getNewValue != null) {
          sql.append(s" and domain_id = ${result.getNewValue}")
        }
      })
      //拥有其中三个即可判断需要delete
      if (sql.toString().contains("name") && sql.toString().contains("type") && sql.toString().contains("tag")) {
        sqlList.add(sql.toString())
      }
      sqlList.addAll(super.generate.asJavaCollection)
      sqlList.asScala.toSeq
    }
  }
}
