package com.zatech.genesis.openapi.platform.migration.handler

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioInstanceDoc
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioInstanceDocMapper
import com.zatech.genesis.openapi.platform.migration._
import com.zatech.genesis.openapi.platform.migration.instance.{InstanceDeleteSqlGenerator, InstanceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import org.apache.ibatis.mapping.BoundSql

import java.util
import scala.jdk.CollectionConverters.{CollectionHasAsScala, IterableHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class InstanceDocGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newVal).map(_.asInstanceOf[ScenarioInstanceDocMapVal].value).orNull
    val oldInfo = Option(oldVal).map(_.asInstanceOf[ScenarioInstanceDocMapVal].value).orNull
    if (newInfo != null && oldInfo != null && newInfo.getId != oldInfo.getId) {
      //删除老的
      (CompareHelper.compare(classOf[ScenarioInstanceDoc])(null, oldInfo) match {
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
      //插入新的
      (CompareHelper.compare(classOf[ScenarioInstanceDoc])(newInfo, null) match {
        case insert: InsertDslCompareResult => Some(new InstanceDocCustomerInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }

    } else {
      (CompareHelper.compare(classOf[ScenarioInstanceDoc])(newInfo, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new InstanceDocCustomerInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new InstanceUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    }
    sqlList
  }

  private class InstanceDocCustomerInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
    extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult) {

    override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioInstanceDocMapper]

    lazy val scenarioInstanceId = classOf[ScenarioInstanceDoc].getDeclaredField("scenarioInstanceId")

    /**
     * 特殊处理了delete的，因此只需要insert的
     */
    override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
      val insertionMappedStatementId = s"${mapperCls.getName}.insert"
      val insertionMappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(insertionMappedStatementId)
      Seq(insertionMappedStatement.getBoundSql(table))
    }

    override def generate: Seq[String] = {
      val sqlList = new util.LinkedList[String]()
      compareResult.getResult.stream().filter(e => e.getField.equals(scenarioInstanceId) && e.getNewValue != null).findFirst()
        .ifPresent(result => sqlList.add(s"DELETE FROM $tableName WHERE scenario_instance_id = ${result.getNewValue} or id = ${result.getNewValue}"))
      sqlList.addAll(super.generate.asJavaCollection)
      sqlList.asScala.toSeq
    }
  }


}
