package com.zatech.genesis.openapi.platform.migration.schemagroup

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioSchemaGroupMapper
import com.zatech.genesis.openapi.platform.migration.model.DeleteDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractDeleteSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
class SchemaGroupDeleteSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: DeleteDslCompareResult)
  extends AbstractDeleteSqlGenerator(sqlGenerateTools, compareResult) {
  override protected val mapperCls: Class[_ <: BaseMapper[_]] = classOf[ScenarioSchemaGroupMapper]
}
