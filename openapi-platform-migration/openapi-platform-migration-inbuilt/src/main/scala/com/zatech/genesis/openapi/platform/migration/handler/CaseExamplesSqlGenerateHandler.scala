package com.zatech.genesis.openapi.platform.migration.handler
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioInstanceCaseExample
import com.zatech.genesis.openapi.platform.migration.instance.{InstanceDeleteSqlGenerator, InstanceInsertSqlGenerator, InstanceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.schema.SchemaDeleteSqlGenerator
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.IterableHasAsJava

class CaseExamplesSqlGenerateHandler extends AbstractGenerateSqlHandler {


  override def getGenerateSql(newMapVal: MapVal, oldMapVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {
    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newMapVal).map(_.asInstanceOf[ScenarioInstanceCaseExampleMapVal].value).getOrElse(Map())
    val oldInfo = Option(oldMapVal).map(_.asInstanceOf[ScenarioInstanceCaseExampleMapVal].value).getOrElse(Map())
    val unionCaseExampleIds = newInfo.keySet.union(oldInfo.keySet)
    //先根据id来拼一条
    unionCaseExampleIds.foreach(unionId =>

      (newInfo.get(unionId) match {
        case None => {
          //新的里没有
          CompareHelper.compare(classOf[ScenarioInstanceCaseExample])(null, oldInfo.get(unionId).orNull) match {
            case update: UpdateDslCompareResult => Some(new InstanceUpdateSqlGenerator(tools.sqlGenerateTools, update))
            case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
            case _ => None
          }
        }
        case some:Some[ScenarioInstanceCaseExample] => {
          val newExistence = some.value
          CompareHelper.compare(classOf[ScenarioInstanceCaseExample])(newExistence, oldInfo.get(unionId).orNull) match {
            case insert: InsertDslCompareResult => Some(new InstanceInsertSqlGenerator(tools.sqlGenerateTools, insert))
            case update: UpdateDslCompareResult => Some(new InstanceUpdateSqlGenerator(tools.sqlGenerateTools, update))
            case delete: DeleteDslCompareResult => Some(new InstanceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
            case _ => None
          }
        }
      })
        .map(generator => {
          sqlList.addAll(generator.generate.asJavaCollection)
        })


    )
   sqlList
  }

}
