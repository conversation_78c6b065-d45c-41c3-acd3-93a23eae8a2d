package com.zatech.genesis.openapi.platform.migration.record

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zatech.genesis.openapi.platform.infra.apiview.mapper._
import com.zatech.genesis.openapi.platform.migration.model.InsertDslCompareResult
import com.zatech.genesis.openapi.platform.migration.{AbstractInsertSqlGenerator, SqlGenerateTools}

/**
 * <AUTHOR>
 * @date 2022/10/13 18:37
 * */
class RecordInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
  extends AbstractInsertSqlGenerator(sqlGenerateTools, compareResult) {

  override protected val mapperCls: Class[_ <: BaseMapper[_]] = tableName match {
    case "api_view_record" => classOf[ApiViewRecordMapper]
    case "api_view_group" => classOf[ApiViewGroupMapper]
    case "api_view_document" => classOf[ApiViewDocumentMapper]
    case "api_view_schema" => classOf[ApiViewSchemaMapper]
    case "r_view_document" => classOf[RViewDocumentMapper]
  }
}
