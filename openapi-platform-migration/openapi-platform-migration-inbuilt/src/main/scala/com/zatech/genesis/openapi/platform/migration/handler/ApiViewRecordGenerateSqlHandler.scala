package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.apiview.entity.ApiViewRecord
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.record.{RecordDeleteSqlGenerator, RecordInsertSqlGenerator, RecordUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.IterableHasAsJava

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class ApiViewRecordGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newVal).map(_.asInstanceOf[ApiViewRecordMapVal].value).orNull
    val oldInfo = Option(oldVal).map(_.asInstanceOf[ApiViewRecordMapVal].value).orNull
    if (newInfo != null && oldInfo != null && newInfo.getId != oldInfo.getId) {
      //删除老的
      (CompareHelper.compare(classOf[ApiViewRecord])(null, oldInfo) match {
        case delete: DeleteDslCompareResult => Some(new RecordDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
      //插入新的
      (CompareHelper.compare(classOf[ApiViewRecordMapVal])(newInfo, null) match {
        case insert: InsertDslCompareResult => Some(new RecordInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }

    } else {
      (CompareHelper.compare(classOf[ApiViewRecord])(newInfo, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new RecordInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new RecordUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new RecordDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    }

    sqlList
  }

}
