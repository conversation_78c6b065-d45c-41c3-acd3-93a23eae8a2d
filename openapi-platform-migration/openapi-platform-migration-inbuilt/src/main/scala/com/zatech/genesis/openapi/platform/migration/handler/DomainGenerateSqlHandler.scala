package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.global.entity.Domain
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.namespace.{NamespaceDeleteSqlGenerator, NamespaceInsertSqlGenerator, NamespaceUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.{IterableHasAsJava, SeqHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class DomainGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newMapVal = Option(newVal).map(_.asInstanceOf[DomainMapVal].value).getOrElse(Map())
    val oldMapVal = Option(oldVal).map(_.asInstanceOf[DomainMapVal].value).getOrElse(Map())
    val unionKeys = newMapVal.keySet.union(oldMapVal.keySet)
    //有则插入，无则删除，同有则更新
    unionKeys.foreach(key =>
      (CompareHelper.compare(classOf[Domain])(newMapVal.get(key).orNull, oldMapVal.get(key).orNull) match {
        case insert: InsertDslCompareResult => Some(new NamespaceInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new NamespaceUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new NamespaceDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    )
    sqlList
  }

}
