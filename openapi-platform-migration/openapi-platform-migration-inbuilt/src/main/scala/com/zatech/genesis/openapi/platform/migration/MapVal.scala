package com.zatech.genesis.openapi.platform.migration

import com.zatech.genesis.openapi.platform.infra.apiview.entity._
import com.zatech.genesis.openapi.platform.infra.application.entity._
import com.zatech.genesis.openapi.platform.infra.global.entity.{Domain, Namespace}
import com.zatech.genesis.openapi.platform.infra.knowledge.flow.inbuilt.entity.FlowSchema
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.entity.{ModelValueDictionary, ProcedureMeta}

/**
 * <AUTHOR>
 * @date 2022/10/13 18:37
 * */
trait MapVal {


  //nameSpace 层级
  case class NamespaceMapVal(value: Namespace) extends MapVal

  //schemaGroup 层级
  case class ScenarioSchemaGroupMapVal(value: ScenarioSchemaGroup) extends MapVal


  //schema 层级
  case class ScenarioSchemaMapVal(value: ScenarioSchema) extends MapVal

  case class ScenarioSchemaModelMetaMapVal(value: Map[Long, ScenarioSchemaModelMeta]) extends MapVal

  case class ScenarioSchemaSampleTemplateMapVal(value: ScenarioSchemaSampleTemplate) extends MapVal

  case class modelValueDictionaryMapVal(value: Map[Long, ModelValueDictionary]) extends MapVal

  case class FlowSchemaMapVal(value: Map[Long, FlowSchema]) extends MapVal

  case class ProcedureMetaMapVal(value: Map[Long, ProcedureMeta]) extends MapVal

  case class DomainMapVal(value: Map[Long, Domain]) extends MapVal

  case class ScenarioInstanceCaseExampleMapVal(value: Map[Long, ScenarioInstanceCaseExample]) extends MapVal

  case class ScenarioApiConfigMapVal(value: Map[Long, ScenarioApiConfig]) extends MapVal


  //instance 层级
  case class ScenarioInstanceMapVal(value: ScenarioInstance) extends MapVal

  case class ScenarioInstanceDocMapVal(value: ScenarioInstanceDoc) extends MapVal

  case class ScenarioInstanceGoodsMapVal(value: ScenarioInstanceGoods) extends MapVal

  case class ScenarioInstancePlanMapVal(value: ScenarioInstancePlan) extends MapVal

  case class CallbackKeyMapVal(value: CallbackKey) extends MapVal

  case class ScenarioInstanceInsurerMapVal(value: ScenarioInstanceInsurer) extends MapVal

  case class RScenarioInstanceFlowSchemaMapVal(value: RScenarioInstanceFlowSchema) extends MapVal

  case class OpenApiDocMapVal(value: OpenApiDoc) extends MapVal

  case class MetaDocMapVal(value: MetaDoc) extends MapVal

  case class ScenarioInstanceCaseExampleI18nMapVal(value: Map[Long, ScenarioInstanceCaseExampleI18n]) extends MapVal

  case class ScenarioInstanceDocI18nMapVal(value: Map[Long, ScenarioInstanceDocI18n]) extends MapVal

  case class OpenApiDocI18nMapVal(value: Map[Long, OpenApiDoc]) extends MapVal

  case class MetaDocI18nMapVal(value: Map[Long, MetaDoc]) extends MapVal

  //API View
  case class ApiViewRecordMapVal(value: ApiViewRecord) extends MapVal

  case class ApiViewGroupMapVal(value: Map[Long, ApiViewGroup]) extends MapVal

  case class ApiViewSchemaMapVal(value: Map[Long, ApiViewSchema]) extends MapVal

  case class ApiViewDocumentMapVal(value: Map[Long, ApiViewDocument]) extends MapVal

  case class RViewDocumentMapVal(value: Map[Long, RViewDocument]) extends MapVal

}
