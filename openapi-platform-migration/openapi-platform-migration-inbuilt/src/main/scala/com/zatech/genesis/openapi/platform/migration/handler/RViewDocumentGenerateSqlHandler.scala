

package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.apiview.entity.RViewDocument
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.record.{RecordDeleteSqlGenerator, RecordInsertSqlGenerator, RecordUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.IterableHasAsJava

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class RViewDocumentGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newMapVal = Option(newVal).map(_.asInstanceOf[RViewDocumentMapVal].value).getOrElse(Map())
    val oldMapVal = Option(oldVal).map(_.asInstanceOf[RViewDocumentMapVal].value).getOrElse(Map())
    val unionKeys = newMapVal.keySet.union(oldMapVal.keySet)
    //有则插入，无则删除，同有则更新
    unionKeys.foreach(key =>
      (CompareHelper.compare(classOf[RViewDocument])(newMapVal.get(key).orNull, oldMapVal.get(key).orNull) match {
        case insert: InsertDslCompareResult => Some(new RecordInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new RecordUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new RecordDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    )
    sqlList
  }

}
