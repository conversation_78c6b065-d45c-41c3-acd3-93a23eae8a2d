package com.zatech.genesis.openapi.platform.migration

import com.zatech.genesis.openapi.platform.migration.model.InsertDslCompareResult
import org.apache.ibatis.mapping.BoundSql

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
abstract class AbstractInsertSqlGenerator(sqlGenerateTools: SqlGenerateTools, compareResult: InsertDslCompareResult)
  extends AbstractSqlGenerator[InsertDslCompareResult](sqlGenerateTools, compareResult) {

  override protected def getBoundSqls(table: Any): Seq[BoundSql] = {
    val insertionMappedStatementId = s"${mapperCls.getName}.insert"
    val insertionMappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(insertionMappedStatementId)
    val iSql = insertionMappedStatement.getBoundSql(table)

    val deletionMappedStatementId = s"${mapperCls.getName}.deleteById"
    val deletionMappedStatement = sqlGenerateTools.sqlSessionFactory.getConfiguration.getMappedStatement(deletionMappedStatementId)
    val dSql = deletionMappedStatement.getBoundSql(table)
    Seq(dSql, iSql)
  }
}
