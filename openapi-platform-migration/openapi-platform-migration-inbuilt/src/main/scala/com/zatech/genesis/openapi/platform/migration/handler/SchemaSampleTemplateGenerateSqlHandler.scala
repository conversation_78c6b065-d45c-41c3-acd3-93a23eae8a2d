package com.zatech.genesis.openapi.platform.migration.handler

import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioSchemaSampleTemplate
import com.zatech.genesis.openapi.platform.migration.model.{DeleteDslCompareResult, InsertDslCompareResult, UpdateDslCompareResult}
import com.zatech.genesis.openapi.platform.migration.schema.{SchemaDeleteSqlGenerator, SchemaInsertSqlGenerator, SchemaUpdateSqlGenerator}
import com.zatech.genesis.openapi.platform.migration.{CompareHelper, DslAnalyzeTools, MapVal}

import java.util
import scala.jdk.CollectionConverters.{IterableHasAsJava, SeqHasAsJava}

/**
 * <AUTHOR>
 * @date 2022/10/19 18:12
 * */
class SchemaSampleTemplateGenerateSqlHandler extends AbstractGenerateSqlHandler {

  override def getGenerateSql(newVal: MapVal, oldVal: MapVal, tools: DslAnalyzeTools): util.List[String] = {

    val sqlList = new util.LinkedList[String]()
    val newInfo = Option(newVal).map(_.asInstanceOf[ScenarioSchemaSampleTemplateMapVal].value).orNull
    val oldInfo = Option(oldVal).map(_.asInstanceOf[ScenarioSchemaSampleTemplateMapVal].value).orNull
    if (newInfo != null && oldInfo != null && newInfo.getId != oldInfo.getId) {
      //删除老的
      (CompareHelper.compare(classOf[ScenarioSchemaSampleTemplate])(null, oldInfo) match {
        case delete: DeleteDslCompareResult => Some(new SchemaDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
      //插入新的
      (CompareHelper.compare(classOf[ScenarioSchemaSampleTemplate])(newInfo, null) match {
        case insert: InsertDslCompareResult => Some(new SchemaInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    } else {
      (CompareHelper.compare(classOf[ScenarioSchemaSampleTemplate])(newInfo, oldInfo) match {
        case insert: InsertDslCompareResult => Some(new SchemaInsertSqlGenerator(tools.sqlGenerateTools, insert))
        case update: UpdateDslCompareResult => Some(new SchemaUpdateSqlGenerator(tools.sqlGenerateTools, update))
        case delete: DeleteDslCompareResult => Some(new SchemaDeleteSqlGenerator(tools.sqlGenerateTools, delete))
        case _ => None
      }) map { generator => sqlList.addAll(generator.generate.asJavaCollection) }
    }

    sqlList
  }

}
