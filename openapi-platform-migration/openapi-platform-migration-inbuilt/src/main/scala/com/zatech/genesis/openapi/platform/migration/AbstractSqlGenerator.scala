package com.zatech.genesis.openapi.platform.migration

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.baomidou.mybatisplus.core.metadata.{TableFieldInfo, TableInfoHelper}
import com.zatech.genesis.openapi.platform.migration.model.DslCompareResult
import com.zatech.genesis.openapi.platform.share.enums.ModelMetaJavaTypeEnum
import com.zatech.genesis.openapi.platform.share.tools.ReflectUtil
import org.apache.ibatis.mapping.{BoundSql, ParameterMapping}

import java.lang.reflect.Field
import scala.jdk.CollectionConverters._

/**
 * <AUTHOR>
 * @Date 2022/8/29
 * */
abstract class AbstractSqlGenerator[T <: DslCompareResult](
                                                            sqlGenerateTools: SqlGenerateTools,
                                                            dslCompareResult: T) {

  protected val mapperCls: Class[_ <: BaseMapper[_]]

  protected val tableInfo = TableInfoHelper.getTableInfo(dslCompareResult.getTableClass)

  protected val tableName = tableInfo.getTableName

  protected val fields: Map[Field, TableFieldInfo] = tableInfo.getFieldList.asScala.map(x => x.getField -> x).toMap

  protected def getBoundSqls(table: Any): Seq[BoundSql]

  protected def getTargetPropertyNameFromParameterMapping(parameterMapping: ParameterMapping): String = parameterMapping.getProperty

  protected def rebuildSqlParams(params: Iterable[Any]): Array[Any] = params.toArray

  def generate: Seq[String] = {
    val table = dslCompareResult.getTableClass.newInstance()
    dslCompareResult.getResult.forEach(item => {
      ReflectUtil.setFieldValue(table, item.getField.getName, item.getNewValue)
    })
    val boundSqls = getBoundSqls(table)
    val res = boundSqls.map(boundSql => {
      val sqlTemplate =
        boundSql.getSql.replace("?", "%s").replace("\n", " ")

      val itemMap = dslCompareResult.getItemMap
      val params = boundSql.getParameterMappings.asScala.flatMap(pm => {
        Option(itemMap.get(getTargetPropertyNameFromParameterMapping(pm))).map(item => {
          val value = Option(item.getNewValue).getOrElse(item.getOldValue)
          ModelMetaJavaTypeEnum.fromClass(item.getField.getType).getSqlParser()(value)
        })
      }).toArray

      val targetSql = sqlTemplate.format(rebuildSqlParams(params): _*)
      Seq(targetSql)
    }).reduce((seq1, seq2) => seq1.concat(seq2))
    res
  }
}
