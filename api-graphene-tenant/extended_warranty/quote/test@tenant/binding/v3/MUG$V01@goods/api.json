{"openapi": "3.0.1", "paths": {"/api/v3/extended_warranty/quotes/binding": {"post": {"requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["issuanceNo"], "properties": {"issuanceNo": {"type": "string", "description": "Issuance no"}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"title": "Policy output", "type": "object", "properties": {"policies": {"title": "Policies", "description": "List of policy", "type": "array", "items": {"title": "Quotation", "type": "object", "properties": {"thirdPartyTransactionNo": {"type": "string", "example": "t_32943923243", "description": "Third-party transaction number, we will use this field for idempotent processing of the API.\r\n`(Each request requires a new third party transaction number to be passed in through API.)`"}, "orderNo": {"title": "Order no", "description": "OrderNo input when binding.", "type": "string"}, "policyNo": {"title": "Policy no", "type": "string", "description": "Policy no"}, "issuanceNo": {"description": "Issuance no", "type": "string"}, "issuanceStatus": {"description": "Issuance status, when issuaceStatus is DECLINED, You need to follow the insurance rejection process to handle subsequent processes.", "type": "string", "enum": ["DATA_ENTRY_IN_PROGRESS", "PENDING_PROPOSAL_CHECK", "WAITING_FOR_ISSUANCE", "DECLINED", "WITHDRAWN", "EFFECTIVE"], "x-id": 208664495407110, "x-required": false, "x-java-type": "ENUM"}}}}}}}}, "headers": {"Api-Trace-Id": {"schema": {"type": "string"}, "description": "A unique identifier used to trace the operations,  is usually used to identify a specific error instance, allowing developers to investigate and resolve the issue."}}}}, "operationId": ""}, "parameters": [], "servers": []}}, "servers": []}