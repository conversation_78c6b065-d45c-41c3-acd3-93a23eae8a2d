package com.zatech.genesis.openapi.platform.security.api.application.payload;


import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/10/10 12:01
 **/
public interface AppPayload {

    /**
     * 是否保留在请求头中提交上来的channelCode，因为一些应用类型可以从token中解析出channelCode，
     * 但insurer类型的应用没有channel code，如果设置为true可以从请求头中获取到channelCode
     * @return
     */
    boolean keepChannelCodeInRequestHeader();

    Optional<String> channelCodeOpt();
}
