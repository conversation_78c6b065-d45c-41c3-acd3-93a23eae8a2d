package com.zatech.genesis.openapi.platform.spec.inbuilt.mcp.handlers

import io.modelcontextprotocol.common.McpTransportContext
import io.modelcontextprotocol.server.McpStatelessRequestHandler

/**
 * <AUTHOR>
 * @create 2025/9/24 10:56
 * */
trait EnhancedMcpStatelessRequestHandler[R] extends McpStatelessRequestHandler[R]{

  /**
   * 对应的mcp方法
   * @return
   */
  def mcpMethod: String

  /**
   * 是否可以处理该请求
   * @param transportContext
   * @param params
   * @return
   */
  def canHandle(transportContext: McpTransportContext, params: AnyRef): <PERSON><PERSON>an
}
