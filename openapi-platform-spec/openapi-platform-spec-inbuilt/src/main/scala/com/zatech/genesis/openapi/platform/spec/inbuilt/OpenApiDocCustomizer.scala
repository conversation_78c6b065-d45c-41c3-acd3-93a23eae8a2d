package com.zatech.genesis.openapi.platform.spec.inbuilt

import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.IApiNode
import com.zatech.genesis.openapi.platform.spec.inbuilt.handler.method.ApiMethodMetaInfo
import io.swagger.v3.oas.models.OpenAPI
import org.springdoc.core.customizers.OpenApiCustomizer


/**
 * <AUTHOR>
 * @create 2024/7/17 14:48
 * 对openapi doc进行客制化变更
 * */
class OpenApiDocCustomizer(apiMethodMetaInfo: ApiMethodMetaInfo, apiNode: IApiNode) extends OpenApiCustomizer {

  override def customise(openApi: OpenAPI): Unit = {
    addByCodeFlag(openApi)
  }

  private def addByCodeFlag(openAPI: OpenAPI): Unit = {
    openAPI.addExtension("x-generate-type", "swagger")
  }
}
