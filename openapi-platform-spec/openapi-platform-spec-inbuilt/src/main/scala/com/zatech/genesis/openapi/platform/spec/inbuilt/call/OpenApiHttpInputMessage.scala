package com.zatech.genesis.openapi.platform.spec.inbuilt.call

import com.zatech.genesis.openapi.platform.share.OpenXPlusJsonParser
import org.springframework.http.{HttpHeaders, HttpInputMessage}

import java.io.{ByteArrayInputStream, InputStream}
import java.nio.charset.StandardCharsets

/**
 * <AUTHOR>
 * @create 2024/8/1 19:51
 * */
class OpenApiHttpInputMessage(val body: java.util.Map[String, Object], headers: HttpHeaders) extends HttpInputMessage {

  private lazy val bodyStream =
    new ByteArrayInputStream(OpenXPlusJsonParser.toJsonString(body).getBytes(StandardCharsets.UTF_8))

  override def getBody: InputStream = bodyStream

  override val getHeaders: HttpHeaders = headers
}
