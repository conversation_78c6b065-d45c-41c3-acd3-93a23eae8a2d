package com.zatech.genesis.openapi.platform.spec.inbuilt.mcp.handlers

import io.modelcontextprotocol.server.McpStatelessServerHandler

/**
 * <AUTHOR>
 * @create 2025/9/24 14:38
 * */
abstract class AbstractMcpStatelessRequestHandler[R](
                                                      delegateServerHandler: McpStatelessServerHandler
                                                    ) extends EnhancedMcpStatelessRequestHandler[R]{

}
