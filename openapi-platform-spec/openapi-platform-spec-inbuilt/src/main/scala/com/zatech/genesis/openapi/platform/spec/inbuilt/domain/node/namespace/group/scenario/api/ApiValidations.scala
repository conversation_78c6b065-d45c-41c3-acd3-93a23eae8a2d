package com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api

/**
 * <AUTHOR>
 * @create 2025/7/10 19:00
 * */
case class ApiValidations(validations: Seq[ApiValidation]) {

  private val validationsMap = validations.map(validation => validation.name -> validation).toMap

  def isEmpty: Boolean = validations.isEmpty

  def getOpt(name: String): Option[ApiValidation] = validationsMap.get(name)
}
