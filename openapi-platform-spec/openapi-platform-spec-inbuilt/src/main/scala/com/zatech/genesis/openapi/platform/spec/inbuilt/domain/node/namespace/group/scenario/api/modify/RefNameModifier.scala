package com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.modify

import com.google.common.collect.Maps
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.OpenApiWrapper
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.OpenApiWrapper.$RefRoot
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.modify.RefNameModifier.{RefKeyHolder, init}
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters.OptionRich
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.media.Schema
import org.springframework.stereotype.Component

import java.util.{Map => JMap}
import scala.annotation.tailrec
import scala.collection.mutable
/**
 * <AUTHOR>
 * @create 2025/4/21 10:12
 * */
@Component
class RefNameModifier extends OpenApiModifier {

  /**
   * 必须最先执行
   */
  override val order: Int = Int.MinValue

  private def buildOptimizedRefMap(originRef: JMap[String, Schema[_]]): Map[String, RefKeyHolder] = {
    val optimizedRefMap: mutable.Map[String, RefKeyHolder] = mutable.Map()
    val refKeySet = originRef.keySet()
    refKeySet.forEach(refKey => init(refKey, optimizedRefMap))
    optimizedRefMap.toMap
  }

  private def recursiveModifyRef(schema: Schema[_], refKeyMap: Map[String, RefKeyHolder]): Unit = {
    if(schema.get$ref() != null) {
      val clearRefKey = schema.get$ref().substring($RefRoot.length)
      refKeyMap.get(clearRefKey) match {
        case Some(refKeyHolder) => schema.set$ref(s"${$RefRoot}${refKeyHolder.targetRefKey}")
        case _ =>
      }
      return
    }
    if(schema.getProperties != null) {
      schema.getProperties.values().forEach(recursiveModifyRef(_, refKeyMap))
    } else if(schema.getItems != null) {
      recursiveModifyRef(schema.getItems, refKeyMap)
    } else if(schema.getAllOf != null) {
      schema.getAllOf.forEach(recursiveModifyRef(_, refKeyMap))
    } else if(schema.getOneOf != null) {
      schema.getOneOf.forEach(recursiveModifyRef(_, refKeyMap))
    } else if(schema.getAnyOf != null) {
      schema.getAnyOf.forEach(recursiveModifyRef(_, refKeyMap))
    }
  }

  private def modifyComponentRefNames(openApi: OpenAPI, refKeyMap: Map[String, RefKeyHolder]): Unit = {
    Option(openApi.getComponents).map_?(_.getSchemas) match {
      case Some(schemas) =>
        val newSchemas = Maps.newHashMap[String, Schema[_]]()
        schemas.forEach((k,v) => {
          if(refKeyMap.contains(k)) newSchemas.put(refKeyMap(k).targetRefKey, v)
          else newSchemas.put(k, v)
        })
        openApi.getComponents.setSchemas(newSchemas)
      case _ =>
    }
  }

  override def modify(openApiWrapper: OpenApiWrapper, context: ModifyContext): OpenApiWrapper = {
    if(openApiWrapper.isModified) return openApiWrapper

    val optimizedRefMap = buildOptimizedRefMap(openApiWrapper.$refs)
    openApiWrapper.requestBodySchemaOpt.foreach(recursiveModifyRef(_, optimizedRefMap))
    openApiWrapper.response200BodySchemaOpt.foreach(recursiveModifyRef(_, optimizedRefMap))
    openApiWrapper.$refs.values().forEach(recursiveModifyRef(_, optimizedRefMap))
    modifyComponentRefNames(openApiWrapper.openApi, optimizedRefMap)
    openApiWrapper.modified
  }
}

object RefNameModifier {

  def init(refKey: String, refMap: mutable.Map[String, RefKeyHolder]): Unit = {
    val packageList: Array[String] = refKey.split('.')
    if(packageList.isEmpty && ! refMap.contains(refKey)) refMap(refKey) = new FinalRefKeyHolder(refKey)
    else refMap(refKey) = new IntermediateRefKeyHolder(packageList, refMap)
  }

  trait RefKeyHolder {
    val targetRefKey: String
    override def toString: String = targetRefKey
  }

  private class FinalRefKeyHolder(refKey: String) extends RefKeyHolder {
    override val targetRefKey: String = refKey.replace(" ", "")
  }

  private class IntermediateRefKeyHolder(packageList: Array[String],
                                         refMap: mutable.Map[String, RefKeyHolder]) extends RefKeyHolder {
    /**
     * 如果已经出现了相同的refKey，则需要递归构建，每次用类名的尾巴做拼接
     * @param index
     * @param refKey
     * @return
     */
    @tailrec
    private def recursiveGetRefKey(index: Int, refKey: String): String = {
      if(! refMap.contains(refKey)) {
        refMap(refKey) = new FinalRefKeyHolder(refKey)
        refKey
      } else {
        recursiveGetRefKey(index - 1, s"${packageList(index)}.$refKey")
      }
    }

    private val refKey = recursiveGetRefKey(packageList.length-2, packageList.last)

    override val targetRefKey: String = refKey.replace(" ", "")
  }
}
