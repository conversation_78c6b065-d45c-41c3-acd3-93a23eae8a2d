package com.zatech.genesis.openapi.platform.knowledge.procedure.api.identity

import com.zatech.genesis.openapi.platform.share.enums.{ProcedureDefinitionCategory, ProcedureMetaTypeEnum}

/**
 * <AUTHOR>
 * @create 2024/11/11 14:21
 * */
trait ProcedureIdentity {

  /**
   * procedure 分类
   */
  val category: ProcedureDefinitionCategory
  /**
   * procedure 名称
   */
  val name: String
  /**
   * procedure 类型
   */
  val `type`: ProcedureMetaTypeEnum

  /**
   * 唯一表示该procedure的字符串key
   * @return
   */
  @deprecated
  def identifier: String
}
