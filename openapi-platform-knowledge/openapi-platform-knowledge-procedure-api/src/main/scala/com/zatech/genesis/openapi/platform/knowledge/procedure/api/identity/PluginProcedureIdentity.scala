package com.zatech.genesis.openapi.platform.knowledge.procedure.api.identity

import com.zatech.genesis.openapi.platform.share.enums.{ProcedureDefinitionCategory, ProcedureMetaTypeEnum}

/**
 * <AUTHOR>
 * @create 2024/11/11 14:17
 * */
case class PluginProcedureIdentity(name: String,
                                   `type`: ProcedureMetaTypeEnum,
                                   namespaceOpt: Option[String],
                                   domain: String,
                                   tagOpt: Option[String],
                                   tenantOpt: Option[String]) extends ProcedureIdentity {

  override val category: ProcedureDefinitionCategory = ProcedureDefinitionCategory.PLUGIN

  /**
   * tenantCode == null也是匹配的
   * @param tenantCode
   * @return
   */
  def matchTenant(tenantCode: String): Boolean = tenantOpt.forall(_ == tenantCode)

  def matchDomain(domainName: String): Boolean = this.domain == domainName

  override def identifier: String = {
    val namespaceKey = namespaceOpt.getOrElse("*")
    val tagKey = tagOpt.getOrElse("*")
    val tenantKey = tenantOpt.getOrElse("*")
    s"${namespaceKey}:${domain}:${tagKey}:${name}:${`type`}:${tenantKey}"
  }
}
