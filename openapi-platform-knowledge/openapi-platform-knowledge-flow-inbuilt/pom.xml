<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>openapi-platform-knowledge</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>openapi-platform-knowledge-flow-inbuilt</artifactId>
    <packaging>jar</packaging>
    <name>openapi-platform-knowledge-flow-inbuilt</name>
    <description>The OpenApi Platform Knowledge Flow Inbuilt Module</description>

    <dependencies>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-knowledge-flow-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-knowledge-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-knowledge-procedure</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-hooks-inbuilt</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-spec-inbuilt</artifactId>
            <version>${project.version}</version>
        </dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-callback-plugin-api</artifactId>
			<version>${project.version}</version>
		</dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>openapi-platform-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <!--<version>1.14</version>-->
        </dependency>
    </dependencies>


</project>
