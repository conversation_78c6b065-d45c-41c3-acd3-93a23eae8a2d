{"directMapping": true, "activities": [{"name": "Start", "type": "Start", "dependEvents": ["Start"]}, {"name": "strategyDemo", "type": "Procedure", "default": "StrategyDefault", "strategy": "exclusive", "procedures": [{"tag": "test", "name": "StrategyIdEqStr1", "type": "RestfulService", "domain": "common"}, {"tag": "test", "name": "StrategyIdEqStr2", "type": "RestfulService", "domain": "common"}, {"tag": "test", "name": "StrategyIdEqStr3", "type": "RestfulService", "domain": "common"}, {"tag": "test", "name": "StrategyDefault", "type": "RestfulService", "domain": "common"}], "dependActivities": ["Start"]}, {"name": "demoPrinterProcedure", "type": "Procedure", "using": "strategyDemo", "result": "StrategyResult", "procedures": [{"tag": "test", "name": "StrategyResult", "type": "RestfulService", "domain": "common"}], "dependActivities": ["strategyDemo"]}, {"name": "End", "type": "End", "using": [{"direction": "OUT", "procedure": "StrategyResult"}], "dependActivities": ["demoPrinterProcedure"]}]}