package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt;

import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.instance.ScenarioInstanceAggregation;
import com.zatech.genesis.openapi.platform.infra.IdUtil;
import com.zatech.genesis.openapi.platform.infra.application.entity.FlowIdGenerated;
import com.zatech.genesis.openapi.platform.infra.application.service.IFlowIdGeneratedService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterMetadataService;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowInstanceIdGenerator;
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception.FlowIdGenerateSourceElementNotFoundException;
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception.FlowIdGenerationException;
import com.zatech.genesis.openapi.platform.knowledge.model.element.IModelValueElement;
import com.zatech.genesis.openapi.platform.knowledge.model.element.PrimaryModelValueElement;
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode;
import com.zatech.genesis.openapi.platform.share.TraceSupport;
import com.zatech.genesis.openapi.platform.share.exception.CallApiErrorCode;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import scala.jdk.javaapi.OptionConverters;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2021/11/4 17:44
 **/
@Component
@Slf4j
@Deprecated(since = "2.80")
public class DefaultFlowInstanceIdGenerator implements IFlowInstanceIdGenerator {

    private static final String SIGNATURE_KEY = "OpenApi";

    @Autowired
    IOuterMetadataService iBizNoService;

    //为了能够让@Transactial生效
    @Autowired
    DefaultFlowInstanceIdGenerator self;

    @Autowired
    private IFlowIdGeneratedService flowIdGeneratedService;

    @Override
    public Long generateFlowInstanceId(ScenarioInstanceAggregation instanceAggregation, IModelValueElement rootValueElement) {

        Long flowInstanceId = IdUtil.newFlowInstanceId();

        Pair<String, String> sourceDataSignature = getAlwaysNewSourceDataAndSignature(flowInstanceId);
        //TODO 后续做幂等性的优化
        //Pair<String, String> sourceDataSignature = getSourceDataAndSignature(instanceAggregation, rootValueElement);

        Optional<FlowIdGenerated> oldFlow = flowIdGeneratedService.selectLatestBySourceDataSignature(instanceAggregation.getId(),
            sourceDataSignature.getValue());

        //为了让事务尽可能短，先求出来
        String bizApplyNo = oldFlow.map(FlowIdGenerated::getBizApplyNo).orElseGet(() -> getBizNo());


        FlowIdGenerated flowIdGenerated = new FlowIdGenerated();
        flowIdGenerated.setFlowInstanceId(flowInstanceId);
        OptionConverters.toJava(instanceAggregation.boundFlowSchemaEntityOpt()).ifPresent(flowSchemaEntity -> {
            flowIdGenerated.setFlowSchemaId(flowSchemaEntity.getId());
        });
        flowIdGenerated.setScenarioInstanceId(instanceAggregation.getId());
        flowIdGenerated.setBizApplyNo(bizApplyNo);
        flowIdGenerated.setSourceData(sourceDataSignature.getKey());
        flowIdGenerated.setSourceDataSignature(sourceDataSignature.getValue());

        self.newFlowInstanceId(oldFlow, flowIdGenerated);
        return flowInstanceId;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void newFlowInstanceId(Optional<FlowIdGenerated> oldFlow, FlowIdGenerated newFlow) {

        oldFlow.ifPresent(x -> flowIdGeneratedService.removeById(x.getId()));
        flowIdGeneratedService.save(newFlow);
    }


    private Pair<String, String> getAlwaysNewSourceDataAndSignature(Long newFlowInstanceId) {

        Map<String, Object> sourceDataMap = new TreeMap<>(Comparator.naturalOrder());
        sourceDataMap.put("flowId", newFlowInstanceId);
        sourceDataMap.put("appId", TraceSupport.getAppIdOrThrow());

        String sourceDataStr = JsonParser.toJsonString(sourceDataMap);
        String sourceDataSignature = getSignature(sourceDataStr);
        return Pair.of(sourceDataStr, sourceDataSignature);
    }

    private Pair<String, String> getSourceDataAndSignature(ScenarioInstanceAggregation instanceAggregation, IModelValueElement rootValueElement) {

        Map<String, Object> sourceDataMap = new TreeMap<>(Comparator.naturalOrder());

        sourceDataMap.put("appId", TraceSupport.getAppIdOrThrow());

        Arrays.stream(instanceAggregation.scenarioSchemaEntity().flowIdGenerateInputModelMetaEntities()).forEach(inputModelMeta -> {
            if (log.isDebugEnabled()) {
                log.debug("begin to find the flow id generate source element: {}", inputModelMeta.inputModelMetaId());
            }
            Optional<IModelValueElement> elementOpt = rootValueElement.findById(inputModelMeta.inputModelMetaId());
            IModelValueElement element = elementOpt.orElseThrow(() ->
                new FlowIdGenerateSourceElementNotFoundException(CallApiErrorCode.flow_id_generate_source_element_not_found));

            if (!(element instanceof PrimaryModelValueElement)) {
                throw new FlowIdGenerationException(KnowledgeErrorCode.inputModelValue_must_be_primaryValue);
            }
            Object value = ((PrimaryModelValueElement) element).getValue();
            sourceDataMap.put(element.getName(), value);
        });
        String sourceDataStr = JsonParser.toJsonString(sourceDataMap);
        String sourceDataSignature = getSignature(sourceDataStr);
        return Pair.of(sourceDataStr, sourceDataSignature);
    }

    @Override
    public Optional<Long> getLastFlowInstanceId(ScenarioInstanceAggregation instanceAggregation, IModelValueElement rootValueElement) {

        Pair<String, String> sourceDataAndSignature = getSourceDataAndSignature(instanceAggregation, rootValueElement);
        return flowIdGeneratedService.selectLatestBySourceDataSignature(instanceAggregation.getId(), sourceDataAndSignature.getValue())
            .map(FlowIdGenerated::getFlowInstanceId);
    }


    private String getBizNo() {

        JsonMap param = new JsonMap();
        param.put("type", "BIZ_APPLY_NO");
        return iBizNoService.getBizno(param);
    }

    private String getSignature(String sourceDataString) {

        HmacUtils hmacUtils = new HmacUtils(HmacAlgorithms.HMAC_MD5, SIGNATURE_KEY);
        return hmacUtils.hmacHex(sourceDataString);
    }

}
