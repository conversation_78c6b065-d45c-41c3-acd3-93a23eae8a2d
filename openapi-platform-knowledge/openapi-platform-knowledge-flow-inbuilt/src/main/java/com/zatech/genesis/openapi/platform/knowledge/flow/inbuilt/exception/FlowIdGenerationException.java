package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception;

import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

/**
 * <AUTHOR>
 * @create 2021/12/13 2:16 PM
 **/
public class FlowIdGenerationException extends OpenApiException {

    public FlowIdGenerationException(IErrorCode errorCode) {
        super(errorCode);
    }

}
