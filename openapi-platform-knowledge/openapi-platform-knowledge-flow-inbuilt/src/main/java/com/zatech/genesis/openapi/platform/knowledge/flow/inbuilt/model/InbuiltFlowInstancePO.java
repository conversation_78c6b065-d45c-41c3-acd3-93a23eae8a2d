package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowInstancePO;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.enums.FlowMessageType;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowMessage;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;

import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Author: felton
 * @Date: 2021/10/24 上午10:15
 * flow实例的状态持久化信息
 */
@NoArgsConstructor
@Getter
public final class InbuiltFlowInstancePO implements IFlowInstancePO {

    @Getter
    private Long id;

    private final List<FlowMessage> activityMessages = Lists.newArrayList();

    private final List<FlowMessage> eventMessages = Lists.newArrayList();

    private final Map<String, FlowActivityStatePO> toDoActivities = Maps.newHashMap();

    private final Map<String, FlowActivityStatePO> finishedActivities = Maps.newHashMap();

    public FlowMessage getEventMessage(int index) {
        return eventMessages.get(index);
    }

    public boolean containsFinishedActivity(String activityName) {
        return finishedActivities.containsKey(activityName);
    }

    public int eventMaxIndex() {
        return eventMessages.size() - 1;
    }

    public synchronized void addMessage(FlowMessage message) {

        if (message.getType() == FlowMessageType.ACTIVITY) {
            activityMessages.add(message);
        } else {
            eventMessages.add(message);
        }
    }

    public synchronized void addFinishedActivityState(FlowActivityStatePO statePO) {

        statePO.setEndTime(new Date());
        finishedActivities.putIfAbsent(statePO.getActivityName(), statePO);
    }

    public synchronized void addTodoActivityState(FlowActivityStatePO statePO) {

        toDoActivities.putIfAbsent(statePO.getActivityName(), statePO);
    }

    public synchronized void addTodoActivityStates(List<FlowActivityStatePO> statePOList) {

        statePOList.forEach(state ->
            toDoActivities.putIfAbsent(state.getActivityName(), state));
    }

    public void addTodoActivityState(String activityName, Supplier<FlowActivityStatePO> supplier) {

        if (!toDoActivities.containsKey(activityName)) {
            synchronized (this) {
                toDoActivities.putIfAbsent(activityName, supplier.get());
            }
        }
    }

    public Set<String> getTodoActivityKeys() {

        return toDoActivities.keySet();
    }

    public synchronized void removeTodoActivityState(String activityName) {

        toDoActivities.remove(activityName);
    }

    public Optional<FlowActivityStatePO> getToDoActivityState(String flowActivityName) {

        return Optional.ofNullable(toDoActivities.get(flowActivityName));
    }

    public Optional<FlowActivityStatePO> getFinishedActivityState(String flowActivityName) {

        return Optional.ofNullable(finishedActivities.get(flowActivityName));
    }

    public IFlowInstancePO setId(Long id) {
        this.id = id;
        return this;
    }

}
