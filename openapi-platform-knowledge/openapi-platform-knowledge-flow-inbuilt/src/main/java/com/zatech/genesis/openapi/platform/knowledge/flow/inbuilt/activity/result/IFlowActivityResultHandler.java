package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.result;

import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowActivity;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowActivityResult;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowMessage;

public interface IFlowActivityResultHandler {

    boolean handle(IFlowActivity activity, FlowActivityResult result, FlowMessage msg);

    Class<? extends FlowActivityResult> forResult();

}
