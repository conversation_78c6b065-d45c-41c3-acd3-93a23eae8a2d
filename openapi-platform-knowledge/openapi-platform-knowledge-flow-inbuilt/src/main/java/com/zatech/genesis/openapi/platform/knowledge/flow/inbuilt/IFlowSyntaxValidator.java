package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt;

import com.zatech.genesis.openapi.platform.api.resource.meta.flow.FlowSyntaxCheckResult;
import com.zatech.genesis.openapi.platform.domain.meta.global.NamespaceEntity;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;

public interface IFlowSyntaxValidator {

    FlowSyntaxCheckResult check(NamespaceEntity nameSpace, JsonMap flowContent);
}
