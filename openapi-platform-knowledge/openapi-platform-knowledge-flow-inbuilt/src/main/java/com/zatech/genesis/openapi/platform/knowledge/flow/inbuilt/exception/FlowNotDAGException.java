package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception;

import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

public class FlowNotDAGException extends OpenApiException {

    public FlowNotDAGException(IErrorCode errorCode, String msg) {
        super(errorCode, null, msg);
    }

}
