package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception;

import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowActivityResult;
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;

/**
 * @Author: felton
 * @Date: 2021/10/27 下午9:06
 */
public class FlowActivityResultHandlerNotFoundException extends OpenApiException {

    public FlowActivityResultHandlerNotFoundException(Class<? extends FlowActivityResult> resultType) {
        super(KnowledgeErrorCode.flowActivityResultHandler_type_not_found, null, resultType.getName());
    }

}

