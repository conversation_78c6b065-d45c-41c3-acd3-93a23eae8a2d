package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model;

import com.google.common.collect.Maps;

import java.util.Date;
import java.util.Map;

import lombok.NoArgsConstructor;

/**
 * @Author: felton
 * @Date: 2021/10/24 上午11:14
 * flow活动的持久化状态信息
 */
@NoArgsConstructor
public class FlowActivityStatePO {

    private String activityName;

    private Date startTime;

    private Date endTime;

    private Object result;

    private int checkOffset = -1;

    private Map<String, Object> states = Maps.newHashMap();

    public FlowActivityStatePO(String activityName) {

        this.activityName = activityName;
    }

    public String getActivityName() {

        return activityName;
    }

    public void setActivityName(String activityName) {

        this.activityName = activityName;
    }

    public Date getStartTime() {

        return startTime;
    }

    public void setStartTime(Date startTime) {

        this.startTime = startTime;
    }

    public Date getEndTime() {

        return endTime;
    }

    public void setEndTime(Date endTime) {

        this.endTime = endTime;
    }

    public int getCheckOffset() {

        return checkOffset;
    }

    public void setCheckOffset(int checkOffset) {

        this.checkOffset = checkOffset;
    }

    public Map<String, Object> getStates() {

        return states;
    }

    public void setStates(Map<String, Object> states) {

        this.states = states;
    }

}
