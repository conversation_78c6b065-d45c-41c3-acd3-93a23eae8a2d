package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception;

import com.zatech.genesis.openapi.platform.share.exception.CallApiErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.CallApiException;

/**
 * <AUTHOR>
 * @Date 2022/4/2
 **/
public class FlowIdGenerateSourceElementNotFoundException extends CallApiException {

    public FlowIdGenerateSourceElementNotFoundException(CallApiErrorCode errorCodeEnum, Object... args) {

        super(errorCodeEnum, args);
    }

}
