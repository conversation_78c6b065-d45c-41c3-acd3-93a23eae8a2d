package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception;

import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

/**
 * <AUTHOR>
 * @create 2024/9/24 14:22
 **/
public enum FlowErrorCodes implements IErrorCode {
    idempotent_data_not_found;

    @Override
    public String getModuleName() {
        return "flow";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
