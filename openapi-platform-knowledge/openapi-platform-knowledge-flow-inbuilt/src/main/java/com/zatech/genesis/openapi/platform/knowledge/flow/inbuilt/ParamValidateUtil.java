package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt;

import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioApiConfig;
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode;
import com.zatech.genesis.openapi.platform.share.RequestSupport;
import com.zatech.genesis.openapi.platform.share.enums.ParamConfigType;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;
import com.zatech.genesis.openapi.platform.share.tools.CollUtil;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.poi.util.StringUtil;


public class ParamValidateUtil {


    public static void validateAndAutoFill(RequestSupport.RequestInfo requestInfo, List<ScenarioApiConfig> scenarioApiConfigList) {
        List<ScenarioApiConfig> requestHeaderScenarioApiConfigList = scenarioApiConfigList.stream().filter(x -> ParamConfigType.REQUEST_HEADER.equals(x.getConfigType())).toList();

        List<ScenarioApiConfig> requestParamScenarioApiConfigList = scenarioApiConfigList.stream().filter(x -> ParamConfigType.REQUEST_PARAM.equals(x.getConfigType())).toList();

        validateAndAutoFill(requestInfo.getHeaders(), requestHeaderScenarioApiConfigList);

        validateAndAutoFill(requestInfo.getParameters(), requestParamScenarioApiConfigList);

    }


    private static void validateAndAutoFill(JsonMap jsonMap, List<ScenarioApiConfig> scenarioApiConfigList) {
        if (CollUtil.isNotEmpty(scenarioApiConfigList)) {
            //1. 如果是必填但是未传字段则直接抛出异常
            //异常数据
            List<ScenarioApiConfig> exceptionList = scenarioApiConfigList.stream().filter(x -> x.isRequired()).filter(x -> !jsonMap.containsKey(x.getName().toLowerCase())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(exceptionList)) {
                throw OpenApiException.by(KnowledgeErrorCode.require_param_is_missing).params(StringUtil.join(",", exceptionList.stream().map(ScenarioApiConfig::getName).collect(Collectors.joining()))).build();
            }
            //2.判断非必传字段如果为传并且设置了默认值则进行填充
            scenarioApiConfigList.stream()
                .filter(x -> !x.isRequired() && Objects.nonNull(x.getDefaultValue()))
                .filter(x -> !jsonMap.containsKey(x.getName()))
                .forEach(x -> jsonMap.put(x.getName(), x.getDefaultValue()));
        }
    }
}
