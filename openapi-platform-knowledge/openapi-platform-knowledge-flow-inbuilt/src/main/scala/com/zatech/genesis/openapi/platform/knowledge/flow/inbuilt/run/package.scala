package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import com.zatech.genesis.openapi.platform.share.RequestSupport.RequestInfo
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
/**
 * <AUTHOR>
 * @create 2024/9/23 17:49
 * */
package object run {
  final val IdempotentKey = "x-idempotent-key"
  final val ApplicationName: String = "zatech-openapi-platform"

  implicit class RichRequestInfo(requestInfo: RequestInfo) {
    def idempotentKeyOpt: Option[String] = {
      requestInfo.getHeaders.getOpt(IdempotentKey).map(_.toString)
    }
    def hasIdempotentKey: Boolean = requestInfo.getHeaders.containsKey(IdempotentKey)
  }

  implicit class RichFlowExecutionContext(context: FlowExecutionContext) {
    //是否需要进行幂等处理
    def shouldIdempotent: Boolean = {
      (context.getInstanceAggregation.idempotentSupported && context.getRequestInfo.hasIdempotentKey)
    }
  }
}
