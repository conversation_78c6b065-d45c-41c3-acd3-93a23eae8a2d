package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.storage

import brave.Tracer
import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.common.storage.{RequestContext, ResponseContext}
import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.{FlowData, IFlowDataSaver}
import com.zatech.genesis.openapi.platform.share.TraceSupport
import com.zatech.genesis.openapi.platform.share.model.EncryptJsonMap
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters.OptionRich
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component

import java.util.{Date, Objects}

/**
 * <AUTHOR>
 * @Date 2022/2/8
 * */
@Component
class DefaultFlowDataSaver @Autowired()(
                                         applicationEventPublisher: ApplicationEventPublisher,
                                         tracer: Tracer
                                       ) extends IFlowDataSaver {

  @Autowired val openApiProps: OpenApiProps = null

  /**
   * 保存成功数据
   *
   * @param data
   */
  override def saveSuccessData(context: FlowExecutionContext, data: FlowData): Unit = {
    //判断是否需要记录调用成功的数据
    if (openApiProps.getFlow.getRecord) {
      saveData(success = true, context, data)
    }
  }

  /**
   * 保存异常数据
   *
   * @param data
   */
  override def saveErrorData(context: FlowExecutionContext, data: FlowData): Unit = {
    saveData(success = false, context, data)
  }

  private def saveData(success: Boolean, context: FlowExecutionContext, data: FlowData): Unit = {
    val flowDataRecord = new FlowDataRecord
    flowDataRecord.setFlowInstanceId(data.getFlowInstanceId)
    flowDataRecord.setGmtStarted(data.getStartTime)
    flowDataRecord.setSuccessFlag(success)
    flowDataRecord.setFinishFlag(true)
    flowDataRecord.setPath(data.getPath)
    flowDataRecord.setChannelCode(TraceSupport.getAppIdOrNull)
    Option(tracer.currentSpan)
      .map_?(_.context())
      .map_?(_.traceIdString()).foreach(flowDataRecord.setTraceId)
    flowDataRecord.setRequestContext(RequestContext.from(context.getRequestInfo).toJson)
    flowDataRecord.setResponseContext(ResponseContext.from(context.getResponseInfo).toJson)
    if (Objects.nonNull(data.getInput)) flowDataRecord.setInput(EncryptJsonMap.of(data.getInput))
    if (Objects.nonNull(data.getOutput)) flowDataRecord.setOutput(EncryptJsonMap.of(data.getOutput))
    flowDataRecord.setGmtEnded(new Date)
    applicationEventPublisher.publishEvent(FlowDataRecordEvent(context, flowDataRecord))
  }
}
