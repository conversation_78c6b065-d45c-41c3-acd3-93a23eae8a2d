package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.storage

import com.github.benmanes.caffeine.cache.{<PERSON><PERSON>, Caffeine}
import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.infra.application.service.IFlowDataRecordService
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowDataLoader
import com.zatech.genesis.openapi.platform.share.json.JsonMap
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.lang
import java.util.Optional
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @Date 2022/2/8
 * */
@Component
private[inbuilt] class DefaultFlowDataLoader @Autowired()(
                                                           flowDataRecordService: IFlowDataRecordService
                                                         ) extends IFlowDataLoader {

  private val flowDataCache: Cache[lang.Long, FlowDataRecord] =
    Caffeine.newBuilder
      .expireAfterWrite(30, TimeUnit.DAYS)
      .maximumSize(200000).build

  /**
   * 查询结果
   *
   * @param flowInstanceId
   * @return
   */
  override def loadResult(flowInstanceId: lang.Long): Optional[JsonMap] = {
    loadRecord(flowInstanceId).flatMap(record => {
      if (!record.getFinishFlag) Optional.empty
      else {
        val result = new JsonMap
        if (record.getOutput != null) result.putAll(record.getOutput)
        Optional.of(result)
      }
    })
  }

  /**
   * 查询整条记录
   *
   * @param flowInstanceId
   * @return
   */
  override def loadRecord(flowInstanceId: lang.Long): Optional[FlowDataRecord] = {
    val recordOrNull = flowDataCache.get(flowInstanceId, _ => {
      flowDataRecordService.selectByFlowInstanceId(flowInstanceId).orElse(null)
    })
    Optional.ofNullable(recordOrNull)
  }
}
