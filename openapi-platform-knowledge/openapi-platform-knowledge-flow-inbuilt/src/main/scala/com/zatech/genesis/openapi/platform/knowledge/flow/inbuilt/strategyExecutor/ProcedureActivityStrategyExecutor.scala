package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.strategyExecutor

import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.{AbstractFlowSchemaAggregation, ProcedureContainer}

import java.util

trait ProcedureActivityStrategyExecutor {
  //先根据name进行区分
  def name() : String

  /**
   * 目前仅用到filter一项功能，procedure本身和activity还是比较耦合的
   */
  def doFilter(procedureContainers: List[ProcedureContainer]) : List[ProcedureContainer]

  def checkSyntax(activityConfiguration: util.Map[String, AnyRef], flowSchemaAggregation: AbstractFlowSchemaAggregation)
}
