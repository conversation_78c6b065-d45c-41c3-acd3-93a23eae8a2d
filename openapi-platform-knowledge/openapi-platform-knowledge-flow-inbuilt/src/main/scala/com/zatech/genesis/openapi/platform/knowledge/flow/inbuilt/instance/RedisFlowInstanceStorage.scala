package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.instance

import com.zatech.genesis.openapi.platform.common.{CacheUtil, Javable, OpenApiProps}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowInstancePO
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowInstanceStorage
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.InbuiltFlowInstancePO
import com.zatech.genesis.openapi.platform.share.json.JsonParser

import java.util.Optional
import java.util.concurrent.TimeUnit

/**
 * @description: flow instance info
 * @author: zhenbin.li
 * @create: 2022-09-06 18:33
 * */
@deprecated(message = "use MemoryFlowInstanceStorage instead", since = "2.76")
class RedisFlowInstanceStorage(cache: CacheUtil,
                               openapiProps: OpenApiProps) extends IFlowInstanceStorage with Javable {
  override def load(flowInstanceId: JLong): Optional[IFlowInstancePO] = {
    Optional.ofNullable(JsonParser.fromJson(cache.getString(RedisFlowInstancePersistence.flowInstanceKey + ":" + String.valueOf(flowInstanceId)), classOf[InbuiltFlowInstancePO]))
  }

  override def save(flowInstance: IFlowInstancePO): Unit = {
    cache.setObject(RedisFlowInstancePersistence.flowInstanceKey + ":" + String.valueOf(flowInstance.getId),
      JsonParser.toJsonString(flowInstance), openapiProps.getFlow.getTimeout.toInt, TimeUnit.MILLISECONDS)
  }

  override def loadOrInit(flowInstanceId: JLong): IFlowInstancePO = {
    load(flowInstanceId).orElseGet(() => {
      val po = new InbuiltFlowInstancePO().setId(flowInstanceId)
      save(po)
      po
    })
  }
}

object RedisFlowInstancePersistence {
  val flowInstanceKey = "flow_instance"
}
