package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.common.iddef.TenantCode
import com.zatech.genesis.openapi.platform.domain.IProcedureMetaAggregation
import com.zatech.genesis.openapi.platform.domain.exception.{DomainErrorCode, DomainObjectNotFoundException}
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.schema.factory.ProcedureMetaAggregationFactory
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.spec.factory.SpecProcedureMetaAggregationFactory
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.model.ProcedureQueryParam
import com.zatech.genesis.openapi.platform.integration.constant.OpenApiConstant
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityResult, FlowActivitySuccessResult, FlowExecution<PERSON><PERSON><PERSON>t, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.ProcedureFlowActivity.UsingKey
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.error.ErrorMapping
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.error.ErrorHolder
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.FlowActivityStatePO
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.{AbstractFlowSchemaAggregation, ProcedureContainer}
import com.zatech.genesis.openapi.platform.knowledge.procedure._
import com.zatech.genesis.openapi.platform.knowledge.procedure.api.enums.ProcedureDataCategoryEnum
import com.zatech.genesis.openapi.platform.knowledge.procedure.api.model.ProcedureData
import com.zatech.genesis.openapi.platform.knowledge.procedure.api.persistence.IProcedureDataSaver
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure
import com.zatech.genesis.openapi.platform.share.enums.{OpenApiSolutionEnum, ProcedureMetaTypeEnum}
import com.zatech.genesis.openapi.platform.share.json.{JsonParser, Jsonable}
import com.zatech.genesis.openapi.platform.share.{Loggable, ThreadPoolContext}
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.SpecManager
import com.zatech.genesis.portal.toolbox.exception.CommonException
import com.zatech.genesis.portal.toolbox.i18n.I18nBundler
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Scope
import org.springframework.context.{ApplicationContext, ApplicationEventPublisher}
import org.springframework.stereotype.Component

import java.util
import java.util.concurrent.TimeUnit
import java.util.{Date, Map, Objects}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/10 10:43
 * @version 1.0
 * */
object ProcedureFlowActivity {
  private val ProcedureConfigKey: String = "procedures"

  private val UsingKey: String = "using"
}

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
class ProcedureFlowActivity(override val flowSchema: AbstractFlowSchemaAggregation, val content: Map[String, AnyRef])
  extends AbstractFlowActivity(flowSchema, content) with Loggable {

  @Autowired val procedureManager: ProcedureManager = null
  @Autowired val procedureMetaAggregationFactory: ProcedureMetaAggregationFactory = null
  @Autowired val specProcedureMetaAggregationFactory: SpecProcedureMetaAggregationFactory = null
  @Autowired val threadPoolContext: ThreadPoolContext = null
  @Autowired val applicationContext: ApplicationContext = null
  @Autowired val procedureDataSaver: IProcedureDataSaver = null
  @Autowired val procedureHookDelegation: ProcedureHookDelegation = null
  @Autowired val procedureCallbackDecryptDelegation: ProcedureCallbackDecryptDelegation = null
  @Autowired val openApiProps: OpenApiProps = null
  @Autowired val applicationEventPublisher: ApplicationEventPublisher = null
  @Autowired val strategyExecutorFactory: StrategyExecutorFactory = null
  @Autowired val specManager: SpecManager = null
  @Autowired val i18nBundler: I18nBundler = null

  val configList = content.get(ProcedureFlowActivity.ProcedureConfigKey)
  val procedureConfigs: util.List[ProcedureConfig] = JsonParser.fromObjToList(configList, classOf[ProcedureConfig])

  //使用哪一个activity的result作为input
  val usingResultActivityName: Option[String] = Option(content.get(UsingKey)).map(ref => ref.asInstanceOf[String])

  var currentActivityResult: Option[Jsonable] = Option.empty

  var flowDataContext: FlowExecutionContext = null

  override def onStarted(context: FlowExecutionContext, msg: FlowMessage): FlowActivityResult = {
    this.flowDataContext = context
    implicit val ec = threadPoolContext.getProcedureExecutor
    log.info("Procedure flow activity started.")
    val activityRunningContext: ActivityRuntimeContext = generateActivityContext(context)
    val strategyExecutor =
      strategyExecutorFactory.getExecutor(activityRunningContext.getStrategy)
    var procedureContainers = procedureConfigs.map(procedureConfig =>
      findProcedure(procedureConfig, TenantCode(context.getTenantCode), activityRunningContext)).toList

    //filter procedureContainers
    procedureContainers.foreach(container => container.prepare())
    procedureContainers = strategyExecutor.doFilter(procedureContainers)
    //针对只有一个procedure的flow不再异步执行
    if (procedureContainers.size == 1) runProcedure(procedureContainers.last)
    else {
      val future = procedureContainers.map(container => Future{ runProcedure(container) })
      Await.result(Future.sequence(future), Duration(openApiProps.getFlow.getProcedure.getTimeout, TimeUnit.MILLISECONDS))
    }

    def runProcedure(container: ProcedureContainer): Unit = {
      val procedureData = new ProcedureData()
        .setProcedureMetaId(container.aggregation.getId)
        .setProcedureName(container.getName)
        .setActivityName(name)
        .setFlowInstanceId(msg.getFlowInstanceId)
      var isSuccess = true
      try { container.execute() } catch { case e: Exception =>
        isSuccess = false
        log.warn("Procedure execution failed: ", e)
        procedureData.setOutput(new ErrorHolder(e).toMap)
        //旧版本的直接抛出
        if(container.solution == OpenApiSolutionEnum.Legacy) throw e

        val mappedException = new ErrorMapping(e, flowDataContext, i18nBundler).mappedException
        throw mappedException
      } finally {
        procedureData.setEndTime(new Date)
        procedureData.setCategory(
          if (isSuccess && (flowSchema.recordable || Objects.nonNull(System.getProperty(OpenApiConstant.SANDBOX_FLAG_KEY))))
            ProcedureDataCategoryEnum.Success
          else if (!isSuccess) ProcedureDataCategoryEnum.Error
          else null
        )

        if (null != procedureData.getCategory) {
          applicationEventPublisher.publishEvent(
            ProcedureDataRecordEvent(
              container.procedure,
              procedureData,
              context.getRequestBody,
              JsonParser.fromObjToJsonMap(container.procedure.getOutput)
            )
          )
        }
      }
      context.addFinishedProcedure(container.procedure)
    }

    this.currentActivityResult = Option(activityRunningContext.getResultOutput)
    new FlowActivitySuccessResult(name)
  }

  private def findProcedure(procedureConfig: ProcedureConfig,
                            tenantCode: TenantCode,
                            activityContext: ActivityRuntimeContext): ProcedureContainer = {
    val queryParam: ProcedureQueryParam = new ProcedureQueryParam
    queryParam.setNamespace(flowSchema.namespace)
    queryParam.setName(procedureConfig.getName)
    val typeEnum: ProcedureMetaTypeEnum = ProcedureMetaTypeEnum.valueOf(procedureConfig.getType)
    queryParam.setType(typeEnum)
    queryParam.setDomain(procedureConfig.getDomain)
    queryParam.setTag(procedureConfig.getTag)
    queryParam.setTenantCode(tenantCode.value)
    //先查找数据库中的Procedure声明
    val flag = Option(activityContext).map_?(_.flowContext).map_?(_.getInstanceAggregation).map_?(_.newVersionFlag).getOrElse(false)

    val metaAggregation = findProcedureMetaAggregation(queryParam, flag)
    //再查找其实现
    val procedure = procedureManager.findAndCreateProcedureOpt(queryParam) match {
      case Some(p: AbstractProcedure[Jsonable, Jsonable]) => p
      case _ => throw new IllegalStateException(s"can not find procedure by param: $queryParam")
    }
    //设置基本信息
    procedure.setId(metaAggregation.getId)
    procedure.setName(procedureConfig.getName)
    ProcedureContainer(procedureConfig, metaAggregation, procedure, activityContext)
  }

  def findProcedureMetaAggregation(procedureConfig: ProcedureConfig, tenantCode: TenantCode, specProcedureFlag: Boolean)
  : IProcedureMetaAggregation = {
    val queryParam: ProcedureQueryParam = new ProcedureQueryParam
    queryParam.setNamespace(flowSchema.namespace)
    queryParam.setName(procedureConfig.getName)
    val typeEnum: ProcedureMetaTypeEnum = ProcedureMetaTypeEnum.valueOf(procedureConfig.getType)
    queryParam.setType(typeEnum)
    queryParam.setDomain(procedureConfig.getDomain)
    queryParam.setTag(procedureConfig.getTag)
    queryParam.setTenantCode(tenantCode.value)
    findProcedureMetaAggregation(queryParam, specProcedureFlag)
  }

  private def findProcedureMetaAggregation(queryParam: ProcedureQueryParam, specProcedureFlag: Boolean)
  : IProcedureMetaAggregation = {
    //先查找数据库中的Procedure声明
    val metaAggregationOpt: Option[IProcedureMetaAggregation] = try {
      if (specProcedureFlag) Some(specProcedureMetaAggregationFactory.getDomainBy(queryParam))
      else Some(procedureMetaAggregationFactory.getDomainBy(queryParam))
    } catch {
      case e: DomainObjectNotFoundException =>
        log.error(s"Domain not found for queryParam: $queryParam", e)
        None
    }
    // 统一检查其是否为空，如果为空则抛出 DomainObjectNotFoundException。
    metaAggregationOpt.getOrElse {
      log.error(s"procedure: $queryParam is not found.")
      throw CommonException.byError(DomainErrorCode.procedure_not_found)
    }

  }

  override def loadState(statePO: FlowActivityStatePO): Unit = {
  }

  override def saveState(statePO: FlowActivityStatePO): Unit = {
  }

  override def getUsingResult(): Option[Jsonable] =
    Option(allDependActivityList)
      .flatMap(list =>
        list.filter(activity => activity.equals(usingResultActivityName.getOrElse("")))
          .flatMap(activity => getFlowActivityResult(activity)).lastOption)
      .orElse(Option(flowDataContext).map(t => t.getProcedureContext.getContext.getOriginInput))

  private def getFlowActivityResult(activity: String): Option[Jsonable] = {
    val procedureFlowActivityOpt = Option(flowSchema.getFlowActivity(activity)).collect { case activity: ProcedureFlowActivity => activity }
    val currentActivityResultOpt = procedureFlowActivityOpt.flatMap(activity => Option(activity.currentActivityResult))

    currentActivityResultOpt.flatMap(_.lastOption)
  }

  private def generateActivityContext(flowData: FlowExecutionContext): ActivityRuntimeContext = {
    val lastActivityInput = if (flowSchema.directMapping) this.getUsingResult() else None
    new ActivityRuntimeContext(
      lastActivityInput,
      procedureHookDelegation,
      procedureCallbackDecryptDelegation,
      flowSchema,
      flowData,
      applicationContext,
      content
    )
  }

  override def checkSyntax(): Unit = {
    val strategy = content.getOrDefault("strategy", "system")
    strategyExecutorFactory.getExecutor(strategy.asInstanceOf[String]).checkSyntax(content, flowSchema)
  }
}


