package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.error

import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.ApiErrorCodes
import com.zatech.genesis.portal.toolbox.exception.context.DefaultMessageSourceLookupContext
import com.zatech.genesis.portal.toolbox.i18n.I18nBundler
import com.zatech.octopus.component.sleuth.TraceOp
import com.zatech.octopus.module.exception.BasicBusinessException

import org.apache.commons.lang3.LocaleUtils

import org.springframework.http.HttpStatus
import org.springframework.util.StringUtils

/**
 * <AUTHOR>
 * @create 2024/8/6 10:01
 * */
class DslErrorCodeMappingHandler(apiErrorCodes: ApiErrorCodes,
                                 i18nBundler: I18nBundler) extends IErrorCodeMappingHandler {

  override def tryMapping(cause: Exception): Option[Exception] = {
    val i18nBundle = i18nBundler.findBundleOrNull(new DefaultMessageSourceLookupContext(TraceOp.getTenant))
    // 检查 errorContent 中是否包含 errorCodeMap 中的某个 sourceCode
    for (content <- cause.contentToMappingOpt;
         similarErrorCode <- apiErrorCodes.similarErrorCodeKeyLike(content)) {
      val message = i18nBundle.getMessage(similarErrorCode.targetCode, LocaleUtils.toLocale(TraceOp.getLanguage), cause.getMessage)
      val targetMessage = if (StringUtils.hasText(message)) message else cause.getMessage
      val targetEx = new BasicBusinessException(targetMessage)
      targetEx.setErrorCode(similarErrorCode.targetCode)
      targetEx.setHttpStatus(HttpStatus.valueOf(similarErrorCode.httpStatus))
      return Some(targetEx)
    }
    None
  }
}
