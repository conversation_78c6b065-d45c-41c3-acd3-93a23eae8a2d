package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityResult, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowActivity, IFlowActivityResultNotifier}
import com.zatech.genesis.openapi.platform.share.ThreadPoolContext
import org.springframework.context.ApplicationEventPublisher

class InbuiltFlowActivityResultNotifier(
                                         applicationEventPublisher: ApplicationEventPublisher,
                                         threadPoolContext: ThreadPoolContext
                                       ) extends IFlowActivityResultNotifier {

  override def onActivityDone(activity: IFlowActivity, msg: FlowMessage, result: FlowActivityResult): Unit = {

      applicationEventPublisher.publishEvent(
        FlowActivityResultEvent(activity, msg, result)
      )

  }
}
