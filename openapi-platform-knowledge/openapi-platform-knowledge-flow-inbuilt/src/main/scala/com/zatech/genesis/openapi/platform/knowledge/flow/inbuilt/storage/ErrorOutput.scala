package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.storage

import com.zatech.genesis.portal.toolbox.exception.ICodeException
import com.zatech.genesis.portal.toolbox.util.ClassUtil
import org.springframework.http.HttpStatus

/**
 * <AUTHOR>
 * @create 2024/11/11 10:44
 * */
class ErrorOutput(exception: Exception) {

  val code: String = exception match {
    case codeMsg: ICodeException => codeMsg.getCode
    case _ => reflectToGetCode
  }

  val message: String = exception match {
    case codeMsg: ICodeException => codeMsg.getMessage
    case  _ => exception.getMessage
  }

  val httpStatus: Int = exception match {
    case codeMsg: ICodeException => codeMsg.getHttpStatus
    case _ => HttpStatus.INTERNAL_SERVER_ERROR.value()
  }

  private def reflectToGetCode: String = {
    try {
      val method = ClassUtil.findUniqueMethodOrNull(exception.getClass, "getCode")
      val result = method.invoke(exception)
      if (result != null) result.toString else null
    } catch { case _: Exception => null }
  }
}
