package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.strategyExecutor

import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.{AbstractFlowSchemaAggregation, ProcedureContainer}
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException
import org.springframework.stereotype.Component

import java.util
import scala.jdk.CollectionConverters.IterableHasAsJava
import scala.jdk.OptionConverters.RichOptional

/**
 * @author:zexin.lin
 * @data: 2023/3/16
 */
@Component class ExclusiveStrategyExecutor extends ProcedureActivityStrategyExecutor {
  override def name = "exclusive"

  override def doFilter(procedureContainers: List[ProcedureContainer]): List[ProcedureContainer] = {
    val defaultExecutor = procedureContainers
      .find(container => container.isDefaultInExclusiveActivity)
      .getOrElse(() => new OpenApiException(KnowledgeErrorCode.exclusive_activity_without_default))
      .asInstanceOf[ProcedureContainer]

    List(procedureContainers.asJavaCollection
        .stream().filter(c => c.shouldExecute())
        .findFirst()
        .toScala
        .getOrElse(defaultExecutor)
    )
  }

  override def checkSyntax(activityConfiguration: util.Map[String, AnyRef], flowSchemaAggregation: AbstractFlowSchemaAggregation) = {
  }
}
