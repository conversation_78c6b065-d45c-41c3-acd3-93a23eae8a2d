package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.result

import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowActivity
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityFailResult, FlowActivityResult, FlowMessage}
import org.springframework.stereotype.Component

/**
 * @Author: felton
 * @Date: 2021/10/27 下午9:01
 */
@Component
class FlowActivityFailResultHandler extends IFlowActivityResultHandler {
  override def handle(activity: IFlowActivity, result: FlowActivityResult, msg: FlowMessage): Boolean =
    false

  override def forResult(): Class[_ <: FlowActivityResult] = classOf[FlowActivityFailResult]
}
