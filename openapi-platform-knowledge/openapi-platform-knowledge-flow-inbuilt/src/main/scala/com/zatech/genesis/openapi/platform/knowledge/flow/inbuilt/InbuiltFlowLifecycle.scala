package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.domain.IInstanceAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowLifecycle, IOpenApiInterceptorManager}
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApiInterceptor
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.enums.ExecuteStrategy
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.{ApiCallContext, ApiCallResponseContext}
import com.zatech.genesis.openapi.platform.share.enums.OpenApiSolutionEnum
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.instance.SpecInstanceAggregation
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @create 2024/12/2 10:19
 * */
@Component
class InbuiltFlowLifecycle @Autowired()(
                                         interceptorManager: IOpenApiInterceptorManager
                                       ) extends IFlowLifecycle {
  private val legacyInterceptors = interceptorManager.getInterceptors(OpenApiSolutionEnum.Legacy)
  private val openxInterceptors = interceptorManager.getInterceptors(OpenApiSolutionEnum.OpenXPlus)

  override def beforeExecuteFlow(context: FlowExecutionContext): Boolean = {
    if(! shouldExecute(context)) return true
    val apiCallContext = new ApiCallContext(
        context.getTenantCode,
        context.getSecurity.appId,
        context.getSecurity.appType,
        context.getSecurity.channelCode,
        context.getSolution,
        context.getInstanceAggregation.namespace,
        context.getInstanceAggregation.group,
        context.getInstanceAggregation.scenario,
        context.getInstanceAggregation.apiVersion.value,
        getSpecPath(context.getInstanceAggregation),
        context.getInstanceAggregation.httpMethod,
        context.getRequestInfo,
        context.getRequestBody
    )
    val param = context.getParam
    var continueExecuteFlow = true
    targetInterceptors(context)
      .forEach(interceptor => {
        val strategy = interceptor.beforeExecuteApiFlow(apiCallContext, param.getHttpRequest, param.getHttpResponse)
        continueExecuteFlow = continueExecuteFlow & (strategy == ExecuteStrategy.goOnExecute)
      })
    continueExecuteFlow
  }

  override def afterFlowExecuteSucceeded(context: FlowExecutionContext): Option[ApiCallResponseContext] = {
    if(! shouldExecute(context)) return None
    val apiCallContext = new ApiCallResponseContext(
        context.getTenantCode,
        context.getSecurity.appId,
        context.getSecurity.appType,
        context.getSecurity.channelCode,
        context.getSolution,
        context.getInstanceAggregation.namespace,
        context.getInstanceAggregation.group,
        context.getInstanceAggregation.scenario,
        context.getInstanceAggregation.apiVersion.value,
        getSpecPath(context.getInstanceAggregation),
        context.getInstanceAggregation.httpMethod,
        context.getRequestInfo,
        context.getRequestBody,
        true,
        null,
        context.getResponseInfo,
        context.getOutputData
    )
    val param = context.getParam
    targetInterceptors(context)
      .forEach(interceptor =>
        interceptor.afterApiFlowExecuted(apiCallContext, param.getHttpRequest, param.getHttpResponse))
    Some(apiCallContext)
  }

  override def afterFlowExecuteFailed(context: FlowExecutionContext,
                                      exception: Exception): Option[ApiCallResponseContext] = {
    if(! shouldExecute(context)) return None
    val apiCallContext = new ApiCallResponseContext(
        context.getTenantCode,
        context.getSecurity.appId,
        context.getSecurity.appType,
        context.getSecurity.channelCode,
        context.getSolution,
        context.getInstanceAggregation.namespace,
        context.getInstanceAggregation.group,
        context.getInstanceAggregation.scenario,
        context.getInstanceAggregation.apiVersion.value,
        getSpecPath(context.getInstanceAggregation),
        context.getInstanceAggregation.httpMethod,
        context.getRequestInfo,
        context.getRequestBody,
        false,
        exception,
        context.getResponseInfo,
        context.getOutputData
    )
    val param = context.getParam
    targetInterceptors(context)
      .forEach(interceptor =>
        interceptor.afterApiFlowExecuted(apiCallContext, param.getHttpRequest, param.getHttpResponse))
    Some(apiCallContext)
  }

  private def getSpecPath(instanceAggregation: IInstanceAggregation): String = {
    instanceAggregation match {
      case specAggregation: SpecInstanceAggregation => specAggregation.apiNode.specPath
      case _ => instanceAggregation.path
    }
  }

  private def shouldExecute(context: FlowExecutionContext): Boolean = {
    context.getSolution match {
      case OpenApiSolutionEnum.Legacy => CollectionUtils.isNotEmpty(legacyInterceptors)
      case OpenApiSolutionEnum.OpenXPlus => CollectionUtils.isNotEmpty(openxInterceptors)
    }
  }

  private def targetInterceptors(context: FlowExecutionContext): java.util.List[IOpenApiInterceptor] = {
    context.getSolution match {
      case OpenApiSolutionEnum.Legacy => legacyInterceptors
      case OpenApiSolutionEnum.OpenXPlus => openxInterceptors
    }
  }
}
