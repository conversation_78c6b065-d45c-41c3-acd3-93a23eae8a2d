package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityResult, FlowActivitySuccessResult, FlowExecutionContext, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.FlowActivityStatePO
import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

import java.util.Map

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/10 10:30
 * @version 1.0
 * */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
class StartFlowActivity(
                         override val flowSchema: AbstractFlowSchemaAggregation,
                         content: Map[String, AnyRef])
  extends AbstractFlowActivity(flowSchema, content) with Loggable {

  override def onStarted(context: FlowExecutionContext, msg: FlowMessage): FlowActivityResult = {
    if (log.isDebugEnabled) {
      log.debug("...FlowInstance: {} StartFlowActivity onStarted...", msg.getFlowInstanceId)
    }
    new FlowActivitySuccessResult(name)
  }

  override def loadState(statePO: FlowActivityStatePO): Unit = {
    //empty implement
  }

  override def saveState(statePO: FlowActivityStatePO): Unit = {
    //empty implement
  }

  override def checkSyntax(): Unit = {

  }
}
