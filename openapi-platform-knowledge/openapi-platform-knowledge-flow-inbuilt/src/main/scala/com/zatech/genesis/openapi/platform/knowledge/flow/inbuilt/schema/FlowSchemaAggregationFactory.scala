package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema

import com.zatech.genesis.openapi.platform.domain.cache.DomainCacheConst
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.flow.inbuilt.factory.FlowSchemaEntityFactory
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowDataSaver
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.FlowActivityFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Component

/**
 * @Author: felton
 * @Date: 2021/10/23 下午8:15
 */
@Component
class FlowSchemaAggregationFactory @Autowired()(
                                      flowSchemaEntityFactory: FlowSchemaEntityFactory,
                                      flowActivityFactory: FlowActivityFactory,
                                      flowDataSaver: IFlowDataSaver
                                    ) {

  @Cacheable(cacheNames = Array(DomainCacheConst.FLOW_SCHEMA_CACHE), keyGenerator = DomainCacheConst.CUSTOM_KEY_GENERATOR, cacheManager = DomainCacheConst.CAFFEINE_CACHE_CACHE_MANAGER)
  def loadFlowSchema(schemaId: Long): FlowSchemaAggregation = {
    new FlowSchemaAggregation(
      flowActivityFactory = flowActivityFactory,
      flowSchemaEntity = flowSchemaEntityFactory.getDomain(schemaId),
      flowDataSaver = flowDataSaver
    )
  }
}
