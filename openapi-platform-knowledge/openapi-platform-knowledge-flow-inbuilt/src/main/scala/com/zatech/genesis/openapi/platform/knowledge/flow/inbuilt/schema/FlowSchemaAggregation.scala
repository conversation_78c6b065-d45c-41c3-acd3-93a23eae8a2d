package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema

import com.google.common.collect.Maps
import com.zatech.genesis.openapi.platform.common.iddef.TenantCode
import com.zatech.genesis.openapi.platform.domain.IProcedureMetaAggregation
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.flow.inbuilt.FlowSchemaEntity
import com.zatech.genesis.openapi.platform.knowledge.flow.api.exception.FlowSyntaxException
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowDataSaver
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation.{<PERSON><PERSON><PERSON>, RecordableKey, directMapping<PERSON>ey}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.{AbstractFlowActivity, FlowActivityFactory, ProcedureFlowActivity}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.enums.ActivityTypeEnum
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException

import scala.collection.mutable.{Set => MSet}
import scala.jdk.CollectionConverters._

/**
 * @Author: felton
 * @Date: 2021/10/23 下午8:12
 */

class FlowSchemaAggregation(
                             flowSchemaEntity: FlowSchemaEntity,
                             flowActivityFactory: FlowActivityFactory,
                             flowDataSaver: IFlowDataSaver
                           ) extends AbstractFlowSchemaAggregation(flowDataSaver, flowActivityFactory) {
  val id = flowSchemaEntity.getId

  val content = flowSchemaEntity.content

  val namespace = flowSchemaEntity.scenarioSchemaEntity.groupEntity.namespaceEntity.name

  val namespaceId = flowSchemaEntity.scenarioSchemaEntity.groupEntity.namespaceEntity.getId

  val groupName = flowSchemaEntity.scenarioSchemaEntity.groupEntity.name

  val groupId = flowSchemaEntity.scenarioSchemaEntity.groupEntity.getId

  val schemaName = flowSchemaEntity.scenarioSchemaEntity.name

  val schemaId = flowSchemaEntity.scenarioSchemaEntity.getId

  val bindInstanceEntities = flowSchemaEntity.scenarioSchemaEntity.scenarioInstanceEntities

  val tenantCode = TenantCode(flowSchemaEntity.scenarioSchemaEntity.groupEntity.tenantCode)

  private val contentMap: JStringMap = content.asInstanceOf[JStringMap]

  val directMapping: Boolean = Option(contentMap.get(directMappingKey)).exists(r => r.asInstanceOf[Boolean])

  lazy val instances = flowSchemaEntity.scenarioSchemaEntity.scenarioInstanceEntities.toList
  /**
   * 是否要记录调用信息，一般为了性能考虑，纯粹的查询请求不记录
   * 当为false时，则所有的activity和procedure都不会再记录调用数据到数据库中
   * 默认为false
   */
  val recordable: Boolean = Option(contentMap.get(RecordableKey)).exists(Boolean.unbox)
  val flowActivityList: Array[AbstractFlowActivity] =
    contentMap.getOrDefault(ActivitiesKey, newJList).asInstanceOf[JAnyList].asScala.map(item =>
      flowActivityFactory.load(this, item.asInstanceOf[JStringMap])
    ).toArray
  val flowActivityMap: Map[String, AbstractFlowActivity] =
    flowActivityList.map(activity => activity.name -> activity).toMap
  val eventTopographyMap: Map[String, Set[AbstractFlowActivity]] = buildEventTopography

  buildActivityTopography

  def getAllProcedureMeta(): Array[IProcedureMetaAggregation] = {
    flowActivityList
      .filter(act => act.`type` == ActivityTypeEnum.Procedure)
      .flatMap(activity => {
        activity.`type` match {
          case ActivityTypeEnum.Procedure => {
            val procedureFlowActivity = activity.asInstanceOf[ProcedureFlowActivity]
            procedureFlowActivity.procedureConfigs.asScala
              .map(procedureConfig =>
                procedureFlowActivity.findProcedureMetaAggregation(procedureConfig, tenantCode, specProcedureFlag = false)
              )
          }
          case _ => throw OpenApiException.by(KnowledgeErrorCode.activity_type_flow_mismatch).build()
        }
      })
  }

  /*def getAllProcedures(): Array[AbstractProcedure[_ <: Jsonable, _ <: Jsonable]] = {
    flowActivityList
      .filter(act => act.`type` == ActivityTypeEnum.Procedure)
      .flatMap(activity => {
        activity.`type` match {
          case ActivityTypeEnum.Procedure => {
            val procedureFlowActivity = activity.asInstanceOf[ProcedureFlowActivity]
            procedureFlowActivity.procedureConfigs.asScala
              .map(procedureConfig =>
                procedureFlowActivity.findProcedure(procedureConfig, tenantCode, Option.empty).procedure
              )
          }
          case _ => throw OpenApiException.by(KnowledgeErrorCode.activity_type_flow_mismatch).build()
        }
      })
  }*/

  private def buildEventTopography = {
    val eventMap = Maps.newHashMap[String, MSet[AbstractFlowActivity]]()
    flowActivityList.foreach(activity => {
      activity.dependEventList.foreach(event => {
        eventMap.computeIfAbsent(event, _ => MSet()) += activity
      })
    })
    eventMap.asScala.map(entry => entry._1 -> entry._2.toSet).toMap
  }

  private def buildActivityTopography = {
    flowActivityList.foreach(activity => {
      activity.directDependActivityList.foreach(depend => {
        flowActivityMap.get(depend) match {
          case Some(parentActivity) => parentActivity.nextActivities += activity
          case None =>
            throw new FlowSyntaxException(KnowledgeErrorCode.activity_name_not_found, activity.name, depend)
        }
      })
    })

    flowActivityList.foreach(activity => {
      val allDependsSet = scala.collection.mutable.Set[String]() ++= activity.directDependActivityList

      def addParents(parent: AbstractFlowActivity): Unit = {
        allDependsSet ++= parent.directDependActivityList
        parent.directDependActivityList.foreach(p => addParents(flowActivityMap(p)))
      }

      addParents(activity)

      activity.allDependActivityList = allDependsSet.toArray
    })
  }

  def checkSyntax(): Unit = {
  }
}

