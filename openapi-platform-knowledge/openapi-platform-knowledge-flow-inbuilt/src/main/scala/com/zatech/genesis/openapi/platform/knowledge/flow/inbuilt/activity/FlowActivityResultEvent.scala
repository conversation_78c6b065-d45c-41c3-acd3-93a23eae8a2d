package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowActivity
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityResult, FlowMessage}
import org.springframework.context.ApplicationEvent

case class FlowActivityResultEvent(
                                    activity: IFlowActivity,
                                    message: FlowMessage,
                                    result: FlowActivityResult,
                                  ) extends ApplicationEvent
