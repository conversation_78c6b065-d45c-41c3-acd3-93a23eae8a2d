package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.result

import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowActivityResult
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception.FlowActivityResultHandlerNotFoundException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Component

import scala.jdk.CollectionConverters._

/**
 * @Author: felton
 * @Date: 2021/10/27 下午9:02
 */
@Component
class FlowActivityResultHandlerManager @Autowired()(
                                                     applicationContext: ApplicationContext
                                                   ) {
  val handlerMap = applicationContext.getBeansOfType(classOf[IFlowActivityResultHandler])
    .values().asScala.map(handler => handler.forResult() -> handler).toMap

  def getHandlerOfResultType(resultType: Class[_ <: FlowActivityResult]): IFlowActivityResultHandler =
    handlerMap.getOrElse(resultType,
      throw new FlowActivityResultHandlerNotFoundException(resultType))
}
