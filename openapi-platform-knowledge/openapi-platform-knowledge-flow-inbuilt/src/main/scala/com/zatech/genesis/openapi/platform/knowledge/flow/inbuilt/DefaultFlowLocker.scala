package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.share.Loggable

/**
 * @Author: felton
 * @Date: 2021/10/23 下午10:45
 */
class DefaultFlowLocker extends <PERSON>lowLocker with Loggable {

  override def lockByWaiting(lockKey: String, expireSeconds: Int): Unit = {
    log.info(s"TODO ---> locked: ${lockKey} in expire: ${expireSeconds}")
  }

  override def releaseLock(lockKey: String): Unit = {
    log.info(s"TODO ---> release lock: ${lockKey}")
  }
}
