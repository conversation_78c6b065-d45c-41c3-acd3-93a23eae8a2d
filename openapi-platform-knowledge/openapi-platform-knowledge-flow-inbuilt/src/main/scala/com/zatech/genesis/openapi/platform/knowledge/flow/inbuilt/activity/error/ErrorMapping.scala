package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.error

import com.netflix.hystrix.exception.HystrixRuntimeException
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.error.ErrorMapping.DefaultMappingErrorCodes
import com.zatech.genesis.openapi.platform.share.enums.OpenApiSolutionEnum
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.instance.ISpecInstanceAggregation
import com.zatech.genesis.portal.toolbox.exception.CommonException
import com.zatech.genesis.portal.toolbox.exception.errorcode.SystemErrorCodes
import com.zatech.genesis.portal.toolbox.exception.feign.IHttpChainException
import com.zatech.genesis.portal.toolbox.i18n.I18nBundler
import org.springframework.http.HttpStatus

/**
 * <AUTHOR>
 * @create 2024/8/5 19:26
 * */
class ErrorMapping(exception: Exception,
                   flowDataContext: FlowExecutionContext,
                   i18nBundler: I18nBundler) {

  /**
   * 分别是从dsl和annotation声明的异常码
   */
  private val (dslMappingOpt, annoMappingOpt) = {
    if(flowDataContext.getParam.getSolution == OpenApiSolutionEnum.OpenXPlus) {
      val apiNode = flowDataContext.getInstanceAggregation match {
        case spec: ISpecInstanceAggregation => spec.apiNode
      }
      val dslMapping = apiNode.errorCodesOpt.map(new DslErrorCodeMappingHandler(_, i18nBundler))
      val annoMapping = apiNode.restfulMethodOpt.flatMap(_.errorMappingOpt).map(new AnnoErrorCodeMappingHandler(_))
      (dslMapping, annoMapping)
    } else (None, None)
  }

  val mappedException: Exception = exception match {
    case e: com.zatech.genesis.portal.toolbox.exception.CommonException => e
    case e: HystrixRuntimeException => tryMappingException(e.getCause.asInstanceOf[Exception])
    case e => tryMappingException(e)
  }

  private def tryMappingException(cause: Exception): Exception = {
    if(cause.shouldMapping) {
      dslMappingOpt.flatMap(_.tryMapping(cause))
        .orElse(annoMappingOpt.flatMap(_.tryMapping(cause)))
        .getOrElse(getDefaultMappingException(cause))
    } else cause
  }

  /**
   * 针对一些特殊的异常码做统一映射.
   * @param cause
   * @return
   */
  private def getDefaultMappingException(cause: Exception): Exception = {
    cause match {
      case httpEx: IHttpChainException =>
        DefaultMappingErrorCodes.get(HttpStatus.valueOf(httpEx.getHttpStatus))
          .map(e => CommonException.byErrorAndParams(e,extractBeforeDot(cause.getMessage)))
          .getOrElse(CommonException.byError(SystemErrorCodes.internal_server_error)).addReasons(Option(cause.getMessage).getOrElse("Unknown error"))
      case _ => CommonException.byError(SystemErrorCodes.internal_server_error)
    }
  }

  private def extractBeforeDot(input: String): String = {
    input.split("\\.", 2).headOption.getOrElse(input)
  }
}


object ErrorMapping {
  val DefaultMappingErrorCodes: Map[HttpStatus, SystemErrorCodes] = Map(
    HttpStatus.FORBIDDEN -> SystemErrorCodes.forbidden,
    HttpStatus.UNAUTHORIZED -> SystemErrorCodes.unauthorized,
    HttpStatus.NOT_FOUND -> SystemErrorCodes.not_found,
    HttpStatus.BAD_REQUEST -> SystemErrorCodes.request_param_invalid
  )
}
