package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity


import com.zatech.genesis.openapi.callback.plugin.api.decrypt.ICallbackDecrypt
import com.zatech.genesis.openapi.platform.infra.application.service.ICallbackKeyService
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IProcedure
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.openapi.platform.share.json.{<PERSON>son<PERSON><PERSON><PERSON>, Jsonable}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException

import java.util.Objects

/**
 * <AUTHOR>
 * @date 2022/8/23 10:25
 */
@Component
class ProcedureCallbackDecryptDelegation @Autowired()(iCallbackDecryptService: ICallbackKeyService) extends Loggable {
  def processCallbackDecrypt(procedure: IProcedure[Jsonable, Jsonable], context: ProcedureExecutionContext): Unit = {
    if (procedure.isInstanceOf[ICallbackDecrypt]) {
      val callbackKey = iCallbackDecryptService.getCallbackKeyByScenarioInstanceId(context.getScenarioInstanceId)
      val decrypt = procedure.asInstanceOf[ICallbackDecrypt]
      if (!Objects.equals(decrypt.getDecryptMethod.toString, callbackKey.getDecryptMethod)) {
        throw OpenApiException.by(KnowledgeErrorCode.decryptMethod_is_not_match).params(decrypt.getDecryptMethod.name(), callbackKey.getDecryptMethod).build()
      }
      val result = decrypt.doDecrypt(context.getOriginInput, callbackKey
        .getDecryptKey)
      context.resetOriginInput(JsonParser.fromObjToJsonMap(result))
    }
  }
}
