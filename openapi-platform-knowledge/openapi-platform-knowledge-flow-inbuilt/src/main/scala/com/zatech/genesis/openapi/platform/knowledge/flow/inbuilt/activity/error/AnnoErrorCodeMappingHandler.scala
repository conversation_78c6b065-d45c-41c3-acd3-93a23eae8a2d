package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.error

import com.zatech.genesis.openapi.platform.share.exception.OpenApiException
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMappingHolders
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._

/**
 * <AUTHOR>
 * @create 2024/8/6 10:02
 * */
class AnnoErrorCodeMappingHandler(sourceErrorMappingHolders: ISourceErrorMappingHolders) extends IErrorCodeMappingHandler {

  override def tryMapping(cause: Exception): Option[Exception] = {
    sourceErrorMappingHolders.mappingErrorCode(cause).map(new OpenApiException(_))
  }
}
