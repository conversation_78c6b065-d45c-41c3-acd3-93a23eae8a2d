package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.error

import com.zatech.genesis.openapi.platform.share.json.JsonMap
import com.zatech.genesis.portal.toolbox.exception.ICodeException
import com.zatech.genesis.portal.toolbox.util.ClassUtil
import com.zatech.octopus.module.exception.BasicBusinessException

/**
 * <AUTHOR>
 * @create 2024/12/16 14:33
 * */
class ErrorHolder(exception: Throwable) {

  val message: String = exception.getMessage

  val code: String = exception match {
    case bs: BasicBusinessException => bs.getErrorCode
    case cs: ICodeException => cs.getCode
    case _ => tryGetCode.orNull
  }

  val httpStatus: Int = exception match {
    case bs: BasicBusinessException => bs.getHttpStatus.value()
    case cs: ICodeException => cs.getHttpStatus
    case _ => tryGetHttpStatus.getOrElse(500)
  }

  def toMap: JsonMap = {
     new JsonMap().add("code", code).add("message", message).add("httpStatus", httpStatus)
  }

  private def tryGetCode: Option[String] = {
    try {
      val method = ClassUtil.findUniqueMethodOrNull(exception.getClass, "getCode")
      val result = method.invoke(exception)
      if (result != null) Option(result.toString) else None
    } catch {case ignored: Exception => None }
  }

  private def tryGetHttpStatus: Option[Int] = {
    try {
      val method = ClassUtil.findUniqueMethodOrNull(exception.getClass, "getHttpStatus")
      val result = method.invoke(exception)
      if (result != null) result match {
        case int: Integer => Option(int)
        case str: String => Option(Integer.parseInt(str))
        case _ => None
      } else None
    } catch {case ignored: Exception => None }
  }
}
