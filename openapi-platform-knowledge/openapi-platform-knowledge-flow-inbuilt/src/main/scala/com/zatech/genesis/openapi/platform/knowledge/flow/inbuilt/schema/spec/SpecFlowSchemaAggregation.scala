/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema.spec

import com.google.common.collect.Maps
import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.domain.IProcedureMetaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.api.exception.FlowSyntaxException
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowDataSaver
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation.{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, directMapping<PERSON>ey}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.{AbstractFlowActivity, FlowActivityFactory}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode
import com.zatech.genesis.openapi.platform.share.json.JsonMap
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.node.namespace.group.scenario.api.IApiNode

import scala.collection.mutable.{Set => MSet}
import scala.jdk.CollectionConverters._

/**
 * <AUTHOR>
 * @date 2024/4/12 15:11
 * */
class SpecFlowSchemaAggregation(
                                 apiNode: IApiNode,
                                 flowActivityFactory: FlowActivityFactory,
                                 flowDataSaver: IFlowDataSaver
                               ) extends AbstractFlowSchemaAggregation(flowDataSaver, flowActivityFactory) with Javable {
  override val id: Long = 0L
  override val content: JsonMap = apiNode.workflowOpt.get

  private val contentMap: JStringMap = content.asInstanceOf[JStringMap]

  val directMapping: Boolean = Option(contentMap.get(directMappingKey)).exists(r => r.asInstanceOf[Boolean])
  /**
   * 是否要记录调用信息，一般为了性能考虑，纯粹的查询请求不记录
   * 当为false时，则所有的activity和procedure都不会再记录调用数据到数据库中
   * 默认为false
   */
  override val recordable: Boolean = Option(contentMap.get(RecordableKey)).exists(Boolean.unbox)
  val flowActivityList: Array[AbstractFlowActivity] =
    contentMap.getOrDefault(ActivitiesKey, newJList).asInstanceOf[JAnyList].asScala.map(item =>
      flowActivityFactory.load(this, item.asInstanceOf[JStringMap])
    ).toArray
  val flowActivityMap: Map[String, AbstractFlowActivity] =
    flowActivityList.map(activity => activity.name -> activity).toMap
  override val eventTopographyMap: Map[String, Set[AbstractFlowActivity]] = buildEventTopography

  buildActivityTopography

  private def buildEventTopography = {
    val eventMap = Maps.newHashMap[String, MSet[AbstractFlowActivity]]()
    flowActivityList.foreach(activity => {
      activity.dependEventList.foreach(event => {
        eventMap.computeIfAbsent(event, _ => MSet()) += activity
      })
    })
    eventMap.asScala.map(entry => entry._1 -> entry._2.toSet).toMap
  }

  private def buildActivityTopography = {
    flowActivityList.foreach(activity => {
      activity.directDependActivityList.foreach(depend => {
        flowActivityMap.get(depend) match {
          case Some(parentActivity) => parentActivity.nextActivities += activity
          case None =>
            throw new FlowSyntaxException(KnowledgeErrorCode.activity_name_not_found, activity.name, depend)
        }
      })
    })

    flowActivityList.foreach(activity => {
      val allDependsSet = scala.collection.mutable.Set[String]() ++= activity.directDependActivityList

      def addParents(parent: AbstractFlowActivity): Unit = {
        allDependsSet ++= parent.directDependActivityList
        parent.directDependActivityList.foreach(p => addParents(flowActivityMap(p)))
      }

      addParents(activity)

      activity.allDependActivityList = allDependsSet.toArray
    })
  }

  override def getAllProcedureMeta(): Array[IProcedureMetaAggregation] = ???

  override val namespace: String = apiNode.scenario.group.namespace.name.name
}
