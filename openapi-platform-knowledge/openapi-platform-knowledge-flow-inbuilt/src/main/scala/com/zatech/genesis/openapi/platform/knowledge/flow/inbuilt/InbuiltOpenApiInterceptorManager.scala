package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.knowledge.flow.api.IOpenApiInterceptorManager
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApiInterceptor
import com.zatech.genesis.openapi.platform.share.enums.OpenApiSolutionEnum
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._

import java.util

/**
 * <AUTHOR>
 * @create 2024/11/29 17:02
 * */
@Component
class InbuiltOpenApiInterceptorManager @Autowired()(
                                                   interceptors: java.util.List[IOpenApiInterceptor]
                                                   ) extends IOpenApiInterceptorManager {

  private val interceptorMap: Map[OpenApiSolutionEnum, Seq[IOpenApiInterceptor]] = {
    OpenApiSolutionEnum.values().map(solution => {
       val array = interceptors.filter(_.solutionsSupported().contains(solution))
      solution -> array.sortBy(_.order())
    }).toMap
  }

  override def getInterceptors(solutionEnum: OpenApiSolutionEnum): util.List[IOpenApiInterceptor] = {
    interceptorMap.getOrElse(solutionEnum, Seq.empty[IOpenApiInterceptor]).toList
  }
}
