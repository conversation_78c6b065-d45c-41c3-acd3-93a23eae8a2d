/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.domain.IProcedureMetaAggregation
import com.zatech.genesis.openapi.platform.integration.constant.OpenApiConstant
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.{FlowData, IFlowDataSaver}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.{AbstractFlowActivity, FlowActivityFactory}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run.RichFlowExecutionContext
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.openapi.platform.share.json.JsonMap

import java.util.Objects


/**
 * <AUTHOR>
 * @date 2024/4/11 11:40
 * */
abstract class AbstractFlowSchemaAggregation(flowDataSaver: IFlowDataSaver,  flowActivityFactory: FlowActivityFactory) extends Loggable with Javable {

  val id: Long

  val content: JsonMap

  val namespace: String

  val eventTopographyMap: Map[String, Set[AbstractFlowActivity]]

  val flowActivityList: Array[AbstractFlowActivity]

  val directMapping: Boolean
  /**
   * 是否要记录调用信息，一般为了性能考虑，纯粹的查询请求不记录
   * 当为false时，则所有的activity和procedure都不会再记录调用数据到数据库中
   * 默认为false
   */
  val recordable: Boolean

  val flowActivityMap: Map[String, AbstractFlowActivity]

  def getFlowActivity(activityName: String): AbstractFlowActivity = flowActivityMap(activityName)

  def getAllProcedureMeta(): Array[IProcedureMetaAggregation]

  /**
   * 保存成功输出数据
   *
   * @param data
   */
  def saveSuccess(context: FlowExecutionContext, dataSupplier: => FlowData): Unit = {
    if (recordable || context.shouldIdempotent ||
        Objects.nonNull(System.getProperty(OpenApiConstant.SANDBOX_FLAG_KEY))) {
      flowDataSaver.saveSuccessData(context, dataSupplier)
    } else if (log.isDebugEnabled) {
      log.debug("This flow: {} is not recordable, so will not save success output.", id)
    }
  }
}

object AbstractFlowSchemaAggregation {
   val ActivitiesKey = "activities"

   val directMappingKey = "directMapping"

   val RecordableKey = "recordable"
}


