package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.share.Loggable
import org.springframework.context.ApplicationListener
import org.springframework.stereotype.Component

@Component
class FlowActivityResultEventListener
  extends ApplicationListener[FlowActivityResultEvent] with Loggable {

  override def onApplicationEvent(event: FlowActivityResultEvent): Unit = {
    if (log.isDebugEnabled) {
      log.debug(s"Activity: [${event.result.getActivityName}] is Finished")
    }
  }
}
