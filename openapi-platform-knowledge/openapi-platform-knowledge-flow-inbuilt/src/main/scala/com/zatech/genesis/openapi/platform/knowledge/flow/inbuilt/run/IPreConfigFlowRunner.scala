package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run

import com.zatech.genesis.openapi.platform.domain.IInstanceAggregation
import com.zatech.genesis.openapi.platform.integration.constant.OpenApiConstant
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowInstancePO, IFlowRunner}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowExecutionContext, FlowParam}
import com.zatech.genesis.openapi.platform.share.OpenApiConsts
import com.zatech.octopus.component.sleuth.TraceOp

import java.util.Objects

/**
 * <AUTHOR>
 * @create 2024/9/10 10:11
 * */
trait IPreConfigFlowRunner extends IFlowRunner {

  def sandboxFlag: Boolean

  abstract override def runFlow(instanceAggregation: IInstanceAggregation,
                                flowInstancePO: IFlowInstancePO,
                                param: FlowParam): FlowExecutionContext = {
    //通过链路传递请求调用来源，在回调场景需要使用判断是否是openapi触发的业务
    TraceOp.setExtItem(OpenApiConsts.SOURCE, OpenApiConsts.OPENAPI)
    if (sandboxFlag && Objects.isNull(System.getProperty(OpenApiConstant.SANDBOX_FLAG_KEY))) {
      //将标志放入系统环境中，方便后续读取
      System.setProperty(OpenApiConstant.SANDBOX_FLAG_KEY, String.valueOf(sandboxFlag))
    }
    super.runFlow(instanceAggregation, flowInstancePO, param)
  }
}
