package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.za.cqrs.util.Jackson
import com.zatech.genesis.openapi.platform.hooks.api.EventName
import com.zatech.genesis.openapi.platform.hooks.api.model.BusinessTriggeredApplicationEvent
import com.zatech.genesis.openapi.platform.hooks.inbuilt.domain.factory.{WebHookTriggerAggregationFactory, WebHookEventAggregationFactory}
import com.zatech.genesis.openapi.platform.hooks.plugin.api.IHookable
import com.zatech.genesis.openapi.platform.infra.application.service.IWebHookRegistryService
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IProcedure
import com.zatech.genesis.openapi.platform.share.json.Jsonable
import com.zatech.genesis.openapi.platform.share.{Loggable, TraceSupport}
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters.java2ScalaSeq
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @Date 2022/6/8
 * */
@Component
class ProcedureHookDelegation @Autowired()(
                                            webHookRegistryService: IWebHookRegistryService,
                                            webHookBusinessEventAggregationFactory: WebHookTriggerAggregationFactory,
                                            applicationEventPublisher: ApplicationEventPublisher,
                                            webHookEventAggregationFactory: WebHookEventAggregationFactory
                                          ) extends Loggable {

  def processHook(procedure: IProcedure[Jsonable, Jsonable]): Unit = {
    if (procedure.isInstanceOf[IHookable[_, _]]) {
      val events = procedure.asInstanceOf[IHookable[Jsonable, Jsonable]].bindHookEvent(procedure.getInput, procedure.getOutput)
      log.info("Publish BusinessTriggeredApplicationEvent of Procedure: {},{}", procedure.getClass.getName,Jackson.toJson(events))
      for(event <- events) {
        val eventAggregation = webHookEventAggregationFactory.getDomain(EventName(event.getEvent.getEvent))
        val ids = webHookRegistryService.listRegistryIdsBy(
          TraceSupport.getAppIdOrThrow,
          eventAggregation.domain.getCode,
          eventAggregation.subscriptions,
          eventAggregation.version,
          eventAggregation.id.value)
        if(CollectionUtils.isNotEmpty(ids)) {
          webHookBusinessEventAggregationFactory.bindBusinessHookEvent(event, eventAggregation, ids)
        }
      }

      val applicationEvent = new BusinessTriggeredApplicationEvent
      applicationEvent.setCategoryList(events)
      applicationEventPublisher.publishEvent(applicationEvent)
    }
  }
}
