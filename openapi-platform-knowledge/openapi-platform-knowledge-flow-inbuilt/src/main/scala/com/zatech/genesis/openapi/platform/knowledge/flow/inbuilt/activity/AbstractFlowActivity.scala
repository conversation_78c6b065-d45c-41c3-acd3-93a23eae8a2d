package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowActivity
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowMessage
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.enums.ActivityTypeEnum
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.{FlowActivityStatePO, InbuiltFlowInstancePO}
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.openapi.platform.share.json.Jsonable

import java.util.Objects
import scala.annotation.tailrec
import scala.collection.mutable.ListBuffer
import scala.jdk.CollectionConverters._

/**
 * @Author: felton
 * @Date: 2021/10/23 下午10:07
 *        每一个实现类都不能是单例类，必须加上@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
 *        以实现线程安全性
 *        TODO 后面从代码层面加上对于@Scope的校验，校验不通过直接不允许启动服务。
 */
abstract class AbstractFlowActivity(
                                     val flowSchema: AbstractFlowSchemaAggregation,
                                     content: FlowActivity.JStringMap,
                                   ) extends IFlowActivity with Loggable with Cloneable {

  val name = content.get(FlowActivity.NameKey).asInstanceOf[String]

  val `type`: ActivityTypeEnum = ActivityTypeEnum.valueOf(content.get(FlowActivity.TypeKey).asInstanceOf[String])

  /**
   * 直接前继节点
   */
  val directDependActivityList: Array[String] =
    content.getOrDefault(FlowActivity.DependActivitiesKey, FlowActivity.newJList).asInstanceOf[FlowActivity.JStringList].asScala.toArray
  val dependEventList: Array[String] =
    content.getOrDefault(FlowActivity.DependEventsKey, FlowActivity.newJList).asInstanceOf[FlowActivity.JStringList].asScala.toArray
  //TODO 检查异常值
  val condition = ConditionEnum.withName(content.getOrDefault(FlowActivity.ConditionKey, "AND").asInstanceOf[String])
  val nextActivities: ListBuffer[AbstractFlowActivity] = ListBuffer()

  /**
   * 所有前继节点，包括前继的前继...
   */
  var allDependActivityList: Array[String] = _

  override def clone(): AnyRef = {
    val newActivity = super.clone().asInstanceOf[AbstractFlowActivity]
    cloneStateToNewActivityIfNeeded(newActivity)
    newActivity
  }

  def cloneStateToNewActivityIfNeeded(newActivity: AbstractFlowActivity): Unit = {
    //子类按需实现
  }

  override def equals(obj: Any): Boolean = {
    if (!obj.isInstanceOf[AbstractFlowActivity]) return false
    Objects.equals(name, obj.asInstanceOf[AbstractFlowActivity].name)
  }

  override def hashCode(): Int = Objects.hashCode(name)

  def nextAlternativeActivityKeysWhenDone: Array[String] = {
    nextActivityKeys.toArray
  }

  def nextActivityKeys = nextActivities.map(_.name)

  def loadState(statePO: FlowActivityStatePO): Unit

  def saveState(statePO: FlowActivityStatePO): Unit

  protected[inbuilt] def canDo(flowInstancePO: InbuiltFlowInstancePO, selfState: FlowActivityStatePO)(flowMessage: FlowMessage): Boolean = {

    val activityChecker = checkActivity(flowInstancePO, selfState) _
    val eventChecker = checkEvent(flowInstancePO, selfState) _

    condition match {
      case ConditionEnum.AND => activityChecker(flowMessage) && eventChecker(flowMessage)
      case ConditionEnum.OR => activityChecker(flowMessage) || eventChecker(flowMessage)
    }
  }

  protected def checkActivity(flowInstancePO: InbuiltFlowInstancePO, selfState: FlowActivityStatePO)(flowMessage: FlowMessage): Boolean = {
    directDependActivityList.forall(flowInstancePO.containsFinishedActivity)
  }

  protected def checkEvent(flowInstancePO: InbuiltFlowInstancePO, selfState: FlowActivityStatePO)(flowMessage: FlowMessage): Boolean = {

    if (dependEventList.isEmpty) return true;

    val dependEvents = dependEventList.toSet.toBuffer

    val eventListMaxIndex = flowInstancePO.eventMaxIndex()

    @tailrec
    def checkMessage(index: Int): Boolean = {
      if (index <= selfState.getCheckOffset) {
        return false
      }
      dependEvents -= flowInstancePO.getEventMessage(index).getName
      if (dependEvents.isEmpty) {
        selfState.setCheckOffset(eventListMaxIndex)
        return true
      }
      checkMessage(index - 1)
    }

    checkMessage(eventListMaxIndex)
  }

  /**
   *
   * 正常应该是提供多个activity结果的并集，但是哪个并哪个可能还需要加接口
   *
   * @return
   */
  def getUsingResult(): Option[Jsonable] = Option.empty

}

object ConditionEnum extends Enumeration {
  val OR, AND = Value
}

object FlowActivity extends Javable {

  val TypeKey = "type"
  val NameKey = "name"
  val DependActivitiesKey = "dependActivities"
  val DependEventsKey = "dependEvents"
  val ConditionKey = "condition"
}

