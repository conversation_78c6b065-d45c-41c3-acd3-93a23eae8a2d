package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowEventListener
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.{IFlowDataLoader, IFlowInstanceStorage}
import com.zatech.genesis.openapi.platform.share.{RequestSupport, TenantSupport}
import com.zatech.octopus.module.cache.ICacheProvider
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @create 2024/9/10 10:32
 * */
@Component
class DefaultFlowRunner @Autowired()(flowEventListener: IFlowEventListener,
                                     flowInstanceStorage: IFlowInstanceStorage,
                                     flowDataLoader: IFlowDataLoader,
                                     override val requestSupport: RequestSupport,
                                     override val tenantSupport: TenantSupport,
                                     override val openapiProps: OpenApiProps,
                                     override val cacheProvider: ICacheProvider,
                                     @Qualifier("idempotentRedisTemplate")
                                     override val redisTemplate: RedisTemplate[String, java.lang.Long])
  extends TrulyFlowRunner(flowEventListener, flowInstanceStorage, flowDataLoader, tenantSupport)
    with IdempotentFlowRunner with IPreConfigFlowRunner {

  override def sandboxFlag: Boolean = openapiProps.getSandbox.getEnabled
}
