package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.common.storage.ResponseContext
import com.zatech.genesis.openapi.platform.domain.IInstanceAggregation
import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowExecutionContext, FlowParam}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowInstancePO, IFlowRunner}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception.FlowErrorCodes
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run.IdempotentFlowRunner.IdempotentContext
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApiSchemaValidator
import com.zatech.genesis.openapi.platform.share.{RequestSupport, TenantSupport, TraceSupport}
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.global.ApiResponseHeaderSchema.ShouldRetryKey
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.instance.ISpecInstanceAggregation
import com.zatech.genesis.portal.toolbox.exception.CommonException
import com.zatech.genesis.portal.toolbox.exception.errorcode.SystemErrorCodes
import com.zatech.octopus.module.cache.ICacheProvider
import org.springframework.data.redis.core.RedisTemplate

import java.lang
import java.util.Optional
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @create 2024/9/10 10:12
 * */
trait IdempotentFlowRunner extends IFlowRunner {

  val cacheProvider: ICacheProvider
  val openapiProps: OpenApiProps
  val requestSupport: RequestSupport
  val tenantSupport: TenantSupport
  val redisTemplate: RedisTemplate[String, java.lang.Long]

  //比flow的timeout时间略长一点
  private val lockExpireTime: Int = (openapiProps.getFlow.getTimeout + 1000L).toInt

  abstract override def runFlow(instanceAggregation: IInstanceAggregation,
                                flowInstancePO: IFlowInstancePO,
                                param: FlowParam): FlowExecutionContext = {
    if(! instanceAggregation.idempotentSupported) return super.runFlow(instanceAggregation, flowInstancePO, param)
    param.getRequestInfo.idempotentKeyOpt match {
      case Some(idempotentKey) =>
        val context = new IdempotentContext(idempotentKey, instanceAggregation, param)
        runWithCache(flowInstancePO, context)
      case _ => super.runFlow(instanceAggregation, flowInstancePO, param)
    }
  }

  abstract override def getRecord(flowInstanceId: lang.Long): Optional[FlowDataRecord] = super.getRecord(flowInstanceId)

  /**
   * 如果有flow的cache，则说明已经处理过了，则直接返回幂等结果，否则执行
   * @param context
   * @return
   */
  private def runWithCache(flowInstancePO: IFlowInstancePO, context: IdempotentContext): FlowExecutionContext = {
    Option(redisTemplate.opsForValue().get(context.flowCacheKey)) match {
      case Some(flowInstanceId) => buildIdempotentResponse(flowInstanceId, context)
      case _ => lockAndRun(flowInstancePO, context)
    }
  }

  private def buildIdempotentResponse(flowInstanceId: Long, context: IdempotentContext): FlowExecutionContext = {
    val flowExecutionContext =
      new FlowExecutionContext(
        flowInstanceId,
        tenantSupport.getTenant,
        context.instanceAggregation,
        context.param,
        IOpenApiSchemaValidator.EMPTY_VALIDATOR)

    getRecord(flowInstanceId).ifPresentOrElse(record => {
      flowExecutionContext.setOutputData(record.getOutput).setSuccess(record.getSuccessFlag)
      val responseContext = ResponseContext.from(record.getResponseContext)
      flowExecutionContext
        .getResponseInfo
        .addHeaders(responseContext.clearedHeaders)
        .setHttpStatus(responseContext.httpStatus)
    }, () => throw CommonException.byError(FlowErrorCodes.idempotent_data_not_found))
    flowExecutionContext
  }

  /**
   * 一次只能有一个线程执行，其余返回重试header
   * @param context
   * @return
   */
  private def lockAndRun(flowInstancePO: IFlowInstancePO, context: IdempotentContext): FlowExecutionContext = {
    val success_? = cacheProvider.tryLock(ApplicationName, context.lockKey, lockExpireTime)
    //不能加锁，说明前面的请求还在处理中，则设置retry
    if(!success_?) {
      requestSupport.getResponseInfo.addHeader(ShouldRetryKey, "true")
      throw CommonException.byError(SystemErrorCodes.unprocessable_entity)
    }
    try {
      super.runFlow(context.instanceAggregation, flowInstancePO, context.param)
    } finally {
      redisTemplate
        .opsForValue()
        .set(context.flowCacheKey, flowInstancePO.getId, context.idempotentExpiredSeconds, TimeUnit.SECONDS)
      cacheProvider.releaseLock(ApplicationName, context.lockKey)
    }
  }
}

object IdempotentFlowRunner {

  class IdempotentContext(val idempotentKey: String,
                          val instanceAggregation: IInstanceAggregation,
                          val param: FlowParam) {
    private val apiNode = instanceAggregation match {
      case spec: ISpecInstanceAggregation => spec.apiNode
      case _ => throw new IllegalStateException(s"idempotent is not supported for : ${instanceAggregation.path}")
    }

    private val appId = TraceSupport.getAppIdOrThrow

    val flowCacheKey = s"$ApplicationName::idempotent::$idempotentKey@${apiNode.specPath}@$appId"

    val lockKey = s"$idempotentKey@${apiNode.specPath}@$appId"

    val idempotentExpiredSeconds: Long =
      apiNode.configOpt.flatMap(_.idempotentExpiredSecondsOpt()).getOrElse(DefaultIdempotentExpiredSeconds)
  }

  final val DefaultIdempotentExpiredSeconds = 24*60*60
}
