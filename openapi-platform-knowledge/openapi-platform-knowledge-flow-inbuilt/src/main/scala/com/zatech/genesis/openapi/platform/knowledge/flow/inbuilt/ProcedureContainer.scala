package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.domain.IProcedureMetaAggregation
import com.zatech.genesis.openapi.platform.integration.util.ValidatorUtil
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.{ActivityRuntimeContext, ProcedureCallbackDecryptDelegation, ProcedureHookDelegation}
import com.zatech.genesis.openapi.platform.knowledge.procedure._
import com.zatech.genesis.openapi.platform.knowledge.procedure.api.{IAutoMappingProcedureInOutCreator, IProcedureInOutCreator, IStandardProcedureInOutCreator}
import com.zatech.genesis.openapi.platform.knowledge.procedure.context.SystemProcedureExecutionContext
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure.ExclusiveProcedure
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.{AbstractProcedure, AutoMappingable}
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.openapi.platform.share.enums.OpenApiSolutionEnum
import com.zatech.genesis.openapi.platform.share.json.Jsonable
import jakarta.validation.Valid
import org.springframework.core.annotation.AnnotationUtils

import scala.jdk.OptionConverters.RichOptional

case class ProcedureContainer(config: ProcedureConfig,
                              aggregation: IProcedureMetaAggregation,
                              procedure: AbstractProcedure[Jsonable, Jsonable],
                              activityContext: ActivityRuntimeContext
                             ) extends Loggable {
  def getName: String = config.getName

  val systemProcedureContext: SystemProcedureExecutionContext = activityContext.flowContext.getProcedureContext

  val solution: OpenApiSolutionEnum = activityContext.flowContext.getParam.getSolution

  val procedureCallbackDecryptDelegation: ProcedureCallbackDecryptDelegation = activityContext.procedureCallbackDecryptDelegation

  val procedureHookDelegation: ProcedureHookDelegation = activityContext.procedureHookDelegation

  lazy val isResultInActivity: Boolean =
    activityContext.getResultProcedureNameOpt
      .map(resultCfg => resultCfg.equals(config.getName))
      //如果只有一个procedure，按道理确实不指定result也是可以的
      .orElse(Option(activityContext.procedureCount).map(e => e == 1))
      .getOrElse(activityContext.getStrategy.equals("exclusive"))

  lazy val isDefaultInExclusiveActivity: Boolean = activityContext.getDefaultConfigOpt.toScala.exists(defaultCfgName => defaultCfgName.equals(config.getName))

  def shouldExecute(): Boolean = {
    Option(procedure).filter(pro => pro.isInstanceOf[ExclusiveProcedure[Jsonable, Jsonable]])
      .exists(pro =>
        pro.asInstanceOf[ExclusiveProcedure[Jsonable, Jsonable]]
          .shouldExecute(systemProcedureContext.getContext, pro.getInput))
  }

  /**
   * 创建in/output
   */
  def prepare(): Unit = {
    //1.前置解密操作如果是必要的话
    procedureCallbackDecryptDelegation.processCallbackDecrypt(procedure, systemProcedureContext.getContext)
    //2. 获取in&out构建器
    val procedureInOutCreator = procedure match {
      case _: AutoMappingable => {
        activityContext.getLastActivityInput.toScala
          .filter(_ => activityContext.isDirectMappingFlow)
          .map(result => new DirectProcedureInOutCreator(result))
          .getOrElse(activityContext.getBean(classOf[IAutoMappingProcedureInOutCreator]))
      }
      case _ => activityContext.getBean(classOf[IStandardProcedureInOutCreator])
    }
    //3. 设置creator的aware信息
    injectAwareInfo(procedureInOutCreator)
    injectProcedure(procedureInOutCreator)
    //4. 初始化
    procedureInOutCreator.init()
    //5. 设置procedure的aware信息
    injectAwareInfo(procedure)
    //6. 构建输入
    procedure.setInData(procedureInOutCreator.createInput(procedure.getInCls))
    //7. 构建输出
    procedure.setOutData(procedureInOutCreator.createOutput(procedure.getOutCls))
    //8. 针对direct mapping的使用@Valid注解以后自动校验input
    val valid = AnnotationUtils.getAnnotation(procedure.getClass, classOf[Valid])
    if (null != valid) {
      ValidatorUtil.validate(procedure.getInput)
    }
    //9. 初始化procedure
    procedure.init(getProcedureClaim)
  }

  def execute(): Unit = {
    //1. 校验request
    val requestBody = systemProcedureContext.getContext.getRequestBody
    if(requestBody != null) activityContext.flowContext.getInstanceAggregation.validateRequest(requestBody)

    //7. 执行procedure
    logTime(s"Procedure: [${getName}] execute") {
      procedure.execute(systemProcedureContext.getContext, procedure.getInput, procedure.getOutput)
    }
    //8. 进行hook处理
    procedureHookDelegation.processHook(procedure)

    //10. 如果是result就设置
    if (isResultInActivity) activityContext.setResultOutput(procedure.getOutput)
  }


  private def getProcedureClaim: ProcedureClaim = {
    val procedureMeta = aggregation.procedureMetaEntity
    ProcedureClaim.builder()
      .solution(solution)
      .config(procedureMeta.configOpt.orNull)
      .apiConfig(systemProcedureContext.getScenarioInstanceAggregation.contentOpt.orNull)
      .productVersion(procedureMeta.productVersion)
      .`type`(procedureMeta.`type`)
      .content(procedureMeta.contentOpt.orNull)
      .params(systemProcedureContext.getContext.getParams)
      .requestBody(systemProcedureContext.getContext.getRequestBody)
      .domain(config.getDomain)
      .namespace(activityContext.flowSchema.namespace)
      .tenantCode(procedureMeta.tenantCode)
      .build()
  }

  private def injectAwareInfo(procedureSomeThing: AnyRef): Unit = {
    //这里不能用pattern matching，match到第一个之后就不执行第二个了，会有bug
    procedureSomeThing match {
      case aware: ProcedureMetaAggregationAware => aware.setProcedureMetaAggregation(aggregation)
      case _ =>
    }
    procedureSomeThing match {
      case aware: SystemProcedureExecutionContextAware => aware.setSystemProcedureExecutionContext(systemProcedureContext)
      case _ =>
    }
  }

  private def injectProcedure(creator: IProcedureInOutCreator): Unit =
    creator match {
      case aware: ProcedureAware => aware.setProcedure(procedure)
      case _ =>
    }


}
