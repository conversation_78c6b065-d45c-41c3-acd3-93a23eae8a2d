package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.storage

import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.infra.application.service.IFlowDataRecordService
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run.RichFlowExecutionContext
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import com.zatech.genesis.openapi.platform.share.model.EncryptJsonMap
import com.zatech.genesis.openapi.platform.share.{Loggable, ThreadPoolContext}
import com.zatech.octopus.module.mq.core.ProducerOperations
import com.zatech.octopus.module.mq.core.annotation.IProducer
import com.zatech.octopus.module.mq.core.dto.OctopusMessageRecord
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationListener
import org.springframework.stereotype.Component

@Component
class FlowDataRecordListener @Autowired()(
                                           threadPoolContext: ThreadPoolContext,
                                           flowDataRecordService: IFlowDataRecordService
                                         ) extends ApplicationListener[FlowDataRecordEvent] with Loggable{

  @IProducer
  private val flowDataProducer: ProducerOperations = null

  override def onApplicationEvent(event: FlowDataRecordEvent): Unit = {
    val flowDataRecord = () => {
      val flowDataRecord = event.flowDataRecord
      //过滤request和response中的文件字段，当前实现通过判断字段value长度是否超过1000来决定是否过滤
      flowDataRecord.setInput(EncryptJsonMap.of(JsonParser.filterJsonMapTooLongValue(flowDataRecord.getInput)))
      flowDataRecord.setOutput(EncryptJsonMap.of(JsonParser.filterJsonMapTooLongValue(flowDataRecord.getOutput)))
      flowDataRecord
    }

    /**
     * 如果是需要幂等的，则需要同步存储结果，避免并发量高时在多次幂等请求下有些请求到达时还没有存到数据库中
     * 否则则可以异步记录结果，因为并不需要幂等返回
     */
    val shouldIdempotent = event.context.shouldIdempotent
    if(shouldIdempotent) syncSaveFlowData(flowDataRecord()) else asyncSaveFlowData(flowDataRecord())
  }

  private def asyncSaveFlowData(flowDataRecord: => FlowDataRecord): Unit = {
    threadPoolContext.getFlowRecordExecutor.execute(() => {
      val messageRecord = new OctopusMessageRecord[FlowDataRecord]
      messageRecord.setMessage(flowDataRecord)
      try {
        flowDataProducer.sendOneWay(messageRecord)
      } catch {
        case e: Exception => log.error("Send flow data error.", e)
      }
    })
  }

  private def syncSaveFlowData(flowDataRecord: => FlowDataRecord): Unit = {
    flowDataRecordService.save(flowDataRecord)
  }
}
