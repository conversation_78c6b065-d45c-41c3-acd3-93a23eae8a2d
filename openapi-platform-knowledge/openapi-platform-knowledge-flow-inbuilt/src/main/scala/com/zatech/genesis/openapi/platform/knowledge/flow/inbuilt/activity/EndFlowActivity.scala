package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityResult, FlowActivitySuccessResult, FlowExecutionContext, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.EndFlowActivity.UsingKey
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.FlowActivityStatePO
import com.zatech.genesis.openapi.platform.knowledge.model.ModelValueElementFactory
import com.zatech.genesis.openapi.platform.knowledge.model.element.IModelValueElement
import com.zatech.genesis.openapi.platform.share.Loggable
import com.zatech.genesis.openapi.platform.share.enums.ModelMetaDirectionEnum
import com.zatech.genesis.openapi.platform.share.json.JsonParser
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.instance.ISpecInstanceAggregation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

import java.util.Map
import scala.beans.BeanProperty
import scala.jdk.CollectionConverters._

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/10 10:34
 * @version 1.0
 * */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
class EndFlowActivity(override val flowSchema: AbstractFlowSchemaAggregation, val content: Map[String, AnyRef])
  extends AbstractFlowActivity(flowSchema, content) with Loggable with Javable {

  @Autowired
  val modelValueElementFactory: ModelValueElementFactory = null

  val usingConfigsOpt: Option[Seq[UsingConfig]] = {
    Option(content.get(UsingKey)).map(JsonParser.fromObjToList(_, classOf[UsingConfig]).asScala.toSeq)
  }

  override def onStarted(context: FlowExecutionContext, msg: FlowMessage): FlowActivityResult = {
    if (log.isDebugEnabled) {
      log.debug("...FlowInstance: {} EndFlowActivity onStarted start...", msg.getFlowInstanceId)
    }
    if (context.getInstanceAggregation.newVersionFlag) {
      usingConfigsOpt.foreach(_.foreach(config => {
        context.getProcedureContext.getContext.getPrevProcedureByName(config.procedure).map(procedure => {
          val data = if (config.direction == ModelMetaDirectionEnum.IN) procedure.getInput else procedure.getOutput
          //TODO 对结果进行裁剪，使用的JsonSchemaAnalyser本来是用来在生成OAS DSL时使用，此处复用会每次都构建json schema node，性能待优化
          val apiNode = context.getInstanceAggregation.asInstanceOf[ISpecInstanceAggregation].apiNode
          for(wrapper <- apiNode.finalOpenApiWrapperOpt; cutResponse <- wrapper.cutResponseData(data)){
            context.setOutputData(JsonParser.fromObjToJsonMap(cutResponse))
          }
        })
      }))
    } else {
      val outputModelMetaOpt = context.getInstanceAggregation.outputModelMetaOpt
      //val systemProcedureContext = context.getProcedureContext
      outputModelMetaOpt.foreach(scenarioInstanceModelMetaAggregation => {
        val outputValueElm: IModelValueElement[AnyRef] = modelValueElementFactory.newScenarioInstanceElement(scenarioInstanceModelMetaAggregation).asInstanceOf[IModelValueElement[AnyRef]]
        /* val modelValueIdMap = systemProcedureContext.getAllModelValueElementMap
        //TODO 此处有问题，如果不可以自动映射的，则直接抛异常了
        val fullMappingList = outputValueElm.getMeta.checkIfCouldMappingAndGetMappingModelList
        //TODO 这个地方可能由于被依赖对象的顺序问题产生潜在bug
        fullMappingList.foreach(mapping => {
          var sourceElm: IModelValueElement[_] = null
          var targetElm: IModelValueElement[_] = null
          //先映射原始输入数据, 再映射前面的procedure
          if (systemProcedureContext.hasOriginInputMapping(mapping.sourceModelMetaRootId)) {
            sourceElm = systemProcedureContext.queryOriginInputSubElement(mapping.sourceModelMetaId).orElse(null)
            targetElm = outputValueElm.findById(mapping.targetModelMetaId).orElse(null)
          }
          else {
            if (modelValueIdMap.containsKey(mapping.sourceModelMetaRootId)) {
              sourceElm = modelValueIdMap.get(mapping.sourceModelMetaRootId).findById(mapping.sourceModelMetaId).orElse(null)
              targetElm = outputValueElm.findById(mapping.targetModelMetaId).orElse(null)
            }
          }
          if (sourceElm != null && targetElm != null) {
            sourceElm.mappingValueTo(targetElm)
          }
        })*/
        //根据using的信息填充数据
        usingConfigsOpt.foreach(_.foreach(config => {
          context.getProcedureContext.getContext.getPrevProcedureByName(config.procedure).map(procedure => {
            val data = if (config.direction == ModelMetaDirectionEnum.IN) procedure.getInput else procedure.getOutput
            outputValueElm.fillValues(JsonParser.fromObjToJsonMap(data))
          })
        }))
        context.setOutputModelValueElement(outputValueElm)
      })
    }


    if (log.isDebugEnabled) {
      log.debug("...FlowInstance: {} EndFlowActivity onStarted end, finish model mapping...", msg.getFlowInstanceId)
    }
    new FlowActivitySuccessResult(name)
  }

  override def loadState(statePO: FlowActivityStatePO): Unit = {
    //empty implement
  }

  override def saveState(statePO: FlowActivityStatePO): Unit = {
    //empty implement
  }

  override def checkSyntax(): Unit = {

  }
}

object EndFlowActivity {
  val UsingKey = "using"
}

class UsingConfig {
  @BeanProperty
  val procedure: String = null

  @BeanProperty
  val direction: ModelMetaDirectionEnum = null
}
