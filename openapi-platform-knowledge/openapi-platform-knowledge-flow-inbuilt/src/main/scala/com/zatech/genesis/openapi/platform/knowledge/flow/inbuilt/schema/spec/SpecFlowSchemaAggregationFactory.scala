/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema.spec

import com.zatech.genesis.openapi.platform.domain.IInstanceAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowDataSaver
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.FlowActivityFactory
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.instance.ISpecInstanceAggregation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2024/4/12 15:12
 * */
@Component
class SpecFlowSchemaAggregationFactory @Autowired()(
                                                     flowActivityFactory: FlowActivityFactory,
                                                     flowDataSaver: IFlowDataSaver
                                                   ) {

  def loadFlowSchema(instanceAggregation: IInstanceAggregation): AbstractFlowSchemaAggregation = {
    val apiNode = instanceAggregation match {
      case specFlowSchemaAggregation: ISpecInstanceAggregation => specFlowSchemaAggregation.apiNode
    }
    new SpecFlowSchemaAggregation(
      apiNode = apiNode,
      flowActivityFactory = flowActivityFactory,
      flowDataSaver = flowDataSaver
    )
  }
}
