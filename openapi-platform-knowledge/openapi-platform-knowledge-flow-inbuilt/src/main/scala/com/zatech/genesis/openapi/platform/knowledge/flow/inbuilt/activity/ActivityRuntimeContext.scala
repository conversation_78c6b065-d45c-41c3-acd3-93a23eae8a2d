package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.procedure.ProcedureConfig
import com.zatech.genesis.openapi.platform.share.json.{JsonParser, Jsonable}
import org.springframework.context.ApplicationContext

import java.util
import java.util.{Collections, Optional}

class ActivityRuntimeContext(
                              lastActivityInput: Optional[Jsonable],
                              val procedureHookDelegation: ProcedureHookDelegation,
                              val procedureCallbackDecryptDelegation: ProcedureCallbackDecryptDelegation,
                              val flowSchema: AbstractFlowSchemaAggregation,
                              val flowContext: FlowExecutionContext,
                              applicationContext: ApplicationContext,
                              activityConfig: util.Map[String, AnyRef]
                            ) extends Javable {

  private var resultOutput: Jsonable = null

  def getBean[T](requiredType: Class[T]): T = applicationContext.getBean(requiredType)

  def getResultProcedureNameOpt: Option[String] = Option(activityConfig.get("result")).map((res: Any) => res.asInstanceOf[String])

  lazy val procedureCount : Integer = activityProcedures.size()

  private lazy val activityProcedures: JList[ProcedureConfig] =
    Option(activityConfig.get("procedures"))
      .map(res => JsonParser.fromObjToList(res, classOf[ProcedureConfig]))
      .getOrElse(Collections.emptyList())

  def getDefaultConfigOpt: Optional[String] = Optional.ofNullable(activityConfig.get("default")).map((res: Any) => res.asInstanceOf[String])

  def getLastActivityInput: Optional[Jsonable] = lastActivityInput

  def getResultOutput: Jsonable = resultOutput

  def setResultOutput(resultOutput: Jsonable): Unit = {
    this.resultOutput = resultOutput
  }

  def isDirectMappingFlow: Boolean = Option(flowSchema).exists(schema => schema.directMapping)

  def getStrategy: String = activityConfig.getOrDefault("strategy", "system").asInstanceOf[String]
}
