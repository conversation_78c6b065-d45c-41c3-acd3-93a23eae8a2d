package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.storage

import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowExecutionContext
import org.springframework.context.ApplicationEvent

case class FlowDataRecordEvent(
                                context: FlowExecutionContext,
                                flowDataRecord: FlowDataRecord
                              ) extends ApplicationEvent(flowDataRecord)
