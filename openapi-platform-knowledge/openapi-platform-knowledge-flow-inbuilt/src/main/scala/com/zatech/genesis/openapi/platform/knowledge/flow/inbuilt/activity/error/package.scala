package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.portal.toolbox.share.ReflectionSupport

import scala.util.Try

/**
 * <AUTHOR>
 * @create 2024/8/6 10:53
 * */
package object error {

  implicit class ErrorOps(exception: Exception) {
    private val errorCodeOpt: Option[String] = {
      //异常类情况太多，直接通过反射找getCode方法
      Try {
        Option(ReflectionSupport.invokeMethod(exception, "getCode"))
      }.toOption.flatten.map(_.toString)
    }

    private val messageOpt: Option[String] = Option(exception.getMessage)

    def contentToMappingOpt: Option[String] = errorCodeOpt.orElse(messageOpt)

    def shouldMapping: Boolean = errorCodeOpt.isDefined || messageOpt.isDefined
  }
}
