package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.FlowActivity.TypeKey
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.enums.ActivityTypeEnum
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Component

/**
 * @Author: felton
 * @Date: 2021/10/23 下午10:57
 */
@Component
class FlowActivityFactory @Autowired()(
                                        applicationContext: ApplicationContext
                                      ) extends Javable {

  def load(flowSchema: AbstractFlowSchemaAggregation, content: JStringMap): AbstractFlowActivity = {

    val activityType = ActivityTypeEnum.valueOf(content.get(TypeKey).asInstanceOf[String])

    val activityCls = activityType match {
      case null | ActivityTypeEnum.Dummy => classOf[DummyFlowActivity]
      case ActivityTypeEnum.Procedure => classOf[ProcedureFlowActivity]
      case ActivityTypeEnum.Start => classOf[StartFlowActivity]
      case ActivityTypeEnum.End => classOf[EndFlowActivity]
    }

    applicationContext.getBean(activityCls, flowSchema, content)
  }
}
