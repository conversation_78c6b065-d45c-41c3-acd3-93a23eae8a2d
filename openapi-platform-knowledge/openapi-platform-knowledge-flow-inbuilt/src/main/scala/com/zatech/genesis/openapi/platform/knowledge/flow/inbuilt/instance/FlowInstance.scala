package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.instance

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowActivityResultNotifier
import com.zatech.genesis.openapi.platform.knowledge.flow.api.enums.FlowMessageType
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowExecutionContext, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.AbstractFlowActivity
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.result.FlowActivityResultHandlerManager
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.exception.FlowNotDAGException
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.{FlowActivityStatePO, InbuiltFlowInstancePO}
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode
import com.zatech.genesis.openapi.platform.share.{Loggable, ThreadPoolContext}

import java.lang
import java.util.Date
import java.util.concurrent.TimeUnit
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContextExecutor, Future}
import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success}

/**
 * @Author: felton
 * @Date: 2021/10/24 上午10:22
 */
class FlowInstance(
                    val flowSchema: AbstractFlowSchemaAggregation,
                    val flowInstancePO: InbuiltFlowInstancePO,
                    val flowActivityDataContext: FlowExecutionContext,
                    threadPoolContext: ThreadPoolContext,
                    flowActivityResultNotifier: IFlowActivityResultNotifier,
                    flowActivityResultHandlerManager: FlowActivityResultHandlerManager,
                    openApiProps: OpenApiProps
                  ) extends Loggable {
  val id: lang.Long = flowInstancePO.getId

  implicit val ec: ExecutionContextExecutor = threadPoolContext.getActivityExecutor

  def onMessage(msg: FlowMessage): Unit = {

    log.info("Flow schema: {} loaded.", flowSchema.id)

    initOnMessage(msg)

    triggerMsg(msg)
  }

  private def initOnMessage(msg: FlowMessage): Unit = {
    flowInstancePO.addMessage(msg)
    if (msg.getType == FlowMessageType.EVENT) {
      flowSchema.eventTopographyMap.get(msg.getName).map(activities => {
        activities.foreach(activity => {
          flowInstancePO.addTodoActivityState(activity.name, () => new FlowActivityStatePO(activity.name))
        })
      })
    }
  }

  def triggerMsg(flowMessage: FlowMessage): Unit = {
    var isUpdated = true

    while (isUpdated) {
      val activityKeys = flowInstancePO.getTodoActivityKeys.asScala.toSet
      val preparedActivities = ListBuffer[AbstractFlowActivity]()
      val sortedActivities = topologicalSortTodoActivities(flowSchema, activityKeys)
      if (log.isDebugEnabled) {
        log.debug("Activities sorted: {}", sortedActivities.map(_.name).mkString("[", ",", "]"))
      }
      for (activity <- sortedActivities) {
        if (activity.canDo(flowInstancePO, flowInstancePO.getToDoActivityState(activity.name).get())(flowMessage)) {
          preparedActivities += activity
        }
      }
      if (log.isDebugEnabled) {
        log.debug("Prepare to do activities: {}", preparedActivities.map(_.name).mkString("[", ",", "]"))
      }
      //TODO 在重新发起流程时，确保有依赖关系的activity不能并行执行
      val filteredActivities = filterActivitiesTodo(preparedActivities)
      if (log.isDebugEnabled) {
        log.debug("Filtered & Begin to do activities: {}", filteredActivities.map(_.name).mkString("[", ",", "]"))
      }

      doActivities(filteredActivities, flowMessage)
      isUpdated = preparedActivities.nonEmpty
      if (log.isDebugEnabled) {
        if (!isUpdated) {
          log.debug("End to do activities, will not trigger.")
        } else {
          log.debug("End to do activities, will trigger again.")
        }
      }
    }
  }

  def filterActivitiesTodo(preparedActivities: ListBuffer[AbstractFlowActivity]): mutable.Buffer[AbstractFlowActivity] = {
    val activityKeys = preparedActivities.map(_.name)
    //当前节点的所有前继节点都不在todo activity list里面，则可以todo
    preparedActivities.filter(activity => {
      activityKeys.intersect(activity.allDependActivityList).isEmpty
    })
  }

  def doActivities(activitiesToDo: mutable.Buffer[AbstractFlowActivity], flowMessage: FlowMessage): Unit = {
    if (1 == activitiesToDo.size) {
      val activity = activitiesToDo.last
      try {
        val afterActivity = executeActivity(activity, flowMessage)
        if (log.isDebugEnabled) {
          log.debug(s"Activity: [${activity.name}] complete.")
        }
        afterActivity.nextAlternativeActivityKeysWhenDone.foreach(actName =>
          flowInstancePO.addTodoActivityState(new FlowActivityStatePO(actName)))
      } catch {
        case exception: Exception =>
          log.warn(s"Activity: [${activity.name}] error.**, flow message: $flowMessage ")
          throw exception
      }
    } else {
      val activityFutures = activitiesToDo.map(activity => activity -> Future({
        executeActivity(activity, flowMessage)
      }).andThen(
        {
          case Success(statefulActivity) => {
            if (log.isDebugEnabled) {
              log.debug(s"Activity: [${activity.name}] complete.")
            }

            statefulActivity.nextAlternativeActivityKeysWhenDone.foreach(actName =>
              flowInstancePO.addTodoActivityState(new FlowActivityStatePO(actName)))
          }
          case Failure(exception) => {
            log.error(s"Activity: [${activity.name}] error.**, flow message: $flowMessage ", exception)
          }
        }
      )).toMap
      Await.result(Future.sequence(activityFutures.values),
        Duration(openApiProps.getFlow.getActivity.getTimeout, TimeUnit.MILLISECONDS))
    }
  }

  def executeActivity(activity: AbstractFlowActivity, flowMessage: FlowMessage): AbstractFlowActivity = {
    val statefulActivity = activity.clone().asInstanceOf[AbstractFlowActivity]
    val statePO = flowInstancePO.getToDoActivityState(activity.name).get()
    logTime(s"Activity: [${activity.name}]") {
      statefulActivity.loadState(statePO)
      statePO.setStartTime(new Date())
      val result = statefulActivity.onStarted(flowActivityDataContext, flowMessage)
      statefulActivity.saveState(statePO)
      val resultHandler = flowActivityResultHandlerManager.getHandlerOfResultType(result.getClass)
      if (resultHandler.handle(statefulActivity, result, flowMessage)) {
        flowActivityResultNotifier.onActivityDone(statefulActivity, flowMessage, result)
        flowInstancePO.addFinishedActivityState(statePO)
        flowInstancePO.removeTodoActivityState(statePO.getActivityName)
      }
    }
    statefulActivity
  }

  def topologicalSortTodoActivities(flowSchema: AbstractFlowSchemaAggregation, activityKeys: Set[String]) = {
    type Pair = (AbstractFlowActivity, mutable.Buffer[String])

    def getFilteredDependActivityList(depends: Array[String]) = depends.filter(activityKeys.contains).toBuffer

    val outList = ListBuffer[AbstractFlowActivity]()
    val todoActivities =
      activityKeys.map(flowSchema.getFlowActivity).map(x => x -> getFilteredDependActivityList(x.allDependActivityList)).toBuffer

    while (!todoActivities.isEmpty) {
      var isUpdated = false
      val toDoActivityPairsTobeRemoved = ListBuffer[Pair]()
      for (toDoActivityPair <- todoActivities) {
        val (activity, dependsX) = toDoActivityPair
        if (dependsX.isEmpty) {
          outList += activity
          for ((_, dependsY) <- todoActivities) {
            dependsY -= activity.name
          }
          toDoActivityPairsTobeRemoved += toDoActivityPair
          isUpdated = true
        }
      }
      todoActivities --= toDoActivityPairsTobeRemoved
      if (!isUpdated) throw new FlowNotDAGException(KnowledgeErrorCode.flowSchema_not_dag, flowSchema.content.toJsonString)
    }
    outList
  }
}
