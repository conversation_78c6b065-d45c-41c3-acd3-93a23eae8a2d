package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.storage

import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.infra.application.service.IFlowDataRecordService
import com.zatech.genesis.openapi.platform.knowledge.procedure.api.persistence.IProcedureDataReceiver
import com.zatech.octopus.component.mq.extension.IdempotentConsumer
import com.zatech.octopus.module.cache.ICacheProvider
import com.zatech.octopus.module.mq.core.annotation.IConsumer
import com.zatech.octopus.module.mq.core.tools.SerializerUtil
import com.zhongan.msg.api.consumer.{AckAction, ReceiveRecord}
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @create 2021/12/2 7:32 下午
 * */
@IConsumer("flowDataConsumer")
class KafkaFlowDataReceiver @Autowired()(
                                          flowDataRecordService: IFlowDataRecordService,
                                          cacheClient: ICacheProvider
                                        ) extends IdempotentConsumer[FlowDataRecord, Boolean](cacheClient) with IProcedureDataReceiver {

  override def execute(key: String, data: FlowDataRecord): Boolean = {
    //根据instanceId做幂等，如果instanceId已经被保存过，后续进来的msg不做更新
    flowDataRecordService.saveOrUpdateByDuplicateKey(data);

  }


  override def convertToValue(record: ReceiveRecord[_]): FlowDataRecord = {
    SerializerUtil.parse(record.getMessage, getProperties.getSerializerType, classOf[FlowDataRecord])
  }

  override def respSelector(result: Boolean): AckAction = {
    if (result) AckAction.Commit else AckAction.Reconsume
  }
}
