package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.strategyExecutor

import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.{AbstractFlowSchemaAggregation, ProcedureContainer}
import org.springframework.stereotype.Component

import java.util

/**
 * @author:zexin.lin
 * @data: 2023/2/22
 */

/**
 * 这里不应该访问到infra层的东西
 */
@Component class DefaultStrategyExecutor extends ProcedureActivityStrategyExecutor {

  override def name = "system"

  override def doFilter(procedureContainers: List[ProcedureContainer]): List[ProcedureContainer] = procedureContainers

  override def checkSyntax(activityConfiguration: util.Map[String, AnyRef], flowSchemaAggregation: AbstractFlowSchemaAggregation) = {
  }
}
