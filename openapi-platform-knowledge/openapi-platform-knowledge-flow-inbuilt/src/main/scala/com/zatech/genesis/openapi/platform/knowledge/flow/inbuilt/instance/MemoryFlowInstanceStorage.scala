package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.instance

import com.github.benmanes.caffeine.cache.{Cache, Caffeine}
import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.knowledge.flow.api.IFlowInstancePO
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.IFlowInstanceStorage
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.InbuiltFlowInstancePO

import java.lang
import java.util.Optional
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @create 2024/7/2 19:29
 * 单机运行，使用memory
 * */
class MemoryFlowInstanceStorage(openApiProps: OpenApiProps) extends IFlowInstanceStorage {

  private val flowInstanceCache: Cache[Long, IFlowInstancePO] =
    Caffeine.newBuilder
      .expireAfterWrite(openApiProps.getFlow.getTimeout, TimeUnit.MILLISECONDS)
      .maximumSize(2000).build[Long, IFlowInstancePO]

  override def load(flowInstanceId: lang.Long): Optional[IFlowInstancePO] =
    Optional.ofNullable(flowInstanceCache.getIfPresent(flowInstanceId))

  override def save(flowInstancePO: IFlowInstancePO): Unit = {
    flowInstanceCache.put(flowInstancePO.getId, flowInstancePO)
  }

  override def loadOrInit(flowInstanceId: lang.Long): IFlowInstancePO = {
    load(flowInstanceId).orElseGet(() =>
      new InbuiltFlowInstancePO().setId(flowInstanceId))
  }
}
