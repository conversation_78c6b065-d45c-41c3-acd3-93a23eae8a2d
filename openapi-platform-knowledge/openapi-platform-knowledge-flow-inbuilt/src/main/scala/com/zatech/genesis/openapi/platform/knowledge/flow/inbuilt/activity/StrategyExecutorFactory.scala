package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.strategyExecutor.{DefaultStrategyExecutor, ProcedureActivityStrategyExecutor}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import scala.jdk.CollectionConverters._


@Component
class StrategyExecutorFactory(
                               @Autowired executors: java.util.List[ProcedureActivityStrategyExecutor],
                               @Autowired defaultExecutor: DefaultStrategyExecutor
                             ) {

  val filterMap = executors.asScala.map(filter => filter.name -> filter).toMap

  def getExecutor(strategyMode: String) = filterMap.getOrElse(strategyMode, defaultExecutor)
}




