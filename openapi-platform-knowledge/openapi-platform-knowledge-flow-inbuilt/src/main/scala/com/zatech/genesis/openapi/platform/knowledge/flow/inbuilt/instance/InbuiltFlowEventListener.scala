package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.instance

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowExecutionContext, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.{FlowData, IFlowDataSaver, IFlowInstanceStorage}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowActivityResultNotifier, IFlowEventListener, IFlowLifecycle}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt._
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.result.FlowActivityResultHandlerManager
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.error.ErrorHolder
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.InbuiltFlowInstancePO
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema.FlowSchemaAggregationFactory
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema.spec.SpecFlowSchemaAggregationFactory
import com.zatech.genesis.openapi.platform.share.json.JsonMap
import com.zatech.genesis.openapi.platform.share.{Loggable, ThreadPoolContext}

import scala.util.{Failure, Success, Try}

/**
 * @Author: felton
 * @Date: 2021/10/24 上午10:04
 */
class InbuiltFlowEventListener(
                                flowLocker: IFlowLocker,
                                flowInstanceStorage: IFlowInstanceStorage,
                                flowActivityResultNotifier: IFlowActivityResultNotifier,
                                flowActivityResultHandlerManager: FlowActivityResultHandlerManager,
                                flowSchemaFactory: FlowSchemaAggregationFactory,
                                flowDataSaver: IFlowDataSaver,
                                threadPoolContext: ThreadPoolContext,
                                openApiProps: OpenApiProps,
                                specFlowSchemaAggregationFactory: SpecFlowSchemaAggregationFactory,
                                flowLifecycle: IFlowLifecycle
                              ) extends IFlowEventListener with Loggable {

  private val LockExpireSeconds = 5

  override def onMsgReceived(context: FlowExecutionContext, msg: FlowMessage): Unit = {

    val flowInstanceId = msg.getFlowInstanceId
    var flowInstancePO: InbuiltFlowInstancePO = null

    def buildFlowData(body: JsonMap): FlowData = {
      new FlowData(flowInstanceId, context.getStartTime, context.getRequestBody, body, context.getApiPath)
    }

    var shouldExecuteFlow = true
    try {
      flowLocker.lockByWaiting(flowInstanceId.toString, LockExpireSeconds)
      flowInstancePO = flowInstanceStorage.loadOrInit(flowInstanceId).asInstanceOf[InbuiltFlowInstancePO]
      val flowSchema: AbstractFlowSchemaAggregation = if (context.getInstanceAggregation.newVersionFlag) {
        specFlowSchemaAggregationFactory.loadFlowSchema(context.getInstanceAggregation)
      } else flowSchemaFactory.loadFlowSchema(msg.getFlowSchemaId)

      /**
       * 本身应该在这里重新从db里面load数据构建Context，这样可以实现上下游无状态
       * TODO 现在先不实现
       */
      val flowInstance = new FlowInstance(
        flowSchema = flowSchema,
        flowInstancePO = flowInstancePO,
        flowActivityDataContext = context,
        threadPoolContext = threadPoolContext,
        flowActivityResultNotifier = flowActivityResultNotifier,
        flowActivityResultHandlerManager = flowActivityResultHandlerManager,
        openApiProps = openApiProps
      )

      log.info("Trigger onMessage of flow instance: {}", flowInstance.id)

      shouldExecuteFlow = flowLifecycle.beforeExecuteFlow(context)
      if(shouldExecuteFlow) {
        flowInstance.onMessage(msg)
        flowLifecycle.afterFlowExecuteSucceeded(context)
      }
      flowSchema.saveSuccess(context, buildFlowData(context.getOutputData))
    } catch { case e: Exception =>
      //当执行life cycle报错时要切换成这个错
      val targetException = if(shouldExecuteFlow) {
        Try{flowLifecycle.afterFlowExecuteFailed(context, e)} match {
          case Success(respContextOpt) => respContextOpt match {
            case Some(respContext) => respContext.getException
            case _ => e
          }
          case Failure(exception) => exception
        }
      } else e

        if (targetException != e) log.error(s"Run flow instance: $flowInstanceId error, origin exception: ", e)
        else log.error(s"Run flow instance: $flowInstanceId error}", e)

      val errorHolder = new ErrorHolder(targetException)
      context.getResponseInfo.setHttpStatus(errorHolder.httpStatus)
      flowDataSaver.saveErrorData(context, buildFlowData(errorHolder.toMap))
      throw targetException
    } finally {
      flowInstanceStorage.save(flowInstancePO)
      flowLocker.releaseLock(flowInstanceId.toString)
    }
  }
}
