package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.run

import com.zatech.genesis.openapi.platform.domain.IInstanceAggregation
import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord
import com.zatech.genesis.openapi.platform.knowledge.flow.api.enums.FlowMessageType
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowExecutionContext, FlowMessage, FlowParam}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.{IFlowDataLoader, IFlowInstanceStorage}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowEventListener, IFlowInstancePO, IFlowRunner}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.ParamValidateUtil
import com.zatech.genesis.openapi.platform.knowledge.model.exception.KnowledgeErrorCode
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApiSchemaValidator
import com.zatech.genesis.openapi.platform.share.TenantSupport
import com.zatech.genesis.openapi.platform.share.json.JsonMap
import com.zatech.genesis.openapi.platform.spec.inbuilt.domain.instance.ISpecInstanceAggregation
import com.zatech.genesis.portal.toolbox.exception.CommonException
import com.zatech.genesis.portal.toolbox.share.Loggable

import java.lang
import java.util.Optional

/**
 * <AUTHOR>
 * @create 2024/9/9 14:15
 * */
class TrulyFlowRunner(
                       flowEventListener: IFlowEventListener,
                       flowInstanceStorage: IFlowInstanceStorage,
                       flowDataLoader: IFlowDataLoader,
                       tenantSupport: TenantSupport
                      ) extends IFlowRunner with Loggable {

  override def runFlow(instanceAggregation: IInstanceAggregation,
                       flowInstancePO: IFlowInstancePO,
                       param: FlowParam): FlowExecutionContext = {

    val startMessage = locally {
      val message = new FlowMessage
      message.setName("Start")
      message.setType(FlowMessageType.EVENT)
      message.setFlowInstanceId(flowInstancePO.getId)
      message
    }

    if(! instanceAggregation.newVersionFlag) {
      instanceAggregation.boundFlowSchemaEntityOpt match {
        case Some(flowSchemaEntity) => startMessage.setFlowSchemaId(flowSchemaEntity.getId)
        case _ => throw CommonException.byErrorAndParams(KnowledgeErrorCode.flow_schema_not_found, flowInstancePO.getId)
      }
    }
    flowInstanceStorage.save(flowInstancePO)

    //支持自主校验的Validator
    val schemaValidator = new IOpenApiSchemaValidator {
      override def validateRequestBody(requestBody: JsonMap): Unit = {
        instanceAggregation match {
          case specAgg: ISpecInstanceAggregation => specAgg.apiNode.validateRequest(requestBody)
          case _ => // do nothing, not an API node
        }
      }

      override def validateResponseBody(requestBody: JsonMap, validationSchemaName: String): Unit = {
        instanceAggregation match {
          case specAgg: ISpecInstanceAggregation =>
            specAgg.apiNode.validateRequest(requestBody, validationSchemaName)
          case _ => // do nothing, not an API node
        }
      }
    }

    val flowDataContext =
      new FlowExecutionContext(
        flowInstancePO.getId, tenantSupport.getTenant,
        instanceAggregation, param, schemaValidator)

    ParamValidateUtil.validateAndAutoFill(param.getRequestInfo, instanceAggregation.scenarioApiConfigList)

    logTime(s"flow: ${flowInstancePO.getId} by message: ${startMessage}") {
      flowEventListener.onMsgReceived(flowDataContext, startMessage)
    }

    flowDataContext
  }

  override def validateFlow(flowContent: JsonMap): Unit = {}

  override def getRecord(flowInstanceId: lang.Long): Optional[FlowDataRecord] = {
    flowDataLoader.loadRecord(flowInstanceId)
  }
}
