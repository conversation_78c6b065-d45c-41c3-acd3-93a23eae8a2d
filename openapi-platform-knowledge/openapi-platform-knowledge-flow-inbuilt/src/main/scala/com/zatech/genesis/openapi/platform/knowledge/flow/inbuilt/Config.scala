package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.common.OpenApiProps
import com.zatech.genesis.openapi.platform.knowledge.flow.api.storage.{IFlowDataSaver, IFlowInstanceStorage}
import com.zatech.genesis.openapi.platform.knowledge.flow.api.{IFlowActivityResultNotifier, IFlowEventListener, IFlowLifecycle}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.InbuiltFlowActivityResultNotifier
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity.result.FlowActivityResultHandlerManager
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.instance.{InbuiltFlowEventListener, MemoryFlowInstanceStorage}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema.FlowSchemaAggregationFactory
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.schema.spec.SpecFlowSchemaAggregationFactory
import com.zatech.genesis.openapi.platform.share.ThreadPoolContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.{Bean, Configuration}
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.serializer.RedisSerializer

import java.lang

/**
 * @Author: felton
 * @Date: 2021/10/24 上午10:05
 */
@Configuration("flowInbuiltConfig")
class Config {

  @Bean
  def flowEventListener(flowSchemaFactory: FlowSchemaAggregationFactory,
                        flowInstanceStorage: IFlowInstanceStorage,
                        flowLocker: IFlowLocker,
                        flowActivityResultNotifier: IFlowActivityResultNotifier,
                        flowActivityResultHandlerManager: FlowActivityResultHandlerManager,
                        flowDataSaver: IFlowDataSaver,
                        threadPoolContext: ThreadPoolContext,
                        openApiProps: OpenApiProps,
                        specFlowSchemaAggregationFactory: SpecFlowSchemaAggregationFactory,
                        flowLifecycle: IFlowLifecycle
                       ): IFlowEventListener =
    new InbuiltFlowEventListener(
      flowLocker = flowLocker,
      flowInstanceStorage = flowInstanceStorage,
      flowSchemaFactory = flowSchemaFactory,
      flowActivityResultNotifier = flowActivityResultNotifier,
      flowActivityResultHandlerManager = flowActivityResultHandlerManager,
      flowDataSaver = flowDataSaver,
      threadPoolContext = threadPoolContext,
      openApiProps = openApiProps,
      specFlowSchemaAggregationFactory = specFlowSchemaAggregationFactory,
      flowLifecycle = flowLifecycle
    )

  @Bean
  def flowActivityResultNotifier(
                                  @Autowired applicationEventPublisher: ApplicationEventPublisher,
                                  @Autowired threadPoolContext: ThreadPoolContext
                                ): IFlowActivityResultNotifier =
    new InbuiltFlowActivityResultNotifier(applicationEventPublisher, threadPoolContext)

  @Bean
  def flowLocker(): IFlowLocker = new DefaultFlowLocker

  @Bean
  def flowInstanceStorage(openApiProps: OpenApiProps): IFlowInstanceStorage = new MemoryFlowInstanceStorage(openApiProps)

  @Bean(Array("idempotentRedisTemplate"))
  def redisTemplate(redisConnectionFactory: RedisConnectionFactory): RedisTemplate[String, java.lang.Long] = {
    val redisTemplate = new RedisTemplate[String, java.lang.Long]
    redisTemplate.setConnectionFactory(redisConnectionFactory)
    //自定义序列化方式
    redisTemplate.setValueSerializer(new RedisSerializer[java.lang.Long]{
      override def serialize(value: lang.Long): Array[Byte] = {
        if(value != null) value.toString.getBytes("UTF-8") else null
      }
      override def deserialize(bytes: Array[Byte]): lang.Long = {
        if(bytes == null) null else new String(bytes).toLong
      }
    })
    redisTemplate
  }
}
