package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.api.resource.meta.flow.{FlowDefinition, FlowSyntaxCheckResult, ProcedureDefinition}
import com.zatech.genesis.openapi.platform.domain.meta.global.NamespaceEntity
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.schema.factory.ProcedureMetaAggregationFactory
import com.zatech.genesis.openapi.platform.infra.global.service.IDomainService
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.entity.ProcedureMeta
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.model.ProcedureQueryParam
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowSyntaxValidator
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowSyntaxValidator.ProcedureConfiguration
import com.zatech.genesis.openapi.platform.knowledge.procedure.ProcedureManager
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum
import com.zatech.genesis.openapi.platform.share.json.{JsonMap, JsonParser, Jsonable}
import com.zatech.genesis.portal.toolbox.share.ImplicitConverters._
import com.zatech.octopus.component.sleuth.TraceOp
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.Optional

@Component
class DefaultFlowValidator(
                            @Autowired val procedureManager: ProcedureManager,
                            @Autowired val procedureMetaAggregationFactory: ProcedureMetaAggregationFactory,
                            @Autowired val domainService: IDomainService
                          ) extends IFlowSyntaxValidator {


  override def check(nameSpace: NamespaceEntity, flowContent: JsonMap): FlowSyntaxCheckResult = {
    val syntax = JsonParser.fromJson(flowContent.toJsonString, classOf[FlowDefinition])

    //查找procedure
    def foundProcedure(procedureSyntax: ProcedureDefinition): ProcedureConfiguration = {
      val queryParam: ProcedureQueryParam = new ProcedureQueryParam
      queryParam.setName(procedureSyntax.getName)
      queryParam.setType(ProcedureMetaTypeEnum.valueOf(procedureSyntax.getType))
      queryParam.setDomain(procedureSyntax.getDomain)
      queryParam.setTag(procedureSyntax.getTag)
      queryParam.setTenantCode(TraceOp.getTenant)
      queryParam.setNamespace(nameSpace.name)
      val metaAggregationOpt = procedureMetaAggregationFactory.getDomainOpt(queryParam)
      //再查找其实现
      val clzOpt =
        procedureManager
          .findProcedureMetaHolderOpt(queryParam)
          .map(_.procedure.asInstanceOf[AbstractProcedure[Jsonable, Jsonable]])
      new ProcedureConfiguration(clzOpt, metaAggregationOpt.isPresent)
    }

    //新建procedure
    def createProcedure(procedureSyntax: ProcedureDefinition): Unit = {
      domainService.queryDomainByName(nameSpace.getId, procedureSyntax.getDomain)
        .ifPresent(domain => {
          val meta: ProcedureMeta = new ProcedureMeta
          meta.setName(procedureSyntax.getName)
          meta.setType(procedureSyntax.getType)
          meta.setTag(procedureSyntax.getTag)
          meta.setDomainId(domain.getId)
          //默认塞值
          meta.setTitle("auto genertated procedure meta")
          meta.setConfig(new JsonMap())
          meta.setContent(new JsonMap())
          meta.setDescription(s"auto genertated procedure meta for ${procedureSyntax.getName}")
          meta.setProductVersion("auto-generated")
          meta.setCreator("auto-generated")
          procedureMetaAggregationFactory.createDomain(meta)
        })
    }

    val flowSyntaxChecker = new FlowSyntaxValidator(syntax, Optional.of(foundProcedure),Optional.of(createProcedure))
    flowSyntaxChecker.checkSyntax()
    flowSyntaxChecker.result()
  }


}