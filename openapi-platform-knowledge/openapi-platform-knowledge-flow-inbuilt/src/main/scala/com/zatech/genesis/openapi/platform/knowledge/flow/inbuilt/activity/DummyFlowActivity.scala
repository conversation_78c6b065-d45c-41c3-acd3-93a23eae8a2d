package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.activity

import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.{FlowActivityResult, FlowActivitySuccessResult, FlowExecutionContext, FlowMessage}
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.AbstractFlowSchemaAggregation
import com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt.model.FlowActivityStatePO
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

/**
 * @Author: felton
 * @Date: 2021/10/23 下午10:51
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
class DummyFlowActivity(
                         override val flowSchema: AbstractFlowSchemaAggregation,
                         content: FlowActivity.JStringMap,
                       ) extends AbstractFlowActivity(flowSchema, content) {
  override def loadState(statePO: FlowActivityStatePO): Unit = {
    log.info("DummyFlowActivity loadState.")
  }

  override def saveState(statePO: FlowActivityStatePO): Unit = {
    log.info("DummyFlowActivity saveState.")
  }

  override def onStarted(context: FlowExecutionContext, msg: FlowMessage): FlowActivityResult = {
    log.info("DummyFlowActivity onStarted.")
    new FlowActivitySuccessResult(this.name)
  }

  override def checkSyntax(): Unit = {}
}
