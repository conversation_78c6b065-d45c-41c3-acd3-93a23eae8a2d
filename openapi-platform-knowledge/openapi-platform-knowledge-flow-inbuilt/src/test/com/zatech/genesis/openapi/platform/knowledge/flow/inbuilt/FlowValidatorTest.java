package com.zatech.genesis.openapi.platform.knowledge.flow.inbuilt;

import com.zatech.genesis.openapi.platform.share.tools.ResourceUtil;

import com.zatech.genesis.openapi.platform.api.resource.meta.flow.FlowDefinition;
import com.zatech.genesis.openapi.platform.knowledge.flow.api.model.FlowSyntaxValidator;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import java.util.Objects;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FlowValidatorTest {

    @Test
    public void flowValidatorTest() {
        var flowSyntax = JsonParser.fromJson(ResourceUtil.readUtf8Str("DirectMappingFlowSample.json"), FlowDefinition.class);
        FlowSyntaxValidator checker = new FlowSyntaxValidator(flowSyntax, Optional.empty(), Optional.empty());
        checker.checkSyntax();
        Assertions.assertTrue(Objects.nonNull(checker.result()));
    }

}
