/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.infra.workOder.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zatech.genesis.openapi.platform.infra.CanModifyBaseEntity;
import com.zatech.octopus.module.mybatis.base.dao.AnnotationDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/6/1 16:06
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("work_order_comment")
@AnnotationDO(seqName = "work_order_comment")
public class WorkOrderComment extends CanModifyBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工单id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 评论信息
     */
    @TableField("comment")
    private String comment;


}
