package com.zatech.genesis.openapi.platform.infra.exception;


import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.enums.ErrorTypeEnum;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.BusinessError$500;
import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.ValidationError$400;

public enum InfraErrorCode implements IErrorCode {


    @StandardErrorCode(status = ValidationError$400, type = ErrorTypeEnum.internal)
    condition_not_in_whiteList,

    @StandardErrorCode(status = BusinessError$500, type = ErrorTypeEnum.internal)
    model_meta_unknown;

    @Override
    public String getModuleName() {
        return "infra";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
