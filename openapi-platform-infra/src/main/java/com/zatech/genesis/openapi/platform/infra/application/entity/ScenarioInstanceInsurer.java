package com.zatech.genesis.openapi.platform.infra.application.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zatech.genesis.openapi.platform.infra.BaseEntity;
import com.zatech.octopus.module.mybatis.base.dao.AnnotationDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/8/22 15:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(ScenarioInstanceInsurer.ID_KEY)
@AnnotationDO(seqName = ScenarioInstanceInsurer.ID_KEY)
public class ScenarioInstanceInsurer extends BaseEntity {

    public static final String ID_KEY = "scenario_instance_insurer";

    @TableField("insurer_code")
    private String insurerCode;

}
