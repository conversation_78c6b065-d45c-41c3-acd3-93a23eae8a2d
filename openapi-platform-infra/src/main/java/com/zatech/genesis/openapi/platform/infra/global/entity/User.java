/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.platform.infra.global.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zatech.genesis.openapi.platform.infra.CanModifyBaseEntity;
import com.zatech.octopus.module.mybatis.base.dao.AnnotationDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
@AnnotationDO(seqName = "user")
public class User extends CanModifyBaseEntity {

    private String username;

    private String password;

    private String email;

    private String mobile;

    private String authorities;

    private String locked;

    private String enabled;

}
