/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.infra.workOder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zatech.genesis.openapi.platform.infra.workOder.entity.WorkOrderLabelRelation;
import com.zatech.genesis.openapi.platform.infra.workOder.mapper.WorkOrderLabelRelationMapper;
import com.zatech.genesis.openapi.platform.infra.workOder.service.IWorkOrderLabelRelationService;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/6/5 16:58
 **/
@Service
public class WorkOrderLabelRelationServiceImpl extends ServiceImpl<WorkOrderLabelRelationMapper, WorkOrderLabelRelation> implements IWorkOrderLabelRelationService {


    @Override
    public void deleteByOrderId(Long orderId) {
        baseMapper.delete(new QueryWrapper<WorkOrderLabelRelation>().eq("order_id", orderId));
    }

}
