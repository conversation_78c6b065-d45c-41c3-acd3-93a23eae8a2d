/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.infra.apiview.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zatech.genesis.openapi.platform.infra.apiview.entity.ApiViewSchema;
import com.zatech.genesis.openapi.platform.infra.apiview.mapper.ApiViewSchemaMapper;
import com.zatech.genesis.openapi.platform.infra.apiview.service.IApiViewSchemaService;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/15 14:50
 **/
@Service
@Slf4j
public class ApiViewSchemaServiceImpl extends ServiceImpl<ApiViewSchemaMapper, ApiViewSchema> implements IApiViewSchemaService {


    @Override
    public List<ApiViewSchema> listByGroupId(Long groupId) {
        return baseMapper.selectList(new QueryWrapper<ApiViewSchema>().eq("group_id", groupId));
    }

    @Override
    public boolean deleteByGroupId(Long groupId) {
        return baseMapper.delete(new QueryWrapper<ApiViewSchema>().eq("group_id", groupId)) > 0;
    }

    @Override
    public boolean updateWeightById(Long weight, Long id, Long groupId) {
        return baseMapper.updateWeightById(weight, id) > 0;
    }

    @Override
    public ApiViewSchema selectById(Long id) {
        return baseMapper.selectById(id);
    }

}
