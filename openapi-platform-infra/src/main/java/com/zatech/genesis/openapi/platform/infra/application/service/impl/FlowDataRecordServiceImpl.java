package com.zatech.genesis.openapi.platform.infra.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zatech.genesis.openapi.platform.api.resource.business.request.SandboxLogPageRequest;
import com.zatech.genesis.openapi.platform.infra.application.entity.FlowDataRecord;
import com.zatech.genesis.openapi.platform.infra.application.mapper.FlowDataRecordMapper;
import com.zatech.genesis.openapi.platform.infra.application.service.IFlowDataRecordService;
import com.zatech.genesis.openapi.platform.infra.exception.InfraErrorCode;
import com.zatech.genesis.openapi.platform.share.TraceSupport;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.model.OrderByWhiteListConstant;
import com.zatech.genesis.openapi.platform.share.model.PageParam;

import java.util.Objects;
import java.util.Optional;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程数据记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Service
public class FlowDataRecordServiceImpl extends ServiceImpl<FlowDataRecordMapper, FlowDataRecord> implements IFlowDataRecordService {

    @Override
    public int updateByFlowInstanceId(Long flowInstanceId, FlowDataRecord record) {

        return baseMapper.update(record, new QueryWrapper<FlowDataRecord>().eq("flow_instance_id", flowInstanceId));
    }

    @Override
    public Optional<FlowDataRecord> selectByFlowInstanceId(Long flowInstanceId) {

        return Optional.ofNullable(baseMapper.selectByFlowInstanceId(flowInstanceId));
    }

    @Override
    public IPage<FlowDataRecord> pageSelectByCondition(PageParam<SandboxLogPageRequest> request) {
        QueryWrapper<FlowDataRecord> wrapper = new QueryWrapper<>();
        Optional.ofNullable(request.getCondition()).ifPresent(condition -> {
            wrapper.lambda()
                .eq(Objects.nonNull(condition.getSuccessFlag()), FlowDataRecord::getSuccessFlag, condition.getSuccessFlag())
                .eq(Objects.nonNull(condition.getFlowInstanceId()), FlowDataRecord::getFlowInstanceId, condition.getFlowInstanceId())
                .eq(Objects.nonNull(condition.getApiPath()), FlowDataRecord::getPath, condition.getApiPath())
                .ge(Objects.nonNull(condition.getStartDate()), FlowDataRecord::getGmtStarted, condition.getStartDate())
                .le(Objects.nonNull(condition.getEndDate()), FlowDataRecord::getGmtEnded, condition.getEndDate());
        });
        wrapper.eq("channel_code", TraceSupport.getAppIdOrNull());
        if (OrderByWhiteListConstant.sandboxOrderByList.contains(request.getOrderBy())) {
            wrapper.orderBy(Objects.nonNull(request.getOrderBy()), request.getOrderByAsc(), request.getOrderBy());
        } else if (request.getOrderBy() != null) {
            throw OpenApiException.by(InfraErrorCode.condition_not_in_whiteList).build();
        }

        return baseMapper.selectPage(new Page<>(request.getPageIndex() + 1L, request.getPageSize()), wrapper);
    }

    @Override
    public boolean saveOrUpdateByDuplicateKey(FlowDataRecord entity) {
        return baseMapper.insertOnDuplicateKey(entity);
    }

}
