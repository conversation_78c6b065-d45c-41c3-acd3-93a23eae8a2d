package com.zatech.genesis.openapi.platform.infra.application.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioInstanceInsurer;
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioInstanceInsurerMapper;
import com.zatech.genesis.openapi.platform.infra.application.service.IScenarioInstanceInsurerService;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/9/20 10:32
 */
@Service
public class ScenarioInstanceInsurerImpl extends ServiceImpl<ScenarioInstanceInsurerMapper, ScenarioInstanceInsurer> implements IScenarioInstanceInsurerService {

}
