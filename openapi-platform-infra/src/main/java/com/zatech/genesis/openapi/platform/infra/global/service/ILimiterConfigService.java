package com.zatech.genesis.openapi.platform.infra.global.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zatech.genesis.openapi.platform.infra.global.entity.LimiterConfig;
import com.zatech.genesis.openapi.platform.share.enums.LimiterRuleTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/3/13 10:56
 **/
public interface ILimiterConfigService extends IService<LimiterConfig> {

    List<LimiterConfig> listByAppId(String appId);

    List<LimiterConfig> listByAppIdAndRuleType(String appId, LimiterRuleTypeEnum ruleType);
}
