package com.zatech.genesis.openapi.platform.infra.application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookEventSubscription;
import com.zatech.genesis.openapi.platform.infra.application.entity.WebHookRegistry;

import java.util.List;

/**
 * <p>
 * The web hook events subscription. 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface IWebHookEventSubscriptionService extends IService<WebHookEventSubscription> {

    List<WebHookEventSubscription> listByWebHookRegistryId(Long registryId);

    List<WebHookRegistry> listEndpointsByEvent(List<String> eventName, String appId);

    boolean eventIsSubscribed(String eventName);

    long unsubscribeEvents(List<Long> registryIds, List<String> events);

    long deleteByRegistryId(Long registryId);

}
