package com.zatech.genesis.openapi.platform.infra.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zatech.genesis.openapi.platform.infra.application.entity.ScenarioSchema;
import com.zatech.genesis.openapi.platform.infra.application.mapper.ScenarioSchemaMapper;
import com.zatech.genesis.openapi.platform.infra.application.service.IScenarioSchemaService;
import com.zatech.genesis.openapi.platform.infra.exception.InfraErrorCode;
import com.zatech.genesis.openapi.platform.share.enums.ModelMetaDirectionEnum;
import com.zatech.genesis.openapi.platform.share.enums.ScenarioSchemaBindingEnum;
import com.zatech.genesis.openapi.platform.share.enums.ScenarioSchemaOriginEnum;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 场景模式表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
public class ScenarioSchemaServiceImpl extends ServiceImpl<ScenarioSchemaMapper, ScenarioSchema> implements IScenarioSchemaService {

    @Override
    public List<ScenarioSchema> listByGroupId(Long groupId) {

        return baseMapper.selectList(new QueryWrapper<ScenarioSchema>().eq("group_id", groupId));
    }

    @Override
    public int updateModelMetaId(Long scenarioSchemaId, Long modelMetaId, ModelMetaDirectionEnum direction) {

        switch (direction) {
            case IN:
                return baseMapper.updateInputModelMeta(scenarioSchemaId, modelMetaId);
            case OUT:
                return baseMapper.updateOutputModelMeta(scenarioSchemaId, modelMetaId);
            default:
                throw OpenApiException.by(InfraErrorCode.model_meta_unknown).params(direction.name()).build();
        }
    }

    @Override
    public Optional<ScenarioSchema> selectByFlowInstanceId(Long flowInstanceId) {

        return Optional.ofNullable(baseMapper.selectByFlowInstanceId(flowInstanceId));
    }

    @Override
    public int updateDefaultFlowSchema(Long scenarioSchemaId, Long defaultFlowSchema) {

        return baseMapper.updateDefaultFlowSchema(scenarioSchemaId, defaultFlowSchema);
    }

    @Override
    public Optional<ScenarioSchema> selectByName(Long groupId, String scenarioSchemaName) {

        Map param = Map.of("group_id", groupId, "name", scenarioSchemaName);
        return Optional.ofNullable(baseMapper.selectOne(new QueryWrapper<>().allEq(param)));
    }

    @Override
    public Optional<ScenarioSchema> selectBy(String namespaceName, String scenarioSchemaGroupName,
                                             String tenantCode, String scenarioSchemaName) {

        return Optional.ofNullable(baseMapper.selectByNamespaceTenantSchemaName(namespaceName, scenarioSchemaGroupName, tenantCode,
            scenarioSchemaName));
    }

    @Override
    public int removeDefaultFlowWhenFlowIsDelete(Long flowSchemaId) {
        return baseMapper.removeDefaultFlowWhenFlowOnDelete(flowSchemaId);
    }

    @Override
    public List<ScenarioSchema> selectBy(ScenarioSchemaOriginEnum inputOrigin, ScenarioSchemaOriginEnum outputOrigin,
                                         ScenarioSchemaBindingEnum binding, String scenarioSchemaName) {

        return baseMapper.selectList(new QueryWrapper<ScenarioSchema>().eq("input_origin", inputOrigin.name()).eq("output_origin",
            outputOrigin.name()).eq("binding",
            binding.name()).eq("name", scenarioSchemaName));
    }

    @Override
    public void editWeight(Long weight, Long scenarioSchemaId) {
        baseMapper.editWeight(weight, scenarioSchemaId);
    }

    @Override
    public Boolean isMandatoryElementsDuplicated(ScenarioSchema dot) {
        QueryWrapper<ScenarioSchema> wrapper = new QueryWrapper<>();
        wrapper.lambda()
            .eq(ScenarioSchema::getName, dot.getName())
            .eq(ScenarioSchema::getBinding, dot.getBinding())
            .eq(ScenarioSchema::getVersion, dot.getVersion())
            .eq(ScenarioSchema::getGroupId, dot.getGroupId());
        if (null != dot.getId()) {
            wrapper.ne("id", dot.getId());
        }
        return baseMapper.selectCount(wrapper) != 0;
    }

}
