/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.domain.apiview.group.factory

import com.zatech.genesis.openapi.platform.domain.AbstractDomainFactory
import com.zatech.genesis.openapi.platform.domain.apiview.group.ApiViewGroupEntity
import com.zatech.genesis.openapi.platform.domain.apiview.schema.factory.ApiViewSchemaEntityFactory
import com.zatech.genesis.openapi.platform.infra.apiview.entity.ApiViewGroup
import com.zatech.genesis.openapi.platform.infra.apiview.service.{IApiViewGroupService, IApiViewSchemaService}
import com.zatech.genesis.openapi.platform.share.Javable
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2023/8/15 14:24
 * */
@Component
class ApiViewGroupEntityFactory @Autowired()(
                                              apiViewGroupService: IApiViewGroupService,
                                              apiViewSchemaService: IApiViewSchemaService,
                                              apiViewSchemaEntityFactory: ApiViewSchemaEntityFactory,
                                            )
  extends AbstractDomainFactory[ApiViewGroupEntity, ApiViewGroup](apiViewGroupService) with Javable {

  override def getDomainByDO(apiViewGroup: ApiViewGroup): ApiViewGroupEntity = {
    new ApiViewGroupEntity(
      apiViewGroup = apiViewGroup,
      apiViewSchemaService = apiViewSchemaService,
      apiViewGroupService = apiViewGroupService,
      apiViewSchemaEntityFactory = apiViewSchemaEntityFactory,
      apiViewGroupEntityFactory = this
    )
  }

  def listDomainsByParentGroupId(parentGroupId: Long): JList[ApiViewGroupEntity] = {
    apiViewGroupService.listByParentGroupId(parentGroupId).stream().map(getDomainByDO).toList
  }
}
