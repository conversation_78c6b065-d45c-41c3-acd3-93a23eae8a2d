package com.zatech.genesis.openapi.platform.domain.meta.event

import com.zatech.genesis.openapi.platform.domain.valueobject.{MetaDomainCategory, MetaDomainCategoryEnum}
import com.zatech.genesis.openapi.platform.share.enums.OperationTypeEnum
import com.zatech.genesis.openapi.platform.share.json.{JsonMap, JsonParser}
import org.springframework.context.ApplicationEvent

/**
 * <AUTHOR>
 * @create 2021/12/31 11:04 AM
 * */
case class MetaUpdatedEvent(
                        domainId: Option[Long],
                        category: MetaDomainCategory,
                        operation: OperationTypeEnum
                      ) extends ApplicationEvent(domainId){

  def toJsonString: String = {
    JsonParser.toJsonString(
      new JsonMap()
      .add("domainId", domainId.filter(_ != 0).orNull)
        .add("category", category.entryName).add("operation", operation))
  }
}

object MetaUpdatedEvent{
  def fromJsonString(jsonStr: String): MetaUpdatedEvent = {
    val json = JsonParser.fromJsonToJsonMap(jsonStr)
    MetaUpdatedEvent(
      Option(json.get("domainId")).map(x => java.lang.Long.valueOf(x.toString)),
      MetaDomainCategoryEnum.withName(json.get("category").asInstanceOf[String]),
      OperationTypeEnum.valueOf(json.get("operation").asInstanceOf[String])
    )
  }
}