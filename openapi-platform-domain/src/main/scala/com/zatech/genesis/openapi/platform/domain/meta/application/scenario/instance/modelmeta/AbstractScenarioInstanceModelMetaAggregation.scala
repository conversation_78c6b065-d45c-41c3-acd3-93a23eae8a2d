package com.zatech.genesis.openapi.platform.domain.meta.application.scenario.instance.modelmeta

import com.fasterxml.jackson.databind.ObjectMapper
import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.dictionary.model.DictionaryItem
import com.zatech.genesis.openapi.platform.domain.DomainObject
import com.zatech.genesis.openapi.platform.domain.exception.{DataRequiredException, DomainErrorCode}
import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.instance.modelmeta.AbstractScenarioInstanceModelMetaAggregation.CacheMap
import com.zatech.genesis.openapi.platform.domain.modelmeta._
import com.zatech.genesis.openapi.platform.domain.modelmeta.factory.{ModelMappingAggregationFactory, ModelValueDictionaryEntityFactory}
import com.zatech.genesis.openapi.platform.share.enums._
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException
import com.zatech.genesis.openapi.platform.share.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JsonParser}
import io.swagger.v3.oas.models.media.Schema
import org.openapi4j.core.validation.{ValidationException, ValidationSeverity}
import org.openapi4j.schema.validator.v3.SchemaValidator

import java.util
import scala.collection.mutable
import scala.jdk.CollectionConverters._
/**
 * <AUTHOR>
 * @create 2021/11/19 5:16 下午
 * 目前instance model meta实现为不支持$ref, schema的ref将被展开
 * */
abstract class AbstractScenarioInstanceModelMetaAggregation(
                                                             aggregationCache: CacheMap,
                                                             scenarioSchemaModelMetaAggregation: ModelMetaAggregation,
                                                             modelValueDictionaryEntityFactory: ModelValueDictionaryEntityFactory,
                                                             modelMetaMappingAggregationFactory: ModelMappingAggregationFactory
                                                           )
  extends DomainObject[Long] with ModelMetaAggregation with IJsonSchemable with Javable with IValidatable with IMappingable with IQueryable{

  override val modelMappingAggregationFactory: ModelMappingAggregationFactory = modelMetaMappingAggregationFactory
  /**
   * 直接使用schema modelmeta的id
   * @return
   */
  override def getId: Long = scenarioSchemaModelMetaAggregation.id

  /**
   * 获取枚举项
   * @return
   */
  def getDictionaryItems: Seq[DictionaryItem] = {
    schemaOpt.flatMap(schema => {
      Option(schema.getEnum).map(enum => {
        val enumCodes = `enum`.asScala.map(_.toString).toSet
        //如果有dictionary记录，则匹配dictionary,否则就没有desc
        modelValueDictionaryIdOpt.fold(enumCodes.map(
                    code => DictionaryItem.from(code, modelValueDictionaryEntityFactory.getDisplayNameI18n(name, code))
        ).toSeq)(dictId => {
          val dictItems = modelValueDictionaryEntityFactory.getDomain(dictId).dictItems.asScala
          dictItems.filter(item => enumCodes.contains(item.getName)).toSeq
        })
      })
    }).getOrElse(Nil)
  }

  /**
   * 如果是从数据生成的，则自己构建schema，否则直接使用schema
   *
   * @return
   */
  final override def toJsonSchemaOpt: Option[Schema[_]] = {
    //class的format用来存储class的名字了，所以不返回
    def getFormat = if(this.isInstanceOf[ScenarioInstanceObjectModelMetaAggregation]) None else formatOpt
    schemaOpt.orElse({
      buildJsonSchemaSpecific.map(schema => {
        schema.setDefault(defaultOpt.orNull)
        val result = schema.name(name)
          .title(titleOpt.orNull)
          .format(getFormat.orNull)
          .example(exampleOpt.orNull)
          .description(descriptionOpt.orNull)
          .maxLength(maxLengthOpt.orNull)
          .extensions(JsonParser.fromObjToMap(extension))
          .asInstanceOf[Schema[_]]
        result
      })
    })
  }

  /**
   * 转换为JsonSchema，子类实现
   *
   * @return
   */
  protected def buildJsonSchemaSpecific: Option[Schema[_]]

  private lazy val parentAggregationOpt: Option[ModelMetaAggregation] = {
    scenarioSchemaModelMetaAggregation.parentOpt.flatMap(parent => aggregationCache.get(parent.id))
  }

  /**
   * 如果是根节点，那root就是自己
   */
  private lazy val rootAggregation: ModelMetaAggregation = parentAggregationOpt.map(_.root).getOrElse(this)

  var schemaOpt: Option[Schema[_]] = None

  final def newFromSwaggerOAS3Schema(schema: Schema[_]): Unit = {
    schemaOpt = Option(schema)
    constructBySwaggerSchema(schema)
  }

  /**
   * 从swagger构建, 子类实现
   * @param schema
   */
  protected def constructBySwaggerSchema(schema: Schema[_]): Unit

  /**
   * 从data构建
   * @param data
   */
  def constructByData(data: AnyRef): Unit

  /**
   * 如果是从数据生成的，则自己构建sample，否则直接使用schema
   * @return
   */
  def asSampleDataOpt: Option[Json] = {
    schemaOpt.map(schema => Json.toJson(schema.getExample)).orElse(toSampleDataOpt)
  }

  /**
   * 获取示例数据, 子类实现
   * @return
   */
  protected def toSampleDataOpt: Option[Json]

  def validate(value: Any): Unit ={
    if((required || scenarioSchemaModelMetaAggregation.required) && value == null){
      throw new DataRequiredException(this)
    }
    asSampleDataOpt   match {
      case Some(sampleData) => AbstractScenarioInstanceModelMetaAggregation.validate(sampleData,"scenario_instance_model_meta")(value)
      case None => throw OpenApiException.by(DomainErrorCode.model_meta_not_match).params(getId + "").build()
    }
  }

  override def findById(modelMetaId: Long): Option[ModelMetaAggregation] = ??? /*{
    if(modelMetaId == getId) return Some(this)
    else {
      for(child: IQueryable <- children){
        val found = child.findById(modelMetaId)
        if(found.isDefined) return found
      }
    }
    None
  }*/

  def findModelMeta(modelMetaId:Long):ModelMetaAggregation ={
    findModelMeta(this,modelMetaId)
  }
  def findModelMeta(modelMeta:ModelMetaAggregation,modelMetaId:Long):ModelMetaAggregation = {
    var aggregation:ModelMetaAggregation = null
    if(modelMetaId == modelMeta.id){
      aggregation = modelMeta
    }else {
      modelMeta.children.foreach(child=>{
        if(aggregation == null){
          aggregation = findModelMeta(child, modelMetaId)
        }
      })
    }
    aggregation
  }

  override def id: Long = getId

  override def name: String = scenarioSchemaModelMetaAggregation.name

  override def titleOpt: Option[String] = scenarioSchemaModelMetaAggregation.titleOpt

  override def defaultOpt: Option[String] = scenarioSchemaModelMetaAggregation.defaultOpt

  override def maxLengthOpt: Option[Integer] = scenarioSchemaModelMetaAggregation.maxLengthOpt

  override def descriptionOpt: Option[String] = scenarioSchemaModelMetaAggregation.descriptionOpt

  override def exampleOpt: Option[AnyRef] = scenarioSchemaModelMetaAggregation.exampleOpt

  override def formatOpt: Option[String] = scenarioSchemaModelMetaAggregation.formatOpt

  override def direction: ModelMetaDirectionEnum = scenarioSchemaModelMetaAggregation.direction

  override def category: ModelMetaCategoryEnum = ModelMetaCategoryEnum.ScenarioInstance

  override def required: Boolean = scenarioSchemaModelMetaAggregation.required

  override def parentOpt: Option[ModelMetaAggregation] = parentAggregationOpt

  override def root: ModelMetaAggregation = rootAggregation

  override def javaType: ModelMetaJavaTypeEnum = scenarioSchemaModelMetaAggregation.javaType

  override def jsonSchemaType: ModelMetaJsonSchemaTypeEnum = scenarioSchemaModelMetaAggregation.jsonSchemaType

  override def extension: ModelMetaExtension = scenarioSchemaModelMetaAggregation.extension

  override def configOpt: Option[_ <: BaseModelMetaConfig] = scenarioSchemaModelMetaAggregation.configOpt

  override def modelValueDictionaryIdOpt: Option[Long] = scenarioSchemaModelMetaAggregation.modelValueDictionaryIdOpt
}

object AbstractScenarioInstanceModelMetaAggregation{
  type CacheMap = mutable.Map[Long, AbstractScenarioInstanceModelMetaAggregation]

  def newCacheMap = mutable.Map[Long, AbstractScenarioInstanceModelMetaAggregation]()
  val mapper = new ObjectMapper()
  def validate(schema: Json, propertyName: String)(value: Any): Unit = {
    val schemaNode = mapper.readTree(schema.toJsonString)
    val contentNode = mapper.readTree(Json.toJson(value).toJsonString)
    val schemaValidator = new SchemaValidator(propertyName, schemaNode)
    try schemaValidator.validate(contentNode)
    catch {
      case ex: ValidationException =>
        val errorList = new util.ArrayList[JsonMap]
        ex.results().items().forEach(item=>{
          item.severity() match {
            case ValidationSeverity.ERROR=>
              val jsonMap = new JsonMap()
              jsonMap.put("message",item.message());
              jsonMap.put("crumbs",item.dataCrumbs())
              errorList.add(jsonMap)
            case _ =>
          }
        })
        if(!errorList.isEmpty){

          val errorMsg = mapper.writeValueAsString(errorList)
          throw OpenApiException.by(DomainErrorCode.work_flow_not_exists).params(errorMsg).build()
        }


      // System.out.println(ex.results) 可以对返回的报错信息进行记录
    }
  }
}
