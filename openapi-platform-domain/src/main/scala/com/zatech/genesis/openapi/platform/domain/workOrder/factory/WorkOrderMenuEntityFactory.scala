/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.domain.workOrder.factory

import com.zatech.genesis.openapi.platform.domain.AbstractDomainFactory
import com.zatech.genesis.openapi.platform.domain.exception.DomainErrorCode
import com.zatech.genesis.openapi.platform.domain.workOrder.WorkOrderMenuEntity
import com.zatech.genesis.openapi.platform.infra.workOder.entity.WorkOrderMenu
import com.zatech.genesis.openapi.platform.infra.workOder.service.IWorkOrderMenuService
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2023/6/1 16:41
 * */
@Component
class WorkOrderMenuEntityFactory @Autowired()(
                                                 workOrderMenuService: IWorkOrderMenuService
                                               )
  extends AbstractDomainFactory[WorkOrderMenuEntity, WorkOrderMenu](workOrderMenuService) {

  def getDomainByDO(workOrderMenu: WorkOrderMenu): WorkOrderMenuEntity = {
    new WorkOrderMenuEntity(
      workOrderMenu = workOrderMenu
    )
  }

  def getDomainByCode(code: String): WorkOrderMenuEntity = {
    Option(workOrderMenuService.selectByCode(code)).map(getDomainByDO).getOrElse(throw new OpenApiException(DomainErrorCode.menu_not_found))
  }
}

