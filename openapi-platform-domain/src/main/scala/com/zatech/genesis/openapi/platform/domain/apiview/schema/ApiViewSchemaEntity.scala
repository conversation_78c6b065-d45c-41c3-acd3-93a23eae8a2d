/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.domain.apiview.schema

import com.zatech.genesis.openapi.platform.api.resource.apiview.schema.response.ApiViewSchemaResponse
import com.zatech.genesis.openapi.platform.domain.DomainObject
import com.zatech.genesis.openapi.platform.domain.meta.{ICreatedable, IModifiedable}
import com.zatech.genesis.openapi.platform.infra.apiview.entity.ApiViewSchema

import java.util.Date
import scala.jdk.CollectionConverters.IterableHasAsJava

/**
 * <AUTHOR>
 * @date 2023/8/15 11:42
 * */
class ApiViewSchemaEntity(
                            apiViewSchema: ApiViewSchema,
                          ) extends DomainObject[Long] with ICreatedable with IModifiedable {
  override def getId: Long = apiViewSchema.getId

  val instanceIds = apiViewSchema.getInstanceIds.trim.split(",")

  val schemaId = apiViewSchema.getSchemaId

  val weight = apiViewSchema.getWeight

  val groupId = apiViewSchema.getGroupId


  override def gmtCreated: Date = apiViewSchema.getGmtCreated

  override def creator: String = apiViewSchema.getCreator

  override def gmtModified: Date = apiViewSchema.getGmtModified

  override def modifier: String = apiViewSchema.getModifier


  def toApi: ApiViewSchemaResponse = {
    val response = new ApiViewSchemaResponse
    response.setId(getId)
    response.setScenarioSchemaId(schemaId)
    response.setWeight(weight)
    val instanceResponses =  instanceIds.map(instanceId => {
      val instance = new ApiViewSchemaResponse.InstanceResponse;
      instance.setId(java.lang.Long.parseLong(instanceId))
      instance
    }).toSeq.asJavaCollection.stream().toList
    response.setInstanceResponses(instanceResponses)
    response
  }
}

