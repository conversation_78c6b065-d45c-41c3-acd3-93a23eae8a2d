package com.zatech.genesis.openapi.platform.domain.meta.knowledge.spec

import com.zatech.genesis.openapi.platform.domain.meta.IProcedureMetaEntity
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.spec.factory.SpecProcedureMetaEntityFactory
import com.zatech.genesis.openapi.platform.domain.modelmeta.ModelMetaAggregation
import com.zatech.genesis.openapi.platform.domain.{DomainObject, IProcedureMetaAggregation}
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.entity.ProcedureMeta

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/1 20:03
 * @version 1.0
 * */
class SpecProcedureMetaAggregation(
                              procedureMeta: ProcedureMeta,
                              specProcedureMetaEntityFactory: SpecProcedureMetaEntityFactory
                              ) extends IProcedureMetaAggregation(procedureMeta: ProcedureMeta) with  DomainObject[Long]{
  override def getId: Long = procedureMeta.getId

  val procedureMetaEntity: IProcedureMetaEntity = specProcedureMetaEntityFactory.getDomainByDO(procedureMeta)

  lazy val inputModelMetaAggregationOpt: Option[ModelMetaAggregation] = None

  lazy val inputModelMetaSummaryAggregationOpt: Option[ModelMetaAggregation] = None
  lazy val outputModelMetaAggregationOpt: Option[ModelMetaAggregation] = None
  lazy val outputModelMetaSummaryAggregationOpt: Option[ModelMetaAggregation] = None
}
