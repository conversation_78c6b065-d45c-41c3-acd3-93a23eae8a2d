package com.zatech.genesis.openapi.platform.domain.meta.knowledge.flow.inbuilt

import com.zatech.genesis.openapi.platform.api.resource.meta.flow.response.FlowSchemaResponse
import com.zatech.genesis.openapi.platform.common.Javable
import com.zatech.genesis.openapi.platform.domain.DomainObject
import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.schema.ScenarioSchemaEntity
import com.zatech.genesis.openapi.platform.domain.meta.application.scenario.schema.factory.ScenarioSchemaEntityFactory
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.schema.ProcedureMetaEntity
import com.zatech.genesis.openapi.platform.domain.meta.knowledge.schema.factory.ProcedureMetaEntityFactory
import com.zatech.genesis.openapi.platform.infra.knowledge.flow.inbuilt.entity.FlowSchema
import com.zatech.genesis.openapi.platform.infra.knowledge.schema.service.IProcedureMetaService

import scala.jdk.CollectionConverters._

/**
 * @Author: felton
 * @Date: 2021/10/23 下午9:03
 */
class FlowSchemaEntity(
                        flowSchema: FlowSchema,
                        scenarioSchemaEntityFactory: ScenarioSchemaEntityFactory,
                        procedureMetaEntityFactory: ProcedureMetaEntityFactory,
                        procedureMetaService: IProcedureMetaService
                      ) extends DomainObject[Long] with Javable {

  override def getId: Long = flowSchema.getId

  val name = flowSchema.getName

  val scenarioSchemaId = flowSchema.getScenarioSchemaId

  val version = flowSchema.getVersion

  val content = flowSchema.getContent

  val description = flowSchema.getDescription

  val creator = flowSchema.getCreator

  lazy val scenarioSchemaEntity: ScenarioSchemaEntity = scenarioSchemaEntityFactory.getDomain(scenarioSchemaId)

  lazy val isDefault = scenarioSchemaEntity.defaultFlowSchemaIdOpt.contains(getId)

  lazy val allAvailableProcedureEntities: Array[ProcedureMetaEntity] = {
    val pluginProcedures = procedureMetaService.listAllAvailableProceduresForFlowSchema(getId).asScala
      .map(procedureMetaEntityFactory.getDomainByDO)
    val systemProcedures = procedureMetaService.listAllSystemProcedures().asScala.map(procedureMetaEntityFactory.getDomainByDO)
    (pluginProcedures ++ systemProcedures).toArray
  }

  //获取bound的procedure 用于导出，导入时候再进行校验
  def getBoundProcedure: Array[ProcedureMetaEntity] = {
    if (content.isEmpty) {
      return Array()
    }
    val procedures = scala.collection.mutable.Set[String]()
    content.get("activities").asInstanceOf[JAnyList].asScala.foreach(item => {
      val activityMap = item.asInstanceOf[JStringMap]
      val list = activityMap.get("type").asInstanceOf[String] match {
        case "Procedure" => {
          activityMap.get("procedures").asInstanceOf[JAnyList].forEach(e => {
            procedures += e.asInstanceOf[JStringMap].get("name").asInstanceOf[String]
          })
        }
        case _ => None
      }
    })
    allAvailableProcedureEntities.filter(procedureMetaEntity => procedures.exists(e => procedureMetaEntity.name.equals(e)))

  }

  def toApi: FlowSchemaResponse = {
    val response = new FlowSchemaResponse
    response.setId(flowSchema.getId)
    response.setVersion(flowSchema.getVersion)
    response.setName(name)
    response.setContent(flowSchema.getContent)
    response.setDescription(flowSchema.getDescription)
    response.setScenarioSchemaId(flowSchema.getScenarioSchemaId)
    response.setDefaultFlag(isDefault)
    response.setCreator(flowSchema.getCreator)
    response.setGmtCreated(flowSchema.getGmtCreated)
    response.setModifier(flowSchema.getModifier)
    response.setGmtModified(flowSchema.getGmtModified)
    response
  }
}
