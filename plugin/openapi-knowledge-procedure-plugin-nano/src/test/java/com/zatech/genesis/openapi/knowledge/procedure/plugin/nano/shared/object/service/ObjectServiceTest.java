package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.object.service;

import com.zatech.genesis.model.policy.Policy;
import com.zatech.genesis.model.policy.object.InsuredObject;
import com.zatech.genesis.model.policy.object.component.InsuredObjectComponentBase;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.util.ResourceUtils;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceInsuredObjectRequest;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.policy.api.base.InsuredObjectComponentGrossProfitBase;

import java.math.BigDecimal;
import java.util.function.Function;

import org.apache.commons.lang3.function.TriConsumer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ObjectServiceTest {

    private ObjectService objectService;

    private Policy policy;

    @BeforeEach
    void setUp() {
        objectService = new ObjectService();
        objectService.init();
        policy = JsonParser.fromJson(ResourceUtils.resourceFileContent("v3/policyRequest.json"), Policy.class);
    }

    @Test
    void testUnifyToReqStrategy_Building() {
        InsuredObject insuredObject = policy.getInsuredObjectList().get(0);
        TriConsumer<IssuanceInsuredObjectRequest, InsuredObject, Policy> strategy = objectService.getUnifyToReqStrategy(insuredObject);
        assertNotNull(strategy, "Strategy should not be null");
        IssuanceInsuredObjectRequest issuanceInsuredObjectRequest = new IssuanceInsuredObjectRequest();

        // 外层标的测试
        strategy.accept(issuanceInsuredObjectRequest, insuredObject, policy);
        assertEquals("7665de1f-72cf-497c-b30a-bb7f85e04f1b", issuanceInsuredObjectRequest.getSerialNo());

        // 内层标的测试
        InsuredObjectComponentBase insuredObjectComponentBase = policy.getInsuredObjectList().get(0).getInsuredComponentList().get(0);
        Function<InsuredObjectComponentBase, com.zatech.genesis.policy.api.base.InsuredObjectComponentBase> componentStrategy = objectService.getUnifyToReqComponentStrategy(insuredObjectComponentBase);
        assertNotNull(componentStrategy, "Component strategy should not be null");
        com.zatech.genesis.policy.api.base.InsuredObjectComponentBase converted = componentStrategy.apply(insuredObjectComponentBase);
        assertEquals("InsuredObjectComponentGrossProfitBase", converted.getClass().getSimpleName());
        InsuredObjectComponentGrossProfitBase grossProfitBase = (InsuredObjectComponentGrossProfitBase) converted;
        assertEquals("前滩", grossProfitBase.getInsuredObjectName());
        assertTrue(new BigDecimal("666.66").equals(grossProfitBase.getAverageAnnualIncomeInPast3Y()));
    }

}
