package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.finance.renew;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.finance.renew.input.QueryInstallmentInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.finance.renew.output.QueryInstallmentOutput;
import com.zatech.genesis.openapi.platform.integration.bcp.response.BillResponse;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterBcpService;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.octopus.common.dao.Page;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;

class QueryInstallmentProcedureTest {

    @InjectMocks
    private QueryInstallmentProcedure queryInstallmentProcedure;

    @Mock
    private IOuterBcpService iOuterBcpService;

    @Test
    void execute() {
        MockitoAnnotations.openMocks(this);
        ProcedureExecutionContext context = ProcedureExecutionContext.builder().build();
        String originInput = "{\"limit\": 10, \"start\": 0, \"transType\": \"INSTALLMENT_PREMIUM\"}";
        context.resetOriginInput(JsonParser.fromJsonToJsonMap(originInput));

        QueryInstallmentInput input = new QueryInstallmentInput();
        QueryInstallmentOutput output = new QueryInstallmentOutput();

        List<BillResponse> billResponseList = new ArrayList<>();
        billResponseList.add(new BillResponse());

        Page<BillResponse> billResponsePage = new Page();
        billResponsePage.setLimit(10);
        billResponsePage.setStart(0);
        billResponsePage.setResults(billResponseList);

        Mockito.when(iOuterBcpService.queryBill(any())).thenReturn(billResponsePage);

        // Act
        queryInstallmentProcedure.execute(context, input, output);

        // Assert
        verify(iOuterBcpService, atLeastOnce()).queryBill(any());
    }

    @Test
    void execute_over_page() {
        MockitoAnnotations.openMocks(this);
        ProcedureExecutionContext context = ProcedureExecutionContext.builder().build();
        String originInput = "{\"limit\": 10, \"start\": 0, \"transType\": \"INSTALLMENT_PREMIUM\"}";
        context.resetOriginInput(JsonParser.fromJsonToJsonMap(originInput));

        QueryInstallmentInput input = new QueryInstallmentInput();
        QueryInstallmentOutput output = new QueryInstallmentOutput();

        List<BillResponse> billResponseList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            billResponseList.add(buildBillResponse(i));
        }
        Page<BillResponse> billResponsePage = new Page();
        billResponsePage.setLimit(10);
        billResponsePage.setStart(0);
        billResponsePage.setTotal(11);
        billResponsePage.setResults(billResponseList);

        Mockito.when(iOuterBcpService.queryBill(any())).thenReturn(billResponsePage);

        // Act
        queryInstallmentProcedure.execute(context, input, output);

        // Assert
        verify(iOuterBcpService, atLeastOnce()).queryBill(any());
    }

    private static BillResponse buildBillResponse(int id) {
        BillResponse billResponse = new BillResponse();
        billResponse.setBillAmount("500");
        billResponse.setBillId((long) id);
        return billResponse;
    }

}
