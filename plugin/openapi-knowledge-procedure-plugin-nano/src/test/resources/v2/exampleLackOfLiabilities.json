{"orderNo": "zaOpenapi{{$timestamp}}", "premium": 40500, "goodsId": "828599301062661", "zoneId": "Asia/Shanghai", "plan": {"holder": {"person": {"elements": {"gender": "1", "certiNo": "496199", "birthday": "1990-02-01", "fullName": "tester1234", "certiType": "1"}}}, "planId": 828602035748870, "elements": {"premiumPeriod": "4", "salesCurrency": "JPY", "coveragePeriod": "3-12", "premiumFrequencyType": 1}, "products": [{"objects": [{"driver": [{"driverTier": "1", "driverBirthday": "1990-02-01", "driverIdNumber": "1235ws1", "occupationCode": "A51-01-01", "drivingLicenseNo": "123ss5ws1", "driverMaritalStatus": "1"}], "vehicle": {"vinNo": "123", "plateNo": "156984", "engineNo": "1233", "vehicleMake": "BENZ", "E_vehicleAge": "1", "vehicleModel": "A180L", "vehicleUsage": "02", "vehicleEngine": "EV", "registrationDate": "2021-01-05", "yearOfManufacturing": 2020, "registrationCategory": "1"}, "carOwner": {"name": "tester1234", "idType": "1", "idNumber": "496199", "E_ifMultiplePolicy": "1"}}], "elements": {"sumInsured": 61000000}, "liabilities": [{"elements": {"sumInsured": 10000000}, "liabilityId": 544}], "insureds": {"person": [{"elements": {"gender": "1", "certiNo": "496199", "birthday": "1990-02-01", "fullName": "tester1234", "certiType": "1", "relationshipWithPolicyholder": "1"}}]}, "productId": 828414030266375}]}, "paymentOrderNo": "zaOpenapi{{$timestamp}}"}