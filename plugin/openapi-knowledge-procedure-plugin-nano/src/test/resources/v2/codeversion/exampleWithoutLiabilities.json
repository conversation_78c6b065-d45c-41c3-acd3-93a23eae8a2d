{"orderNo": "zaOpenapiv2203111406", "premium": 40500, "goodsCode": "Nano_Demo_JP_AT_011", "goodsVersion": "V01", "zoneId": "Asia/Shanghai", "plan": {"holder": {"person": {"elements": {"gender": "1", "certiNo": "202311031738231225", "birthday": "1990-02-01", "fullName": "<PERSON>", "certiType": "1", "emails": [{"email": "<EMAIL>"}, {"email": "<EMAIL>"}]}}}, "planCode": "Nano_Demo_JP_AT_011", "elements": {"premiumPeriod": "4", "salesCurrency": "JPY", "coveragePeriod": "3-12", "premiumFrequencyType": 1}, "products": [{"objects": [{"driver": [{"driverTier": "1", "driverBirthday": "1990-02-01", "driverIdNumber": "1235ws1", "occupationCode": "A51-01-01", "drivingLicenseNo": "18528904505", "driverMaritalStatus": "1"}], "vehicle": {"vinNo": "123", "plateNo": "18528904505", "engineNo": "1233", "vehicleMake": "BENZ", "E_vehicleAge": "1", "vehicleModel": "A180L", "vehicleUsage": "02", "vehicleEngine": "EV", "registrationDate": "2021-01-05", "yearOfManufacturing": 2020, "registrationCategory": "1"}, "carOwner": {"name": "<PERSON>", "idType": "1", "idNumber": "202311031738231225", "E_ifMultiplePolicy": "1"}}], "elements": {"sumInsured": 61000000}, "insureds": {"person": [{"elements": {"gender": "1", "certiNo": "18528904505", "birthday": "1990-02-01", "fullName": "<PERSON>", "certiType": "1", "relationshipWithPolicyholder": "1", "emails": [{"email": "<EMAIL>"}, {"email": "<EMAIL>"}]}}]}, "productCode": "Nano_Demo_JP_AT_011", "productVersion": "V01", "liabilities": [{"elements": {"sumInsured": 10000000}, "liabilityCode": "1571"}, {"liabilityCode": "1579"}, {"elements": {"sumInsured": 1000000}, "liabilityCode": "1585"}, {"elements": {"sumInsured": 50000000}, "liabilityCode": "1840"}]}]}, "paymentOrderNo": "zaOpenapiv20231111406"}