{"goodsId": ***************, "orderNo": "auto18531468507", "paymentMethod": 100, "paymentOrderNo": "auto18531468507", "plans": {"***************": {"planId": ***************, "holder": {"person": {"accountSubType": "VISA_CARD", "accountType": "1", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "address15": "detail", "addressType": "1", "bankBranchCode": "A001", "bankBranchName": "branch name", "bankCode": "A01", "bankName": "bank name", "birthday": "1990-01-01", "cardHolderName": "test", "cardNumber": "****************", "certiNo": "S54368753N1", "certiType": "99", "countryCode": "+86", "email": "<EMAIL>", "expiryDate": "2050-01-01", "fullName": "Peppa ITGGH", "gender": "2", "jisCode": "A00001", "mobileNo": "***********", "nationality": "AFGHANISTAN", "occupationCode": "1", "phoneNo": "*********", "phoneType": "1", "safeNo": "001", "zipCode": "A00001"}}, "insured": {"persons": [{"accountSubType": "VISA_CARD", "accountType": "1", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "address15": "detail", "addressType": "1", "bankBranchCode": "A001", "bankBranchName": "branch name", "bankCode": "A01", "bankName": "bank name", "birthday": "1990-09-09", "cardHolderName": "test", "cardNumber": "****************", "certiNo": "S54368753N1", "certiType": "99", "countryCode": "+86", "email": "<EMAIL>", "expiryDate": "2050-01-01", "fullName": "Peppa ITGGH", "gender": "1", "insuredType": "2", "jisCode": "A00001", "mobileNo": "***********", "nationality": "AFGHANISTAN", "occupationCode": "1", "phoneNo": "*********", "phoneType": "1", "relationshipWithPolicyholder": "SELF", "safeNo": "001", "smoke": "NO", "zipCode": "A00001"}]}, "objects": [{"carOwner": {"extensions": {"E_ifMultiplePolicy": "1"}}, "driver": [{"driverTier": "1"}], "vehicle": {"drivingDistance": 1000, "engineNo": "1233", "extensions": {"E_vehicleAge": "1"}, "plateNo": "001", "registrationCategory": "1", "registrationDate": "2022-09-07", "registrationNo": "001", "vehicleMake": "BENZ", "vehicleModel": "A180L", "vehicleUsage": "02", "vinNo": "123", "yearOfManufacturing": 2020}}], "products": {"***************": {"baseCurrency": 4, "effectiveDate": "2022-08-11T17:21:58.299+08:00", "expiryDate": "2025-08-11T17:21:58.299+08:00", "productId": ***************, "sumInsured": 61000000}}}}, "premium": 40500, "salesCurrency": 4, "zoneId": "Asia/Shanghai"}