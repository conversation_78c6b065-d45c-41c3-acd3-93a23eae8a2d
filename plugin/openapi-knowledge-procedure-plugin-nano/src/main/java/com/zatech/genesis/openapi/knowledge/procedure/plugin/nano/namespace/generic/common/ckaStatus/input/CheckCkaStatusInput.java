package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.common.ckaStatus.input;

import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/10 14:55
 */
@Data
@Schema(description = "Check cka status input")
public class CheckCkaStatusInput implements Json {

    @Schema(description = "CertiType of customer, refer to the API reference. [Detail](/openx/docs/common/guide/reference/certificate-type)", required = true, maxLength = 9)
    private CertiTypeEnum certiType;

    @Schema(description = "CertiNo of customer", required = true, maxLength = 256)
    private String certiNo;

    @Schema(description = "FullName of customer", maxLength = 256)
    private String fullName;

    @Schema(description = "Birthday of customer")
    private LocalDate birthday;

}
