/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.withdraw_case.input;

import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "CaseWithdrawInput", description = " Claim withdraw case input ")
public class CaseWithdrawInput extends WithdrawInput implements Json {

    @Schema(title = "caseNo", description = "Number of claim case", hidden = true)
    private String caseNo;

}
