package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.sync.input;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.openapi.platform.integration.metadata.base.DynamicMetaField;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2023/12/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Policy customer phone input")
public class PolicyCustomerPhoneInput extends DynamicMetaField implements Json {

    @Schema(description = "Phone no")
    private String phoneNo;

    @Schema(description = "Phone type")
    private PhoneTypeEnum phoneType;

    @Schema(description = "Country code")
    private String countryCode;

    @Schema(description = "Is default")
    private YesNoEnum isDefault;

}

