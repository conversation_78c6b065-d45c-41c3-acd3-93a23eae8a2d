/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim;

import com.zatech.gaia.resource.graphene.policy.PolicyRelationTypeEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model.output.BatchChangeOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.application_list.output.ApplicationListOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.approval.input.ClaimCaseApprovalInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.approval.output.CaseApprovalOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.assessment.output.CaseAssessmentOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.assessment_config.input.AssessmentConfigInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.assessment_config.output.AssessmentConfigOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.calculation_level.input.CaseLevelInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.calculation_level.output.CaseLevelOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.changeIncident.input.ChangeIncidentContentInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.changeIncident.output.ChangeIncidentContentOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.createCase.input.CreateCaseInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.createCase.output.CreateCaseOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.documents.input.ClaimDocumentsInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.list_by_policy_no.output.QueryPageCasesByPolicyNoOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.query.output.ClaimQueryCaseByCaseNoOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.query_claim_history.output.QueryClaimHistoryOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.query_detail_by_application_no.output.ClaimQueryApplicationNoOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.track_change.output.ClaimQueryCaseChangeByCaseNoOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.update_application.output.UpdateApplicationOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.update_case_payee.input.UpdatePayeeInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.update_case_payee.output.UpdatePayeeOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.withdraw_application.input.ApplicationWithdrawInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.withdraw_application.output.ApplicationWithdrawOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.withdraw_case.input.CaseWithdrawInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.withdraw_case.output.CaseWithdrawOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.claim.assessment.input.UpdateCaseAssessmentInput;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApi;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.ApiSpec;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.ErrorCodes;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.OpenApi;
import com.zatech.genesis.openapi.platform.share.exception.ClaimErrorCode;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@OpenApi
@RequestMapping("/api/v3")
public interface ClaimApi extends IOpenApi {

    @ApiSpec(namespace = "common", group = "claim", scenario = "query")
    @Operation(summary = "query claim case detail")
    @GetMapping("/claims/cases/{caseNo}")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = ClaimQueryCaseByCaseNoOutput.class))
    )
    JsonMap query(
        @PathVariable(name = "caseNo") @Parameter(name = "caseNo", required = true, in = ParameterIn.PATH, example = "CLMGC00725001", schema = @Schema(type = "string", maxLength = 64))
        String caseNo);

    @ApiSpec(namespace = "common", group = "claim", scenario = "track_change")
    @Operation(summary = "query claim case traces")
    @GetMapping("/claims/cases/{caseNo}/traces")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = ClaimQueryCaseChangeByCaseNoOutput.class))
    )
    JsonMap queryTrace(
        @PathVariable(name = "caseNo") @Parameter(name = "caseNo", required = true, in = ParameterIn.PATH, example = "CLMGC00725001", schema = @Schema(type = "string", maxLength = 64))
        String caseNo);


    @ApiSpec(namespace = "common", group = "claim", scenario = "create_case")
    @Operation(summary = "Create a claim case")
    @PostMapping("/policies/{policyNo}/claims")
    @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CreateCaseOutput.class)))
    JsonMap createCase(
        @PathVariable(name = "policyNo") @Parameter(name = "policyNo", required = true, in = ParameterIn.PATH, example = "B02034220982", schema = @Schema(type = "string", maxLength = 64)) String policyNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = CreateCaseInput.class)) JsonMap input);


    @ApiSpec(namespace = "common", group = "claim", scenario = "create_case_group")
    @Operation(summary = "Create a group policy claim case")
    @PostMapping("/policies/{policyNo}/group/claims")
    @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CreateCaseOutput.class)))
    JsonMap groupCreateCase(
        @PathVariable(name = "policyNo") @Parameter(name = "policyNo", required = true, in = ParameterIn.PATH, example = "B02034220982", schema = @Schema(type = "string", maxLength = 64)) String policyNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = CreateCaseInput.class)) JsonMap input);




    @ApiSpec(namespace = "common", group = "claim", scenario = "change_incident_info")
    @Operation(summary = "change incident info")
    @PutMapping("/claims/incidents")
    @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ChangeIncidentContentOutput.class)))
    JsonMap batchChangeIncident(@RequestBody @Parameter(name = "input", schema = @Schema(implementation = ChangeIncidentContentInput.class)) JsonMap input);


    @ApiSpec(namespace = "common", group = "claim", scenario = "documents")
    @Operation(summary = "upload document")
    @PostMapping("/claims/cases/documents")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = BatchChangeOutput.class))
    )
    JsonMap documents(
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = ClaimDocumentsInput.class)) JsonMap input);

    @ApiSpec(namespace = "common", group = "claim", scenario = "assessment")
    @Operation(summary = "Claim assessment")
    @PostMapping("/claims/{caseNo}/assessment")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = CaseAssessmentOutput.class))
    )
    JsonMap assessment(
        @PathVariable(name = "caseNo") @Parameter(name = "caseNo", required = true, in = ParameterIn.PATH, example = "s000007", schema = @Schema(type = "string", maxLength = 64)) String caseNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = UpdateCaseAssessmentInput.class)) JsonMap input);

    @ApiSpec(namespace = "common", group = "claim", scenario = "list_by_policy_no")
    @Operation(summary = "Claim list query")
    @GetMapping({"/policies/{policyNo}/claims/basic", "/policies/{policyNo}/claims/basics"})
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = QueryPageCasesByPolicyNoOutput.class))
    )
    JsonMap list_by_policy_no(
        @PathVariable(name = "policyNo") @Parameter(name = "policyNo", required = true, in = ParameterIn.PATH, example = "B02034220982", schema = @Schema(type = "string", maxLength = 64)) String policyNo,
        @RequestParam("relation_type") @Parameter(name = "relation_type", in = ParameterIn.QUERY, schema = @Schema(type = "string")) PolicyRelationTypeEnum relationType,
        @RequestParam("page_size") @Parameter(name = "page_size", example = "10", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "number", defaultValue = "10")) Integer pageSize,
        @RequestParam("page_index") @Parameter(name = "page_index", example = "0", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "number", defaultValue = "0")) Integer pageIndex);

    @ErrorCodes(errorCodes = ClaimErrorCode.class)
    @ApiSpec(namespace = "common", group = "claim", scenario = "query_detail_by_application_no")
    @Operation(summary = "query claim detail")
    @GetMapping("/claims/applications/{applicationNo}")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = ClaimQueryApplicationNoOutput.class))
    )
    JsonMap queryByApplicationNo(
        @PathVariable(name = "applicationNo") @Parameter(name = "applicationNo", required = true, in = ParameterIn.PATH, example = "901730000782001", schema = @Schema(type = "string", maxLength = 64))
        String applicationNo);

    @ErrorCodes(errorCodes = ClaimErrorCode.class)
    @ApiSpec(namespace = "common", group = "claim", scenario = "update_application")
    @Operation(summary = "Update the claim application")
    @PutMapping("/claims/applications/{applicationNo}")
    @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UpdateApplicationOutput.class)))
    JsonMap updateApplication(
        @PathVariable(name = "applicationNo") @Parameter(name = "applicationNo", required = true, in = ParameterIn.PATH, example = "901730000782001", schema = @Schema(type = "string", maxLength = 64)) String applicationNo,
        @RequestBody @Parameter(name = "input") JsonMap input);


    @ApiSpec(namespace = "common", group = "claim", scenario = "approval")
    @Operation(summary = "approval of case")
    @PutMapping("/claims/{caseNo}/approval")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = CaseApprovalOutput.class))
    )
    JsonMap caseApproval(
        @PathVariable(name = "caseNo") @Parameter(name = "caseNo", required = true, in = ParameterIn.PATH, example = "901730000782001", schema = @Schema(type = "string", maxLength = 64)) String caseNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = ClaimCaseApprovalInput.class)) JsonMap input);

    @ApiSpec(namespace = "common", group = "claim", scenario = "withdraw_case")
    @Operation(summary = "Claim withdraw case")
    @PostMapping("/claims/cases/{caseNo}/withdraw")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = CaseWithdrawOutput.class))
    )
    JsonMap withdrawCase(
        @PathVariable(name = "caseNo") @Parameter(name = "caseNo", required = true, in = ParameterIn.PATH, example = "901730000782001", schema = @Schema(type = "string", maxLength = 64)) String caseNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = CaseWithdrawInput.class)) JsonMap input);


    @ApiSpec(namespace = "common", group = "claim", scenario = "withdraw_application")
    @Operation(summary = "Claim withdraw application")
    @PostMapping("/claims/applications/{applicationNo}/withdraw")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApplicationWithdrawOutput.class))
    )
    JsonMap withdrawApplication(
        @PathVariable(name = "applicationNo") @Parameter(name = "applicationNo", required = true, in = ParameterIn.PATH, example = "CLMAP202407310144405", schema = @Schema(type = "string", maxLength = 64)) String applicationNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = ApplicationWithdrawInput.class)) JsonMap input);


    @ApiSpec(namespace = "common", group = "claim", scenario = "assessment_config")
    @Operation(summary = "Query  Attachment Configurations")
    @PostMapping("/claims/attachments/configurations")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = AssessmentConfigOutput.class))
    )
    JsonMap assessment_config(@RequestBody @Parameter(name = "input", schema = @Schema(implementation = AssessmentConfigInput.class)) JsonMap input);

    @ApiSpec(namespace = "common", group = "claim", scenario = "application_list")
    @Operation(summary = "Search Application  List")
    @GetMapping("/policies/{policyNo}/applications")
    @ErrorCodes(errorCodes = ClaimErrorCode.class)
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApplicationListOutput.class))
    )
    JsonMap application_list(@PathVariable(name = "policyNo") @Parameter(name = "policyNo", required = true, in = ParameterIn.PATH, example = "B02034220982", schema = @Schema(type = "string", maxLength = 64)) String policyNo,
                             @RequestParam("page_size") @Parameter(name = "page_size", example = "10", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "number", defaultValue = "10")) Integer pageSize,
                             @RequestParam("page_index") @Parameter(name = "page_index", example = "0", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "number", defaultValue = "0")) Integer pageIndex);

    @ApiSpec(namespace = "common", group = "claim", scenario = "calculation_level")
    @Operation(summary = "Calculate Claim Case Level")
    @PostMapping("/policies/{policyNo}/level/calculation")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = CaseLevelOutput.class))
    )
    JsonMap getCaseLevel(
        @PathVariable(name = "policyNo") @Parameter(name = "policyNo", required = true, in = ParameterIn.PATH, example = "B02034220982", schema = @Schema(type = "string", maxLength = 64)) String policyNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = CaseLevelInput.class)) JsonMap input);


    @ApiSpec(namespace = "common", group = "claim", scenario = "update_case_payee")
    @Operation(summary = "Update payees of case")
    @PutMapping("/claims/cases/{caseNo}/payees")
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = UpdatePayeeOutput.class))
    )
    JsonMap updatePayeeByCaseNo(
        @PathVariable(name = "caseNo") @Parameter(name = "caseNo", required = true, in = ParameterIn.PATH, example = "CLMAP202407310144405", schema = @Schema(type = "string", maxLength = 64)) String caseNo,
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = UpdatePayeeInput.class)) JsonMap input);

    @ApiSpec(namespace = "common", group = "claim", scenario = "query_claim_history")
    @Operation(summary = "Query Claim History")
    @GetMapping("/policies/{policyNo}/claims/histories")
    @ErrorCodes(errorCodes = ClaimErrorCode.class)
    @ApiResponse(
        responseCode = "200",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = QueryClaimHistoryOutput.class))
    )
    JsonMap queryClaimHistory(@PathVariable(name = "policyNo") @Parameter(name = "policyNo", required = true, in = ParameterIn.PATH, example = "B014570072", schema = @Schema(type = "string", maxLength = 64)) String policyNo,
                              @RequestParam("product_code") @Parameter(name = "product_code", example = "ZA_VHIS_PRODUCT", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "string", maxLength = 64)) String productCode,
                              @RequestParam("liability_code") @Parameter(name = "liability_code", example = "454694983958530", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "string", maxLength = 64)) String liabilityCode);


}
