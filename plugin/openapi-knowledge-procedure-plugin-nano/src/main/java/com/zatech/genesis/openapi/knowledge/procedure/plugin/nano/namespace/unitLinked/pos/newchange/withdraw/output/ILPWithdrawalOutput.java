package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.unitLinked.pos.newchange.withdraw.output;

import com.zatech.genesis.openapi.platform.integration.pos.response.PosCaseApplicationResponse;
import com.zatech.genesis.openapi.platform.share.json.Json;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/8/31 20:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ILPWithdrawalOutput extends PosCaseApplicationResponse implements Json {

}
