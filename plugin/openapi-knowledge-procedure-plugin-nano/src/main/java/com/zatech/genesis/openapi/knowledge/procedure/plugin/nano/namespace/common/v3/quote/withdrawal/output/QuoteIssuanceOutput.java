package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.quote.withdrawal.output;

import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.openapi.platform.share.annotation.Description;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2 11:11
 */

@Data
@Schema(description = "Withdrawal issuance response", name = "WithdrawalQuoteIssuanceOutput")
public class QuoteIssuanceOutput implements Json {


    @Schema(description = "quotationNo input when issue. Scenario 1：set any value as the associated number\n" +
        " Scenario 2: uses proposalNo in the response body of the Quote Creation Api as a value.")
    private String quotationNo;

    @Schema(description = "Issuance Status after cancellation,normal is WITHDRAWN")
    private IssuanceStatusEnum status;

    @Description("主要是为了兼容nanojp已经接入的产品使用，新版替换为quotationNo")
    @Deprecated(since = "v3.0", forRemoval = true)
    @Schema(description = "quotationNo input when issue. Scenario 1：set any value as the associated number\n" +
        " Scenario 2: uses proposalNo in the response body of the Quote Creation Api as a value.")
    private String proposalNo;

    @Description("主要是为了兼容nanojp已经接入的产品使用，新版替换为status")
    @Deprecated(since = "v3.0", forRemoval = true)
    @Schema(description = "Issuance Status after cancellation,normal is WITHDRAWN")
    private IssuanceStatusEnum proposalStatus;
}
