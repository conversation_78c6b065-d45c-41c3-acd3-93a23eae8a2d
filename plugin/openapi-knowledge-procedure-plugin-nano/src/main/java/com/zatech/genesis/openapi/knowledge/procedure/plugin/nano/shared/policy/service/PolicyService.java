package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.service;

import com.google.common.collect.Lists;
import com.za.taylor.enums.RuleActionType;
import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.FeeTypeEnum;
import com.zatech.gaia.resource.components.enums.common.PayerTypeEnum;
import com.zatech.gaia.resource.components.enums.common.PhoneCategoryEnum;
import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.common.UserTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PolicyCustomerEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum;
import com.zatech.gaia.resource.components.enums.market.PolicyEffectiveWithoutCollectionEnum;
import com.zatech.gaia.resource.components.enums.policy.CoverageRefTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyScenarioEnum;
import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayPeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.ObjectComponent;
import com.zatech.gaia.resource.components.enums.schema.SaPremiumCalculationMethodEnum;
import com.zatech.gaia.resource.customer.scenario.share.dto.base.CustOptionEnum;
import com.zatech.gaia.resource.graphene.policy.SignOffStatusEnum;
import com.zatech.gaia.resource.graphene.product.CoveragePeriodValueTypeEnum;
import com.zatech.genesis.model.policy.Policy;
import com.zatech.genesis.model.policy.customer.Customer;
import com.zatech.genesis.model.policy.customer.PartyIndividual;
import com.zatech.genesis.model.policy.customer.Payer;
import com.zatech.genesis.model.policy.customer.party.BaseParty;
import com.zatech.genesis.model.policy.customer.party.PartyAccount;
import com.zatech.genesis.model.policy.product.ProductLiability;
import com.zatech.genesis.model.policy.questionnaire.Questionnaire;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.common.GoodsContext;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.convert.PolicyConvertUnified;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.sync.input.PolicyHolderInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.sync.input.PolicyInsurantInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.sync.input.PolicyPayerInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.goods.PolicyOccupationClassHolder;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.goods.service.MarketService;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.object.service.ObjectService;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.Liability;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.Plan;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.Product;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.common.CustomerAccountItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.common.CustomerBaseInfo;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.common.EmailItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.common.MobilePhoneItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.common.AddressInfo;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.output.IssuanceCreateResponseOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.BeneficialOwnerItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.PolicyHolderInfo;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.PolicyPayeeInfo;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.PolicyPayerInfo;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.QueryPolicyOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.SecondInsurantItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.product.Fund;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.product.ProductInfo;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.product.insurant.BeneficiaryItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.product.insurant.InsurantItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.query.output.policyresult.product.insurant.TrusteeItem;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.uw.output.UwOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.questionnaire.IssuanceQuestionnaireBuilder;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.utils.CustomerUtils;
import com.zatech.genesis.openapi.platform.integration.bcp.request.CreateCollectionBalanceRequest;
import com.zatech.genesis.openapi.platform.integration.customer.base.AccountBaseDTO;
import com.zatech.genesis.openapi.platform.integration.customer.base.PartyCustomerExtBaseDTO;
import com.zatech.genesis.openapi.platform.integration.customer.request.PartyCompanyRequestDTO;
import com.zatech.genesis.openapi.platform.integration.customer.request.PartyCustomerRequestDTO;
import com.zatech.genesis.openapi.platform.integration.customer.request.PartyRequestDTO;
import com.zatech.genesis.openapi.platform.integration.customer.response.PartyCompanyResponseDTO;
import com.zatech.genesis.openapi.platform.integration.customer.response.PartyCustomerResponseDTO;
import com.zatech.genesis.openapi.platform.integration.customer.response.PartyResponseDTO;
import com.zatech.genesis.openapi.platform.integration.fund.response.AccountResponse;
import com.zatech.genesis.openapi.platform.integration.market.base.GoodsBasicInfoBase;
import com.zatech.genesis.openapi.platform.integration.market.base.GoodsSalesAttributesBase;
import com.zatech.genesis.openapi.platform.integration.market.base.PackageProductLiabilityBase;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsCoveragePlanResponse;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsRelatingResponse;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsSalesAttributesResponse;
import com.zatech.genesis.openapi.platform.integration.market.response.PackageInfoResponse;
import com.zatech.genesis.openapi.platform.integration.market.response.PackageProductAndLiabilityResponse;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterBcpService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterCustomerService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterFundService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterProductService;
import com.zatech.genesis.openapi.platform.integration.policy.base.CustomerUwBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.InsuredObjectComponentBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceAccountBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceAttachmentBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceCustomerAddressBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceCustomerBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceCustomerPhoneBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceEmailBase;
import com.zatech.genesis.openapi.platform.integration.policy.request.CampaignRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.CoverageRelationBase;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceAttachmentRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceBeneficiaryRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceHolderRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceInsurantRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceInsuredLoanGuaranteeRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceInsuredObjectRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuancePayeeRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuancePayerRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceProductBonusRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceProductDiscountRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceProductLiabilityRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceProductRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceTrusteeRequest;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCreateResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuancePayerResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.underwriting.RuleResponse;
import com.zatech.genesis.openapi.platform.integration.policy.response.underwriting.RuleResponseMsg;
import com.zatech.genesis.openapi.platform.integration.policy.response.underwriting.UnderwritingResponse;
import com.zatech.genesis.openapi.platform.integration.policy.uw.UnderwritingRequest;
import com.zatech.genesis.openapi.platform.integration.product.request.QueryProductRequest;
import com.zatech.genesis.openapi.platform.integration.product.response.CoveragePeriodAgreementResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.PremiumFrequencyAndInstallmentItemResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.PremiumFrequencyItemResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.PremiumPeriodAgreementResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.ProductLevelAgreementsResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.ProductResponse;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.CampaignInfoConverter;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.LiabilityConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.SalesChannelConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.UnifiedModelIssuanceCustomerConvert;
import com.zatech.genesis.openapi.platform.integration.transform.questionnaire.convert.QuestionnaireConvert;
import com.zatech.genesis.openapi.platform.integration.transform.sme.config.OpenApiSmeSwitchOnUtils;
import com.zatech.genesis.openapi.platform.integration.transform.sme.issuance.InsuredObjectTransformer;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;
import com.zatech.genesis.openapi.platform.share.TraceSupport;
import com.zatech.genesis.openapi.platform.share.exception.CommonBizErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.MarketErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.openapi.platform.share.tools.CollUtil;
import com.zatech.genesis.openapi.platform.share.tools.IdUtil;
import com.zatech.genesis.policy.api.base.Currency;
import com.zatech.genesis.policy.api.reqeust.IssuanceProductInvestmentPlanRequest;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.exception.errorcode.SystemErrorCodes;
import com.zatech.octopus.common.util.LocalDateUtils;
import com.zatech.octopus.core.util.JacksonUtil;
import com.zatech.octopus.framework.threadpool.AsyncConfiguration;
import com.zatech.octopus.module.web.dto.ResultBase;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.za.cqrs.util.Functions.doIf;
import static com.za.cqrs.util.Functions.doIfPresent;
import static com.za.cqrs.util.Functions.throwIf;
import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.WAITING_FOR_CREATEISSUANCE;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PolicyService {

    @Autowired
    private IOuterCustomerService iOuterCustomerService;

    @Autowired
    private MarketService marketService;

    @Autowired
    private IOuterBcpService iOuterBcpService;

    @Autowired
    private IOuterProductService iOuterProductService;

    private Executor executor;

    @Autowired
    private AsyncConfiguration asyncConfiguration;

    @Autowired
    private IOuterFundService iOuterFundService;

    @Autowired
    private ObjectService objectService;

    @Autowired
    private IssuanceQuestionnaireBuilder issuanceQuestionnaireBuilder;

    @PostConstruct
    public void init() {

        this.executor = asyncConfiguration.getAsyncExecutor();
    }

    public List<IssuanceCreateResponseOutput> buildIssuanceCreateResponseOutput(List<IssuanceCreateResponse> response) {

        List<IssuanceCreateResponseOutput> issuanceCreatedList = new ArrayList<>();
        ofNullable(response).ifPresent(issuanceList -> {
            issuanceList.forEach(issuanceCreateResponse -> {
                IssuanceCreateResponseOutput issuanceCreateResponseOutput =
                    new IssuanceCreateResponseOutput();
                BeanUtils.copyProperties(issuanceCreateResponse, issuanceCreateResponseOutput);
                UwOutput uwOutput = buildUwOutput(issuanceCreateResponse.getUnderwritingResult(), issuanceCreateResponse.getRuleResultList());
                issuanceCreateResponseOutput.setUwOutput(uwOutput);
                issuanceCreatedList.add(issuanceCreateResponseOutput);
            });
        });
        return issuanceCreatedList;
    }

    public UwOutput buildUwOutput(UnderwritingResponse underwritingResult, List<RuleResponse> ruleResultList) {
        return
            ofNullable(underwritingResult)
                .map(result -> {
                    UwOutput output = new UwOutput();
                    output.setPass(result.getPass());
                    List<UwOutput.RuleResponse> resp =
                        buildAccumulationDetailList(
                            ofNullable(result.getRuleResponses())
                                .filter(list -> !list.isEmpty())
                                .orElse(ruleResultList)
                        );
                    output.setRuleResponseList(resp);
                    return output;
                })
                .orElse(null);
    }

    public List<UwOutput.RuleResponse> buildAccumulationDetailList(List<RuleResponse> ruleResultList) {

        List<UwOutput.RuleResponse> ruleResponseDetailList = new ArrayList<>();
        ofNullable(ruleResultList).ifPresent(
            ruleResponseList -> {
                ruleResponseList.forEach(ruleResponse -> {
                        UwOutput.RuleResponse ruleResponseDetail = new UwOutput.RuleResponse();
                        //最外层大的分类ruleResponse归类到一个分组
                        ruleResponseDetail.setRuleCode(ruleResponse.getRuleId().getCode());
                        ruleResponseDetail.setRuleRevision(ruleResponse.getRuleId().getRevision());
                        List<UwOutput.SubRuleResponse> subRuleResponseList = new ArrayList<>();
                        ofNullable(ruleResponse.getMessages()).ifPresent(
                            //按照caseTag进行分类
                            ruleResponseMsgList -> ruleResponseMsgList
                                .stream()
                                .collect(Collectors.groupingBy(RuleResponseMsg::getCaseTag)).forEach((k, v) -> {
                                    UwOutput.SubRuleResponse subRuleResponse = new UwOutput.SubRuleResponse();
                                    //这个对应的是基线规则引擎中配置的if/else结构体中计算的结果，按照caseTag分类之后按照需要的类型进行筛选
                                    subRuleResponse.setCaseTag(k);
                                    List<UwOutput.AccumulationDetail> accumulationDetailList = new ArrayList<>();
                                    List<UwOutput.UwDecisionDetail> uwDecisionDetailList = new ArrayList<>();
                                    List<UwOutput.GeneralDecisionDetail> generalDecisionDetailList = new ArrayList<>();
                                    //对应规则引擎中的ACCUMULATION_DETAIL 部分，解释为数据累积
                                    v.stream().filter(ruleResponseMsg -> RuleActionType.ACCUMULATION_DETAIL.name()
                                            .equals(ruleResponseMsg.getActionType()))
                                        .forEach(ruleResponseMsg -> ofNullable(ruleResponseMsg.getAccumulationDetails())
                                            .ifPresent(list -> list.forEach(accumulationDetailResponse -> {
                                                UwOutput.AccumulationDetail accumulationDetail =
                                                    new UwOutput.AccumulationDetail();
                                                ofNullable(ruleResponseMsg.getRuleId()).ifPresent(
                                                    x -> {
                                                        accumulationDetail.setRuleCode(x.getCode());
                                                        accumulationDetail.setRuleRevision(x.getRevision());
                                                    }
                                                );
                                                BeanUtils.copyProperties(accumulationDetailResponse, accumulationDetail);
                                                accumulationDetailList.add(accumulationDetail);
                                            })));
                                    //核保决策信息
                                    v.stream().filter(ruleResponseMsg -> RuleActionType.UW_DECISION.name().equals(ruleResponseMsg.getActionType()))
                                        .forEach(ruleResponseMsg -> ofNullable(ruleResponseMsg.getDecisionDetails())
                                            .ifPresent(list -> list.forEach(uwDecisionDetailResponse -> {
                                                UwOutput.UwDecisionDetail uwDecisionDetail = new UwOutput.UwDecisionDetail();
                                                ofNullable(ruleResponseMsg.getRuleId()).ifPresent(
                                                    x -> {
                                                        uwDecisionDetail.setRuleCode(x.getCode());
                                                        uwDecisionDetail.setRuleRevision(x.getRevision());
                                                    }
                                                );
                                                BeanUtils.copyProperties(uwDecisionDetailResponse, uwDecisionDetail);
                                                uwDecisionDetailList.add(uwDecisionDetail);
                                            })));
                                    //通用决策信息
                                    v.stream().filter(ruleResponseMsg -> RuleActionType.GENERAL_DECISION.name()
                                            .equals(ruleResponseMsg.getActionType()))
                                        .forEach(ruleResponseMsg -> {
                                            UwOutput.GeneralDecisionDetail generalDecisionDetail =
                                                new UwOutput.GeneralDecisionDetail();
                                            ofNullable(ruleResponseMsg.getRuleId()).ifPresent(
                                                x -> {
                                                    generalDecisionDetail.setRuleCode(x.getCode());
                                                    generalDecisionDetail.setRuleRevision(x.getRevision());
                                                }
                                            );
                                            generalDecisionDetail.setDecisionType(ruleResponseMsg.getDecisionType());
                                            generalDecisionDetailList.add(generalDecisionDetail);
                                        });
                                    subRuleResponse.setAccumulationDetailList(accumulationDetailList);
                                    subRuleResponse.setUwDecisionDetailList(uwDecisionDetailList);
                                    subRuleResponse.setGeneralDecisionDetailList(generalDecisionDetailList);
                                    subRuleResponseList.add(subRuleResponse);
                                }));
                        ruleResponseDetail.setSubRuleResponseList(subRuleResponseList);
                        ruleResponseDetailList.add(ruleResponseDetail);
                    }
                );
            }
        );
        return ruleResponseDetailList;
    }

    public void buildUwOutput(List<RuleResponse> ruleResultList, UwOutput output, RuleActionType ruleActionType) {

        List<UwOutput.RuleResponseDetail> ruleResponseDetailList = new ArrayList<>();
        ofNullable(ruleResultList).ifPresent(
            ruleResponseList -> {
                ruleResponseList.forEach(ruleResponse -> {
                        UwOutput.RuleResponseDetail ruleResponseDetail = new UwOutput.RuleResponseDetail();
                        //最外层大的分类ruleResponse归类到一个分组
                        ofNullable(ruleResponse.getMessages()).ifPresent(

                            //按照caseTag进行分类
                            ruleResponseMsgList -> ruleResponseMsgList
                                .stream()
                                .collect(Collectors.groupingBy(RuleResponseMsg::getCaseTag)).forEach((k, v) -> {
                                    //核保决策信息
                                    v.forEach(ruleResponseMsg -> {
                                        if (ruleActionType != null && ruleActionType.name().equals(ruleResponseMsg.getActionType())) {
                                            ruleResponseDetail.setRuleDecision(ruleResponseMsg.getDecisionType());
                                            ruleResponseDetail.setRuleCode(ruleResponseMsg.getRuleId().getCode());
                                        }
                                        if (RuleActionType.MESSAGE.name().equals(ruleResponseMsg.getActionType())) {
                                            ruleResponseDetail.setRuleMessage(ruleResponseMsg.getMsgValue());
                                        }
                                    });
                                    ruleResponseDetailList.add(ruleResponseDetail);
                                }));
                    }
                );
            }
        );
        output.setRuleResponseMessageList(ruleResponseDetailList);
    }

    public PayerCustomerInfo createPayerByHolder(String paymentOrderNo, IssuanceHolderRequest holder) {

        if (holder.getUserType() == UserTypeEnum.PERSONAL) {
            PartyCustomerRequestDTO request = CustomerUtils.mapPerson(paymentOrderNo, holder);
            log.info("Person request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCustomerResponseDTO> response = iOuterCustomerService.createPartyCustomer(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create person payer" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PayerCustomerInfo.builder()
                .payerId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        } else {
            PartyCompanyRequestDTO request = CustomerUtils.mapCompany(paymentOrderNo, holder);
            log.info("Company request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCompanyResponseDTO> response = iOuterCustomerService.createPartyCompany(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create company payer" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PayerCustomerInfo.builder()
                .payerId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        }
    }

    public PartyCustomerInfo createHolder(PolicyHolderInput holder, String policyNo) {

        if (holder.getUserType() == UserTypeEnum.PERSONAL) {
            PartyCustomerRequestDTO request = CustomerUtils.mapPerson(PolicyCustomerEnum.HOLDER, holder.getPerson(), policyNo);
            log.info("Person request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCustomerResponseDTO> response = iOuterCustomerService.createPartyCustomer(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create person c" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PartyCustomerInfo.builder()
                .partyId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        } else {
            PartyCompanyRequestDTO request = CustomerUtils.mapCompany(PolicyCustomerEnum.HOLDER, holder.getCompany(), policyNo);
            log.info("Company request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCompanyResponseDTO> response = iOuterCustomerService.createPartyCompany(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create company holder" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PartyCustomerInfo.builder()
                .partyId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        }
    }

    public PartyCustomerInfo createPayer(PolicyPayerInput holder, String policyNo) {

        if (holder.getUserType() == UserTypeEnum.PERSONAL) {
            PartyCustomerRequestDTO request = CustomerUtils.mapPerson(PolicyCustomerEnum.PAYEE, holder.getPerson(), policyNo);
            log.info("Person request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCustomerResponseDTO> response = iOuterCustomerService.createPartyCustomer(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create person c" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PartyCustomerInfo.builder()
                .partyId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        } else {
            PartyCompanyRequestDTO request = CustomerUtils.mapCompany(PolicyCustomerEnum.PAYEE, holder.getCompany(), policyNo);
            log.info("Company request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCompanyResponseDTO> response = iOuterCustomerService.createPartyCompany(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create company holder" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PartyCustomerInfo.builder()
                .partyId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        }
    }

    public PartyCustomerInfo createIsurant(PolicyInsurantInput holder, String policyNo) {

        if (holder.getUserType() == UserTypeEnum.PERSONAL) {
            PartyCustomerRequestDTO request = CustomerUtils.mapPerson(PolicyCustomerEnum.INSURED, holder.getPerson(), policyNo);
            log.info("Person request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCustomerResponseDTO> response = iOuterCustomerService.createPartyCustomer(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create person c" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PartyCustomerInfo.builder()
                .partyId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        } else {
            PartyCompanyRequestDTO request = CustomerUtils.mapCompany(PolicyCustomerEnum.INSURED, holder.getCompany(), policyNo);
            log.info("Company request: {}", JsonParser.toJsonString(request));
            ResultBase<PartyCompanyResponseDTO> response = iOuterCustomerService.createPartyCompany(request);
            if (response == null || response.getValue() == null || response.getValue().getPartyId() == null) {
                throw new CommonException(SystemErrorCodes.internal_server_error, null, "Failed to create company holder" +
                    ofNullable(response).map(x -> ", code: " + x.getCode() + ", msg: " + x.getMsg()).orElse(""));
            }
            return PartyCustomerInfo.builder()
                .partyId(response.getValue().getPartyId())
                .accountBaseDTOList(response.getValue().getAccounts()).build();
        }
    }

    private void createCollectionBalance(PayMethodEnum paymentMethod, String payNo, Long payeeId, String channelCode, String payOrderNo,
                                         IssuanceRequest issuanceRequest) {
        CreateCollectionBalanceRequest request = new CreateCollectionBalanceRequest();
        request.setFeeType(FeeTypeEnum.ORDER_TEMPORARILY);
        request.setFeeAmount(issuanceRequest.getPremium());
        //TODO这个地方暂时优先取premiumCurrency,其实实收的应该取真正支付的币种，从需要用户传过来
        CurrencyEnum currency = ofNullable(issuanceRequest.getMultiCurrency()).map(Currency::getPremiumCurrency)
            .orElseGet(issuanceRequest::getSalesCurrency);
        request.setCurrency(currency);
        request.setSourceCurrency(currency);
        request.setSourceAmount(issuanceRequest.getPremium());
        request.setBizApplyNo(issuanceRequest.getBizApplyNo());
        request.setOrderNo(issuanceRequest.getOrderNo());
        request.setPayNo(payNo);
        request.setPayMethod(paymentMethod);
        request.setPayeeId(payeeId);
        request.setTransType(TransTypeEnum.EFECTIVE_POLICY);
        request.setBusinessTransactionNo(issuanceRequest.getTradeNo());
        request.setChannelCode(channelCode);
        request.setZoneId(issuanceRequest.getZoneId());
        request.setPayOrderNo(payOrderNo);
        log.info("Finance request: {}", JsonParser.toJsonString(request));
        iOuterBcpService.createCollectionTransaction(request);
    }

    private void createCollectionBalance(PayMethodEnum paymentMethod, String payNo, Long payeeId, String channelCode,
                                         IssuanceResponse issuanceResponse, String feeAmount, String payOrderNo) {

        CreateCollectionBalanceRequest request = new CreateCollectionBalanceRequest();
        request.setFeeType(FeeTypeEnum.ORDER_TEMPORARILY);
        request.setFeeAmount(feeAmount);

        //TODO这个地方暂时优先取premiumCurrency,其实实收的应该取真正支付的币种，从需要用户传过来
        CurrencyEnum currency = ofNullable(issuanceResponse.getMultiCurrency()).map(
            com.zatech.genesis.openapi.platform.integration.policy.response.Currency::getPremiumCurrency).orElseGet(issuanceResponse::getCurrency);
        request.setCurrency(currency);
        request.setSourceCurrency(currency);
        request.setSourceAmount(feeAmount);
        request.setBizApplyNo(issuanceResponse.getBizApplyNo());
        request.setOrderNo(issuanceResponse.getOrderNo());
        request.setPayNo(payNo);
        request.setPayMethod(paymentMethod);
        request.setPayeeId(payeeId);
        request.setTransType(TransTypeEnum.EFECTIVE_POLICY);
        request.setBusinessTransactionNo(issuanceResponse.getTradeNo());
        request.setChannelCode(channelCode);
        request.setZoneId(issuanceResponse.getZoneId());
        request.setPayOrderNo(payOrderNo);

        log.info("Finance request: {}", JsonParser.toJsonString(request));
        iOuterBcpService.createCollectionTransaction(request);
    }

    public void createPayerAndTransaction(String tradeNo, IssuanceResponse issuanceResponse, List<IssuancePayerRequest> issuancePayerList,
                                          String feeAmount) {

        createPayerAndTransaction(tradeNo, issuanceResponse, issuancePayerList, feeAmount, null);
    }

    public void createPayerAndTransaction(String tradeNo, IssuanceResponse issuanceResponse, List<IssuancePayerRequest> issuancePayerList,
                                          String feeAmount, String payOrderNo) {
        issuancePayerList.forEach(payer -> {
            PartyCustomerResponseDTO response = getOrCreateParty(tradeNo, payer);

            Long partyId = response.getPartyId();
            //取payer的第一个支付方式
            if (payer.getPayerType().equals(PayerTypeEnum.FIRST)) {
                log.info("Payer payerId: {}", partyId);
                createCollectionBalance(payer.getPayMethod(), tradeNo, partyId, TraceSupport.getChannelCodeOrNull(), issuanceResponse, feeAmount, payOrderNo);
            }
            payer.setAccount(getIssuanceAccountBaseList(response.getAccounts(), payer.getPayMethod()));
            payer.setCustomerId(partyId);
        });
    }

    private PartyCustomerResponseDTO getOrCreateParty(String tradeNo, IssuancePayerRequest payer) {
        if (payer.getCustomerId() == null) {
            PartyCustomerRequestDTO partyCustomerRequestDTO = CustomerUtils.mapPerson(tradeNo, payer);
            return iOuterCustomerService.createPartyCustomer(partyCustomerRequestDTO).getValue();
        }

        PartyRequestDTO partyRequestDTO = new PartyRequestDTO();
        partyRequestDTO.setPartyId(payer.getCustomerId());
        ResultBase<PartyResponseDTO> partyInfo = iOuterCustomerService.getPartyInfo(partyRequestDTO);

        PartyResponseDTO partyResponseDTO = partyInfo.getValue();
        List<AccountBaseDTO> accounts = partyResponseDTO.getPartyType() == PartyTypeEnum.COMPANY ? partyResponseDTO.getPartyCompany().getAccounts() : partyResponseDTO.getPartyCustomer().getAccounts();

        PartyCustomerResponseDTO partyCustomerResponseDTO = new PartyCustomerResponseDTO();
        partyCustomerResponseDTO.setAccounts(accounts);
        partyCustomerResponseDTO.setPartyId(payer.getCustomerId());

        return partyCustomerResponseDTO;

    }

    public void createPayerAndTransaction(PayMethodEnum paymentMethod, IssuanceRequest request) {

        String paymentOrderNo = request.getTradeNo();
        if (CollectionUtils.isEmpty(request.getIssuancePayerList())) {
            PayerCustomerInfo payerCustomerInfo = createPayerByHolder(paymentOrderNo, request.getIssuanceHolder());
            createCollectionBalance(paymentMethod, paymentOrderNo, payerCustomerInfo.getPayerId(), request.getChannelCode(), request.getTradeNo(), request);
            IssuancePayerRequest payerRequest = new IssuancePayerRequest();
            BeanUtils.copyProperties(request.getIssuanceHolder(), payerRequest);
            log.info("Holder payerId: {}", payerCustomerInfo.getPayerId());
            payerRequest.setPayerType(PayerTypeEnum.FIRST);
            payerRequest.setCustomerId(payerCustomerInfo.getPayerId());
            payerRequest.setAccount(getIssuanceAccountBaseList(payerCustomerInfo.getAccountBaseDTOList(), paymentMethod));
            payerRequest.setPayMethod(paymentMethod);
            payerRequest.setPayAmount(request.getPremium());
            payerRequest.setMainInsuredRelation(RelationEnum.SELF);
            request.setIssuancePayerList(Collections.singletonList(payerRequest));
        } else {
            request.getIssuancePayerList().forEach(payer -> {
                PartyCustomerResponseDTO response = getOrCreateParty(paymentOrderNo, payer);
                Long partyId = response.getPartyId();
                //取payer的第一个支付方式
                if (PayerTypeEnum.FIRST == payer.getPayerType()) {
                    log.info("Payer payerId: {}", partyId);
                    createCollectionBalance(payer.getPayMethod(), paymentOrderNo, partyId, request.getChannelCode(), request.getTradeNo(), request);
                }
                payer.setAccount(getIssuanceAccountBaseList(response.getAccounts(), paymentMethod));
                payer.setCustomerId(partyId);
            });
        }
    }

    public void createBcpTransaction(IssuanceResponse response) {

        PayMethodEnum payMethodEnum = response.getIssuancePayerList().stream().findFirst().map(IssuancePayerResponse::getPayMethod).orElseThrow();
        String paymentOrderNo = response.getTradeNo();
        response.getIssuancePayerList().forEach(payer -> {
            PartyCustomerRequestDTO partyCustomerRequestDTO = CustomerUtils.mapPerson(paymentOrderNo, payer);
            ResultBase<PartyCustomerResponseDTO> partyCustomerResponseDTOResultBase = iOuterCustomerService.createPartyCustomer(
                partyCustomerRequestDTO);
            Long partyId = partyCustomerResponseDTOResultBase.getValue().getPartyId();
            //取payer的第一个支付方式
            if (PayerTypeEnum.FIRST == payer.getPayerType()) {
                log.info("createBcpTransaction Payer payerId: {}", partyId);
                IssuanceRequest request = getIssuanceRequestForCollection(response);
                createCollectionBalance(payer.getPayMethod(), paymentOrderNo, partyId, TraceSupport.getChannelCodeOrNull(), response.getTradeNo(), request);
            }
            payer.setAccount(getIssuanceAccountBaseList(partyCustomerResponseDTOResultBase.getValue().getAccounts(), payMethodEnum));
            payer.setCustomerId(partyId);
        });
    }

    public void createPartyCustomer(IssuanceRequest request, PayMethodEnum payMethodEnum) {
        String paymentOrderNo = request.getTradeNo();
        request.getIssuancePayerList().forEach(payer -> {
            PartyCustomerRequestDTO partyCustomerRequestDTO = CustomerUtils.mapPerson(paymentOrderNo, payer);
            ResultBase<PartyCustomerResponseDTO> partyCustomerResponseDTOResultBase = iOuterCustomerService.createPartyCustomer(
                partyCustomerRequestDTO);
            Long partyId = partyCustomerResponseDTOResultBase.getValue().getPartyId();
            payer.setAccount(getIssuanceAccountBaseList(partyCustomerResponseDTOResultBase.getValue().getAccounts(), payMethodEnum));
            payer.setCustomerId(partyId);
        });
    }

    private static IssuanceRequest getIssuanceRequestForCollection(IssuanceResponse response) {
        IssuanceRequest request = new IssuanceRequest();
        request.setPremium(response.getPremium());
        request.setSalesCurrency(response.getCurrency());
        request.setMultiCurrency(com.zatech.octopus.common.util.BeanUtils.copyProperties(response.getMultiCurrency(), Currency.class));
        request.setBizApplyNo(response.getBizApplyNo());
        request.setTradeNo(response.getTradeNo());
        request.setOrderNo(response.getOrderNo());
        request.setZoneId(response.getZoneId());
        return request;
    }

    private List<IssuanceAccountBase> getIssuanceAccountBaseList(List<AccountBaseDTO> accountBaseDTOList, PayMethodEnum paymentMethod) {

        if (CollectionUtils.isNotEmpty(accountBaseDTOList)) {
            return accountBaseDTOList.stream().map(x -> {
                IssuanceAccountBase issuanceAccountBase = new IssuanceAccountBase();
                BeanUtils.copyProperties(x, issuanceAccountBase);
                issuanceAccountBase.setPayAccountId(x.getAccountId());
                issuanceAccountBase.setPaymentMethod(paymentMethod);
                return issuanceAccountBase;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * fill policy info what you need
     */
    public void fillNecessaryInfo(QueryPolicyOutput policy) {
        //投保人信息
        CompletableFuture<QueryPolicyOutput> policyHolderInfo = CompletableFuture
            .supplyAsync(() -> setPolicyHolderInfo(policy), executor);
        CompletableFuture<QueryPolicyOutput> policySecondaryInsurant = CompletableFuture
            .supplyAsync(() -> setPolicySecondaryInsurant(policy), executor);
        CompletableFuture<QueryPolicyOutput> policyBeneficialOwner = CompletableFuture
            .supplyAsync(() -> setPolicyBeneficialOwner(policy), executor);
        CompletableFuture<QueryPolicyOutput> policyPayerInfo = CompletableFuture
            .supplyAsync(() -> setPolicyPayerInfo(policy), executor);
        CompletableFuture<QueryPolicyOutput> policyPayee = CompletableFuture
            .supplyAsync(() -> setPolicyPayeeInfo(policy), executor);
        //产品信息，内部包含被保人和受益人
        CompletableFuture<QueryPolicyOutput> productInfo = CompletableFuture
            .supplyAsync(() -> setProductInfo(policy), executor);
        //填充fund信息
        CompletableFuture<QueryPolicyOutput> fundInfo = CompletableFuture
            .supplyAsync(() -> setFundInfo(policy), executor);

        List<CompletableFuture> cfList = Arrays.asList(policyHolderInfo, policyPayerInfo, policyPayee, policySecondaryInsurant, policyBeneficialOwner,
            productInfo, fundInfo);
        CompletableFuture.allOf(cfList.toArray(new CompletableFuture[cfList.size()])).join();

    }

    /**
     * policy对于没有signOff的保单也会返回signOffDate，对外会有点confused，统一为，如果保单未signOff，不返回signOffDate
     */
    public void handleSignOffDate(QueryPolicyOutput policy) {
        Optional.ofNullable(policy.getSignOffStatus()).ifPresent(x -> {
            if (SignOffStatusEnum.PENDING_CONFIRMATION.equals(x)) {
                policy.setSignOffDate(null);
            }
        });
    }

    private QueryPolicyOutput setFundInfo(QueryPolicyOutput policy) {
        if (policy.getHasInvestAccount() == YesNoEnum.YES) {
            List<AccountResponse> accountResponses = iOuterFundService.queryFundAccounts(policy.getPolicyNo(), null);
            List<ProductInfo> policyProductList = policy.getPolicyProductList();
            policyProductList.forEach(
                productInfo -> {
                    if (CollectionUtils.isEmpty(accountResponses)) {
                        productInfo.setPolicyProductFundList(Collections.emptyList());
                        return;
                    }
                    List<Fund> fundList = accountResponses.stream().map(x -> {
                        Fund fund = new Fund();
                        fund.setFundCode(x.getFundCode());
                        fund.setFundName(x.getFundName());
                        fund.setFundStatus(x.getStatus());
                        fund.setHoldingAmount(x.getHoldingAmount());
                        fund.setPendingTransactionAmount(x.getPendingTransactionAmount());
                        return fund;
                    }).toList();
                    productInfo.setPolicyProductFundList(fundList);
                }
            );
        }
        return policy;
    }


    /**
     * 投保人信息设置
     */
    private QueryPolicyOutput setPolicyHolderInfo(QueryPolicyOutput policy) {

        PolicyHolderInfo policyHolder = policy.getPolicyHolder();
        PartyResponseDTO party = getPartyInfoByPartyId(policyHolder.getCustomerId());
        if (party.getPartyCustomer() != null) {
            setCustomerBaseInfo(policyHolder, party.getPartyCustomer());
        }
        return policy;
    }

    private QueryPolicyOutput setPolicyBeneficialOwner(QueryPolicyOutput policy) {

        List<BeneficialOwnerItem> policyBeneficialOwnerList = policy.getPolicyBeneficialOwnerList();
        if (CollectionUtils.isEmpty(policyBeneficialOwnerList)) {
            return policy;
        }
        policyBeneficialOwnerList.forEach(policyBeneficialOwner -> {
            PartyResponseDTO party = getPartyInfoByPartyId(policyBeneficialOwner.getCustomerId());
            if (party.getPartyCustomer() != null) {
                setCustomerBaseInfo(policyBeneficialOwner, party.getPartyCustomer());
            }
        });
        return policy;
    }


    private QueryPolicyOutput setPolicySecondaryInsurant(QueryPolicyOutput policy) {

        List<SecondInsurantItem> policySecondaryInsurantList = policy.getPolicySecondaryInsurantList();
        if (CollectionUtils.isEmpty(policySecondaryInsurantList)) {
            return policy;
        }
        policySecondaryInsurantList.forEach(SecondaryInsurant -> {
            PartyResponseDTO party = getPartyInfoByPartyId(SecondaryInsurant.getCustomerId());
            if (party.getPartyCustomer() != null) {
                setCustomerBaseInfo(SecondaryInsurant, party.getPartyCustomer());
            }
        });
        return policy;
    }


    private QueryPolicyOutput setPolicyPayerInfo(QueryPolicyOutput policy) {

        List<PolicyPayerInfo> policyPayerList = policy.getPolicyPayerList();
        if (CollectionUtils.isEmpty(policyPayerList)) {
            return policy;
        }
        policyPayerList.forEach(policyPayerInfo -> {
            PartyResponseDTO party = getPartyInfoByPartyId(policyPayerInfo.getCustomerId());
            if (party.getPartyCustomer() != null) {
                setCustomerBaseInfo(policyPayerInfo, party.getPartyCustomer());
            }
        });
        return policy;
    }

    private QueryPolicyOutput setPolicyPayeeInfo(QueryPolicyOutput policy) {

        List<PolicyPayeeInfo> policyPayeeList = policy.getPolicyPayeeList();
        if (CollectionUtils.isEmpty(policyPayeeList)) {
            return policy;
        }
        policyPayeeList.forEach(policyPayeeInfo -> {
            doIfPresent(policyPayeeInfo.getCustomerId(), id -> {
                doIfPresent(getPartyInfoByPartyId(id), party -> {
                    setCustomerBaseInfo(policyPayeeInfo, party.getPartyCustomer());
                });
            });
        });
        return policy;
    }


    /**
     * 设置产品信息
     */
    private QueryPolicyOutput setProductInfo(QueryPolicyOutput policy) {

        List<ProductInfo> policyProductList = policy.getPolicyProductList();
        for (ProductInfo policyProduct : policyProductList) {
            List<InsurantItem> policyInsurantList = policyProduct.getPolicyInsurantList();
            for (InsurantItem insurantItem : policyInsurantList) {
                setInsurdInfo(insurantItem);
                List<BeneficiaryItem> beneficiariyList = insurantItem.getPolicyBeneficiaryList();
                setBeneficiaryInfo(beneficiariyList);
                insurantItem.setPolicyTrusteeList(buildTrusteeInfo(insurantItem.getPolicyTrusteeList()));
            }
        }
        return policy;
    }

    private List<TrusteeItem> buildTrusteeInfo(List<TrusteeItem> policyTrusteeList) {
        if (CollUtil.isNotEmpty(policyTrusteeList)) {
            return policyTrusteeList.stream().map(x -> {
                if (Objects.nonNull(x.getCustomerId())) {
                    PartyResponseDTO party = getPartyInfoByPartyId(x.getCustomerId());
                    if (Objects.nonNull(party.getPartyCustomer())) {
                        setCustomerBaseInfo(x, party.getPartyCustomer());
                    }
                }
                return x;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 设置受益人信息
     */
    private void setBeneficiaryInfo(List<BeneficiaryItem> policyBeneficiariyList) {

        if (CollectionUtils.isEmpty(policyBeneficiariyList)) {
            return;
        }
        for (BeneficiaryItem beneficiaryItem : policyBeneficiariyList) {
            // 受益人可能不是customer
            if (beneficiaryItem.getCustomerId() == null) {
                continue;
            }
            PartyResponseDTO party = getPartyInfoByPartyId(beneficiaryItem.getCustomerId());
            if (party.getPartyCustomer() != null) {
                setCustomerBaseInfo(beneficiaryItem, party.getPartyCustomer());
            }
        }
    }

    /**
     * 设置被保人信息
     */
    private void setInsurdInfo(InsurantItem insurantItem) {

        PartyResponseDTO party = getPartyInfoByPartyId(insurantItem.getCustomerId());
        if (party.getPartyCustomer() != null) {
            setCustomerBaseInfo(insurantItem, party.getPartyCustomer());
        }
        insurantItem.setTitle(party.getPartyCustomer().getTitle());
    }


    public PartyResponseDTO getPartyInfoByPartyId(long partyId) {

        PartyRequestDTO party = new PartyRequestDTO(partyId);
        List<CustOptionEnum> options = Arrays.asList(CustOptionEnum.values());
        party.setOptions(options);

        ResultBase<PartyResponseDTO> response = iOuterCustomerService.getPartyInfo(party);
        if (response != null && response.isSuccess()) {
            return response.getValue();
        }
        return new PartyResponseDTO();
    }

    /**
     * 基本信息设置
     */
    public void setCustomerBaseInfo(CustomerBaseInfo baseInfo, PartyCustomerExtBaseDTO partyCustomer) {

        if (baseInfo == null || partyCustomer == null) {
            log.info("setCustomerBaseInfo, baseInfo or partyCustomer is null ");
            return;
        }
        BeanUtils.copyProperties(partyCustomer, baseInfo);
        //设置账户信息
        List<CustomerAccountItem> accountItems = new ArrayList<>();
        ofNullable(partyCustomer.getAccounts()).orElse(new ArrayList<>()).forEach(accountBaseDTO -> {
            CustomerAccountItem accountItem = new CustomerAccountItem();
            BeanUtils.copyProperties(accountBaseDTO, accountItem);
            accountItem.setPayAccountId(accountBaseDTO.getAccountId());
            accountItems.add(accountItem);

        });
        baseInfo.setAccount(accountItems);
        baseInfo.setAccounts(accountItems);

        //设置动态拓展字段
        Map<String, String> extensions = baseInfo.getExtensions();
        if (MapUtils.isEmpty(extensions)) {
            extensions = new HashMap<>();
        }
        extensions.putAll(ofNullable(partyCustomer.getExtensionMap()).orElse(Collections.emptyMap()));
        baseInfo.setExtensions(extensions);

        //地址信息
        List<AddressInfo> addressInfos = new ArrayList<>();
        ofNullable(partyCustomer.getAddresses()).orElse(Collections.emptyList()).forEach(address -> {
            AddressInfo addressInfo = new AddressInfo();
            BeanUtils.copyProperties(address, addressInfo);
            if (MapUtils.isEmpty(addressInfo.getExtensions())) {
                addressInfo.setExtensions(new HashMap<>());
            }
            addressInfo.setAddressId(address.getAddressId());
            addressInfo.getExtensions().putAll(ofNullable(address.getExtensionMap()).orElse(Collections.emptyMap()));
            addressInfos.add(addressInfo);
        });
        baseInfo.setAddress(addressInfos);
        baseInfo.setAddresses(addressInfos);

        //邮箱信息
        List<EmailItem> emailItems = new ArrayList<>();
        ofNullable(partyCustomer.getEmails()).orElse(Collections.emptyList()).forEach(email -> {
            EmailItem emailItem = new EmailItem();
            BeanUtils.copyProperties(email, emailItem);
            emailItems.add(emailItem);
        });
        baseInfo.setEmail(emailItems);
        baseInfo.setEmails(emailItems);

        //电话信息
        List<MobilePhoneItem> phoneItems = new ArrayList<>();
        ofNullable(partyCustomer.getPhones()).orElse(Collections.emptyList()).forEach(phone -> {
            MobilePhoneItem mobilePhoneItem = new MobilePhoneItem();
            BeanUtils.copyProperties(phone, mobilePhoneItem);
            phoneItems.add(mobilePhoneItem);
        });
        baseInfo.setMobilePhone(phoneItems);
        baseInfo.setPhones(phoneItems);


    }

    public void fillPolicyParameter(IssuanceRequest issuanceRequest, Long planId, Long goodsId) {

        GoodsRelatingResponse goodsRelatingResponse = queryGoodsRelating(goodsId, planId);
        if (Objects.isNull(goodsRelatingResponse)) {
            throw new OpenApiException(CommonBizErrorCode.goods_info_is_not_present);
        }
        //填充责任项参数
        Map<Long, List<Long>> productAndRequiredLiabilityIdList = getProductAndRequiredLiabilityIdList(goodsRelatingResponse);
        fillRequiredLiabilityIdList(issuanceRequest, productAndRequiredLiabilityIdList);
        Map<Long, ProductLevelAgreementsResponse> productLevelAgreementsResponseMap =
            goodsRelatingResponse.getPackageList().get(0).getPackageProductAndLiabilityList().stream().map(x -> {
                Long productId = x.getProductId();
                QueryProductRequest queryProductRequest = new QueryProductRequest();
                queryProductRequest.setProductId(productId);
                queryProductRequest.setQueryProductLevelAgreements(true);
                return iOuterProductService.queryProduct(queryProductRequest);
            }).collect(Collectors.toMap(ProductResponse::getProductId, ProductResponse::getProductLevelAgreements));
        //填充保障周期，缴费周期，缴费频率的默认参数
        if (CollectionUtils.isNotEmpty(issuanceRequest.getIssuanceProductList())) {
            issuanceRequest.setIssuanceProductList(issuanceRequest.getIssuanceProductList().stream().map(x -> {
                ProductLevelAgreementsResponse productLevelAgreements = productLevelAgreementsResponseMap.get(x.getProductId());
                if (Objects.nonNull(productLevelAgreements)) {
                    ofNullable(productLevelAgreements.getCoveragePeriodAgreementList())
                        .ifPresent(coveragePeriodAgreementList ->
                            fillIssuanceProductRequestCoverage(x, coveragePeriodAgreementList.get(0)));
                    ofNullable(productLevelAgreements.getPremiumPeriodAgreementList())
                        .ifPresent(premiumPeriodAgreementList ->
                            fillIssuanceProductRequestPremiumPeriod(x, premiumPeriodAgreementList.get(0)));
                    ofNullable(productLevelAgreements.getPremiumFrequencyAndInstallment())
                        .ifPresent(premiumFrequencyAndInstallmentList ->
                            fillIssuanceProductRequestPremiumFrequency(x,
                                productLevelAgreements.getPremiumFrequencyAndInstallmentList().get(0)));
                }
                return x;
            }).collect(Collectors.toList()));
        }
    }


    public void fillPolicyParameterV3(Policy policy, IssuanceRequest issuanceRequest) {

        //如果product里面有值，并且Liability也有则直接返回不处理
        if (CollectionUtils.isNotEmpty(policy.getProductList()) && CollectionUtils.isNotEmpty(policy.getProductList().get(0).getProductLiabilityList())) {
            return;
        }
        GoodsRelatingResponse goodsRelatingResponse = queryGoodsRelating(issuanceRequest.getGoodsId(), issuanceRequest.getPlanId());
        if (Objects.isNull(goodsRelatingResponse)) {
            throw new OpenApiException(CommonBizErrorCode.goods_info_is_not_present);
        }
        //填充product和Liability项参数
        Map<String, List<String>> productAndRequiredLiabilityCodeList = getProductAndRequiredLiabilityCodeListV3(goodsRelatingResponse);
        fillRequiredLiabilityIdListV3(policy, productAndRequiredLiabilityCodeList);
        if (Objects.nonNull(policy.getCoveragePeriodType()) || Objects.nonNull(policy.getCoveragePeriod())) {
            return;
        }
        Map<String, ProductLevelAgreementsResponse> productLevelAgreementsResponseMap =
            goodsRelatingResponse.getPackageList().get(0).getPackageProductAndLiabilityList().stream().map(x -> {
                Long productId = x.getProductId();
                QueryProductRequest queryProductRequest = new QueryProductRequest();
                queryProductRequest.setProductId(productId);
                queryProductRequest.setQueryProductLevelAgreements(true);
                queryProductRequest.setQueryProductBasicInfo(true);
                return iOuterProductService.queryProduct(queryProductRequest);
            }).collect(Collectors.toMap(
                response -> response.getProductBasicInfo().getProductCode(), // 使用lambda表达式获取productCode作为键
                ProductResponse::getProductLevelAgreements // 使用方法引用获取productLevelAgreements作为值
            ));
        //填充保障周期
        if (CollectionUtils.isNotEmpty(policy.getProductList())) {
            policy.getProductList().forEach(x -> {
                ProductLevelAgreementsResponse productLevelAgreements = productLevelAgreementsResponseMap.get(x.getProductCode());
                if (Objects.nonNull(productLevelAgreements)) {
                    ofNullable(productLevelAgreements.getCoveragePeriodAgreementList())
                        .ifPresent(coveragePeriodAgreementList ->
                            //保障周期
                            fillIssuanceProductRequestCoverage(policy, coveragePeriodAgreementList.get(0)));
                }
            });
        }
    }

    public void fillIssuanceProductRequestPremiumFrequency(IssuanceProductRequest issuanceProductRequest,
                                                           PremiumFrequencyAndInstallmentItemResponse premiumFrequencyAndInstallmentItemResponse) {

        if (Objects.isNull(issuanceProductRequest.getPremiumFrequencyType())) {
            PremiumFrequencyItemResponse premiumFrequencyItem = premiumFrequencyAndInstallmentItemResponse.getPremiumFrequencyItem();
            if (PayFrequencyTypeEnum.SINGLE.getCode().equals(premiumFrequencyItem.getPaymentFrequencyType())) {
                issuanceProductRequest.setPremiumFrequencyType(PayFrequencyTypeEnum.SINGLE);
            } else {
                if (Objects.isNull(issuanceProductRequest.getPremiumFrequency())) {
                    issuanceProductRequest.setPremiumFrequencyType(Arrays.stream(PayFrequencyTypeEnum.values())
                        .filter(payFrequencyTypeEnum -> premiumFrequencyItem.getPaymentFrequencyType()
                            .equals(payFrequencyTypeEnum.getCode())).findFirst().orElse(null));
                    issuanceProductRequest.setPremiumFrequency(String.valueOf(premiumFrequencyItem.getInterval()));
                }
            }
        }
    }

    public void fillIssuanceProductRequestPremiumPeriod(IssuanceProductRequest issuanceProductRequest,
                                                        PremiumPeriodAgreementResponse premiumPeriodAgreementResponse) {

        if (Objects.isNull(issuanceProductRequest.getPremiumPeriodType())) {
            if (PayPeriodTypeEnum.SINGLE.getCode().equals(premiumPeriodAgreementResponse.getPremiumPeriodType())) {
                issuanceProductRequest.setPremiumPeriodType(PayPeriodTypeEnum.SINGLE);
            } else if (CoveragePeriodValueTypeEnum.PRE_DEFINED.getCode().equals(premiumPeriodAgreementResponse.getValueType())) {
                issuanceProductRequest.setPremiumPeriod(String.valueOf(premiumPeriodAgreementResponse.getMinPeriod()));
                issuanceProductRequest.setPremiumPeriodType(Arrays.stream(PayPeriodTypeEnum.values())
                    .filter(payPeriodTypeEnum -> premiumPeriodAgreementResponse.getPremiumPeriodType()
                        .equals(payPeriodTypeEnum.getCode())).findFirst().orElse(null));
            }
        }
    }

    public void fillIssuanceProductRequestCoverage(IssuanceProductRequest issuanceProductRequest,
                                                   CoveragePeriodAgreementResponse coveragePeriodAgreementResponse) {

        if (Objects.isNull(issuanceProductRequest.getCoveragePeriodType())) {
            if (CoveragePeriodValueTypeEnum.PRE_DEFINED.getCode().equals(coveragePeriodAgreementResponse.getValueType())) {
                issuanceProductRequest.setCoveragePeriod(String.valueOf(coveragePeriodAgreementResponse.getMinPeriod()));
                issuanceProductRequest.setCoveragePeriodType(Arrays.stream(CoveragePeriodTypeEnum.values())
                    .filter(coveragePeriodTypeEnum -> coveragePeriodAgreementResponse.getCoveragePeriodType()
                        .equals(coveragePeriodTypeEnum.getCode())).findFirst().orElse(null));
            }
        }
    }

    public void fillIssuanceProductRequestCoverage(Policy policy,
                                                   CoveragePeriodAgreementResponse coveragePeriodAgreementResponse) {

        if (CoveragePeriodValueTypeEnum.PRE_DEFINED.getCode().equals(coveragePeriodAgreementResponse.getValueType())) {
            policy.setCoveragePeriod(String.valueOf(coveragePeriodAgreementResponse.getMinPeriod()));
            policy.setCoveragePeriodType(Arrays.stream(CoveragePeriodTypeEnum.values())
                .filter(coveragePeriodTypeEnum -> coveragePeriodAgreementResponse.getCoveragePeriodType()
                    .equals(coveragePeriodTypeEnum.getCode())).findFirst().orElse(null));
        }
    }

    public boolean needCreateTransaction(IssuanceRequest issuanceRequest) {
        if (issuanceRequest.getIssuanceTransactionType() == WAITING_FOR_CREATEISSUANCE) {
            return false;
        }

        YesNoEnum issueWithoutPayment = issuanceRequest.getIssueWithoutPayment();
        if (issueWithoutPayment == YesNoEnum.NO) {
            return true;
        }
        if (issueWithoutPayment == YesNoEnum.YES) {
            return false;
        }
        // 当为null时，执行原逻辑
        return haveEffectiveWithPay(queryGoodsRelating(issuanceRequest.getGoodsId(), issuanceRequest.getPlanId()));
    }


    public boolean haveEffectiveWithPay(GoodsRelatingResponse goodsRelatingResponse) {

        //暂时只处理见费的场景，即PolicyEffectiveWithoutPay=NO
        return PolicyEffectiveWithoutCollectionEnum.NO.getCode().equals(ofNullable(goodsRelatingResponse.getSalesAttributes()).map(GoodsSalesAttributesBase::getPolicyEffectiveWithoutPayCode).orElse(null));
    }

    public GoodsRelatingResponse queryGoodsRelating(Long goodsId, Long planId) {

        throwIf(Objects.isNull(goodsId), () -> new OpenApiException(MarketErrorCode.goodsId_can_not_be_empty));
        return marketService.queryGoodsRelating(goodsId, planId);
    }

    public GoodsRelatingResponse queryGoodsPackageRelating(Long goodsId, Long packageId) {

        throwIf(Objects.isNull(goodsId), () -> new OpenApiException(MarketErrorCode.goodsId_can_not_be_empty));
        return marketService.queryGoodsRelating(goodsId, packageId);
    }

    public Map<Long, List<Long>> getProductAndRequiredLiabilityIdList(GoodsRelatingResponse goodsRelatingResponse) {

        List<PackageProductAndLiabilityResponse> packageProductAndLiabilityList =
            goodsRelatingResponse.getPackageList().get(0).getPackageProductAndLiabilityList();
        return ofNullable(packageProductAndLiabilityList).map(packageProductAndLiabilityResponses ->
            packageProductAndLiabilityResponses.stream().collect(Collectors.toMap(PackageProductAndLiabilityResponse::getProductId,
                packageProductAndLiabilityResponse -> packageProductAndLiabilityResponse.getPackageLiabilityList()
                    .stream().filter(liability -> YesNoEnum.NO.equals(liability.getIsOptional()))
                    .map(PackageProductLiabilityBase::getLiabilityId).collect(Collectors.toList()), (a, b) -> a))
        ).orElseThrow(() -> new OpenApiException(MarketErrorCode.package_product_and_liability));
    }

    public Map<String, List<String>> getProductAndRequiredLiabilityCodeListV3(GoodsRelatingResponse goodsRelatingResponse) {
        List<PackageProductAndLiabilityResponse> packageProductAndLiabilityList =
            goodsRelatingResponse.getPackageList().get(0).getPackageProductAndLiabilityList();
        return ofNullable(packageProductAndLiabilityList).map(packageProductAndLiabilityResponses ->
            packageProductAndLiabilityResponses.stream().collect(Collectors.toMap(PackageProductAndLiabilityResponse::getProductCode,
                packageProductAndLiabilityResponse -> packageProductAndLiabilityResponse.getPackageLiabilityList()
                    .stream().filter(liability -> YesNoEnum.NO.equals(liability.getIsOptional()))
                    .map(PackageProductLiabilityBase::getLiabilityCode).collect(Collectors.toList()), (a, b) -> a))
        ).orElseThrow(() -> new OpenApiException(MarketErrorCode.package_product_and_liability));
    }


    public void fillRequiredLiabilityIdList(IssuanceRequest issuanceRequest, Map<Long, List<Long>> productAndRequiredLiabilityIdList) {

        Optional.ofNullable(issuanceRequest.getIssuanceProductList())
            .ifPresent(issuanceProductRequests -> issuanceProductRequests.forEach(issuanceProductRequest -> {
                if (!CollectionUtils.isEmpty(issuanceProductRequest.getIssuanceProductLiabilityList())
                    && issuanceProductRequest.getIssuanceProductLiabilityList().stream().anyMatch(x -> Objects.nonNull(x.getSumInsured()))) {
                    return;
                }
                List<Long> requiredLiabilityList = productAndRequiredLiabilityIdList.get(issuanceProductRequest.getProductId());
                ofNullable(requiredLiabilityList).ifPresent(list -> {
                    List<IssuanceProductLiabilityRequest> issuanceProductLiabilityRequestList = list.stream().map(id -> {
                        IssuanceProductLiabilityRequest issuanceProductLiabilityRequest = new IssuanceProductLiabilityRequest();
                        issuanceProductLiabilityRequest.setLiabilityId(id);
                        if (SaPremiumCalculationMethodEnum.CALCULATE_PREMIUM.equals(issuanceRequest.getCalculateMethod())) {
                            issuanceProductLiabilityRequest.setSumInsured(issuanceProductRequest.getSumInsured());
                        }
                        return issuanceProductLiabilityRequest;
                    }).collect(Collectors.toList());
                    if (Objects.isNull(issuanceProductRequest.getIssuanceProductLiabilityList())) {
                        issuanceProductRequest.setIssuanceProductLiabilityList(issuanceProductLiabilityRequestList);
                    } else {
                        Map<Long, IssuanceProductLiabilityRequest> liabilityIdMap = issuanceProductRequest.getIssuanceProductLiabilityList().stream()
                            .collect(Collectors.toMap(IssuanceProductLiabilityRequest::getLiabilityId, Function.identity()));
                        issuanceProductLiabilityRequestList.forEach(issuanceProductLiabilityRequest -> {
                            if (liabilityIdMap.containsKey(issuanceProductLiabilityRequest.getLiabilityId())) {
                                liabilityIdMap.put(issuanceProductLiabilityRequest.getLiabilityId(), issuanceProductLiabilityRequest);
                            }
                        });
                        issuanceProductRequest.setIssuanceProductLiabilityList(liabilityIdMap.values().stream().collect(Collectors.toList()));
                    }
                });
            }));
    }

    public void fillRequiredLiabilityIdListV3(Policy policy, Map<String, List<String>> productAndRequiredLiabilityIdList) {

        if (CollectionUtils.isEmpty(policy.getProductList())) {
            productAndRequiredLiabilityIdList.forEach((key, requiredLiabilityList) -> {
                com.zatech.genesis.model.policy.product.Product product = new com.zatech.genesis.model.policy.product.Product();
                ofNullable(requiredLiabilityList).ifPresent(list -> {
                    List<ProductLiability> productLiabilityList = list.stream().map(liabilityCode -> {
                        ProductLiability productLiability = new ProductLiability();
                        productLiability.setLiabilityCode(liabilityCode);
                        return productLiability;
                    }).toList();
                    product.setProductCode(key);
                    product.getProductLiabilityList().addAll(productLiabilityList);
                    policy.getProductList().add(product);
                });
            });
        } else {
            policy.getProductList().forEach(product -> {
                List<String> requiredLiabilityList = productAndRequiredLiabilityIdList.get(product.getProductCode());
                ofNullable(requiredLiabilityList).ifPresent(list -> {
                    List<ProductLiability> productLiabilityList = list.stream().map(liabilityCode -> {
                        ProductLiability productLiability = new ProductLiability();
                        productLiability.setLiabilityCode(liabilityCode);
                        if (SaPremiumCalculationMethodEnum.CALCULATE_PREMIUM.equals(policy.getCalculationMethod())) {
                            productLiability.setSumInsured(product.getSumInsured());
                        }
                        return productLiability;
                    }).toList();
                    if (CollectionUtils.isEmpty(product.getProductLiabilityList())) {
                        product.getProductLiabilityList().addAll(productLiabilityList);
                    }
                });
            });
        }
    }

    public void processUWAndIssuanceInfo(Map<String, Object> jsonMap) {
        Long goodsId = (Long) jsonMap.get("goodsId");
        Map<String, Map<String, Object>> planMap = (Map<String, Map<String, Object>>) jsonMap.get("plans");
        planMap.forEach((planId, planInfo) -> {
            GoodsRelatingResponse goodsRelatingResponse = queryGoodsRelating(goodsId, Long.valueOf(planId));
            if (Objects.isNull(goodsRelatingResponse)) {
                throw new OpenApiException(CommonBizErrorCode.goods_info_is_not_present, planId, goodsId);
            }
            Plan plan = JsonParser.fromJson(JsonParser.toJsonString(planInfo), Plan.class);
            Map<Long, List<Long>> productLiabilityIdsMap = getProductAndRequiredLiabilityIdList(goodsRelatingResponse);
            Map<String, Map<String, Object>> productMap = (Map<String, Map<String, Object>>) planInfo.get("products");
            Map<String, Product> products = plan.getProducts().entrySet().stream().map(productEntry -> {
                Long productId = Long.valueOf(productEntry.getKey());
                Product product = productEntry.getValue();
                QueryProductRequest queryProductRequest = new QueryProductRequest();
                queryProductRequest.setProductId(productId);
                queryProductRequest.setQueryProductLevelAgreements(true);
                ProductResponse productResponse = iOuterProductService.queryProduct(queryProductRequest);
                ProductLevelAgreementsResponse productLevelAgreements = productResponse.getProductLevelAgreements();
                if (Objects.nonNull(productLevelAgreements)) {
                    ofNullable(productLevelAgreements.getCoveragePeriodAgreementList())
                        .ifPresent(coveragePeriodAgreementList ->
                            fillProductDpCoverage(product, coveragePeriodAgreementList.get(0)));
                    ofNullable(productLevelAgreements.getPremiumPeriodAgreementList())
                        .ifPresent(premiumPeriodAgreementList ->
                            fillProductDpPremiumPeriod(product, premiumPeriodAgreementList.get(0)));
                    ofNullable(productLevelAgreements.getPremiumFrequencyAndInstallmentList())
                        .ifPresent(premiumFrequencyAndInstallmentList ->
                            fillProductDpPremiumFrequency(product,
                                productLevelAgreements.getPremiumFrequencyAndInstallmentList().get(0)));
                }
                fillRequiredLiabilityIdListForDp(product, productLiabilityIdsMap.get(productId));
                return productEntry;
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> b));

            //内部填充productMap的参数
            productMap.forEach((k, v) -> {
                Product product = products.get(k);
                //只有不存在时才会默认填充进去，如果productMap中本身有了数据就不会再填充了
                v.putIfAbsent("coveragePeriodType", product.getCoveragePeriodType());
                v.putIfAbsent("coveragePeriod", product.getCoveragePeriod());
                v.putIfAbsent("premiumFrequencyType", product.getPremiumFrequencyType());
                v.putIfAbsent("premiumPeriodType", product.getPremiumPeriodType());
                v.putIfAbsent("premiumPeriod", product.getPremiumPeriod());
                v.putIfAbsent("liabilities", product.getLiabilities());
            });
        });
    }

    private void fillProductDpPremiumFrequency(Product product,
                                               PremiumFrequencyAndInstallmentItemResponse premiumFrequencyAndInstallmentItemResponse) {
        if (Objects.isNull(product.getPremiumFrequencyType())) {
            PremiumFrequencyItemResponse premiumFrequencyItem = premiumFrequencyAndInstallmentItemResponse.getPremiumFrequencyItem();
            product.setPremiumFrequencyType(Arrays.stream(PayFrequencyTypeEnum.values())
                .filter(payFrequencyTypeEnum -> premiumFrequencyItem.getPaymentFrequencyType()
                    .equals(payFrequencyTypeEnum.getCode())).findFirst().map(PayFrequencyTypeEnum::getCode).orElse(null));
        }
    }

    private void fillProductDpPremiumPeriod(Product product,
                                            PremiumPeriodAgreementResponse premiumPeriodAgreementResponse) {
        if (Objects.isNull(product.getPremiumPeriodType())) {
            if (PayPeriodTypeEnum.SINGLE.getCode().equals(premiumPeriodAgreementResponse.getPremiumPeriodType())) {
                product.setPremiumPeriodType(PayPeriodTypeEnum.SINGLE.getCode());
            } else if (CoveragePeriodValueTypeEnum.PRE_DEFINED.getCode().equals(premiumPeriodAgreementResponse.getValueType())) {
                product.setPremiumPeriod(PayPeriodTypeEnum.SINGLE.getCode());
                product.setPremiumPeriodType(Arrays.stream(PayPeriodTypeEnum.values())
                    .filter(payPeriodTypeEnum -> premiumPeriodAgreementResponse.getPremiumPeriodType()
                        .equals(payPeriodTypeEnum.getCode())).findFirst().map(PayPeriodTypeEnum::getCode).orElse(null));
            }
        }
    }

    private void fillProductDpCoverage(Product product,
                                       CoveragePeriodAgreementResponse coveragePeriodAgreementResponse) {

        if (Objects.isNull(product.getCoveragePeriodType())) {
            if (CoveragePeriodValueTypeEnum.PRE_DEFINED.getCode().equals(coveragePeriodAgreementResponse.getValueType())) {
                product.setCoveragePeriod(coveragePeriodAgreementResponse.getMinPeriod());
                product.setCoveragePeriodType(
                    Arrays.stream(CoveragePeriodTypeEnum.values())
                        .filter(coveragePeriodTypeEnum -> coveragePeriodAgreementResponse.getCoveragePeriodType()
                            .equals(coveragePeriodTypeEnum.getCode())).findFirst().map(CoveragePeriodTypeEnum::getCode).orElse(null));
            }
        }
    }

    public void fillRequiredLiabilityIdListForDp(Product product, List<Long> productLiabilityIds) {

        if (CollUtil.isEmpty(product.getLiabilities())) {
            Map<Long, Liability> liabilities = new HashMap<>();
            Optional.ofNullable(productLiabilityIds).ifPresent(ids -> ids.forEach(liabilityId -> {
                Liability liability = new Liability();
                liability.setLiabilityId(liabilityId);
                liabilities.put(liabilityId, liability);
            }));
            product.setLiabilities(liabilities);
        }
    }

    public IssuanceRequest fillDefaultValue(IssuanceRequest issuanceRequest) {

        IssuanceRequest copy = new IssuanceRequest();
        BeanUtils.copyProperties(issuanceRequest, copy);
        copy.setIssuanceProductList(
            Optional.ofNullable(copy.getIssuanceProductList()).map(productRequestList -> productRequestList.stream().map(x -> {
                x.setIssuanceInsuredObject(Optional.ofNullable(x.getIssuanceInsuredObject()).map(objectList -> objectList.stream().map(object -> {
                    IssuanceInsuredLoanGuaranteeRequest issuanceInsuredLoanGuarantee = object.getIssuanceInsuredLoanGuarantee();
                    if (Objects.nonNull(issuanceInsuredLoanGuarantee)) {
                        if (Objects.isNull(issuanceInsuredLoanGuarantee.getRemainingLoanAmount())) {
                            issuanceInsuredLoanGuarantee.setRemainingLoanAmount(issuanceInsuredLoanGuarantee.getLoanAmount());
                        }
                    }
                    return object;
                }).collect(Collectors.toList())).orElse(Lists.newArrayList()));
                return x;
            }).collect(Collectors.toList())).orElse(Lists.newArrayList()));

        return copy;
    }

    public void processQuestionnaire(IssuanceRequest issuanceRequest, Policy policy) {
        issuanceQuestionnaireBuilder.fillQuestionnaire(policy.getQuestionnaire());
        Questionnaire questionnaire = policy.getQuestionnaire();
        if (questionnaire == null) {
            return;
        }
        CustomerUwBase customerUwBase = QuestionnaireConvert.INSTANCE.convert(questionnaire);
        issuanceRequest.setCustomerUwList(List.of(customerUwBase));
    }

    public void processQuestionnaire(UnderwritingRequest underwritingRequest, Policy policy) {
        issuanceQuestionnaireBuilder.fillQuestionnaire(policy.getQuestionnaire());
        Questionnaire questionnaire = policy.getQuestionnaire();
        if (questionnaire == null) {
            return;
        }
        CustomerUwBase customerUwBase = QuestionnaireConvert.INSTANCE.convert(questionnaire);
        underwritingRequest.setCustomerUwList(List.of(customerUwBase));
    }

    public void processIssuanceBasic(IssuanceRequest issuanceRequest, GoodsRelatingResponse goodsRelatingResponse,
                                     Policy policy) {

        GoodsCoveragePlanResponse currentPlanCoverageResponse = getGoodsCoveragePlanResponse(goodsRelatingResponse, policy);
        if (currentPlanCoverageResponse == null && policy.getPlanCode() != null) {
            log.info("planCode not match goodsId:{}", JacksonUtil.toJSONString(goodsRelatingResponse));
            throw new OpenApiException(MarketErrorCode.plan_code_not_match_goodsId);
        }
        issuanceRequest.setChannelCode(TraceSupport.getChannelCodeOrNull());
        issuanceRequest.setIssuanceTransactionType(IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE);
        issuanceRequest.setEnableAsync(YesNoEnum.NO);
        issuanceRequest.setEnableFullSync(BooleanToYesNoEnum.convert(policy.getEnableFullSync()));
        issuanceRequest.setEnableTaylor(true);
        issuanceRequest.setCoveragePeriod(policy.getCoveragePeriod());
        issuanceRequest.setCoveragePeriodType(policy.getCoveragePeriodType());
        issuanceRequest.setIsLegalBeneficiary(policy.getIsLegalBeneficiary() == null ? Boolean.FALSE : policy.getIsLegalBeneficiary());
        issuanceRequest.setGoodsId(goodsRelatingResponse.getGoodsId());
        Optional.ofNullable(goodsRelatingResponse.getGoodsBasicInfo()).map(GoodsBasicInfoBase::getGoodsCode)
            .or(() -> Optional.ofNullable(policy.getGoodsCode())).ifPresent(issuanceRequest::setGoodsCode);
        //考虑group eb多plan的情况。
        if (policy.getPolicyType() != PolicyScenarioEnum.GROUP_POLICY && currentPlanCoverageResponse != null) {
            issuanceRequest.setPlanId(currentPlanCoverageResponse.getPlanId());
            if (currentPlanCoverageResponse.getCurrentPackage() != null) {
                issuanceRequest.setPackageCode(currentPlanCoverageResponse.getCurrentPackage().getPackageBasicInfo().getPackageCode());
                issuanceRequest.setPackageDefId(currentPlanCoverageResponse.getCurrentPackage().getPackageId());
            }
        }
        ofNullable(policy.getThirdPartyTransactionNo()).ifPresent(issuanceRequest::setBizApplyNo);
        //如果外部沒有传入zoneId，使用产品配置的上的zoneId作为默认值
        ofNullable(policy.getZoneId()).ifPresentOrElse(issuanceRequest::setZoneId,
            () -> ofNullable(goodsRelatingResponse.getSalesAttributes()).map(GoodsSalesAttributesResponse::getZoneId)
                .ifPresent(issuanceRequest::setZoneId));
        issuanceRequest.setMultiCurrency(buildCurrency(policy, goodsRelatingResponse));
        issuanceRequest.setAutoRenewal(BooleanToYesNoEnum.convert(policy.getAutoRenewal()));
        issuanceRequest.setPremium(StringUtils.isEmpty(policy.getPremium()) ? policy.getPaidPremium() : policy.getPremium());
        issuanceRequest.setInsureDate(policy.getApplicationDate());
        issuanceRequest.setEffectiveDate(policy.getEffectiveDate());
        issuanceRequest.setExpiryDate(policy.getExpiryDate());
        issuanceRequest.setTradeNo(StringUtils.isEmpty(policy.getTradeNo()) ? policy.getThirdPartyTransactionNo() : policy.getTradeNo());
        issuanceRequest.setOrderNo(policy.getOrderNo());
        issuanceRequest.setQuotationNo(policy.getQuotationNo());
        issuanceRequest.setIssuanceNo(policy.getProposalNo());
        issuanceRequest.setPolicyNo(policy.getPolicyNo());
        issuanceRequest.setThirdPartyTransactionNo(policy.getThirdPartyTransactionNo());
        issuanceRequest.setExtensions(assembleExtensions(issuanceRequest.getExtensions(), policy.getExtensions()));
        issuanceRequest.setRiskClasses(policy.getRiskClasses());
        issuanceRequest.setIndustry(policy.getIndustry());
        issuanceRequest.setTypeOfBusiness(policy.getTypeOfBusiness());
        issuanceRequest.setHasInvestAccount(BooleanToYesNoEnum.convert(policy.getHasInvestAccount()));
        issuanceRequest.setPolicyType(policy.getPolicyType() != null ? policy.getPolicyType() : PolicyScenarioEnum.NORMAL);
        issuanceRequest.setCreateIssuanceByEvent(policy.getCreateIssuanceByEvent() == null ? YesNoEnum.NO : BooleanToYesNoEnum.convert(policy.getCreateIssuanceByEvent()));
        issuanceRequest.setRemark(policy.getRemark());
        issuanceRequest.setAgreementNo(policy.getAgreementNo());
        issuanceRequest.setRemark(policy.getRemark());
        issuanceRequest.setRenewalSource(policy.getRenewalMethod());
        issuanceRequest.setSystemSource(policy.getSystemSource());
        issuanceRequest.setPolicyDeliveryMethod(policy.getPolicyDeliveryMethod());
        issuanceRequest.setNormalPolicyNo(policy.getNormalPolicyNo());
        issuanceRequest.setInsureCreator(policy.getInsureCreator());
        issuanceRequest.setCollectionDate(policy.getCollectionDate());
        SalesChannelConvert.INSTANCE.fillBySalesChannelList(policy.getSalesChannelList(), issuanceRequest);
        Optional.ofNullable(policy.getIssueWithoutPayment()).map(BooleanToYesNoEnum::convert).ifPresent(issuanceRequest::setIssueWithoutPayment);
    }

    private com.zatech.genesis.policy.api.base.Currency buildCurrency(Policy policy, GoodsRelatingResponse goodsRelatingResponse) {
        if (policy.getCurrency() == null || policy.getCurrency().getSaCurrency() == null || policy.getCurrency().getPremiumCurrency() == null) {
            //如果没有传入币种，使用package上的currency
            var currency = new com.zatech.genesis.policy.api.base.Currency();
            var currentPlanCoverageResponse = getGoodsCoveragePlanResponse(goodsRelatingResponse, policy);
            if (currentPlanCoverageResponse != null) {
                goodsRelatingResponse.getPackageList().stream().filter(e -> e.getPackageId().equals(
                        currentPlanCoverageResponse.getPackageId() == null ? currentPlanCoverageResponse.getCurrentPackage().getPackageId()
                            : currentPlanCoverageResponse.getPackageId()))
                    .findFirst().map(PackageInfoResponse::getPackageBenefit).ifPresent(e -> {
                        CurrencyEnum saCurrency = ofNullable(policy.getCurrency()).map(com.zatech.genesis.model.policy.base.Currency::getSaCurrency)
                            .orElseGet(() -> e.getMultiCurrency().get(0));
                        CurrencyEnum premiumCurrency = ofNullable(policy.getCurrency()).map(com.zatech.genesis.model.policy.base.Currency::getPremiumCurrency)
                            .orElseGet(() -> e.getPremiumCurrency().get(0));
                        currency.setSaCurrency(saCurrency);
                        currency.setPremiumCurrency(premiumCurrency);
                    });
            }
            return currency;
        }
        return com.zatech.octopus.common.util.BeanUtils.copyProperties(policy.getCurrency(), com.zatech.genesis.policy.api.base.Currency.class);
    }

    public Map<String, String> assembleExtensions(Map<String, String> original, Map<String, String> data) {
        if (original == null) {
            original = new HashMap<>();
        }
        Map<String, String> finalTarget = original;
        Optional.ofNullable(data).ifPresent(d -> {
            d.forEach((k, v) -> {
                doIf(v != null, () -> finalTarget.put(k, v));
            });
        });
        return finalTarget;
    }


    public GoodsCoveragePlanResponse getGoodsCoveragePlanResponse(GoodsRelatingResponse goodsRelatingResponse, Policy policy) {

        // 获取当前计划代码：优先使用policy的planCode，如果为空则从planList中获取第一个
        String currentPlanCode = Optional.ofNullable(policy.getPlanCode())
            .filter(StringUtils::isNotEmpty)
            .orElseGet(() -> Optional.ofNullable(policy.getPlanList())
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0).getPlanCode())
                .orElse(null));

        GoodsCoveragePlanResponse currentPlanCoverageResponse = Optional.ofNullable(goodsRelatingResponse.getCoveragePlans())
            .flatMap(planS -> planS.stream()
                .filter(plan -> plan.getPlanCode().equals(currentPlanCode))
                .findFirst())
            .orElse(null);
        if (currentPlanCoverageResponse == null) {
            currentPlanCoverageResponse = goodsRelatingResponse.getPackageList().stream()
                .filter(e -> Objects.equals(e.getPackageBasicInfo().getPackageCode(), policy.getPackageCode()))
                .findFirst()
                .map(packageInfo -> {
                    GoodsCoveragePlanResponse newPlan = new GoodsCoveragePlanResponse();
                    newPlan.setCurrentPackage(packageInfo);
                    return newPlan;
                })
                .orElse(null);
        }
        return currentPlanCoverageResponse;
    }

    /**
     * @description: 被保人填充 {@link #fillInsuredList(Policy policy)}
     * <AUTHOR>
     * @date 2025/3/10 22:27
     * @version 1.0
     */

    public void fillPolicyHolder(Policy policy, IssuanceRequest issuanceRequest) {

        ofNullable(policy.getPolicyHolder()).ifPresent(holder -> {
            IssuanceHolderRequest issuanceHolderRequest = new IssuanceHolderRequest();
            if (Objects.nonNull(holder.getIndividual())) {
                issuanceHolderRequest = createAndCopyProperties(holder, IssuanceHolderRequest.class);
                issuanceHolderRequest.setPartyType(PartyTypeEnum.INDIVIDUAL);
                issuanceHolderRequest.setUserType(UserTypeEnum.PERSONAL);
                issuanceHolderRequest.setResidenceCountry(holder.getIndividual().getResidenceCountry());
                if (StringUtils.isBlank(issuanceHolderRequest.getOccupationClass())) {
                    issuanceHolderRequest.setOccupationClass(new PolicyOccupationClassHolder(policy).getHolderOccupationClass());
                }
            } else if (Objects.nonNull(holder.getOrganization())) {
                issuanceHolderRequest = createAndCopyProperties(holder, IssuanceHolderRequest.class);
                issuanceHolderRequest.setResidenceCountry(holder.getOrganization().getCountry());
                issuanceHolderRequest.setPartyType(PartyTypeEnum.COMPANY);
                issuanceHolderRequest.setUserType(UserTypeEnum.COMPANY);
                issuanceHolderRequest.setCompany(UnifiedModelIssuanceCustomerConvert.INSTANCE.convertCompanyBase(holder.getOrganization()));
            }
            issuanceRequest.setIssuanceHolder(issuanceHolderRequest);
        });
    }

    // fillPayeeList
    public void fillPayeeList(Policy policy, IssuanceRequest issuanceRequest) {
        List<IssuancePayeeRequest> issuancePayeeRequests = ofNullable(policy.getPayeeList()).orElse(Collections.emptyList())
            .stream()
            .map(payee -> {
                IssuancePayeeRequest issuancePayeeRequest = createAndCopyProperties(payee, IssuancePayeeRequest.class);
                if (StringUtils.isBlank(issuancePayeeRequest.getOccupationClass())) {
                    issuancePayeeRequest.setOccupationClass(new PolicyOccupationClassHolder(policy).getHolderOccupationClass());
                }
                issuancePayeeRequest.setAutoDeduction(BooleanToYesNoEnum.convert(payee.getAutoDeduction()));
                return issuancePayeeRequest;
            }).toList();
        issuanceRequest.setIssuancePayeeList(issuancePayeeRequests);
    }


    // 创建并复制属性的通用方法
    private <T extends Customer, R extends IssuanceCustomerBase> R createAndCopyProperties(T source, Class<R> targetClass) {
        try {
            R target = targetClass.getDeclaredConstructor().newInstance();
            copySpecificProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error(" PolicyService copySpecificProperties, Failed to create and copy customer properties. ");
            throw CommonException.byError(SystemErrorCodes.internal_server_error);
        }
    }

    private void copySpecificProperties(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        //personal 外层属性赋值
        BeanUtils.copyProperties(source, target);
        if (source instanceof Customer customer && target instanceof IssuanceCustomerBase issuanceCustomerBase) {
            if (customer.getIndividual() != null || customer.getOrganization() != null) {
                copyIndividualDetailsToRequest(customer, issuanceCustomerBase);
            } else {
                throw CommonException.byErrorAndParams(CommonBizErrorCode.public_error,
                    "Customer must have either individual or organization details.");
            }
        }
    }


    /**
     * 将源对象的详细信息复制到目标请求对象中。
     */
    private void copyIndividualDetailsToRequest(Customer customer, IssuanceCustomerBase issuanceCustomerBase) {
        if (customer.getIndividual() != null) {
            PartyIndividual individual = customer.getIndividual();
            // 使用BeanUtils或MapStruct等工具进行属性复制
            BeanUtils.copyProperties(individual, issuanceCustomerBase, "emailList", "phoneList", "addressList", "accountList", "extensions");

            issuanceCustomerBase.setUserType(UserTypeEnum.PERSONAL);
            issuanceCustomerBase.setCkaIndicator(BooleanToYesNoEnum.convert(individual.getCkaIndicator()));
            issuanceCustomerBase.setSmoke(BooleanToYesNoEnum.convert(individual.getSmoke()));
            issuanceCustomerBase.setIsPromotional(BooleanToYesNoEnum.convert(individual.getIsPromotional()));
            issuanceCustomerBase.setSocialSecurity(BooleanToYesNoEnum.convert(individual.getSocialSecurity()));
            issuanceCustomerBase.setDeathOrNot(BooleanToYesNoEnum.convert(individual.getDeathOrNot()));
            issuanceCustomerBase.setIsCustomer(BooleanToYesNoEnum.convert(individual.getIsCustomer()));
            Optional.ofNullable(BooleanToYesNoEnum.convert(individual.getIssueWithoutPayment())).ifPresent(issuanceCustomerBase::setIssueWithoutPayment);
            issuanceCustomerBase.setMarriageStatus(individual.getMarriageStatus() != null ?
                String.valueOf(individual.getMarriageStatus()) : null);
            issuanceCustomerBase.setRelationshipWithPolicyholder(individual.getHolderRelation());
            setPersonalCommonInfo(customer.getIndividual(), issuanceCustomerBase);
        }

        if (customer.getOrganization() != null) {
            BeanUtils.copyProperties(customer.getOrganization(), issuanceCustomerBase,
                "email", "mobilePhone", "address", "account", "extensions");
            issuanceCustomerBase.setUserType(UserTypeEnum.COMPANY);
            Optional.ofNullable(BooleanToYesNoEnum.convert(customer.getOrganization().getIssueWithoutPayment())).ifPresent(issuanceCustomerBase::setIssueWithoutPayment);
            issuanceCustomerBase.setIsPromotional(BooleanToYesNoEnum.convert(customer.getOrganization().getIsPromotional()));
            setPersonalCommonInfo(customer.getOrganization(), issuanceCustomerBase);
        }
    }

    //emal/account/phone/addresslist
    private void setPersonalCommonInfo(BaseParty baseParty, IssuanceCustomerBase issuanceCustomerBase) {
        if (baseParty == null || issuanceCustomerBase == null) {
            return;
        }
        // Email list
        List<IssuanceEmailBase> emailList = new ArrayList<>();
        ofNullable(baseParty.getEmailList()).ifPresent(emails -> emails.forEach(email -> {
            IssuanceEmailBase tempEmail = new IssuanceEmailBase();
            tempEmail.setEmail(email.getEmail());
            emailList.add(tempEmail);
        }));
        issuanceCustomerBase.setEmail(emailList);

        // Phone list
        List<IssuanceCustomerPhoneBase> phoneList = new ArrayList<>();
        ofNullable(baseParty.getPhoneList()).ifPresent(phones -> phones.forEach(phone -> {
            IssuanceCustomerPhoneBase tempPhone = new IssuanceCustomerPhoneBase();
            tempPhone.setPhoneNo(phone.getPhoneNo());
            tempPhone.setPhoneType(phone.getPhoneType());
            tempPhone.setCountryCode(phone.getCountryCode());
            if (phone.getIsDefault() != null) {
                tempPhone.setPhoneCategory(phone.getIsDefault() ? PhoneCategoryEnum.DEFAULT : PhoneCategoryEnum.NON_DEFAULT);
            }
            phoneList.add(tempPhone);
        }));
        issuanceCustomerBase.setMobilePhone(phoneList);

        // Address list
        List<IssuanceCustomerAddressBase> addressList = new ArrayList<>();
        ofNullable(baseParty.getAddressList()).ifPresent(addresses -> addresses.forEach(address -> {
            IssuanceCustomerAddressBase tempAddress = new IssuanceCustomerAddressBase();
            tempAddress.setAddressType(address.getAddressType());
            tempAddress.setZipCode(address.getZipCode());
            tempAddress.setAddress11(address.getAddress11());
            tempAddress.setAddress12(address.getAddress12());
            tempAddress.setAddress13(address.getAddress13());
            tempAddress.setAddress14(address.getAddress14());
            tempAddress.setAddress15(address.getAddress15());
            tempAddress.setAddress16(address.getAddress16());
            tempAddress.setAddress(address.getAddress());
            tempAddress.setExtensions(assembleExtensions(tempAddress.getExtensions(), address.getExtensions()));
            tempAddress.setOrganizationAddressType(address.getOrganizationAddressType());
            addressList.add(tempAddress);
        }));
        issuanceCustomerBase.setAddress(addressList);
        // Account list
        List<IssuanceAccountBase> accountList = new ArrayList<>();
        ofNullable(baseParty.getAccountList()).ifPresent(accounts -> accounts.forEach(account -> {
            IssuanceAccountBase tempAccount = new IssuanceAccountBase();
            tempAccount.setAccountType(account.getAccountType());
            tempAccount.setBankCode(account.getBankCode());
            tempAccount.setBankName(account.getBankName());
            tempAccount.setBankBranchCode(account.getBankBranchCode());
            tempAccount.setBankBranchName(account.getBankBranchName());
            tempAccount.setCardHolderName(account.getCardHolderName());
            tempAccount.setCardNumber(account.getCardNumber());
            tempAccount.setAccountSubType(account.getAccountSubType());
            Optional.ofNullable(account.getExpiryDate())
                .map(LocalDateUtils::convertToDate)
                .ifPresent(tempAccount::setExpiryDate);
            tempAccount.setMobileNo(account.getMobileNo());
            tempAccount.setSafeNo(account.getSafeNo());
            tempAccount.setBankCity(account.getBankCity());
            tempAccount.setPaymentMethod(account.getPayMethod());
            tempAccount.setThirdPartyPayVoucher(account.getThirdPartyPayVoucher());
            tempAccount.setIban(account.getIban());
            tempAccount.setSwiftCode(account.getSwiftCode());
            tempAccount.setBankAddress(account.getBankAddress());
            tempAccount.setBankBranchAddress(account.getBankBranchAddress());
            tempAccount.setExtensions(account.getExtensions());
            accountList.add(tempAccount);
        }));

        // Account list
        List<IssuanceAttachmentBase> attachmentList = new ArrayList<>();
        ofNullable(baseParty.getAttachmentList()).ifPresent(attachments -> attachments.forEach(attachment -> {
            IssuanceAttachmentBase issuanceAttachmentBase = ObjectService.getAttachment(attachment);
            attachmentList.add(issuanceAttachmentBase);
        }));
        issuanceCustomerBase.setAttachmentList(attachmentList);
        issuanceCustomerBase.setAccount(accountList);
        issuanceCustomerBase.setExtensions(assembleExtensions(issuanceCustomerBase.getExtensions(), baseParty.getExtensions()));
    }

    public List<IssuanceProductRequest> fillProductInfoList(Policy policy, GoodsRelatingResponse goodsRelatingResponse) {

        GoodsContext goodsContext = new GoodsContext(policy.getGoodsCode());
        List<IssuanceProductRequest> productRequests = new ArrayList<>();

        ofNullable(policy.getProductList()).ifPresent(x -> x.forEach(product -> {
            //base info and Liability
            IssuanceProductRequest issuanceProductRequest = productBaseAndLiability(policy, product, goodsContext);
            //UL product(InvestmentPlan/Bonus)
            fillULProductRequest(product, issuanceProductRequest, goodsContext);
            productRequests.add(issuanceProductRequest);
        }));
        return productRequests;
    }

    private static void fillULProductRequest(com.zatech.genesis.model.policy.product.Product product, IssuanceProductRequest issuanceProductRequest, GoodsContext goodsContext) {
        Optional.ofNullable(product.getProductInvestmentPlanList()).ifPresent(productInvestmentPlanList -> {
            List<IssuanceProductInvestmentPlanRequest> issuanceProductInvestmentPlanRequests = new ArrayList<>();
            productInvestmentPlanList.forEach(
                productInvestmentPlan -> issuanceProductInvestmentPlanRequests.add(
                    PolicyConvertUnified.INSTANCE.convertIssuanceProductInvestmentRequest(productInvestmentPlan)));
            issuanceProductRequest.setIssuanceProductInvestmentPlanList(issuanceProductInvestmentPlanRequests);
        });
        Optional.ofNullable(product.getProductBonusList()).ifPresent(productBonusList -> {
            List<IssuanceProductBonusRequest> issuanceProductBonusRequests = new ArrayList<>();
            productBonusList.forEach(
                productBonus -> issuanceProductBonusRequests.add(
                    PolicyConvertUnified.INSTANCE.convertIssuanceProductBonusRequest(productBonus)));
            issuanceProductRequest.setIssuanceProductBonusList(issuanceProductBonusRequests);
        });

        //discount
        List<IssuanceProductDiscountRequest> productDiscountRequests = ofNullable(product.getProductLiabilityList()).orElse(Collections.emptyList())
            .stream().flatMap(e -> ofNullable(e.getDiscountList()).orElse(Collections.emptyList())
                .stream().map(d -> {
                    IssuanceProductDiscountRequest issuanceProductDiscountRequest = new IssuanceProductDiscountRequest();
                    issuanceProductDiscountRequest.setProductId(issuanceProductRequest.getProductId());
                    issuanceProductDiscountRequest.setLiabilityId(goodsContext.getLiabilityId(e.getLiabilityCode()));
                    issuanceProductDiscountRequest.setDiscountRate(d.getDiscountRate());
                    issuanceProductDiscountRequest.setDiscountType(d.getPremiumDiscountType());
                    issuanceProductDiscountRequest.setPremiumDiscount(d.getPeriodDiscountPremium());
                    issuanceProductDiscountRequest.setExtensions(d.getExtensions());
                    return issuanceProductDiscountRequest;
                })).toList();
        issuanceProductRequest.setIssuanceProductPremiumDiscountDetailList(productDiscountRequests);
    }

    private IssuanceProductRequest productBaseAndLiability(Policy policy, com.zatech.genesis.model.policy.product.Product product, GoodsContext goodsContext) {
        IssuanceProductRequest issuanceProductRequest = new IssuanceProductRequest();

        List<IssuanceProductLiabilityRequest> liabilityRequests = ofNullable(product.getProductLiabilityList()).orElse(Collections.emptyList())
            .stream()
            .map(LiabilityConvert.INSTANCE::convert).toList();

        issuanceProductRequest.setIssuanceProductLiabilityList(liabilityRequests);

        //product base info
        Long productId = goodsContext.getProductIdByCode(product.getProductCode());
        if (productId == null) {
            throw new OpenApiException(MarketErrorCode.product_id_not_exist, product.getProductCode());
        }
        issuanceProductRequest.setProductId(productId);
        issuanceProductRequest.setProductCode(product.getProductCode());
        issuanceProductRequest.setCoveragePeriodType(Objects.nonNull(product.getCoveragePeriodType()) ? product.getCoveragePeriodType()
            : policy.getCoveragePeriodType());
        issuanceProductRequest.setCoveragePeriod(Objects.nonNull(product.getCoveragePeriod()) ? product.getCoveragePeriod()
            : policy.getCoveragePeriod());
        issuanceProductRequest.setPremiumPeriodType(Objects.nonNull(product.getPaymentPeriodType()) ? product.getPaymentPeriodType()
            : policy.getPaymentPeriodType());
        issuanceProductRequest.setPremiumPeriod(Objects.nonNull(product.getPeriodPremium()) ? product.getPremiumPeriod()
            : policy.getPaymentPeriod());
        issuanceProductRequest.setPeriodStandardPremium(product.getPeriodStandardPremium());
        // 检查 product 中的 premiumFrequencyType 是否为空
        if (Objects.nonNull(product.getPremiumFrequencyType())) {
            issuanceProductRequest.setPremiumFrequencyType(product.getPremiumFrequencyType());
        } else {
            // 如果 product 中 premiumFrequencyType 为空，则根据 issuanceProductRequest 和 policy 的情况设置 premiumFrequencyType
            if (issuanceProductRequest.getPremiumPeriodType() == PayPeriodTypeEnum.SINGLE) {
                issuanceProductRequest.setPremiumFrequencyType(PayFrequencyTypeEnum.SINGLE);
            } else {
                issuanceProductRequest.setPremiumFrequencyType(policy.getPremiumFrequencyType());
                issuanceProductRequest.setPremiumFrequencyValue(product.getPremiumFrequencyValue());
            }
        }
        issuanceProductRequest.setSumInsured(product.getSumInsured());
        issuanceProductRequest.setEffectiveDate(Objects.nonNull(product.getEffectiveDate()) ? product.getEffectiveDate()
            : policy.getEffectiveDate());
        issuanceProductRequest.setExpiryDate(Objects.nonNull(product.getExpiryDate()) ? product.getExpiryDate()
            : policy.getExpiryDate());
        issuanceProductRequest.setSerialNo(IdUtil.getOrGenerateSerialNo(product.getSerialNo(), "product"));
        issuanceProductRequest.setCalculateMethod(product.getCalculateMethod());
        issuanceProductRequest.setPlannedPremiumCalMethod(product.getPlannedPremiumCalMethod());
        if (CollectionUtils.isNotEmpty(policy.getInsuredList())) {
            issuanceProductRequest.setInsuredNum(policy.getInsuredList().size());
        } else {
            issuanceProductRequest.setInsuredNum(product.getInsuredNum());
        }
        if (policy.getPolicyType() == PolicyScenarioEnum.GROUP_POLICY) {
            //eb product
            issuanceProductRequest.setCoveragePlanCode(product.getCoveragePlanCode());
        }
        return issuanceProductRequest;
    }

    public List<IssuanceInsuredObjectRequest> fillInsuredObjectRequests(Policy policy) {
        return ofNullable(policy.getInsuredObjectList()).map(
            insuredObjects -> insuredObjects.stream().map(insuredObject -> {
                IssuanceInsuredObjectRequest issuanceInsuredObjectRequest = new IssuanceInsuredObjectRequest();
                // 外层标的的转换
                ofNullable(objectService.getUnifyToReqStrategy(insuredObject)).ifPresentOrElse(
                    consumer -> consumer.accept(issuanceInsuredObjectRequest, insuredObject, policy),
                    () -> {
                        throw new OpenApiException(CommonBizErrorCode.object_is_not_support,
                            "Unsupported insuredObject type: %s".formatted(insuredObject.getClass().getName()));
                    });

                List<com.zatech.genesis.model.policy.object.component.InsuredObjectComponentBase> insuredComponentList = insuredObject.getInsuredComponentList();

                // 内层标的转换
                if (CollectionUtils.isNotEmpty(insuredComponentList)) {
                    //relationComponentList根据用户传的insuredComponentRequestList的componentType去重
                    List<ObjectComponent> relationComponentList = insuredComponentList.stream()
                        .map(com.zatech.genesis.model.policy.object.component.InsuredObjectComponentBase::getComponentType)
                        .distinct()
                        .collect(Collectors.toList());

                    List<InsuredObjectComponentBase> insuredComponentRequestList = insuredComponentList.stream().map(
                        insuredComponent -> {
                            Function<com.zatech.genesis.model.policy.object.component.InsuredObjectComponentBase, InsuredObjectComponentBase> function = objectService.getUnifyToReqComponentStrategy(insuredComponent);
                            if (Objects.isNull(function)) {
                                throw new OpenApiException(CommonBizErrorCode.object_is_not_support,
                                    "Unsupported insured component type: %s".formatted(insuredComponent.getClass().getName()));
                            }
                            return function.apply(insuredComponent);
                        }
                    ).collect(Collectors.toList());
                    issuanceInsuredObjectRequest.setInsuredComponentList(insuredComponentRequestList);
                    issuanceInsuredObjectRequest.setRelationComponents(relationComponentList);
                }
                //判断外部是否传insuredRelationList, 如果没有认为标的参数为旧的结构, 需要转为最新SME结构
                if (policy.getInsuredRelationList().isEmpty() && !OpenApiSmeSwitchOnUtils.isV3Enabled()) {
                    InsuredObjectTransformer.transformInsuredObject(issuanceInsuredObjectRequest);
                }
                return issuanceInsuredObjectRequest;
            }).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    public List<IssuanceInsurantRequest> fillInsuredList(Policy policy) {
        return Optional.ofNullable(policy.getInsuredList())
            .map(insureds -> insureds.stream().map(insured -> {
                IssuanceInsurantRequest issuanceInsurantRequest = createAndCopyProperties(insured, IssuanceInsurantRequest.class);
                issuanceInsurantRequest.setId(insured.getPolicyInsurantId());
                if (Objects.nonNull(insured.getIndividual())) {
                    issuanceInsurantRequest.setPartyType(PartyTypeEnum.INDIVIDUAL);
                    issuanceInsurantRequest.setUserType(UserTypeEnum.PERSONAL);
                    issuanceInsurantRequest.setResidenceCountry(insured.getIndividual().getResidenceCountry());
                    issuanceInsurantRequest.setRelationshipWithPolicyholder(insured.getIndividual().getHolderRelation());
                    if (StringUtils.isBlank(issuanceInsurantRequest.getOccupationClass())) {
                        String customerOccupationClass = new PolicyOccupationClassHolder(policy).getCustomerOccupationClassByMainProduct(insured);
                        issuanceInsurantRequest.setOccupationClass(customerOccupationClass);
                    }
                    // 设置其他 Insurant,非人的common信息
                    issuanceInsurantRequest.setInsuredOrder(insured.getInsuredOrder());
                    issuanceInsurantRequest.setInsuredType(insured.getMainInsurantRelation());
                    issuanceInsurantRequest.setVirtualKidNumber(insured.getVirtualKidNumber());
                    issuanceInsurantRequest.setVirtualAdultNumber(insured.getVirtualAdultNumber());
                    issuanceInsurantRequest.setVirtualTotalNumber(insured.getVirtualTotalNumber());
                    issuanceInsurantRequest.setInsuredEffectiveDate(insured.getInsuredEffectiveDate());
                    issuanceInsurantRequest.setInsuredExpiryDate(insured.getInsuredExpiryDate());
                    issuanceInsurantRequest.setInsuredTerminationDate(insured.getInsuredTerminationDate());
                    issuanceInsurantRequest.setInsuredTerminationReason(insured.getInsuredTerminationReason());
                    issuanceInsurantRequest.setStatus(insured.getStatus());
                    issuanceInsurantRequest.setPlanCode(insured.getPlanCode());
                    issuanceInsurantRequest.setIsDeleted(insured.getDeletedFlag() != null && insured.getDeletedFlag() ? "Y" : "N");
                    // 如果是 same holder，需要将 holder 信息复制到 insured 上
                    if (policy.getPolicyHolder() != null && (Objects.nonNull(insured.getIndividual()) && insured.getIndividual().getHolderRelation() == RelationEnum.SELF
                        || Objects.nonNull(insured.getOrganization()) && insured.getOrganization().getHolderRelation() == RelationEnum.SELF)) {
                        issuanceInsurantRequest = createAndCopyProperties(policy.getPolicyHolder(), IssuanceInsurantRequest.class);
                        issuanceInsurantRequest.setRelationshipWithPolicyholder(RelationEnum.SELF);
                        issuanceInsurantRequest.setCustomerId(null);
                    }
                    issuanceInsurantRequest.setSerialNo(IdUtil.getOrGenerateSerialNo(insured.getSerialNo(), "insurant"));
                    //设置受益人
                    setPolicyBeneficiaryRequest(policy, issuanceInsurantRequest);
                    setPolicyTrusteeRequest(policy, issuanceInsurantRequest);
                    return issuanceInsurantRequest;
                } else if (Objects.nonNull(insured.getOrganization())) {
                    issuanceInsurantRequest = createAndCopyProperties(insured, IssuanceInsurantRequest.class);
                    issuanceInsurantRequest.setResidenceCountry(insured.getOrganization().getCountry());
                    issuanceInsurantRequest.setPartyType(PartyTypeEnum.COMPANY);
                    issuanceInsurantRequest.setUserType(UserTypeEnum.COMPANY);
                    issuanceInsurantRequest.setCompany(UnifiedModelIssuanceCustomerConvert.INSTANCE.convertCompanyBase(insured.getOrganization()));
                    issuanceInsurantRequest.setRelationshipWithPolicyholder(insured.getOrganization().getHolderRelation());
                    return issuanceInsurantRequest;
                }
                return null;
            }).collect(Collectors.toList()))
            .orElse(Collections.emptyList());
    }

    public void setPolicyBeneficiaryRequest(Policy policy, IssuanceInsurantRequest issuanceInsurantRequest) {
        List<IssuanceBeneficiaryRequest> issuanceBeneficiaryRequests = new ArrayList<>();
        Optional.ofNullable(policy.getBeneficiaryList())
            .map(beneficiaries -> beneficiaries.stream().map(beneficiary -> {
                if (Objects.nonNull(beneficiary.getIndividual())) {
                    IssuanceBeneficiaryRequest issuanceBeneficiaryRequest = createAndCopyProperties(beneficiary, IssuanceBeneficiaryRequest.class);
                    issuanceBeneficiaryRequest.setBeneficiaryRatio(beneficiary.getBenefitRatio());
                    issuanceBeneficiaryRequest.setRelationshipWithInsured(beneficiary.getInsurantRelation());
                    issuanceBeneficiaryRequests.add(issuanceBeneficiaryRequest);
                }
                return issuanceBeneficiaryRequests;
            }).collect(Collectors.toList()));
        issuanceInsurantRequest.setIssuanceBeneficiaryList(issuanceBeneficiaryRequests);
    }

    public void setPolicyTrusteeRequest(Policy policy, IssuanceInsurantRequest issuanceInsurantRequest) {
        List<IssuanceTrusteeRequest> issuanceTrusteeRequestList = new ArrayList<>();
        Optional.ofNullable(policy.getTrusteeList())
            .map(trusteeLists -> trusteeLists.stream().map(trustee -> {
                if (Objects.nonNull(trustee.getIndividual())) {
                    IssuanceTrusteeRequest issuanceTrusteeRequest = createAndCopyProperties(trustee, IssuanceTrusteeRequest.class);
                    issuanceTrusteeRequest.setRelationshipWithInsured(trustee.getInsurantRelation());
                    issuanceTrusteeRequestList.add(issuanceTrusteeRequest);
                }
                return issuanceTrusteeRequestList;
            }).collect(Collectors.toList()));
        issuanceInsurantRequest.setIssuanceTrusteeList(issuanceTrusteeRequestList);
    }

    public PayMethodEnum fillPayerInfo(Policy policy, IssuanceRequest issuanceRequest) {
        Optional<Payer> payer = policy.getPayerList().stream().findFirst();
        PayMethodEnum payMethodEnum = payer.map(Payer::getPayMethod).orElse(null);
        //如果payer信息传入则填充
        List<IssuancePayerRequest> issuancePayerList = Lists.newArrayList();
        policy.getPayerList().forEach(payer1 -> {
            IssuancePayerRequest issuancePayerRequest;
            if (payer1.getIndividual() != null && payer1.getIndividual().getHolderRelation() != RelationEnum.SELF) {
                issuancePayerRequest = createAndCopyProperties(payer1, IssuancePayerRequest.class);
                issuancePayerRequest.setRelationshipWithPolicyholder(payer1.getIndividual().getHolderRelation());
                issuancePayerRequest.setPayerType(payer1.getPayerType());
                issuancePayerRequest.setPayMethod(payMethodEnum);
                issuancePayerRequest.setAutoDeduction(BooleanToYesNoEnum.convert(payer1.getAutoDeduction()));
                if (StringUtils.isBlank(issuancePayerRequest.getOccupationClass())) {
                    String customerOccupationClass = new PolicyOccupationClassHolder(policy).getCustomerOccupationClassByMainProduct(payer1);
                    issuancePayerRequest.setOccupationClass(customerOccupationClass);
                }
            } else {
                issuancePayerRequest = createAndCopyProperties(policy.getPolicyHolder(), IssuancePayerRequest.class);
                issuancePayerRequest.setRelationshipWithPolicyholder(RelationEnum.SELF);
                issuancePayerRequest.setPayerType(payer1.getPayerType() != null ? payer1.getPayerType() : PayerTypeEnum.FIRST);
                issuancePayerRequest.setAutoDeduction(issuancePayerRequest.getAutoDeduction() == null && payer1.getAutoDeduction() != null ? BooleanToYesNoEnum.convert(payer1.getAutoDeduction()) : issuancePayerRequest.getAutoDeduction());
                issuancePayerRequest.setPayMethod(payMethodEnum);
                issuancePayerRequest.setCustomerId(null);
                // 如果payer中有account信息则使用payer的account信息
                if (payer1.getIndividual() != null && CollectionUtils.isNotEmpty(payer1.getIndividual().getAccountList())) {
                    List<IssuanceAccountBase> accountList = new ArrayList<>();
                    for (PartyAccount partyAccount : payer1.getIndividual().getAccountList()) {
                        IssuanceAccountBase issuanceAccountBase = mapAccountToIssuanceAccountBase(partyAccount);
                        accountList.add(issuanceAccountBase);
                    }
                    issuancePayerRequest.setAccount(accountList);
                }
            }

            if (payer1.getOrganization() != null && payer1.getOrganization().getHolderRelation() != RelationEnum.SELF) {
                issuancePayerRequest = createAndCopyProperties(payer1, IssuancePayerRequest.class);
                issuancePayerRequest.setResidenceCountry(payer1.getOrganization().getCountry());
                issuancePayerRequest.setPartyType(PartyTypeEnum.COMPANY);
                issuancePayerRequest.setUserType(UserTypeEnum.COMPANY);
                issuancePayerRequest.setAutoDeduction(BooleanToYesNoEnum.convert(payer1.getAutoDeduction()));
                issuancePayerRequest.setCompany(UnifiedModelIssuanceCustomerConvert.INSTANCE.convertCompanyBase(payer1.getOrganization()));
                issuancePayerRequest.setRelationshipWithPolicyholder(payer1.getOrganization().getHolderRelation());
            }
            issuancePayerList.add(issuancePayerRequest);
        });
        issuanceRequest.setIssuancePayerList(issuancePayerList);
        return payMethodEnum;
    }

    private IssuanceAccountBase mapAccountToIssuanceAccountBase(PartyAccount account) {
        if (account == null) {
            return null;
        }
        IssuanceAccountBase tempAccount = new IssuanceAccountBase();
        tempAccount.setAccountType(account.getAccountType());
        tempAccount.setBankCode(account.getBankCode());
        tempAccount.setBankName(account.getBankName());
        tempAccount.setBankBranchCode(account.getBankBranchCode());
        tempAccount.setBankBranchName(account.getBankBranchName());
        tempAccount.setCardHolderName(account.getCardHolderName());
        tempAccount.setCardNumber(account.getCardNumber());
        tempAccount.setAccountSubType(account.getAccountSubType());
        Optional.ofNullable(account.getExpiryDate())
            .map(LocalDateUtils::convertToDate) // 假设 account.getExpiryDate() 返回 LocalDate
            .ifPresent(tempAccount::setExpiryDate);
        tempAccount.setMobileNo(account.getMobileNo());
        tempAccount.setSafeNo(account.getSafeNo());
        tempAccount.setBankCity(account.getBankCity());
        tempAccount.setPaymentMethod(account.getPayMethod());
        tempAccount.setThirdPartyPayVoucher(account.getThirdPartyPayVoucher());
        tempAccount.setIban(account.getIban());
        tempAccount.setSwiftCode(account.getSwiftCode());
        tempAccount.setBankAddress(account.getBankAddress());
        tempAccount.setBankBranchAddress(account.getBankBranchAddress());
        tempAccount.setExtensions(account.getExtensions());
        tempAccount.setPayAccountId(account.getPayAccountId());
        return tempAccount;
    }


    public List<IssuanceAttachmentRequest> getIssuanceAttachmentRequests(Policy policy, Boolean needUpdate) {
        List<IssuanceAttachmentRequest> issuanceAttachmentList = new ArrayList<>();
        ofNullable(policy.getAttachmentList()).ifPresent(attachments -> {
            attachments.forEach(attachment -> {
                IssuanceAttachmentRequest issuanceAttachmentRequest = new IssuanceAttachmentRequest();
                IssuanceAttachmentBase issuanceAttachmentBase = ObjectService.getAttachment(attachment);
                issuanceAttachmentBase.setNeedUpdate(BooleanToYesNoEnum.convert(needUpdate));
                BeanUtils.copyProperties(issuanceAttachmentBase, issuanceAttachmentRequest);
                issuanceAttachmentList.add(issuanceAttachmentRequest);
            });
        });
        return issuanceAttachmentList;
    }

    /**
     * @description: issuanceRequest主要针对未传Relation关系，又系统自动生成。
     * <AUTHOR>
     * @date 2025/2/5 14:39
     */

    public List<CoverageRelationBase> fillCoverageRelationList(Policy policy, IssuanceRequest issuanceRequest) {
        List<CoverageRelationBase> coverageRelationBaseList = new ArrayList<>();
        //保单关系列表
        ofNullable(policy.getInsuredRelationList()).ifPresent(coverageRelations -> coverageRelations.forEach(coverageRelation -> {
            CoverageRelationBase issuanceCoverageRelation = new CoverageRelationBase();
            issuanceCoverageRelation.setRefType(CustomerUtils.getCoverageEnum(coverageRelation.getRelationType()));
            issuanceCoverageRelation.setProductSerialNo(coverageRelation.getProductSerialNo());
            issuanceCoverageRelation.setSerialNo(coverageRelation.getSerialNo());
            issuanceCoverageRelation.setLiabilitySerialNos(coverageRelation.getLiabilitySerialNoList());
            coverageRelationBaseList.add(issuanceCoverageRelation);
        }));

        //如果外部没有传入，则内部构建 build insurant level coverage relation,主动填充关系，只适用于单险种单标的的情况，多标的多险种则需要手工传入relation关系
        if (coverageRelationBaseList.isEmpty()) {
            issuanceRequest.getIssuanceInsurantList().forEach(issuanceInsurantRequest -> {
                issuanceRequest.getIssuanceProductList().forEach(issuanceProductRequest -> {
                    CoverageRelationBase issuanceCoverageRelation = new CoverageRelationBase();
                    issuanceCoverageRelation.setRefType(CoverageRefTypeEnum.INSURANT_LEVEL);
                    issuanceCoverageRelation.setSerialNo(issuanceInsurantRequest.getSerialNo());
                    issuanceCoverageRelation.setProductSerialNo(issuanceProductRequest.getSerialNo());
                    issuanceCoverageRelation.setLiabilitySerialNos(getIssuanceAllLiabilitySerialNos(issuanceRequest));
                    coverageRelationBaseList.add(issuanceCoverageRelation);
                });
            });
            //如果外部没有传入，则内部构建 build object level coverage relation
            issuanceRequest.getIssuanceInsuredObjectList().forEach(issuanceInsuredObjectRequest -> {
                issuanceRequest.getIssuanceProductList().forEach(issuanceProductRequest -> {
                    CoverageRelationBase issuanceCoverageRelation = new CoverageRelationBase();
                    issuanceCoverageRelation.setRefType(CoverageRefTypeEnum.OBJECT_LEVEL);
                    issuanceCoverageRelation.setSerialNo(issuanceInsuredObjectRequest.getSerialNo());
                    issuanceCoverageRelation.setProductSerialNo(issuanceProductRequest.getSerialNo());
                    issuanceCoverageRelation.setLiabilitySerialNos(getIssuanceAllLiabilitySerialNos(issuanceRequest));
                    coverageRelationBaseList.add(issuanceCoverageRelation);
                });
            });
        }
        return coverageRelationBaseList;
    }

    @NotNull
    private static List<String> getIssuanceAllLiabilitySerialNos(IssuanceRequest issuanceRequest) {
        List<String> liabilitySerialNos = new ArrayList<>();
        for (IssuanceProductRequest issuanceProductRequest : issuanceRequest.getIssuanceProductList()) {
            List<String> serialNos = issuanceProductRequest.getIssuanceProductLiabilityList()
                .stream()
                .map(IssuanceProductLiabilityRequest::getSerialNo)
                .toList();
            liabilitySerialNos.addAll(serialNos);
        }
        return liabilitySerialNos;
    }

    public List<CampaignRequest> buildCampaignList(Policy policy) {
        return CampaignInfoConverter.INSTANCE.convertCampaignInfoList(policy.getCampaignList());
    }

    public void createPayer(PayMethodEnum paymentMethod, IssuanceRequest request) {

        String paymentOrderNo = request.getTradeNo();
        if (CollectionUtils.isEmpty(request.getIssuancePayerList())) {
            PayerCustomerInfo payerCustomerInfo = createPayerByHolder(paymentOrderNo, request.getIssuanceHolder());
            IssuancePayerRequest payerRequest = new IssuancePayerRequest();
            BeanUtils.copyProperties(request.getIssuanceHolder(), payerRequest);
            log.info("Holder payerId: {}", payerCustomerInfo.getPayerId());
            payerRequest.setPayerType(PayerTypeEnum.FIRST);
            payerRequest.setCustomerId(payerCustomerInfo.getPayerId());
            payerRequest.setAccount(getIssuanceAccountBaseList(payerCustomerInfo.getAccountBaseDTOList(), paymentMethod));
            payerRequest.setPayMethod(paymentMethod);
            payerRequest.setPayAmount(request.getPremium());
            payerRequest.setMainInsuredRelation(RelationEnum.SELF);
            request.setIssuancePayerList(Collections.singletonList(payerRequest));
        } else {
            request.getIssuancePayerList().forEach(payer -> {
                PartyCustomerResponseDTO response = getOrCreateParty(paymentOrderNo, payer);
                Long partyId = response.getPartyId();
                payer.setAccount(getIssuanceAccountBaseList(response.getAccounts(), paymentMethod));
                payer.setCustomerId(partyId);
            });
        }
    }
}
