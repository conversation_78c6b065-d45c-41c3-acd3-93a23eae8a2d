package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.critical_illness.v3.quote.calculate;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.template.AbstractQuotationProcedureTemplate;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/13
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"critical_illness"}, domain = "quote", tag = "nano", name = "calculate", type = ProcedureMetaTypeEnum.RestfulService)
public class QuoteCICalculationProcedure extends AbstractQuotationProcedureTemplate {

}
