package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.common;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model.PolicyOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.service.PolicyService;
import com.zatech.genesis.openapi.platform.integration.outer.IOutDpService;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;
import com.zatech.genesis.openapi.platform.share.json.Jsonable;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/1/12 11:31
 */
@Slf4j
public class IssuanceByProductCategoryV2Procedure<IN extends Jsonable, OUT extends Jsonable> extends IssuanceByProductCategoryProcedure<JsonMap, PolicyOutput> {

    private String phase = "POLICY_API_V2";

    public IssuanceByProductCategoryV2Procedure(PolicyService policyService, IOutDpService iOutDpService) {

        super(policyService, iOutDpService);
    }

    @Override
    public void execute(ProcedureExecutionContext context, JsonMap input, PolicyOutput output) {

        super.execute(context, input, output, phase);
    }

}
