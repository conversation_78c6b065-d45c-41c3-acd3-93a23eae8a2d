package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.shared.output;

import com.zatech.genesis.model.policy.Policy;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/26 13:21
 * @description: 统一模型实体
 */

@Schema(description = "Query Unified Model Output", name = "QueryUnifiedModelOutput")
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryUnifiedModelOutput extends Policy implements Json {


    @Schema(description = "The final premium amount calculated for an insurance policy, which is the real premium that the policyholder actually pays after considering all applicable factors, such as risk assessment, discounts, and loadings.")
    private BigDecimal actualPremium;

}
