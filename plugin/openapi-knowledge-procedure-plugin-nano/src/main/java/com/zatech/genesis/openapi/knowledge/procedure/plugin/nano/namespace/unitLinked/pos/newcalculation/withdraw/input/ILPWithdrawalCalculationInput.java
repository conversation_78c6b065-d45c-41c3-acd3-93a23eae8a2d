/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.unitLinked.pos.newcalculation.withdraw.input;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.unitLinked.pos.newchange.withdraw.input.ILPWithdrawalInput;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/31 20:08
 */
@Data
@Schema(description = "POS ILP withdrawal input")
public class ILPWithdrawalCalculationInput extends ILPWithdrawalInput implements Json {


}
