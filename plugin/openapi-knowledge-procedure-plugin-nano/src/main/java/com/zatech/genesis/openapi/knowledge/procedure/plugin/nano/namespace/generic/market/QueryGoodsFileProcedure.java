/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.market;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.market.input.QueryGoodsDocumentInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.market.output.QueryGoodsDocumentOutput;
import com.zatech.genesis.openapi.platform.integration.market.base.GoodsDocumentsDetailBase;
import com.zatech.genesis.openapi.platform.integration.market.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsDocumentsDisplayResponse;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsDocumentsResponse;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsRelatingResponse;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterMarketService;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AutoMappingable;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.exception.CommonBizErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.MarketErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/2 14:47
 **/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"generic", "common"}, domain = "market", tag = "nano", name = "goodsDocument", type = ProcedureMetaTypeEnum.RestfulService)
public class QueryGoodsFileProcedure extends AbstractProcedure<QueryGoodsDocumentInput, QueryGoodsDocumentOutput> implements AutoMappingable {

    @Autowired
    private IOuterMarketService marketService;

    @Override
    public void init(ProcedureClaim claim) {

    }

    @Override
    public void execute(ProcedureExecutionContext context, QueryGoodsDocumentInput input, QueryGoodsDocumentOutput output) {
        log.info("QueryGoodsFileProcedure request : {}", JsonParser.toJsonString(context.getRequestBody()));
        QueryGoodsDocumentInput queryGoodsDocumentInput = JsonParser.fromJsonMapToObj(context.getRequestBody(), QueryGoodsDocumentInput.class);

        QueryGoodsRelatingRequest request = new QueryGoodsRelatingRequest();
        assert queryGoodsDocumentInput != null;
        request.setGoodsId(queryGoodsDocumentInput.getGoodsId());
        request.setGoodsCode(queryGoodsDocumentInput.getGoodsCode());
        request.setGoodsVersion(queryGoodsDocumentInput.getGoodsVersion());
        if (request.getGoodsId() == null && request.getGoodsCode() == null) {
            throw OpenApiException.by(MarketErrorCode.goodsCode_can_not_be_empty).build();
        }
        request.setQueryDocumentsDisplay(true);
        GoodsRelatingResponse goodsRelatingResponse = marketService.queryGoodsRelating(request);
        log.info("market goodsRelatingResponse response : {}", JsonParser.toJsonString(goodsRelatingResponse));
        if (goodsRelatingResponse.getDocumentsDisplay() == null) {
            throw OpenApiException.by(CommonBizErrorCode.doc_not_found).build();
        } else {
            goodsRelatingResponse.setDocumentsDisplay(filterByInputCondition(queryGoodsDocumentInput, goodsRelatingResponse.getDocumentsDisplay()));
        }
        BeanUtils.copyProperties(goodsRelatingResponse.getDocumentsDisplay(), output);
        log.info("QueryGoodsFileProcedure response : {}", JsonParser.toJsonString(output));
    }

    private GoodsDocumentsDisplayResponse filterByInputCondition(QueryGoodsDocumentInput input,
                                                                 GoodsDocumentsDisplayResponse documentsDisplayResponse) {
        Stream<GoodsDocumentsResponse> stream = documentsDisplayResponse.getDocuments().stream();
        if (CollectionUtils.isNotEmpty(input.getDocTypes())) {
            stream = stream.filter(doc -> input.getDocTypes().contains(doc.getDocType()));
        }
        if (input.getEffectiveTime() != null) {
            stream = stream.filter(Objects::nonNull).map(doc -> filterDocuments(doc, file ->
                (file.getEffectiveTime().before(input.getEffectiveTime()) || input.getEffectiveTime().equals(file.getEffectiveTime()))
                    && (file.getExpireTime().after(input.getEffectiveTime()))));
        }
        if (input.getLanguage() != null) {
            stream = stream.filter(Objects::nonNull)
                .map(doc -> filterDocuments(doc, file -> file.getLanguage().equals(String.valueOf(input.getLanguage().getCode()))));
        }
        List<GoodsDocumentsResponse> documents = stream.filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(documents)) {
            throw OpenApiException.by(CommonBizErrorCode.doc_not_found).build();
        }
        documentsDisplayResponse.setDocuments(documents);
        return documentsDisplayResponse;
    }

    private GoodsDocumentsResponse filterDocuments(GoodsDocumentsResponse doc, Predicate<GoodsDocumentsDetailBase> predicate) {
        GoodsDocumentsResponse goodsDocumentsResponse = new GoodsDocumentsResponse();
        List<GoodsDocumentsDetailBase> fileList = doc.getFileList().stream()
            .filter(predicate)
            .toList();
        if (CollectionUtils.isNotEmpty(fileList)) {
            goodsDocumentsResponse.setDocType(doc.getDocType());
            goodsDocumentsResponse.setFileList(fileList);
        } else {
            return null;
        }
        return goodsDocumentsResponse;

    }
}
