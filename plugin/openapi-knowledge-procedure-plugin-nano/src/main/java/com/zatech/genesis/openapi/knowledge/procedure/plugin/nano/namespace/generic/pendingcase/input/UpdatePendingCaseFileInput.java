package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.pendingcase.input;

import com.zatech.gaia.resource.pendingcase.BusinessModuleEnum;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 **/
@Data
@Schema(title = "Update pendingCaseFile input")
public class UpdatePendingCaseFileInput implements Json {

    @Schema(title = "businessModule", description = "BusinessModule of pendingCase")
    private BusinessModuleEnum businessModule;

    @Schema(title = "pendingCaseId", description = "PendingCaseId of upload file", required = true)
    private Long pendingCaseId;

    @Schema(title = "update file list", required = true)
    private List<UpdatePendingCaseFileRequest> updatePendingCaseFileRequestList;

    @Schema(title = "Third information")
    private String thirdInformation;

    @Getter
    @Setter
    @Schema(title = "Pending case file request")
    public static class UpdatePendingCaseFileRequest {

        @Schema(title = "File unique code", required = true)
        private String fileUniqueCode;

        @Schema(title = "File document type")
        private String documentType;

        @Schema(title = "File url")
        private String fileUrl;

        @Schema(title = "File name")
        private String fileName;

        @Schema(title = "File description")
        private String fileDescription;

        @Schema(title = "Third file url")
        private String thirdFileUrl;

    }

}
