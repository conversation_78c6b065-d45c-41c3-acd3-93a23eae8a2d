package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.application.query.input;

import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 16:07
 */

@Data
@Schema(description = "Query quotation input", name = "QueryApplicationInput")
public class QueryApplicationInput implements Json {

    @Schema(description = "proposal no", requiredMode = RequiredMode.REQUIRED)
    private String proposalNo;

    @Schema(description = "Temporary or not", defaultValue = "YesNoEnum.YES")
    private Boolean temporary;

}
