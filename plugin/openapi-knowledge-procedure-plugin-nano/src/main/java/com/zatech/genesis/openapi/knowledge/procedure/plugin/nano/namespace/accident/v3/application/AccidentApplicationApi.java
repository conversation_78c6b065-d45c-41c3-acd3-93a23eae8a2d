package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.accident.v3.application;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model.PolicyOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.extended_warranty.v3.policy.issue.input.PolicyEwUnifiedInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.quotation.output.UpdateQuotationOutput;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApi;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.ApiSpec;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.OpenApi;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Application 更新与创建
 * @date 2025/04/26 19:16
 */

@OpenApi
@RequestMapping("/api/v3/accident")
public interface AccidentApplicationApi extends IOpenApi {

    @ApiSpec(namespace = "accident", group = "application", scenario = "create")
    @PostMapping("/applications")
    @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PolicyOutput.class)))
    JsonMap create(@RequestBody @Parameter(name = "policy", schema = @Schema(implementation = PolicyEwUnifiedInput.class)) JsonMap policy);


    @ApiSpec(namespace = "accident", group = "application", scenario = "change")
    @PutMapping("/applications/{applicationNo}")
    @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UpdateQuotationOutput.class)))
    JsonMap change(@PathVariable(name = "applicationNo") @Parameter(name = "applicationNo", required = true, in = ParameterIn.PATH, example = "B02034220982", schema = @Schema(type = "string", maxLength = 64)) String applicationNo,
                   @RequestBody @Parameter(name = "policy", schema = @Schema(implementation = PolicyEwUnifiedInput.class)) JsonMap policy);

}


