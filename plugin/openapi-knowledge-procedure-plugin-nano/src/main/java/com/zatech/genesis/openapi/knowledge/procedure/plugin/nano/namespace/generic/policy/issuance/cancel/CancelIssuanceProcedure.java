package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.issuance.cancel;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.issuance.cancel.input.CancelIssuanceInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.issuance.cancel.output.CancelIssuanceOutput;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterIssuanceService;
import com.zatech.genesis.openapi.platform.integration.outer.response.CancelIssuanceResponse;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.octopus.module.feign.decoder.exception.InternalCircuitBreakFeignException;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Date 2022/7/10
 **/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"generic", "common"}, domain = "policy", tag = "nano", name = "cancelIssuance", type = ProcedureMetaTypeEnum.RestfulService)
public class CancelIssuanceProcedure extends AbstractProcedure<CancelIssuanceInput, CancelIssuanceOutput> {

    @Autowired
    private IOuterIssuanceService iOuterIssuanceService;

    private final String errorCode = "BIZ_ISS_200015";

    @Override
    public void init(ProcedureClaim claim) {
        claim.initInputByParam(this);
    }

    @Override
    public void execute(ProcedureExecutionContext context, CancelIssuanceInput input, CancelIssuanceOutput output) {

        CancelIssuanceInput cancelIssuanceInput = JsonParser.fromMapToObj(context.getRequestBody(), CancelIssuanceInput.class);
        String issuanceNo;
        if (cancelIssuanceInput != null && cancelIssuanceInput.getIssuanceNo() != null) {
            issuanceNo = cancelIssuanceInput.getIssuanceNo();
        } else {
            issuanceNo = input.getIssuanceNo();
        }
        log.info("Cancel issuance request : {}", issuanceNo);
        CancelIssuanceResponse cancelIssuanceResponse;
        try {
            cancelIssuanceResponse = iOuterIssuanceService.cancelIssuance(issuanceNo);
        } catch (InternalCircuitBreakFeignException e) {
            if (!cancelIssuanceInput.getIdempotent() || !errorCode.equals(e.getCode())) {
                throw new RuntimeException(e.getCode());
            }
            cancelIssuanceResponse = CancelIssuanceResponse.toDefaultResponse(cancelIssuanceInput.getIssuanceNo());
        }
        log.info("Cancel issuance response:{}", JsonParser.toJsonString(cancelIssuanceResponse));
        if (Objects.nonNull(cancelIssuanceResponse)) {
            BeanUtils.copyProperties(cancelIssuanceResponse, output);
        }
    }

}
