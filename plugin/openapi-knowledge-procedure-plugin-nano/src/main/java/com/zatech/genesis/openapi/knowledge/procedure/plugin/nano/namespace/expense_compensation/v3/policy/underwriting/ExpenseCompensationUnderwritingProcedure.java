/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.expense_compensation.v3.policy.underwriting;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.components.enums.policymanagement.NBConfigurationTypeEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.template.AbstractUnderWritingProcedureTemplate;
import com.zatech.genesis.openapi.platform.integration.policy.uw.UnderwritingRequest;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {
    "expense_compensation"}, domain = "policy", tag = "nano", name = "underwriting", type = ProcedureMetaTypeEnum.RestfulService)
public class ExpenseCompensationUnderwritingProcedure extends AbstractUnderWritingProcedureTemplate {

    @Override
    protected void fillNecessaryInfo(UnderwritingRequest underwritingRequest) {
        underwritingRequest.setNbConfigurationTypeList(Lists.newArrayList(NBConfigurationTypeEnum.PROPOSAL_UNDERWRITING));
    }
}
