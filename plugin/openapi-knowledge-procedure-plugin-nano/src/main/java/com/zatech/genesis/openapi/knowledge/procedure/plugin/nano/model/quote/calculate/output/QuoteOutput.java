package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model.quote.calculate.output;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.graphene.product.CalculatePremiumForInstallmentCategoryEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model.quote.calculate.output.campaign.CampaignRelatingOutput;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/4/13
 */
@Data
@Schema(description = "Premium calculation output")
public class QuoteOutput implements Json {

    @Schema(description = "The unique transaction number assigned by a third-party system involved in the quote process")
    private String thirdPartyTransactionNo;

    @Schema(description = "The gross premium before any discounts or surcharges are applied, representing the standard or base premium amount.")
    private String standardGrossPremium;

    @Schema(description = "The net premium after deducting commissions from the gross premium, but before taxes and other fees are added. It represents the insurer's portion of the premium.")
    private String standardNetPremium;

    @Schema(description = "The total tax amount calculated based on the net premium, including any applicable taxes such as sales tax, VAT, or other taxes.")
    private String totalTax;

    @Schema(description = "A list of tax components included in the premium calculation, such as sales tax, VAT, or other applicable taxes.")
    private List<Tax> taxList;

    @Schema(description = "Detailed information for each premium installment, including due dates, installment amounts, and payment status.")
    private List<QuoteInstallmentOutput> installmentList;

    @Schema(description = "Breakdown of the premium at the product level, showing the premium contribution from each individual product or coverage within the quote.")
    private List<SaPremiumCalcProductOutput> productList;

    private CampaignRelatingOutput campaignRelating;

    @Schema(description = "The final premium amount payable by the customer, which includes the base premium, taxes, and any applicable fees or discounts.")
    private String totalPremium;

    @Schema(description = "The total amount of commission payable, which is compensation for agents or brokers for selling or servicing the insurance policy.")
    private String totalCommission;

    @Schema(description = "A list of commission components, detailing how the total commission is calculated and distributed.")
    private List<Commission> commissionList;

    @Schema(description = "The total amount of service fees charged for insurance-related services provided by the insurance company, such as policy administration or customer support.")
    private String totalServiceFee;

    @Schema(description = "A list of service fee components, detailing the different types of service fees included in the quote.")
    private List<ServiceFee> serviceFeeList;

    @Schema(description = "The result or output containing information about the base currency. This typically includes details such as the base currency code and potentially other relevant attributes. It is part of the quote output and reflects the base currency configured for the tenant, which is the standard currency used by the organization for financial operations.")
    private QuoteOutput baseCurrencyResult;

    @Schema(description = "The currency used for this quote, represented as an enumeration of currency types, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)")
    private CurrencyEnum currency;

    @Schema(description = "The method used to calculate the premium for installment payments, represented as an enumeration of different calculation categories.")
    private CalculatePremiumForInstallmentCategoryEnum installmentCalculationMethod;

}
