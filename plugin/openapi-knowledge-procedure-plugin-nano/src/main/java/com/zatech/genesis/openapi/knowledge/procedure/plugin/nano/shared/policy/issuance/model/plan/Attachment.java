/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.model.plan;

/**
 * <AUTHOR>
 * @date 2023/11/7 11:03
 **/
public class Attachment {

    private String attachmentUrl;

    private String attachmentName;

    private String attachmentType;

}
