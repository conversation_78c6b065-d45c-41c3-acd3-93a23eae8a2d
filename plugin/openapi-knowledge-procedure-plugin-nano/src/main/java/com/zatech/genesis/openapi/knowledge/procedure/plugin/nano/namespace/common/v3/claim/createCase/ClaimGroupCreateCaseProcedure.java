package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.createCase;

import com.google.common.collect.Lists;
import com.zatech.genesis.cdc.es.client.index.query.util.Expression;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.createCase.input.CreateCaseInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.createCase.output.CreateCaseOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.claim.service.ClaimService;
import com.zatech.genesis.openapi.platform.hooks.plugin.api.IHookable;
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.BusinessEventBound;
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.EventCategory;
import com.zatech.genesis.openapi.platform.integration.claim.base.CustomerApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.base.IncidentContentApiBase;
import com.zatech.genesis.openapi.platform.integration.claim.request.RegistrationApiRequest;
import com.zatech.genesis.openapi.platform.integration.claim.response.RegistrationApiResponse;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterCdcService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterClaimService;
import com.zatech.genesis.openapi.platform.integration.outer.response.PolicyInsuredListResponse;
import com.zatech.genesis.openapi.platform.integration.transform.claim.UnifiedClaimTransform;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AutoMappingable;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.IdUtil;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.exception.CommonBizErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.ClaimEventCategoryEnum.CLAIM_APPLICATION_RESULT;
import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.ClaimEventCategoryEnum.CLAIM_CASE_PROCESS;
import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.ClaimEventCategoryEnum.CLAIM_PENDING_CASE_RESULT;
import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.ClaimEventCategoryEnum.CLAIM_RESULT;

/**
 * <AUTHOR>
 * @date 2024/04/18 11:28
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(domain = "claim", tag = "nano", name = "group_createCaseV3", type = ProcedureMetaTypeEnum.RestfulService)
public class ClaimGroupCreateCaseProcedure extends AbstractProcedure<CreateCaseInput, CreateCaseOutput> implements IHookable<CreateCaseInput,
    CreateCaseOutput>, AutoMappingable {

    @Autowired
    private IOuterClaimService iOuterClaimService;

    @Autowired
    private ClaimService claimService;

    private ClaimGroupCreateCaseProcedure.Param param;

    @Autowired
    private IOuterCdcService iOuterCdcService;

    @Getter
    @Setter
    public static class Param {

        private String policyNo;
    }


    @Override
    public void init(ProcedureClaim claim) {
        claim.initInputByParam(this);
        param = claim.getParamsOrNullAs(ClaimGroupCreateCaseProcedure.Param.class);
        claim.initInputByRequestBody(this);
    }

    @Override
    public List<EventCategory> eventCategories() {
        return Lists.newArrayList(CLAIM_RESULT.getEventCategory(), CLAIM_CASE_PROCESS.getEventCategory(),
            CLAIM_APPLICATION_RESULT.getEventCategory(), CLAIM_PENDING_CASE_RESULT.getEventCategory());
    }

    @Override
    public void execute(ProcedureExecutionContext context, CreateCaseInput createCaseInput, CreateCaseOutput output) {
        log.info("CreateCaseProcedure request : {}", JsonParser.toJsonString(context.getRequestBody()));
        claimService.setSubscription();

        if (StringUtils.isEmpty(param.getPolicyNo())) {
            throw OpenApiException.by(CommonBizErrorCode.policyNo_cannot_be_empty).build();
        }

        if (CollectionUtils.isEmpty(createCaseInput.getCaseList())) {
            throw OpenApiException.by(CommonBizErrorCode.validate_error).build();
        }

        createCaseInput.setPolicyNo(param.policyNo);

        RegistrationApiRequest registrationApiRequest = new UnifiedClaimTransform(createCaseInput).toRegistrationApiRequestForGroup();


        fillInsuredsPartyId(param.policyNo, registrationApiRequest.getInsureds());

        handleIncidentContent(registrationApiRequest);
        log.info("feign createCase request : {}", JsonParser.toJsonString(registrationApiRequest));
        RegistrationApiResponse response = iOuterClaimService.createCase(registrationApiRequest);
        log.info("CreateCaseProcedure response : {}", JsonParser.toJsonString(response));
        BeanUtils.copyProperties(response, output);
    }


    /**
     * 初始化PartyId主要针对group的被保人
     *
     * @param policyNo 保单号
     * @param insureds 被保人信息
     * @return 返回certiNo对应的PartyId
     */
    private void fillInsuredsPartyId(String policyNo, List<CustomerApiBase> insureds) {
        if (CollectionUtils.isEmpty(insureds) || StringUtils.isBlank(policyNo)) {
            return;
        }
        // 为被保险人设置customerId (作为partyId使用)
        insureds.stream()
            .filter(Objects::nonNull)
            .forEach(customerApiBase -> {
                Page<PolicyInsuredListResponse> policyResponse = searchInsurantListForPolicy(customerApiBase, policyNo);
                if (CollectionUtils.isNotEmpty(policyResponse.getContent())) {
                    customerApiBase.setPartyId(policyResponse.getContent().get(0).getCustomerId());
                }
            });
    }

    public Page<PolicyInsuredListResponse> searchInsurantListForPolicy(CustomerApiBase customerApiBase, String policyNo) {
        Expression condition = Expression.of();
        Optional.ofNullable(customerApiBase.getIdNo()).ifPresent(idNo -> condition.eq("idNo", customerApiBase.getIdNo(), false));
        Optional.ofNullable(customerApiBase.getIdType().getCode()).ifPresent(Type -> condition.eq("idType", customerApiBase.getIdType().getCode(), false));
        condition.eq("isDeleted", "N", false);
        condition.eq("policyNo", policyNo, false);
        condition.page(0, 100);
        return iOuterCdcService.searchPolicyInsurantList(condition.build());
    }

    private void handleIncidentContent(RegistrationApiRequest registrationApiRequest) {
        /*
         * 如果之前调用理赔C端注册接口，在lossParty层级传的incidentContentDetail
         * 那么升级V2.58之后需要用新的结构来适配lossParty和incidentContent的绑定关系
         * IncidentContentDetailApiBase.lossPartyIdentifier=LossPartyApiBase.identifier属性
         */
        if (CollectionUtils.isNotEmpty(registrationApiRequest.getLossParties())) {
            List<IncidentContentApiBase> incidentContents = registrationApiRequest.getIncidentContents();
            for (int i = 0; i < registrationApiRequest.getLossParties().size(); i++) {
                String identifier = String.valueOf(IdUtil.nextValue());
                registrationApiRequest.getLossParties().get(i).setIdentifier(identifier);
                if (CollectionUtils.isNotEmpty(incidentContents) && i < incidentContents.size()) {
                    incidentContents.get(i).setLossPartyIdentifier(identifier);
                }
                if (CollectionUtils.isNotEmpty(registrationApiRequest.getLossPartyInsuredObjectRelations())) {
                    registrationApiRequest.getLossPartyInsuredObjectRelations()
                        .forEach(relation -> relation.setLossPartyIdentifier(identifier));
                }
            }
        }
    }

    @Override
    public EventCategory eventCategory() {
        return CLAIM_RESULT.getEventCategory();
    }

    @Override
    public List<BusinessEventBound> bindHookEvent(CreateCaseInput input, CreateCaseOutput createCaseOutputV3) {
        if (createCaseOutputV3 != null && createCaseOutputV3.getCaseNo() != null) {
            List<BusinessEventBound> eventBoundList = new ArrayList<>();
            BusinessEventBound bound = new BusinessEventBound();
            bound.setBizNo(input.getBizNo());
            bound.setEvent(CLAIM_RESULT.getEventCategory());
            bound.setBusinessId(createCaseOutputV3.getCaseNo());
            eventBoundList.add(bound);

            BusinessEventBound process = new BusinessEventBound();
            process.setEvent(CLAIM_CASE_PROCESS.getEventCategory());
            process.setBizNo(input.getBizNo());
            process.setBusinessId(createCaseOutputV3.getCaseNo());
            eventBoundList.add(process);

            BusinessEventBound applicationProcess = new BusinessEventBound();
            applicationProcess.setEvent(CLAIM_APPLICATION_RESULT.getEventCategory());
            applicationProcess.setBizNo(input.getBizNo());
            applicationProcess.setBusinessId(createCaseOutputV3.getApplicationNo());
            eventBoundList.add(applicationProcess);

            //claim pending case
            if (!CollectionUtils.isEmpty(createCaseOutputV3.getCaseList())) {
                createCaseOutputV3.getCaseList().forEach(caseApiBase -> {
                    BusinessEventBound pendingCaseProcess = new BusinessEventBound();
                    pendingCaseProcess.setEvent(CLAIM_PENDING_CASE_RESULT.getEventCategory());
                    pendingCaseProcess.setBizNo(input.getBizNo());
                    pendingCaseProcess.setBusinessId(caseApiBase.getCaseNo());
                    eventBoundList.add(pendingCaseProcess);
                });
            }
            return eventBoundList;
        }
        return Collections.emptyList();
    }
}
