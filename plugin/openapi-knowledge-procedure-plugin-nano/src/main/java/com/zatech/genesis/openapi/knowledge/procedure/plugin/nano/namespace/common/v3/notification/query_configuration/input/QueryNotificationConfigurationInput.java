package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.notification.query_configuration.input;

import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/04/29
 */
@Data
@Schema(title = "Query Notification Rule input")
public class QueryNotificationConfigurationInput implements Json {

    @Schema(title = "Business Module", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessModule;

    @Schema(title = "Trigger Point", requiredMode = Schema.RequiredMode.REQUIRED)
    private String triggerPoint;

}
