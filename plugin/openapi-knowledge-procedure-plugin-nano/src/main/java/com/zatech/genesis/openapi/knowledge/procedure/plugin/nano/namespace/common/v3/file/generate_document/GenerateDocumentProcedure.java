package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.file.generate_document;

import com.za.cqrs.util.Jackson;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.file.generate_document.input.GenerateDocumentInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.file.generate_document.output.GenerateDocumentOutput;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterPrinterService;
import com.zatech.genesis.openapi.platform.integration.outer.request.PrintRequest;
import com.zatech.genesis.openapi.platform.integration.outer.response.PrintResponse;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AutoMappingable;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/05/08
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"common"}, domain = "common", tag = "nano", name = "generateDocument", type = ProcedureMetaTypeEnum.RestfulService)
public class GenerateDocumentProcedure extends AbstractProcedure<GenerateDocumentInput, GenerateDocumentOutput> implements AutoMappingable {

    @Autowired
    private IOuterPrinterService printerService;

    @Override
    public void init(ProcedureClaim claim) {
        claim.initInputByRequestBody(this);
    }

    @Override
    public void execute(ProcedureExecutionContext context, GenerateDocumentInput input, GenerateDocumentOutput output) {
        PrintRequest printRequest = input.toPrintRequest();
        log.info("print request:{}", Jackson.toJson(printRequest));
        PrintResponse printResponse = printerService.print(printRequest);
        log.info("print response:{}", Jackson.toJson(printResponse));
        output.from(printResponse);
    }

}
