/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.policy.procedure.search.convert;

import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.graphene.cdc.support.share.enums.QueryFieldEnum;
import com.zatech.genesis.cdc.es.client.index.query.BoolQueryBuilder;
import com.zatech.genesis.cdc.es.client.index.query.NestedQueryBuilder;
import com.zatech.genesis.cdc.es.client.index.query.QueryBuilder;
import com.zatech.genesis.cdc.es.client.search.builder.SearchSourceBuilder;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.policy.procedure.search.input.AdvancedQueryRequest;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.policy.procedure.search.input.SearchPolicyHolderInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.policy.procedure.search.input.SearchPolicyInput;
import com.zatech.genesis.openapi.platform.share.BooleanToYesNoEnum;
import com.zatech.genesis.openapi.platform.share.TraceSupport;
import com.zatech.octopus.common.util.DateUtil;
import com.zatech.octopus.common.util.LocalDateUtils;

import java.util.Collections;
import java.util.Optional;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import static com.zatech.gaia.resource.graphene.cdc.support.share.enums.QueryFieldEnum.ISSUANCE_ISSUANCE_NO;
import static com.zatech.gaia.resource.graphene.cdc.support.share.enums.QueryFieldEnum.POLICY_CHANNEL_CHANNELCODE;
import static com.zatech.gaia.resource.graphene.cdc.support.share.enums.QueryFieldEnum.POLICY_GOODSID;
import static com.zatech.gaia.resource.graphene.cdc.support.share.enums.QueryFieldEnum.POLICY_POLICYNO;
import static com.zatech.gaia.resource.graphene.cdc.support.share.enums.QueryFieldEnum.POLICY_POLICYSTATUS;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.boolQuery;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.likeQuery;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.nestedQuery;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.rangeQuery;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.termQuery;

public class SearchPolicyInputConverter {

    private SearchPolicyInputConverter() {
    }

    public static SearchSourceBuilder convert(SearchPolicyInput reqCondition) {
        SearchSourceBuilder searchSource = SearchSourceBuilder.searchSource();
        //设置去CDC模查询每页显示的条数pageSize
        searchSource.size(reqCondition.getPageSize());

        //设置去CDC模块查询的分页,是第几页的数据
        searchSource.from(reqCondition.getPageIndex() * reqCondition.getPageSize());

        SearchPolicyInput.SearchCondition condition = reqCondition.getCondition();
        if (condition == null) {
            return searchSource;
        }

        BoolQueryBuilder bool = boolQuery();

        if (StringUtils.isNotBlank(TraceSupport.getChannelCodeOrNull())) {
            bool.must(nestedQuery(POLICY_CHANNEL_CHANNELCODE.getNestedKey(), termQuery(buildFileName(POLICY_CHANNEL_CHANNELCODE), TraceSupport.getChannelCodeOrNull())));
        }

        //保单参数
        if (ObjectUtils.isNotEmpty(condition.getPolicyNo())) {
            bool.must(termOrLike(POLICY_POLICYNO.getFieldName(), POLICY_POLICYNO.getFieldName(), condition.getPolicyNo(), null));
        }

        //投保单参数
        if (ObjectUtils.isNotEmpty(condition.getIssuanceNo())) {
            bool.must(termOrLike(ISSUANCE_ISSUANCE_NO.getFieldName(), ISSUANCE_ISSUANCE_NO.getFieldName(), condition.getIssuanceNo(), null));
        }

        //商品id
        if (ObjectUtils.isNotEmpty(condition.getGoodsId())) {
            bool.must(termOrLike(POLICY_GOODSID.getFieldName(), POLICY_GOODSID.getFieldName(), condition.getGoodsId(), null));
        }

        //商品code
        if (ObjectUtils.isNotEmpty(condition.getGoodsCode())) {
            bool.must(termOrLike("goodsCode", "goodsCode", condition.getGoodsCode(), null));
        }

        if (ObjectUtils.isNotEmpty(condition.getPolicyStatus())) {
            bool.must(termQuery(POLICY_POLICYSTATUS.getFieldName(), condition.getPolicyStatus().getCode()));
        }

        // 是否续保保单
        Integer isRenewalPolicyYesNoCode = BooleanToYesNoEnum.convertYesNoCode(condition.getIsRenewalPolicy());
        if (ObjectUtils.isNotEmpty(isRenewalPolicyYesNoCode)) {
            bool.must(termQuery("renewal", isRenewalPolicyYesNoCode));
        }

        if (condition.getEffectiveStartDate() != null && condition.getEffectiveEndDate() != null) {
            bool.must(nestedQuery(QueryFieldEnum.NESTED_POLICY_PRODUCT.getNestedKey(), rangeQuery(QueryFieldEnum.NESTED_POLICY_PRODUCT.getNestedKey() + "." + QueryFieldEnum.POLICY_PRODUCT_EFFECTIVEDATE.getFieldName()).gte(DateUtil.formatCnHmsDate(condition.getEffectiveStartDate())).lte(DateUtil.formatCnHmsDate(condition.getEffectiveEndDate()))));
        }

        if (ObjectUtils.isNotEmpty(condition.getAgreementNo())) {
            bool.must(termOrLike(QueryFieldEnum.POLICY_AGREEMENTNO.getFieldName(), QueryFieldEnum.POLICY_AGREEMENTNO.getFieldName(), condition.getAgreementNo(), null));
        }

        if (ObjectUtils.isNotEmpty(condition.getRelationPolicyNo())) {
            bool.must(termOrLike(QueryFieldEnum.POLICY_RELATION_NO.getFieldName(), QueryFieldEnum.POLICY_RELATION_NO.getFieldName(), condition.getRelationPolicyNo(), null));
        }


        //投保人信息模糊查询
        String policyHolderPath = QueryFieldEnum.FIELD_POLICY_HOLDER.getFieldName();
        if (condition.getPolicyHolder() != null) {
            SearchPolicyHolderInput policyHolder = condition.getPolicyHolder();

            if (null != policyHolder.getCertiType()) {
                bool.must(nestedQuery(policyHolderPath, termQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_CERTITYPE), policyHolder.getCertiType().getCode())));
            }

            if (null != policyHolder.getBirthday()) {
                bool.must(nestedQuery(policyHolderPath, termQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_BIRTHDAY), DateUtil.formatCnHmsDate(LocalDateUtils.convertToDate(policyHolder.getBirthday())))));
            }

            if (null != policyHolder.getOrganizationIdType()) {
                bool.must(nestedQuery(policyHolderPath, termQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_ORGANIZATION_IDTYPE), policyHolder.getOrganizationIdType().getCode())));
            }
            if (null != policyHolder.getCertiNo()) {
                bool.must(nestedQuery(policyHolderPath, likeQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_CERTINO), policyHolder.getCertiNo()).caseInsensitive(true)));
            }

            if (null != policyHolder.getOrganizationIdNo()) {
                bool.must(nestedQuery(policyHolderPath, likeQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_ORGANIZATION_IDNO), policyHolder.getCertiNo()).caseInsensitive(true)));
            }
            if (policyHolder.getCustomerType() != null) {
                bool.must(nestedQuery(policyHolderPath, termQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_USER_TYPE), policyHolder.getCustomerType().getCode())));
            }

            if (policyHolder.getPersonId() != null) {
                bool.must(nestedQuery(policyHolderPath, termQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_PERSONID), policyHolder.getPersonId())));
            }

            //设置FullName为查询条件--模糊查询
            if (StringUtils.isNotEmpty(policyHolder.getFullName())) {
                NestedQueryBuilder personalQuery = nestedQuery(policyHolderPath, likeQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_FULLNAME), policyHolder.getFullName()).caseInsensitive(true));
                NestedQueryBuilder companyQuery = nestedQuery(policyHolderPath, likeQuery(buildFileName(QueryFieldEnum.POLICY_HOLDER_ORGANIZATION_NAME), policyHolder.getFullName()).caseInsensitive(true));
                if (PartyTypeEnum.INDIVIDUAL == policyHolder.getCustomerType()) {
                    bool.must(personalQuery);
                } else if (PartyTypeEnum.COMPANY == policyHolder.getCustomerType()) {
                    bool.must(companyQuery);
                } else {
                    BoolQueryBuilder temp = boolQuery();
                    temp.should(personalQuery);
                    temp.should(companyQuery);
                    bool.must(temp);
                }
            }

        }

        if (bool.hasClauses()) {
            searchSource.query(bool);
        }

        return searchSource;
    }

    private static QueryBuilder termOrLike(String name, String fieldName, Object value, AdvancedQueryRequest request) {
        boolean caseInsensitive = Optional.ofNullable(request).map(AdvancedQueryRequest::getCaseInsensitiveFields).orElse(Collections.emptySet()).contains(fieldName);
        boolean fuzzyQueryFields = Optional.ofNullable(request).map(AdvancedQueryRequest::getFuzzyQueryFields).orElse(Collections.emptySet()).contains(fieldName);
        if (fuzzyQueryFields) {
            return likeQuery(name, value.toString()).caseInsensitive(caseInsensitive);
        } else {
            return termQuery(name, value).caseInsensitive(caseInsensitive);
        }
    }

    public static String buildFileName(QueryFieldEnum queryFieldEnum) {
        return queryFieldEnum.getNestedKey() == null ? queryFieldEnum.getFieldName() : queryFieldEnum.getNestedKey() + "." + queryFieldEnum.getFieldName();
    }

}
