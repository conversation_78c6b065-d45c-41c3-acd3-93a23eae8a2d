/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.pos.config.output;

import com.zatech.genesis.openapi.platform.integration.pos.configuration.TenantBizDictConfigDetail;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

@Data
public class TenantBizDictConfigDetailOutput {
    /**
     * 字典key
     */
    @Schema(title = "Tenant Dict Key", description = "The unique identifier associated with a specific value within a dictionary data structure that is used to access or reference a corresponding value. Keys are typically used to uniquely identify and retrieve values stored in the tenant data within the Configuration Center.")
    private String dictKey;

    /**
     * 字典key名称
     */
    @Schema(title = "Tenant Dict Key Name", description = "The display name of a dictionary key, which serves as a friendly display name for the dictionary key. In our system, it refers to the name displayed under the Tenant Data Configuration in Configuration Center.")
    private String dictKeyName;

    /**
     * 字典value
     */
    @Schema(title = "Tenant Dict Value", description = "The data associated with a specific key within a dictionary. When accessing a dictionary using a key, you retrieve the corresponding value that is stored in the dictionary. In our system, it refers to the key value of enumeration value(s) on the right side when you access the tenant data in Configuration Center.")
    private String dictValue;

    /**
     * 字典value名称
     */
    @Schema(title = "Tenant Dict Value Name", description = "The display name of a dictionary value. In our system, it refers to the key name of enumeration values on the right side when you access an enumerated value or tenant data in Configuration Center.")
    private String dictValueName;

    /**
     * 字典描述
     */
    @Schema(title = "Dict Description", description = "A description providing additional details or context about the dictionary key or value.")
    private String dictDesc;

    @Schema(title = "Enum Item Name", description = "The name or label for a specific item within an enumerated list defined in the tenant data configuration.")
    private String enumItemName;

    @Schema(title = "Item Extend 1", description = "An extension field for custom data associated with the dictionary item.")
    private String itemExtend1;

    @Schema(title = "Item Extend 1 Description", description = "A description for the custom data stored in itemExtend1.")
    private String itemExtend1Desc;

    @Schema(title = "Item Extend 2", description = "An extension field for custom data associated with the dictionary item.")
    private String itemExtend2;

    @Schema(title = "Item Extend 2 Description", description = "A description for the custom data stored in itemExtend2.")
    private String itemExtend2Desc;

    @Schema(title = "Item Extend 3", description = "An extension field for custom data associated with the dictionary item.")
    private String itemExtend3;

    @Schema(title = "Item Extend 3 Description", description = "A description for the custom data stored in itemExtend3.")
    private String itemExtend3Desc;

    @Schema(title = "Item Extend 4", description = "An extension field for custom data associated with the dictionary item.")
    private String itemExtend4;

    @Schema(title = "Item Extend 4 Description", description = "A description for the custom data stored in itemExtend4.")
    private String itemExtend4Desc;

    @Schema(title = "Item Extend 5", description = "An extension field for custom data associated with the dictionary item.")
    private String itemExtend5;

    @Schema(title = "Item Extend 5 Description", description = "A description for the custom data stored in itemExtend5.")
    private String itemExtend5Desc;

    @Schema(title = "Item Extend 6", description = "An extension field for custom data associated with the dictionary item.")
    private String itemExtend6;

    @Schema(title = "Item Extend 6 Description", description = "A description for the custom data stored in itemExtend6.")
    private String itemExtend6Desc;

    public static TenantBizDictConfigDetailOutput from(TenantBizDictConfigDetail tenantBizDictConfigDetail) {
        if (tenantBizDictConfigDetail == null) {
            return null;
        }
        TenantBizDictConfigDetailOutput tenantBizDictConfigDetailOutput = new TenantBizDictConfigDetailOutput();
        tenantBizDictConfigDetailOutput.setDictKey(tenantBizDictConfigDetail.getDictKey());
        tenantBizDictConfigDetailOutput.setDictKeyName(tenantBizDictConfigDetail.getDictKeyName());
        tenantBizDictConfigDetailOutput.setDictValue(tenantBizDictConfigDetail.getDictValue());
        tenantBizDictConfigDetailOutput.setDictValueName(tenantBizDictConfigDetail.getDictValueName());
        tenantBizDictConfigDetailOutput.setDictDesc(tenantBizDictConfigDetail.getDictDesc());
        tenantBizDictConfigDetailOutput.setEnumItemName(tenantBizDictConfigDetail.getEnumItemName());
        tenantBizDictConfigDetailOutput.setItemExtend1(tenantBizDictConfigDetail.getItemExtend1());
        tenantBizDictConfigDetailOutput.setItemExtend1Desc(tenantBizDictConfigDetail.getItemExtend1Desc());
        tenantBizDictConfigDetailOutput.setItemExtend2(tenantBizDictConfigDetail.getItemExtend2());
        tenantBizDictConfigDetailOutput.setItemExtend2Desc(tenantBizDictConfigDetail.getItemExtend2Desc());
        tenantBizDictConfigDetailOutput.setItemExtend3(tenantBizDictConfigDetail.getItemExtend3());
        tenantBizDictConfigDetailOutput.setItemExtend3Desc(tenantBizDictConfigDetail.getItemExtend3Desc());
        tenantBizDictConfigDetailOutput.setItemExtend4(tenantBizDictConfigDetail.getItemExtend4());
        tenantBizDictConfigDetailOutput.setItemExtend4Desc(tenantBizDictConfigDetail.getItemExtend4Desc());
        tenantBizDictConfigDetailOutput.setItemExtend5(tenantBizDictConfigDetail.getItemExtend5());
        tenantBizDictConfigDetailOutput.setItemExtend5Desc(tenantBizDictConfigDetail.getItemExtend5Desc());
        tenantBizDictConfigDetailOutput.setItemExtend6(tenantBizDictConfigDetail.getItemExtend6());
        tenantBizDictConfigDetailOutput.setItemExtend6Desc(tenantBizDictConfigDetail.getItemExtend6Desc());
        return tenantBizDictConfigDetailOutput;
    }

}