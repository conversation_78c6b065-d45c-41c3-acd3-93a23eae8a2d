package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.sync.input;

import com.zatech.gaia.resource.components.enums.common.RelationEnum;
import com.zatech.gaia.resource.components.enums.common.UserTypeEnum;
import com.zatech.genesis.openapi.platform.integration.metadata.base.DynamicMetaField;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2023/12/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Policy payer input")
public class PolicyInsurantInput extends DynamicMetaField implements Json {
    /**
     * 用户类型1：个人，2：公司
     */
    @Schema(description = "User type")
    private UserTypeEnum userType;

    @Schema(description = "Relationship with policyholder, refer to the API reference. [Detail](/openx/docs/common/guide/reference/relationship-type)")
    private RelationEnum relationshipWithPolicyholder;

    @Schema(description = "Person")
    private PolicyCustomerPersonInput person;

    @Schema(description = "Company")
    private PolicyCustomerCompanyInput company;

    @Schema(description = "Attachment list")
    private List<PolicyAttachmentInput> attachmentList;

}
