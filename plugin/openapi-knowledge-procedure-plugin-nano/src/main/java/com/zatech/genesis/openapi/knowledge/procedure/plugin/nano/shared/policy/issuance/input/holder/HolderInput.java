package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.input.holder;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.common.CustomerBaseInput;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/6/23 15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "Holder input", name = "HolderInput")
public class HolderInput extends CustomerBaseInput {


}
