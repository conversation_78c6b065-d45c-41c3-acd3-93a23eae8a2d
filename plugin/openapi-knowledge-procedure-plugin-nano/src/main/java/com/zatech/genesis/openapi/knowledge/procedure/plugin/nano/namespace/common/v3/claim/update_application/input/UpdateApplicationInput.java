package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.claim.update_application.input;

import com.zatech.genesis.model.claim.Claim;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "Update application input")
public class UpdateApplicationInput extends Claim implements Json {

    @Schema(title = "bizNo", description = "Event callback will return this", maxLength = 128)
    private String bizNo;

}
