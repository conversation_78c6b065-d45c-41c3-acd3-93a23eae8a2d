package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.claim.query.output;

import com.zatech.genesis.openapi.platform.integration.claim.response.CaseApiResponse;
import com.zatech.genesis.openapi.platform.share.json.Json;
import com.zatech.genesis.openapi.platform.share.model.OpenApiPage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/1/8 14:54
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "Query case by person info output")
public class QueryCaseByCustomerInfoOutput extends OpenApiPage implements Json {

    @Schema(title = "claim info list")
    private List<CaseApiResponse> claimInfoList;

}
