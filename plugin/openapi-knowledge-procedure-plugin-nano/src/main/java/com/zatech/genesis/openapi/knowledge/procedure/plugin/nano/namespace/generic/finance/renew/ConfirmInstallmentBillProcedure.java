package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.finance.renew;

import com.zatech.gaia.resource.components.enums.bcp.BillStatusEnum;
import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.graphene.market.campaign.PremiumPayerTypeEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.finance.renew.input.ConfirmInstallmentBillInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.finance.renew.output.ConfirmInstallmentBillOutput;
import com.zatech.genesis.openapi.platform.integration.bcp.request.QueryBillRequest;
import com.zatech.genesis.openapi.platform.integration.bcp.response.BillResponse;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterBcpService;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.exception.CommonBizErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.FinanceErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.octopus.common.dao.Page;

import java.math.BigDecimal;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/08/25 17:58
 **/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"generic"}, domain = "finance", tag = "nano", name = "confirmInstallmentBill", type = ProcedureMetaTypeEnum.RestfulService)
public class ConfirmInstallmentBillProcedure extends AbstractProcedure<ConfirmInstallmentBillInput, ConfirmInstallmentBillOutput> {

    @Autowired
    private IOuterBcpService iOuterBcpService;

    @Override
    public void init(ProcedureClaim claim) {

    }

    @Override
    public void execute(ProcedureExecutionContext context, ConfirmInstallmentBillInput input, ConfirmInstallmentBillOutput output) {
        log.info("ConfirmInstallmentBillProcedure request : {}", JsonParser.toJsonString(context.getRequestBody()));
        ConfirmInstallmentBillInput request = JsonParser.fromJsonMapToObj(context.getRequestBody(), ConfirmInstallmentBillInput.class);

        BillResponse billInfo = getBillInfo(request);

        //如果换入billId不存在直接抛异常
        if (billInfo == null) {
            throw OpenApiException.by(CommonBizErrorCode.policy_not_found).build();
        }

        buildBillPayRequest(billInfo, request);
        //续期支付，冲销账单
        iOuterBcpService.payBill(request);

        output.setSuccess(true);

        log.info("ConfirmInstallmentBillProcedure response : {}", JsonParser.toJsonString(output));
    }

    private void buildBillPayRequest(BillResponse billInfo, ConfirmInstallmentBillInput request) {
        request.setBillId(billInfo.getBillId());
        request.setPayeeId(billInfo.getPayeeId());
        request.setTransType(TransTypeEnum.INSTALLMENT_PREMIUM);
        if (Objects.isNull(request.getFeeAmount())) {
            if (new BigDecimal(request.getSourceAmount()).compareTo(new BigDecimal(billInfo.getBillAmount())) != 0 || !request.getSourceCurrency()
                .equals(billInfo.getCurrency())) {
                throw CommonException.byError(FinanceErrorCode.confirm_source_bill_not_same);
            }
            request.setFeeAmount(billInfo.getBillAmount());
        }
        if (Objects.isNull(request.getCurrency())) {
            request.setCurrency(billInfo.getCurrency());
        }
        request.setBusinessTransactionNo(billInfo.getBusinessTransactionNo());
        request.setPayAccountNo(billInfo.getArapAccountNo());
    }

    public BillResponse getBillInfo(ConfirmInstallmentBillInput input) {
        QueryBillRequest request = new QueryBillRequest();
        request.setPolicyNo(input.getPolicyNo());
        request.setBillId(input.getBillId());
        request.setTransType(TransTypeEnum.INSTALLMENT_PREMIUM);
        log.info("BcpService query bill request : {}", JsonParser.toJsonString(request));
        Page<BillResponse> billResponse = iOuterBcpService.queryBill(request);
        log.info("BcpService query bill response : {}", JsonParser.toJsonString(billResponse));
        if (Objects.isNull(billResponse) || Objects.isNull(billResponse.getResults())) {
            throw OpenApiException.by(FinanceErrorCode.bill_not_found).build();
        }
        return billResponse.getResults()
            .stream()
            .filter(bill -> BillStatusEnum.PENDING.equals(bill.getBillStatus()) && PremiumPayerTypeEnum.CUSTOMER.equals(bill.getPayerType()))
            .findFirst()
            .orElse(null);
    }

}
