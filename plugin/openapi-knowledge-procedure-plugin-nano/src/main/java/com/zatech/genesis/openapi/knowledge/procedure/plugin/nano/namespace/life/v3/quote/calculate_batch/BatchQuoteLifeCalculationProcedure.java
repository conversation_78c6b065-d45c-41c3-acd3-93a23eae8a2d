package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.life.v3.quote.calculate_batch;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.template.AbstractBatchQuotationProcedureTemplate;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"life"}, domain = "quote", tag = "nano", name = "calculate_batch", type = ProcedureMetaTypeEnum.RestfulService)
public class BatchQuoteLifeCalculationProcedure extends AbstractBatchQuotationProcedureTemplate {

}
