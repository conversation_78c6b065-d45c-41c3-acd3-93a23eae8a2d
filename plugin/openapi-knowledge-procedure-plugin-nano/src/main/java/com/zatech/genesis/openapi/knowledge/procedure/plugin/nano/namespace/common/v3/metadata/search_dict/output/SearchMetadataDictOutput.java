/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.metadata.search_dict.output;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.openapi.platform.share.json.Json;
import com.zatech.genesis.openapi.platform.share.model.PageInfo;
import com.zatech.octopus.common.dao.Page;
import com.zatech.octopus.common.util.BeanUtils;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "SearchOutput")
public class SearchMetadataDictOutput extends PageInfo<SearchMetadataDictOutput.BaseBizDictItems> implements Json {

    public void fill(Page<com.zatech.genesis.openapi.platform.integration.metadata.response.BaseBizDictItems> page) {

        this.setPageIndex(page.getPageIndex());
        this.setTotal(page.getTotal() == null ? null : page.getTotal().longValue());
        this.setPageSize(page.getLimit());
        List<BaseBizDictItems> data = Optional.ofNullable(page.getResults()).orElse(Collections.emptyList())
            .stream()
            .map(BaseBizDictItems::from)
            .toList();
        this.setData(data);
    }

    @Data
    @Schema(name = "BaseBizDictItems")
    public static class BaseBizDictItems {

        @Schema(description = "Id")
        private Long id;

        /**
         * 父字典key
         */
        @Schema(description = "The key that identifies a specific parent element in a data dictionary structure which has child elements associated with it. This key is used to establish parent-child relationships within hierarchical data configurations in the system.")
        private String parentKey;

        /**
         * 父字典value
         */
        @Schema(description = "The actual value corresponding to the parent key in a hierarchical data structure. It represents the data element associated with the parent key that serves as the parent node for child elements in the configuration.")
        private String parentValue;

        /**
         * 父字典name
         */
        @Schema(description = "The display name of the parent value in hierarchical data structures. This user-friendly label helps identify the parent value when viewing parent-child relationships in configuration interfaces like Tenant Data Configuration.\n")
        private String parentValueName;

        /**
         * 字典key
         */
        @Schema(description = "Dict key")
        private String dictKey;

        /**
         * 字典key名称
         */
        @Schema(description = "Dict key name")
        private String dictKeyName;

        /**
         * 字典value
         */
        @Schema(description = "Dict value")
        private String dictValue;

        /**
         * 字典value名称
         */
        @Schema(description = "Dict value name")
        private String dictValueName;

        /**
         * 不带国际化的字典value名称
         */
        @Schema(description = "The name of the original dictionary value")
        private String dictValueNameOriginal;

        /**
         * 字典描述
         */
        @Schema(description = "Dict desc")
        private String dictDesc;

        @Schema(description = "The name of the enumerated item")
        private String enumItemName;

        @Schema(description = "Extended fields")
        private String itemExtend1;

        @Schema(description = "Extended field descriptions")
        private String itemExtend1Desc;

        @Schema(description = "Extended fields")
        private String itemExtend2;

        @Schema(description = "Extended field descriptions")
        private String itemExtend2Desc;

        @Schema(description = "Extended fields")
        private String itemExtend3;

        @Schema(description = "Extended field descriptions")
        private String itemExtend3Desc;

        @Schema(description = "Extended fields")
        private String itemExtend4;

        @Schema(description = "Extended field descriptions")
        private String itemExtend4Desc;

        @Schema(description = "Extended fields")
        private String itemExtend5;

        @Schema(description = "Extended field descriptions")
        private String itemExtend5Desc;

        @Schema(description = "Extended fields")
        private String itemExtend6;

        @Schema(description = "Extended field descriptions")
        private String itemExtend6Desc;

        /**
         * is_optional 是否可选
         */
        @Schema(description = "Whether it is optional or not")
        private YesNoEnum isOptional;

        /**
         * 是否可编辑, ref. YesNoEnum. 1: Yes, 2: No
         */
        @Schema(description = "Whether it can be edited")
        private YesNoEnum editable;

        public static BaseBizDictItems from(com.zatech.genesis.openapi.platform.integration.metadata.response.BaseBizDictItems baseBizDictItems) {
            if (baseBizDictItems == null) {
                return null;
            }
            BaseBizDictItems result = BeanUtils.copyProperties(baseBizDictItems, BaseBizDictItems.class);
            result.setIsOptional(YesNoEnum.getEnumDbView(baseBizDictItems.getIsOptional()));
            result.setEditable(YesNoEnum.getEnumDbView(baseBizDictItems.getEditable()));

            return result;
        }
    }

}

