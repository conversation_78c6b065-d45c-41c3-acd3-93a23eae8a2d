/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.payment.model;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "PayInfoResp", description = "Pay info response")
public class PayInfoResp {

    /**
     * 支付人名称
     */
    @Schema(description = "Payer's name", type = "String")
    private String payerName;

    /**
     * 支付人邮箱
     */
    @Schema(description = "Payer's email", type = "String")
    private String email;

    /**
     * 支付人手机号
     */
    @Schema(description = "Payer's phone", type = "String")
    private String phone;

    /**
     * 支付人卡号
     */
    @Schema(description = "card number", type = "String")
    private String cardNumber;

    /**
     * 是否自动绑定卡
     */
    @Schema(description = "auto bind card flag", type = "YesNoEnum")
    private YesNoEnum autoBindCardFlag;

    /**
     * 支付信息扩展参数
     */
    @Schema(description = "extension params", type = "HashMap<String, Object>")
    private Map<String, Object> extensionParams;
}
