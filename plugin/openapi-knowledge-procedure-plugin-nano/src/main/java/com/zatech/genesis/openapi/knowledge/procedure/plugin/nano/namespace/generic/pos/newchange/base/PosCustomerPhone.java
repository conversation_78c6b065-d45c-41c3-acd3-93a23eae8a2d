package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.pos.newchange.base;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "Customer phone information")
@EqualsAndHashCode(callSuper = false)
public class PosCustomerPhone extends BaseCommon {

    /**
     * 客户电话ID
     */
    @Schema(description = "The identification of customer phone")
    private Long mobileId;

    /**
     * 电话号码
     */
    @Schema(description = "The number of phone")
    private String phoneNo;

    /**
     * 电话国家码
     */
    @Schema(description = "The country code of phone")
    private String countryCode;

    /**
     * 电话类型
     */
    @Schema(description = "The type of phone")
    private PhoneTypeEnum phoneType;

}
