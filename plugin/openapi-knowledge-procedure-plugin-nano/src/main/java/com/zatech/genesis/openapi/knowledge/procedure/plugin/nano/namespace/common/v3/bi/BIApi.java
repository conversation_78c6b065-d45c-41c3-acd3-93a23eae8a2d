/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.bi;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.bi.query.input.QueryBIMetricsInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.bi.query.output.QueryBIMetricsOutput;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.IOpenApi;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.ApiSpec;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.OpenApi;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @Date 2025/03/07
 */
@OpenApi
@RequestMapping("/api/v3/bi")
public interface BIApi extends IOpenApi {

    @ApiSpec(namespace = "common", group = "bi", scenario = "query")
    @PostMapping("/search")
    @ApiResponse(
        responseCode = "200",
        description = "Success",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = QueryBIMetricsOutput.class))
    )
    JsonMap query(
        @RequestBody @Parameter(name = "input", schema = @Schema(implementation = QueryBIMetricsInput.class)) JsonMap input);

}
