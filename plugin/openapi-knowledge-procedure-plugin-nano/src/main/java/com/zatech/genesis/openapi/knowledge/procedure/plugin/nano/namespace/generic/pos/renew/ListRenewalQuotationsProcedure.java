package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.pos.renew;

import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.pos.renew.input.ListRenewalQuotationsInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.pos.renew.output.ListRenewalQuotationsOutput;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterRenewalService;
import com.zatech.genesis.openapi.platform.integration.outer.request.ListRenewalQuotationsRequest;
import com.zatech.genesis.openapi.platform.integration.renew.response.RenewalQuotationResponse;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/11/23 17:58
 **/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"generic"}, domain = "pos", tag = "nano", name = "listRenewalQuotations", type = ProcedureMetaTypeEnum.RestfulService)
public class ListRenewalQuotationsProcedure extends AbstractProcedure<ListRenewalQuotationsInput, ListRenewalQuotationsOutput> {

    @Autowired
    private IOuterRenewalService iOuterRenewalService;

    @Override
    public void init(ProcedureClaim claim) {

    }

    @Override
    public void execute(ProcedureExecutionContext context, ListRenewalQuotationsInput input, ListRenewalQuotationsOutput output) {
        log.info("ListRenewalQuotationsProcedure request : {}", JsonParser.toJsonString(context.getOriginInput()));
        ListRenewalQuotationsRequest param = JsonParser.fromJsonMapToObj(context.getOriginInput(), ListRenewalQuotationsRequest.class);

        Page<RenewalQuotationResponse> responses = iOuterRenewalService.listQuotations(param);

        if (responses != null && responses.getTotalElements() != 0) {
            output.setTotal(responses.getTotalElements());
            output.setPage(responses.getNumber());
            output.setSize(responses.getSize());
            output.setContent(responses.getContent());
        }
        log.info("ListRenewalQuotationsProcedure response : {}", JsonParser.toJsonString(output));

    }

}
