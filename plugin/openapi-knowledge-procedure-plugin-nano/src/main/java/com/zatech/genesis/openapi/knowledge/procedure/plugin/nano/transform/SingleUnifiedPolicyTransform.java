/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.transform;

import com.za.cqrs.util.Functions;
import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.NBConfigurationTypeEnum;
import com.zatech.gaia.resource.graphene.policy.ApplicationTypeEnum;
import com.zatech.genesis.model.policy.Policy;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.convert.PolicyConvertUnified;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.goods.service.MarketService;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.service.PolicyService;
import com.zatech.genesis.openapi.platform.integration.market.response.GoodsRelatingResponse;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceInsurantRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRelationRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceSpecialAgreementRequest;
import com.zatech.genesis.openapi.platform.integration.policy.uw.UnderwritingRequest;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.ClaimStackConfigConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.ClaimStackTemplateConvert;
import com.zatech.genesis.openapi.platform.integration.transform.issuance.convert.UserAuthInfoConvert;
import com.zatech.genesis.openapi.platform.share.OpenXPlusJsonParser;
import com.zatech.genesis.openapi.platform.share.exception.MarketErrorCode;
import com.zatech.genesis.policy.api.base.CoveragePlanBase;
import com.zatech.genesis.policy.api.base.UnnamedInsuredBase;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.function.LazySupplier;
import com.zatech.genesis.portal.toolbox.util.ApplicationContextUtil;
import com.zatech.octopus.common.util.BeanUtils;
import com.zatech.octopus.core.util.JacksonUtil;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Supplier;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

//单个 Single
public class SingleUnifiedPolicyTransform<T extends Policy> {

    public final T policy;

    private final Supplier<GoodsRelatingResponse> goodsSupplier;

    private final PolicyService policyService;

    private final IssuanceRelationGenerate issuanceRelationGenerate;

    public SingleUnifiedPolicyTransform(T policy, PolicyService policyService, MarketService marketService, @Nullable IssuanceRelationGenerate issuanceRelationGenerate) {
        this.policy = policy;
        this.policyService = policyService;
        goodsSupplier = new LazySupplier<>(() -> {
            GoodsRelatingResponse goods = marketService.queryGoodsRelating(policy.getGoodsCode());
            if (Objects.isNull(goods)) {
                throw CommonException.byError(MarketErrorCode.goods_not_found);
            }
            return goods;
        });
        this.issuanceRelationGenerate = issuanceRelationGenerate;
    }

    public SingleUnifiedPolicyTransform(T policy, PolicyService policyService, MarketService marketService) {
        this(policy, policyService, marketService, null);
    }

    public SingleUnifiedPolicyTransform(T policy, @Nullable IssuanceRelationGenerate issuanceRelationGenerate) {
        this(policy, ApplicationContextUtil.getBean(PolicyService.class), ApplicationContextUtil.getBean(MarketService.class), issuanceRelationGenerate);
    }

    public SingleUnifiedPolicyTransform(T policy) {
        this(policy, null);
    }


    public IssuanceRequest toIssuanceRequestForQuotation() {
        IssuanceRequest issuanceRequest = new IssuanceRequest();
        issuanceRequest.setBizApplyNo(UUID.randomUUID().toString());
        //fill Issuance Basic element
        policyService.processIssuanceBasic(issuanceRequest, goodsSupplier.get(), policy);
        if (StringUtils.isNotEmpty(policy.getPlanCode()) || StringUtils.isNotEmpty(policy.getPackageCode())) {
            policyService.fillPolicyParameterV3(policy, issuanceRequest);
        }
        // fill policyHolder
        policyService.fillPolicyHolder(policy, issuanceRequest);

        //fill product
        issuanceRequest.setIssuanceProductList(policyService.fillProductInfoList(policy, goodsSupplier.get()));
        issuanceRequest.setIssuancePlanList(OpenXPlusJsonParser.fromJsonStringToList(JacksonUtil.toJSONString(policy.getPlanList()),
            CoveragePlanBase.class));
        //insuredList and Beneficiary
        issuanceRequest.setIssuanceInsurantList(policyService.fillInsuredList(policy));
        //insuredObject
        issuanceRequest.setIssuanceInsuredObjectList(policyService.fillInsuredObjectRequests(policy));
        // fill coverageRelationList
        issuanceRequest.setCoverageRelationList(policyService.fillCoverageRelationList(policy, issuanceRequest));

        //payer信息传给 policy
        policyService.fillPayerInfo(policy, issuanceRequest);

        //fill special Agreement
        List<IssuanceSpecialAgreementRequest> issuanceSpecialAgreementRequests = BeanUtils.copyPropertiesOfList(policy.getSpecialAgreementList(),
            IssuanceSpecialAgreementRequest.class);
        issuanceRequest.setIssuanceSpecialAgreementList(issuanceSpecialAgreementRequests);

        // fill payeeList
        policyService.fillPayeeList(policy, issuanceRequest);

        issuanceRequest.setCampaignCodeList(policyService.buildCampaignList(policy));
        issuanceRequest.setUserAuthInfo(UserAuthInfoConvert.INSTANCE.convert(policy.getUserAuthInfo()));
        issuanceRequest.setClaimStackTemplateList(ClaimStackTemplateConvert.INSTANCE.convertList(policy.getClaimStackTemplateList()));
        issuanceRequest.setClaimStackConfigList(ClaimStackConfigConvert.INSTANCE.convertList(policy.getClaimStackConfigList()));
        return issuanceRequest;
    }

    public IssuanceRequest toIssuanceRequestForCreateQuotation() {
        IssuanceRequest issuanceRequest = new IssuanceRequest();
        issuanceRequest.setBizApplyNo(UUID.randomUUID().toString());
        //fill Issuance Basic element
        policyService.processIssuanceBasic(issuanceRequest, goodsSupplier.get(), policy);
        //填充product和Liability以及coveragePeriod/type
        if (StringUtils.isNotEmpty(policy.getPlanCode()) || StringUtils.isNotEmpty(policy.getPackageCode())) {
            policyService.fillPolicyParameterV3(policy, issuanceRequest);
        }

        issuanceRequest.setIssuanceTransactionType(IssuanceTransactionTypeEnum.WAITING_FOR_CREATEISSUANCE);
        // fill policyHolder
        policyService.fillPolicyHolder(policy, issuanceRequest);

        //fill product
        issuanceRequest.setIssuanceProductList(policyService.fillProductInfoList(policy, goodsSupplier.get()));
        //insuredList and Beneficiary
        issuanceRequest.setIssuanceInsurantList(policyService.fillInsuredList(policy));
        //insuredObject
        issuanceRequest.setIssuanceInsuredObjectList(policyService.fillInsuredObjectRequests(policy));
        // fill coverageRelationList
        issuanceRequest.setCoverageRelationList(policyService.fillCoverageRelationList(policy, issuanceRequest));
        // fill payeeList
        policyService.fillPayeeList(policy, issuanceRequest);

        // fill attachment
        issuanceRequest.setIssuanceAttachmentList(policyService.getIssuanceAttachmentRequests(policy, Boolean.FALSE));

        //fill payer
        PayMethodEnum payMethodEnum = policyService.fillPayerInfo(policy, issuanceRequest);

        policyService.createPartyCustomer(issuanceRequest, payMethodEnum);

        // fill paymentPlan
        issuanceRequest.setPaymentPlan(PolicyConvertUnified.INSTANCE.convertPayment(policy.getPaymentPlan()));

        issuanceRequest.setSaveCalcResult(YesNoEnum.YES);
        policyService.processQuestionnaire(issuanceRequest, policy);

        issuanceRequest.setCampaignCodeList(policyService.buildCampaignList(policy));
        issuanceRequest.setUserAuthInfo(UserAuthInfoConvert.INSTANCE.convert(policy.getUserAuthInfo()));
        issuanceRequest.setClaimStackTemplateList(ClaimStackTemplateConvert.INSTANCE.convertList(policy.getClaimStackTemplateList()));
        issuanceRequest.setClaimStackConfigList(ClaimStackConfigConvert.INSTANCE.convertList(policy.getClaimStackConfigList()));
        return issuanceRequest;
    }

    public IssuanceRequest toIssuanceRequestForPolicy() {
        GoodsRelatingResponse goods = goodsSupplier.get();
        IssuanceRequest issuanceRequest = new IssuanceRequest();

        issuanceRequest.setBizApplyNo(UUID.randomUUID().toString());
        //fill Issuance Basic element
        policyService.processIssuanceBasic(issuanceRequest, goods, policy);

        // fill policyHolder
        policyService.fillPolicyHolder(policy, issuanceRequest);

        //填充product和Liability以及coveragePeriod/type
        if (StringUtils.isNotEmpty(policy.getPlanCode()) || StringUtils.isNotEmpty(policy.getPackageCode())) {
            policyService.fillPolicyParameterV3(policy, issuanceRequest);
        }

        //fill product/insured/object
        issuanceRequest.setIssuanceProductList(policyService.fillProductInfoList(policy, goods));
        //insuredList and Beneficiary
        issuanceRequest.setIssuanceInsurantList(policyService.fillInsuredList(policy));
        //insuredObject
        issuanceRequest.setIssuanceInsuredObjectList(policyService.fillInsuredObjectRequests(policy));
        // fill coverageRelationList
        issuanceRequest.setCoverageRelationList(policyService.fillCoverageRelationList(policy, issuanceRequest));
        issuanceRequest.setIssuanceRelationList(BeanUtils.copyPropertiesOfList(policy.getIssuanceRelationList(),
            IssuanceRelationRequest.class));
        //fill payer
        policyService.fillPayerInfo(policy, issuanceRequest);
        //fill special Agreement
        List<IssuanceSpecialAgreementRequest> issuanceSpecialAgreementRequests = BeanUtils.copyPropertiesOfList(policy.getSpecialAgreementList(),
            IssuanceSpecialAgreementRequest.class);
        issuanceRequest.setIssuanceSpecialAgreementList(issuanceSpecialAgreementRequests);

        // fill payeeList
        policyService.fillPayeeList(policy, issuanceRequest);

        // fill attachment
        issuanceRequest.setIssuanceAttachmentList(policyService.getIssuanceAttachmentRequests(policy, Boolean.FALSE));

        // fill paymentPlan
        issuanceRequest.setPaymentPlan(PolicyConvertUnified.INSTANCE.convertPayment(policy.getPaymentPlan()));

        Functions.doIfPresent(issuanceRelationGenerate, () -> issuanceRequest.setIssuanceRelationDetailList(issuanceRelationGenerate.generate()));
        policyService.processQuestionnaire(issuanceRequest, policy);

        issuanceRequest.setCampaignCodeList(policyService.buildCampaignList(policy));
        issuanceRequest.setUserAuthInfo(UserAuthInfoConvert.INSTANCE.convert(policy.getUserAuthInfo()));
        issuanceRequest.setIssuanceUnnamedInsuredList(OpenXPlusJsonParser.fromJsonStringToList(JacksonUtil.toJSONString(policy.getUnnamedInsuredList()),
            UnnamedInsuredBase.class));
        issuanceRequest.setIssuancePlanList(OpenXPlusJsonParser.fromJsonStringToList(JacksonUtil.toJSONString(policy.getPlanList()),
            CoveragePlanBase.class));
        issuanceRequest.setClaimStackTemplateList(ClaimStackTemplateConvert.INSTANCE.convertList(policy.getClaimStackTemplateList()));
        issuanceRequest.setClaimStackConfigList(ClaimStackConfigConvert.INSTANCE.convertList(policy.getClaimStackConfigList()));
        return issuanceRequest;
    }

    public List<IssuanceInsurantRequest> toIssuanceInsurantRequest() {
        return policyService.fillInsuredList(policy);
    }


    public UnderwritingRequest toUnderwritingRequest() {
        GoodsRelatingResponse goods = goodsSupplier.get();
        UnderwritingRequest underwritingRequest = new UnderwritingRequest();

        //fill Issuance Basic element
        policyService.processIssuanceBasic(underwritingRequest, goods, policy);
        if (StringUtils.isNotEmpty(policy.getPlanCode()) || StringUtils.isNotEmpty(policy.getPackageCode())) {
            policyService.fillPolicyParameterV3(policy, underwritingRequest);
        }
        // fill policyHolder
        policyService.fillPolicyHolder(policy, underwritingRequest);

        //fill payer
        policyService.fillPayerInfo(policy, underwritingRequest);

        //fill product and insured
        underwritingRequest.setIssuanceProductList(policyService.fillProductInfoList(policy, goods));
        //insuredList and Beneficiary
        underwritingRequest.setIssuanceInsurantList(policyService.fillInsuredList(policy));
        //insuredObject
        underwritingRequest.setIssuanceInsuredObjectList(policyService.fillInsuredObjectRequests(policy));
        // fill coverageRelationList
        underwritingRequest.setCoverageRelationList(policyService.fillCoverageRelationList(policy, underwritingRequest));

        policyService.processQuestionnaire(underwritingRequest, policy);

        underwritingRequest.setCampaignCodeList(policyService.buildCampaignList(policy));
        underwritingRequest.setUserAuthInfo(UserAuthInfoConvert.INSTANCE.convert(policy.getUserAuthInfo()));
        underwritingRequest.setClaimStackTemplateList(ClaimStackTemplateConvert.INSTANCE.convertList(policy.getClaimStackTemplateList()));
        underwritingRequest.setClaimStackConfigList(ClaimStackConfigConvert.INSTANCE.convertList(policy.getClaimStackConfigList()));
        return underwritingRequest;
    }


    public UnderwritingRequest toUnderwritingRequestForQuotation() {
        UnderwritingRequest underwritingRequest = toUnderwritingRequest();
        underwritingRequest.setNbConfigurationTypeList(List.of(NBConfigurationTypeEnum.QUOTATION_UNDERWRITING));
        underwritingRequest.setApplicationType(ApplicationTypeEnum.QUOTATION);
        return underwritingRequest;
    }

}
