package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model;

import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;


/**
 * <AUTHOR>
 * @create 2022/06/20 5:20 下午
 **/
@Data
@Schema(title = "Query issuance trace request")
public class NanoQueryIssuanceTraceInput implements Json {

    @Schema(required = true, title = "投保单号")
    private String issuanceNo;

}
