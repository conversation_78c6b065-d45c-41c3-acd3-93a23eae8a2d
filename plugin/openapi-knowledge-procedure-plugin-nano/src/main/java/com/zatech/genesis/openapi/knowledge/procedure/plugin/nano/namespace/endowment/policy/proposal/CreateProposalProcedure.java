package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.endowment.policy.proposal;

import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.input.CreateIssuanceInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.issuance.output.CreateIssuanceOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.service.PolicyService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterIssuanceService;
import com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceCreateResponse;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.system.IGetBizApplyNoProcedure;
import com.zatech.genesis.openapi.platform.share.TraceSupport;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.exception.CommonBizErrorCode;
import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;
import com.zatech.genesis.openapi.platform.share.exception.PolicyErrorCode;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:55
 * 出投保单流程
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = "endowment", domain = "policy", tag = "nano", name = "createProposal", type = ProcedureMetaTypeEnum.RestfulService)
public class CreateProposalProcedure extends AbstractProcedure<CreateIssuanceInput, CreateIssuanceOutput> {

    @Autowired
    private PolicyService policyService;

    @Autowired
    private IOuterIssuanceService iOuterIssuanceService;

    @Override
    public void init(ProcedureClaim claim) {

    }

    @Override
    public void execute(ProcedureExecutionContext context, CreateIssuanceInput input, CreateIssuanceOutput output) {

        log.info("start create proposal.");
        //调用投保单
        long start = System.currentTimeMillis();
        log.info("origin input :{}", context.getOriginInput().toJsonString());
        CreateIssuanceInput origin = JsonParser.fromMapToObj(context.getOriginInput(), CreateIssuanceInput.class);
        IssuanceRequest issuanceRequest = JsonParser.fromMapToObj(context.getOriginInput(), IssuanceRequest.class);
        issuanceRequest.setChannelCode(TraceSupport.getChannelCodeOrNull());

        //针对寿险添加受益人信息的校验
        if (!issuanceRequest.getIsLegalBeneficiary()) {
            Optional.ofNullable(issuanceRequest.getIssuanceProductList()).ifPresent(issuanceProductRequests ->
                issuanceProductRequests.forEach(issuanceProductRequest -> issuanceProductRequest.getIssuanceInsurantList()
                    .stream().findFirst().ifPresent(issuanceInsurantRequest -> {
                        if (CollectionUtils.isEmpty(issuanceInsurantRequest.getIssuanceBeneficiaryList())) {
                            throw new OpenApiException(CommonBizErrorCode.validate_error, "issuanceBeneficiary can`t be null");
                        }
                    })));
        }
        //通用参数校验
        validateRequest(issuanceRequest);

        if (Objects.isNull(issuanceRequest.getIssuanceTransactionType())) {
            issuanceRequest.setIssuanceTransactionType(IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE);
        }
        if (StringUtils.isEmpty(issuanceRequest.getBizApplyNo())) {
            context.getPrevProcedureByName("defaultGetBizApplyNo").ifPresent(bizApplyNoProcedure -> {
                issuanceRequest.setBizApplyNo(((IGetBizApplyNoProcedure) bizApplyNoProcedure).getOutput().getBizApplyNo());
            });
        }
        if (Objects.isNull(issuanceRequest.getEnableTaylor())) {
            issuanceRequest.setEnableTaylor(Boolean.TRUE);
        }

        if (IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE.equals(issuanceRequest.getIssuanceTransactionType())) {
            policyService.fillPolicyParameter(issuanceRequest, issuanceRequest.getPlanId(), issuanceRequest.getGoodsId());

            //创建投保单时不进行创建实收动作
            IssuanceRequest finalRequest = policyService.fillDefaultValue(issuanceRequest);
            log.info("createProposal request:{}", JsonParser.toJsonString(finalRequest));
            List<IssuanceCreateResponse> response = iOuterIssuanceService.createIssuance(finalRequest);
            log.info("feign request cost: {}ms", System.currentTimeMillis() - start);
            log.info("createProposal response:{}", JsonParser.toJsonString(response));
            output.setIssuanceCreatedList(policyService.buildIssuanceCreateResponseOutput(response));
        } else {
            //目前只支持UW_AND_CREATEISSUANCE
            throw new OpenApiException(PolicyErrorCode.issuance_transaction_type_not_support);
        }
    }

    private void validateRequest(IssuanceRequest issuanceRequest) {

        issuanceRequest.getIssuanceProductList().forEach(issuanceProductRequest -> issuanceProductRequest.getIssuanceInsurantList()
            .forEach(issuanceInsurantRequest -> Optional.ofNullable(issuanceInsurantRequest.getAccount()).ifPresent(issuanceAccountBases ->
                issuanceAccountBases.stream().filter(t -> PayMethodEnum.BANK_SLIP.equals(t.getPaymentMethod()))
                    .forEach(issuanceAccountBase -> {
                        if (StringUtils.isEmpty(issuanceAccountBase.getBankName())
                            || StringUtils.isEmpty(issuanceAccountBase.getCardNumber())) {
                            throw new OpenApiException(CommonBizErrorCode.validate_error,
                                "when paymethod is bank slip, cardNo and bankName can`t be null");
                        }
                    }))));
    }

}
