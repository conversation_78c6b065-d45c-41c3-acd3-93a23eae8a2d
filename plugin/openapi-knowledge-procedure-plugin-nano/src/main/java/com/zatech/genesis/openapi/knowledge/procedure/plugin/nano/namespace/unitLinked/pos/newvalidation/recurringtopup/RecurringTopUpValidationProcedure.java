package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.unitLinked.pos.newvalidation.recurringtopup;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.pos.newvalidation.PosValidationProcedureTemplate;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.unitLinked.pos.newvalidation.recurringtopup.input.RecurringTopUpValidationInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.unitLinked.pos.newvalidation.recurringtopup.output.RecurringTopUpValidationOutput;
import com.zatech.genesis.openapi.platform.integration.pos.request.PosCaseValidationRequest;
import com.zatech.genesis.openapi.platform.integration.pos.response.PosCaseValidationResponse;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(namespaces = {"unit_linked"}, domain = "pos", tag = "nano", name = "posRecurringSingleTopUpValidation", type = ProcedureMetaTypeEnum.RestfulService)
public class RecurringTopUpValidationProcedure extends PosValidationProcedureTemplate<RecurringTopUpValidationInput, RecurringTopUpValidationOutput> {

    @Override
    protected TransTypeEnum posType() {
        return TransTypeEnum.ILP_RECURRING_SINGLE_TOP_UP_CHANGE;
    }

    @Override
    protected PosCaseValidationRequest convertFromInput(RecurringTopUpValidationInput input) {
        return JsonParser.fromJson(input.toJsonString(), PosCaseValidationRequest.class);
    }

    @Override
    protected void copyInfoToOutput(PosCaseValidationResponse result, RecurringTopUpValidationOutput output) {
        BeanUtils.copyProperties(result, output);
    }

}
