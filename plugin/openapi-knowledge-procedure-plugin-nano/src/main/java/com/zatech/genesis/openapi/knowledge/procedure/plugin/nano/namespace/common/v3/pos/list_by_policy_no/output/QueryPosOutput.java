package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.pos.list_by_policy_no.output;

import com.zatech.genesis.openapi.platform.integration.pos.response.PosQueryListResultResponse;
import com.zatech.genesis.openapi.platform.share.json.Json;
import com.zatech.genesis.openapi.platform.share.model.PageInfo;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "QueryPosOutput")
public class QueryPosOutput extends PageInfo<PosQueryListResultResponse> implements Json {

}
