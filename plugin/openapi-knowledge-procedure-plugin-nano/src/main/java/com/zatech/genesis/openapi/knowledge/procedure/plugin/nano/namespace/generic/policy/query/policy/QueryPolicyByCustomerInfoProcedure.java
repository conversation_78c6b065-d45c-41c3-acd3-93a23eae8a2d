package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.query.policy;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.customer.PolicyCustomerEnum;
import com.zatech.gaia.resource.components.enums.policy.QueryOption;
import com.zatech.gaia.resource.components.enums.policy.QueryOptions;
import com.zatech.genesis.cdc.es.client.index.query.BoolQueryBuilder;
import com.zatech.genesis.cdc.es.client.search.builder.SearchSourceBuilder;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.query.policy.input.QueryPolicyByCustomerInfoInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.generic.policy.query.policy.output.QueryPolicyByCustomerInfoOutput;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyHolderResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyInsurantResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyProductResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyResponseDTO;
import com.zatech.genesis.openapi.platform.integration.cdc.response.PolicyTaskResponseDTO;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterCdcService;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterIssuanceService;
import com.zatech.genesis.openapi.platform.integration.policy.base.IssuanceBase;
import com.zatech.genesis.openapi.platform.integration.policy.base.PolicyProductBase;
import com.zatech.genesis.openapi.platform.integration.policy.response.IssuanceResponse;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.annotation.Procedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.enums.ProcedureMetaTypeEnum;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.validation.constraints.NotNull;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.boolQuery;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.nestedQuery;
import static com.zatech.genesis.cdc.es.client.index.query.QueryBuilders.termQuery;

/**
 * <AUTHOR>
 * @date 2023/10/8 15:56
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Procedure(domain = "policy", tag = "nano", name = "queryPolicyByCustomerInfo", type = ProcedureMetaTypeEnum.RestfulService)
public class QueryPolicyByCustomerInfoProcedure extends AbstractProcedure<QueryPolicyByCustomerInfoInput, QueryPolicyByCustomerInfoOutput> {

    @Autowired
    private IOuterCdcService cdcService;

    @Autowired
    private IOuterIssuanceService issuanceService;

    private static final Map<PolicyCustomerEnum, String> POLICY_PATH =
        Map.of(
            PolicyCustomerEnum.INSURED, "policyInsurants",
            PolicyCustomerEnum.HOLDER, "policyHolder"
        );

    private static final Map<PolicyCustomerEnum, String> ISSUANCE_PATH =
        Map.of(
            PolicyCustomerEnum.INSURED, "issuanceInsurants",
            PolicyCustomerEnum.HOLDER, "issuanceHolder"
        );

    @Override
    public void init(ProcedureClaim claim) {

    }

    @Override
    public void execute(ProcedureExecutionContext context, QueryPolicyByCustomerInfoInput input, QueryPolicyByCustomerInfoOutput output) {
        //TODO 因为demo原因，暂时未处理分页的问题，需要处理一下
        QueryPolicyByCustomerInfoInput infoInput = JsonParser.fromMapToObj(context.getRequestBody(), QueryPolicyByCustomerInfoInput.class);
        List<QueryPolicyByCustomerInfoOutput.Result> results = switch (infoInput.getRoleType()) {
            case INSURED, HOLDER -> {
                //这里取role是因为stream里用到的需要是不可变的，因此大括号取一把
                var role = infoInput.getRoleType();
                yield infoInput.getCertiNoList().stream().map(e -> getResult(e, role)).toList();
            }
            default -> Collections.emptyList();
        };
        output.setResultList(results);
    }

    @NotNull
    private QueryPolicyByCustomerInfoOutput.Result getResult(QueryPolicyByCustomerInfoInput.CertiNoItem x, PolicyCustomerEnum type) {
        QueryPolicyByCustomerInfoOutput.Result result = new QueryPolicyByCustomerInfoOutput.Result();
        Page<PolicyResponseDTO> policyResponsePage = queryCdcPolicy(POLICY_PATH.get(type), x.getCertiType().getCode(), x.getCertiNo());
        List<IssuanceResponse> issuanceResponseList = queryCdcIssuance(ISSUANCE_PATH.get(type), x.getCertiType().getCode(), x.getCertiNo());
        //投保单转换为保单结构
        List<PolicyResponseDTO> policyResponseDTOS = issuanceResponseList.stream().map(this::convertIssuanceResponseToPolicyResponse).toList();
        //查询保单和投保单的uw task
        List<QueryPolicyByCustomerInfoOutput.PolicyResponseInfo> policyResponseInfoList = getUWTaskList(policyResponsePage);
        List<QueryPolicyByCustomerInfoOutput.PolicyResponseInfo> uwPolicyResponseInfoList = getIssuanceUWTaskList(policyResponseDTOS);

        result.setCertiNo(x.getCertiNo());
        result.setCertiType(x.getCertiType());
        result.setPolicyInfo(getUWTaskList(policyResponsePage));
        policyResponseInfoList.addAll(uwPolicyResponseInfoList);
        Map<CurrencyEnum, List<QueryPolicyByCustomerInfoOutput.PolicyResponseInfo>> groupPolicyResponse =
            policyResponseInfoList
                .stream()
                .filter(e -> null != e.getCurrency())
                .collect(Collectors.groupingBy(QueryPolicyByCustomerInfoOutput.PolicyResponseInfo::getCurrency));
        List<QueryPolicyByCustomerInfoOutput.PremiumItem> premiumItems = new ArrayList<>();
        groupPolicyResponse.forEach((c, p) -> {
            QueryPolicyByCustomerInfoOutput.PremiumItem premiumItem = new QueryPolicyByCustomerInfoOutput.PremiumItem();
            premiumItem.setCurrency(c);
            premiumItem.setTotalAnnualPrem(
                p.stream()
                    .flatMap(policyResponseInfo -> policyResponseInfo.getPolicyProductList().stream())
                    .map(PolicyProductBase::getAnnualPremium)
                    .filter(Objects::nonNull)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).toPlainString()
            );
            premiumItem.setTotalSinglePrem(
                p.stream()
                    .flatMap(policyResponseInfo -> policyResponseInfo.getPolicyProductList().stream())
                    .map(PolicyProductBase::getPeriodPremium)
                    .filter(Objects::nonNull)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).toPlainString()
            );
            premiumItem.setTotal(
                new BigDecimal(premiumItem.getTotalSinglePrem())
                    .add(new BigDecimal(premiumItem.getTotalAnnualPrem()))
                    .toPlainString()
            );
            premiumItems.add(premiumItem);
        });
        result.setTotalPremium(premiumItems);
        return result;
    }

    @NotNull
    private List<QueryPolicyByCustomerInfoOutput.PolicyResponseInfo> getIssuanceUWTaskList(List<PolicyResponseDTO> policyResponseDTOS) {
        return policyResponseDTOS.stream().map(policyResponseDTO -> {
            QueryPolicyByCustomerInfoOutput.PolicyResponseInfo policyResponseInfo = JsonParser.copyObj(policyResponseDTO,
                QueryPolicyByCustomerInfoOutput.PolicyResponseInfo.class);
            policyResponseInfo.setPolicyIndicator(YesNoEnum.NO);
            SearchSourceBuilder uwSearchSourceBuilder = SearchSourceBuilder.searchSource()
                .query(termQuery("issuanceNo", policyResponseDTO.getIssuanceNo())).from(0).size(100);
            Page<PolicyTaskResponseDTO> taskResponseDTOS = cdcService.queryPolicyTask(uwSearchSourceBuilder);
            List<QueryPolicyByCustomerInfoOutput.UwTaskDecisionInfo> uwTaskDecisionInfoList = taskResponseDTOS.getContent().stream()
                .map(policyTaskResponseDTO -> {
                    QueryPolicyByCustomerInfoOutput.UwTaskDecisionInfo uwTaskDecisionInfo = new QueryPolicyByCustomerInfoOutput.UwTaskDecisionInfo();
                    BeanUtils.copyProperties(policyTaskResponseDTO, uwTaskDecisionInfo);
                    uwTaskDecisionInfo.setUnderwriter(String.valueOf(policyTaskResponseDTO.getCurrentHandler()));
                    return uwTaskDecisionInfo;
                }).toList();
            policyResponseInfo.setUwHistory(uwTaskDecisionInfoList);
            return policyResponseInfo;
        }).collect(Collectors.toList());
    }

    @NotNull
    private List<QueryPolicyByCustomerInfoOutput.PolicyResponseInfo> getUWTaskList(Page<PolicyResponseDTO> policyResponsePage) {
        return policyResponsePage.getContent().stream().map(x -> {
            QueryPolicyByCustomerInfoOutput.PolicyResponseInfo policyResponseInfo = JsonParser.copyObj(x,
                QueryPolicyByCustomerInfoOutput.PolicyResponseInfo.class);
            policyResponseInfo.setPolicyIndicator(YesNoEnum.YES);
            SearchSourceBuilder uwSearchSourceBuilder = SearchSourceBuilder.searchSource()
                .query(termQuery("policyNo", x.getPolicyNo())).from(0).size(100);
            Page<PolicyTaskResponseDTO> taskResponseDTOS = cdcService.queryPolicyTask(uwSearchSourceBuilder);
            List<QueryPolicyByCustomerInfoOutput.UwTaskDecisionInfo> uwTaskDecisionInfoList = taskResponseDTOS.getContent().stream()
                .map(policyTaskResponseDTO -> {
                    QueryPolicyByCustomerInfoOutput.UwTaskDecisionInfo uwTaskDecisionInfo = new QueryPolicyByCustomerInfoOutput.UwTaskDecisionInfo();
                    BeanUtils.copyProperties(policyTaskResponseDTO, uwTaskDecisionInfo);
                    uwTaskDecisionInfo.setUnderwriter(String.valueOf(policyTaskResponseDTO.getCurrentHandler()));
                    return uwTaskDecisionInfo;
                }).toList();
            policyResponseInfo.setUwHistory(uwTaskDecisionInfoList);
            return policyResponseInfo;
        }).collect(Collectors.toList());
    }

    private PolicyResponseDTO convertIssuanceResponseToPolicyResponse(IssuanceResponse issuanceResponse) {
        PolicyResponseDTO policyResponseDTO = JsonParser.copyObj(issuanceResponse, PolicyResponseDTO.class);
        policyResponseDTO.setPolicyHolder(JsonParser.copyObj(issuanceResponse.getIssuanceHolder(), PolicyHolderResponseDTO.class));
        policyResponseDTO.setPolicyProductList(issuanceResponse.getIssuanceProductList().stream().map(x -> {
            PolicyProductResponseDTO policyProductResponseDTO = JsonParser.copyObj(x, PolicyProductResponseDTO.class);
            policyProductResponseDTO.setPolicyInsurantList(
                x.getIssuanceInsurantList().stream().map(c -> JsonParser.copyObj(c, PolicyInsurantResponseDTO.class)).toList());
            return policyProductResponseDTO;
        }).toList());

        return policyResponseDTO;
    }

    private Page<PolicyResponseDTO> queryCdcPolicy(String path, Integer certiTypeCode, String certiNo) {
        BoolQueryBuilder boolQueryBuilder = boolQuery();
        boolQueryBuilder
            .must(nestedQuery(path, termQuery("%s.certiType".formatted(path), certiTypeCode)))
            .must(nestedQuery(path, termQuery("%s.certiNo".formatted(path), certiNo)));
        SearchSourceBuilder searchSourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).from(0).size(100);
        return cdcService.queryPolicy(searchSourceBuilder);
    }

    private List<IssuanceResponse> queryCdcIssuance(String path, Integer certiTypeCode, String certiNo) {
        BoolQueryBuilder issuanceBoolQueryBuilder = boolQuery()
            .must(nestedQuery(path, termQuery("%s.idType".formatted(path), certiTypeCode)))
            .must(nestedQuery(path, termQuery("%s.idNo".formatted(path), certiNo)));
        SearchSourceBuilder issuanceSearchSourceBuilder = SearchSourceBuilder.searchSource().query(issuanceBoolQueryBuilder).from(0).size(100);
        Page<IssuanceResponse> issuanceResponsePage = cdcService.queryIssuance(issuanceSearchSourceBuilder);
        return issuanceResponsePage.getContent().stream()
            .map(IssuanceBase::getIssuanceNo)
            .map(issuanceNo -> issuanceService.queryIssuance(issuanceNo, YesNoEnum.NO,
                QueryOptions.of(QueryOption.ALL).exclude(QueryOption.CAMPAIGN_ITEM, QueryOption.PREMIUM_ITEM))).toList();
    }


}
