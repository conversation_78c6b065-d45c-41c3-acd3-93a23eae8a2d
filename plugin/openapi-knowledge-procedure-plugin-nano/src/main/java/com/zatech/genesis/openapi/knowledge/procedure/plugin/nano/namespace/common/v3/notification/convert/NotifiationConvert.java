package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.notification.convert;


import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.notification.otp_send.input.SendOtpInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.notification.otp_validate.input.ValidateOtpInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.user.login.input.LoginUserInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.user.password.input.UpdatePasswordInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.user.register.input.RegisterUserInput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.namespace.common.v3.user.update_account.input.UpdateAccountInput;
import com.zatech.genesis.openapi.platform.integration.outer.request.EmailByPwdChannelCustomerRequest;
import com.zatech.genesis.openapi.platform.integration.outer.request.MobileByPwdChannelCustomerRequest;
import com.zatech.genesis.openapi.platform.integration.outer.request.SendVerificationCodeRequest;
import com.zatech.genesis.openapi.platform.integration.outer.request.ValidateVerificationCodeRequest;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface NotifiationConvert {
    NotifiationConvert INSTANCE = Mappers.getMapper(NotifiationConvert.class);

    ValidateVerificationCodeRequest convertValidateOtpInputToRequest(ValidateOtpInput input);


    SendVerificationCodeRequest convertSendOtpInputToRequest(SendOtpInput input);


    EmailByPwdChannelCustomerRequest convertRegisterUserEmailInputToRequest(RegisterUserInput input);

    MobileByPwdChannelCustomerRequest convertRegisterUserMobileInputToRequest(RegisterUserInput input);

    EmailByPwdChannelCustomerRequest convertLoginUserEmailInputToRequest(LoginUserInput input);

    MobileByPwdChannelCustomerRequest convertLoginUserMobileInputToRequest(LoginUserInput input);

    EmailByPwdChannelCustomerRequest convertUpdatePasswordEmailInputToRequest(UpdatePasswordInput input);

    MobileByPwdChannelCustomerRequest convertUpdatePasswordMobileInputToRequest(UpdatePasswordInput input);

    EmailByPwdChannelCustomerRequest convertUpdateAccountEmailInputToRequest(UpdateAccountInput input);

    MobileByPwdChannelCustomerRequest convertUpdateAccountMobileInputToRequest(UpdateAccountInput input);
}
