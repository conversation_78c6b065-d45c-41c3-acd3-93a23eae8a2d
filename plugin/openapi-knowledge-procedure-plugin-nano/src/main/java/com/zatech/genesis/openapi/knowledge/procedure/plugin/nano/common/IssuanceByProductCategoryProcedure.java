package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.common;

import com.za.cqrs.util.Jackson;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.model.PolicyOutput;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.shared.policy.service.PolicyService;
import com.zatech.genesis.openapi.platform.hooks.plugin.api.IHookable;
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.BusinessEventBound;
import com.zatech.genesis.openapi.platform.hooks.plugin.api.model.EventCategory;
import com.zatech.genesis.openapi.platform.integration.outer.IOutDpService;
import com.zatech.genesis.openapi.platform.integration.outer.request.DpApiDispatchRequest;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.AbstractProcedure;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.TraceSupport;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;
import com.zatech.genesis.openapi.platform.share.json.JsonParser;
import com.zatech.genesis.openapi.platform.share.json.Jsonable;
import com.zatech.genesis.openapi.platform.share.tools.CollectionUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.ApplicationEventCategoryEnum.CREATE_PROPOSAL_EVENT_CATEGORY_ENUM;
import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.PolicyEventCategoryEnum.CREATE_EVENT_POLICY_EVENT_CATEGORY_ENUM;
import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.PolicyEventCategoryEnum.CREATE_ISSUANCE_EVENT_CATEGORY_ENUM;
import static com.zatech.genesis.openapi.platform.hooks.plugin.api.enums.PolicyEventCategoryEnum.UNDERWRITING_EVENT_CATEGORY_ENUM;

@Slf4j
public class IssuanceByProductCategoryProcedure<IN extends Jsonable, OUT extends Jsonable> extends AbstractProcedure<JsonMap, PolicyOutput> implements IHookable<JsonMap, PolicyOutput> {


    private PolicyService policyService;


    private IOutDpService iOutDpService;

    @Lazy
    @Autowired
    private CommonService commonService;

    private final String phase = "POLICY_API";

    private final String eventFlag = "createIssuanceByEvent";

    public static final String ISSUANCE_TRANSACTION_TYPE = "issuanceTransactionType";

    public IssuanceByProductCategoryProcedure(PolicyService policyService, IOutDpService iOutDpService) {

        this.policyService = policyService;
        this.iOutDpService = iOutDpService;
    }

    public void setCommonService(CommonService commonService) {

        this.commonService = commonService;
    }

    @Override
    public void init(ProcedureClaim claim) {

    }


    @Override
    public void execute(ProcedureExecutionContext context, JsonMap input, PolicyOutput output) {

        execute(context, input, output, phase);
    }

    public void execute(ProcedureExecutionContext context, JsonMap input, PolicyOutput output, String phase) {

        Long geminiSchemaId = commonService.getGeminiSchemaId(context, phase);
        DpApiDispatchRequest dpApiDispatchRequest = new DpApiDispatchRequest();
        dpApiDispatchRequest.setPhase(phase);
        dpApiDispatchRequest.setSchemaId(geminiSchemaId);
        dpApiDispatchRequest.setRevision(1);
        dpApiDispatchRequest.setChannelCode(TraceSupport.getChannelCodeOrNull());
        String json = context.getRequestBody().toJsonString();
        log.info("issuance input is {}", json);

        Map<String, Object> jsonMap = new JsonMap();
        jsonMap.putAll(context.getRequestBody());
        //核保/出单参数处理
        policyService.processUWAndIssuanceInfo(jsonMap);
        log.info("after fill ,inssurance input is {}", JsonParser.toJsonString(jsonMap));
        dpApiDispatchRequest.setJson(JsonParser.toJsonString(jsonMap));
        String result = iOutDpService.apiDispatch(dpApiDispatchRequest);
        PolicyOutput policyOutput = JsonParser.fromJson(result, PolicyOutput.class);
        BeanUtils.copyProperties(policyOutput, output);
        log.info("issuance output is {}", policyOutput.toJsonString());
    }

    @Override
    public List<BusinessEventBound> bindHookEvent(JsonMap input, PolicyOutput policyOutput) {

        List<BusinessEventBound> bounds = new ArrayList<>();
        if (Objects.nonNull(policyOutput) && !CollectionUtil.isEmpty(policyOutput.getPolicies())) {
            policyOutput.getPolicies().forEach(policy -> {
                //事件保单出单 TODO此处拿不到input，确认一下是否直接根据output返回是否包含eventNo来判断是event保单，如果不可以的话需要修改flow逻辑，把input传递过来
                if (input.containsKey(eventFlag) && Objects.equals(String.valueOf(input.get(eventFlag)), "1")
                        && Objects.nonNull(policy.getEventNo())) {
                    BusinessEventBound eventPolicyEvent = new BusinessEventBound();
                    eventPolicyEvent.setEvent(CREATE_EVENT_POLICY_EVENT_CATEGORY_ENUM.getEventCategory());
                    eventPolicyEvent.setBusinessId(policy.getEventNo());
                    bounds.add(eventPolicyEvent);
                    //事件保单只注册这一个事件
                    return;
                }
                if (Objects.isNull(policy.getPolicyNo())) {
                    //只有创建暂存单的时候会不返回保单号，创建暂存单不需要创建回调事件，因为暂存单最终还是要走出单流程
                    return;
                }
                BusinessEventBound bound = new BusinessEventBound();
                bound.setEvent(CREATE_ISSUANCE_EVENT_CATEGORY_ENUM.getEventCategory());
                bound.setBusinessId(policy.getPolicyNo());
                bounds.add(bound);

                if (policy.getIssuanceStatus() == IssuanceStatusEnum.PENDING_PROPOSAL_CHECK) {
                    //创建投保单事件
                    BusinessEventBound proposalEvent = new BusinessEventBound();
                    proposalEvent.setEvent(CREATE_PROPOSAL_EVENT_CATEGORY_ENUM.getEventCategory());
                    proposalEvent.setBusinessId(StringUtils.isEmpty(policy.getIssuanceNo()) ? policy.getProposalNo() : policy.getIssuanceNo());
                    bounds.add(proposalEvent);
                    BusinessEventBound underwritingEvent = new BusinessEventBound();
                    underwritingEvent.setEvent(UNDERWRITING_EVENT_CATEGORY_ENUM.getEventCategory());
                    underwritingEvent.setBusinessId(StringUtils.isEmpty(policy.getIssuanceNo()) ? policy.getProposalNo() : policy.getIssuanceNo());
                    bounds.add(underwritingEvent);
                }
                log.info("bindHookEvent: {},policy:{}", Jackson.toJson(bounds), Jackson.toJson(policy));
            });
        }
        return bounds;
    }

    @Override
    public EventCategory eventCategory() {

        return CREATE_ISSUANCE_EVENT_CATEGORY_ENUM.getEventCategory();
    }

    @Override
    public List<EventCategory> eventCategories() {

        return
                Arrays.asList(
                        CREATE_ISSUANCE_EVENT_CATEGORY_ENUM.getEventCategory(),
                        CREATE_PROPOSAL_EVENT_CATEGORY_ENUM.getEventCategory(),
                        UNDERWRITING_EVENT_CATEGORY_ENUM.getEventCategory()
                );
    }

}
