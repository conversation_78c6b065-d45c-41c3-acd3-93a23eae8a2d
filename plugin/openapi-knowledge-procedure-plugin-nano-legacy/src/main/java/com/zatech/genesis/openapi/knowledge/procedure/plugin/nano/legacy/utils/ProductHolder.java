/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.legacy.utils;

import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayPeriodTypeEnum;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.legacy.model.convert.BaseConverter;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.legacy.model.policy.base.CoveragePeriod;
import com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.legacy.model.policy.base.PremiumPeriod;
import com.zatech.genesis.openapi.platform.integration.outer.IOuterProductService;
import com.zatech.genesis.openapi.platform.integration.product.base.PremiumFrequencyItemBase;
import com.zatech.genesis.openapi.platform.integration.product.request.QueryProductRequest;
import com.zatech.genesis.openapi.platform.integration.product.response.CoveragePeriodAgreementResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.PremiumFrequencyAndInstallmentItemResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.PremiumPeriodAgreementResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.ProductLevelAgreementsResponse;
import com.zatech.genesis.openapi.platform.integration.product.response.ProductResponse;
import com.zatech.genesis.openapi.platform.share.exception.MarketErrorCode;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.function.LazySupplier;
import com.zatech.genesis.portal.toolbox.util.ApplicationContextUtil;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import jakarta.annotation.Nullable;

import org.apache.commons.collections4.CollectionUtils;

public class ProductHolder implements BaseConverter {

    private final LazySupplier<ProductResponse> productSupplier;

    public ProductHolder(Supplier<ProductResponse> productSupplier) {
        this.productSupplier = new LazySupplier<>(productSupplier);
    }

    public ProductHolder(ProductResponse product) {
        this.productSupplier = new LazySupplier<>(() -> product);
    }

    public ProductHolder(final Long productId) {
        this.productSupplier = new LazySupplier<>(() -> queryProductInfoByCodeAndBizDate(productId, null));
    }


    public ProductResponse getProductOrThrow() {
        return Optional.ofNullable(productSupplier.get()).orElseThrow(() -> CommonException.byError(MarketErrorCode.product_not_found));
    }

    public CoveragePeriod getFixedCoveragePeriodConfigOrNull() {
        ProductResponse productResponse = getProductOrThrow();
        ProductLevelAgreementsResponse productLevelAgreements = productResponse.getProductLevelAgreements();
        List<CoveragePeriodAgreementResponse> coveragePeriodAgreementList = productLevelAgreements.getCoveragePeriodAgreementList();
        if (CollectionUtils.size(coveragePeriodAgreementList) != 1) {
            return null;
        }
        CoveragePeriodAgreementResponse coveragePeriodConfig = coveragePeriodAgreementList.get(0);
        Integer coveragePeriodType = coveragePeriodConfig.getCoveragePeriodType();
        Integer coveragePeriod = coveragePeriodConfig.getValueType() == 2 ? coveragePeriodConfig.getValue() : (Objects.equals(coveragePeriodConfig.getMinPeriod(), coveragePeriodConfig.getMaxPeriod()) ? coveragePeriodConfig.getMinPeriod() : null);

        if (coveragePeriodType == null && coveragePeriod == null) {
            return null;
        }
        return CoveragePeriod.of(convert(coveragePeriodType, CoveragePeriodTypeEnum.class), coveragePeriod);
    }

    public PremiumPeriod getFixedPremiumPeriodConfigOrNull() {
        ProductResponse productResponse = getProductOrThrow();
        ProductLevelAgreementsResponse productLevelAgreements = productResponse.getProductLevelAgreements();

        List<PremiumPeriodAgreementResponse> premiumPeriodAgreementList = productLevelAgreements.getPremiumPeriodAgreementList();
        if (CollectionUtils.size(premiumPeriodAgreementList) != 1) {
            return null;
        }
        PremiumPeriodAgreementResponse premiumPeriodConfig = premiumPeriodAgreementList.get(0);
        Integer premiumPeriodType = premiumPeriodConfig.getPremiumPeriodType();
        Integer premiumPeriod = Objects.equals(premiumPeriodConfig.getMinPeriod(), premiumPeriodConfig.getMaxPeriod()) ? premiumPeriodConfig.getMinPeriod() : null;
        if (premiumPeriodType == null && premiumPeriod == null) {
            return null;
        }
        return PremiumPeriod.of(convert(premiumPeriodType, PayPeriodTypeEnum.class), premiumPeriod);
    }

    public PayFrequencyTypeEnum getFixedPremiumFrequencyTypeOrNull() {
        ProductResponse productResponse = getProductOrThrow();
        ProductLevelAgreementsResponse productLevelAgreements = productResponse.getProductLevelAgreements();

        List<PremiumFrequencyAndInstallmentItemResponse> premiumFrequencyAndInstallmentList = productLevelAgreements.getPremiumFrequencyAndInstallmentList();

        if (CollectionUtils.size(premiumFrequencyAndInstallmentList) != 1) {
            return null;
        }

        PremiumFrequencyAndInstallmentItemResponse premiumFrequencyConfig = premiumFrequencyAndInstallmentList.get(0);
        Integer premiumFrequencyType = Optional.ofNullable(premiumFrequencyConfig.getPremiumFrequencyItem()).map(PremiumFrequencyItemBase::getPaymentFrequencyType).orElse(null);
        if (premiumFrequencyType == null) {
            return null;
        }
        return convert(premiumFrequencyType, PayFrequencyTypeEnum.class);
    }

    public ProductResponse queryProductInfoByCodeAndBizDate(Long productId, @Nullable Date bizDate) {
        QueryProductRequest request = new QueryProductRequest();
        request.setProductId(productId);
        request.setQueryProductFiles(true);
        request.setQueryProductOrgCurrency(true);
        request.setQueryProductLevelAgreements(true);
        request.setQueryLiabilityLevelAgreements(true);
        request.setQueryBenefitIllustrationAgreement(true);
        request.setQueryInvestmentProductAgreements(true);
        request.setQueryParticipatingAgreement(true);
        request.setQueryProductExtensionObject(true);
        request.setQueryProductRelationshipMatrix(true);
        request.setQueryProductLiabilityList(true);
        request.setQueryProductBasicInfo(true);
        request.setBizDate(bizDate);
        return ApplicationContextUtil.getBean(IOuterProductService.class).queryProduct(request);
    }


}
