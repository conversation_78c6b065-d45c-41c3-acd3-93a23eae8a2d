package com.zatech.genesis.openapi.knowledge.procedure.plugin.nano.legacy.model.policy.base.calculate;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class CalculatePremiumOutput {

    private List<ProductSaPremium> productList;

    @Data
    public static class ProductSaPremium {

        private Long productId;

        private Date effectiveDate;

        private Date expiryDate;

    }


}
