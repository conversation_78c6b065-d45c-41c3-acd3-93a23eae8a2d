<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zatech.genesis</groupId>
		<artifactId>openapi-platform-plugin-parent</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>openapi-application-scenario-schema-plugin-nano</artifactId>
	<name>openapi-application-scenario-schema-plugin-nano</name>
	<description>The OpenApi Applicaton Scenario Schema Plugin Nano Module</description>

	<dependencies>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>openapi-platform-application-scenario-schema-plugin-api</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-util</artifactId>
			<version>${portal-toolbox-version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>false</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
						<configuration>
							<pomElements>
								<distributionManagement>remove</distributionManagement>
								<repositories>remove</repositories>
							</pomElements>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
