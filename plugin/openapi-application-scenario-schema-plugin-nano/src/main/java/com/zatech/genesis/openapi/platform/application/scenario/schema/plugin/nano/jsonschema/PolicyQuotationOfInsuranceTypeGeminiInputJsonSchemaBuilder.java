package com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.nano.jsonschema;

import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.annotation.ScenarioInstanceJsonSchemaBuilder;
import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.context.model.ScenarioInstanceContext;
import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.jsonschema.IScenarioInstanceInputJsonSchemaBuilder;
import com.zatech.genesis.openapi.platform.integration.outer.IOutDpService;
import com.zatech.genesis.openapi.platform.integration.outer.response.CreateSchemaResponse;
import com.zatech.genesis.openapi.platform.share.SubsriptionConsts;

import io.swagger.v3.oas.models.media.Schema;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Date 2022/3/23
 **/
@Slf4j
@Component
@ScenarioInstanceJsonSchemaBuilder(
    namespaces = {"travel", "auto", "property", "medical", "accident", "ci", "term_life", "whole_life", "liability", "endowment", "annuity",
        "expense", "unit_linked"},
    scenarioSchemaGroup = "policy",
    tags = {SubsriptionConsts.GRAPHENE, SubsriptionConsts.NANO},
    scenarioSchema = "quotation")
public class PolicyQuotationOfInsuranceTypeGeminiInputJsonSchemaBuilder implements IScenarioInstanceInputJsonSchemaBuilder {

    @Autowired
    private IOutDpService iOutDpShareService;

    @Autowired
    private JsonSchemaService jsonSchemaService;


    @Override
    @SneakyThrows
    public Schema buildInput(Long instanceId, ScenarioInstanceContext context) {

        CreateSchemaResponse response = iOutDpShareService.createSchema("QUOTATION_API", 1);

        return jsonSchemaService.rebuildContext(response, context);
    }

}
