package com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.nano.jsonschema;

import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.annotation.ScenarioInstanceJsonSchemaBuilder;
import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.context.model.ScenarioInstanceContext;
import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.jsonschema.IScenarioInstanceInputJsonSchemaBuilder;
import com.zatech.genesis.openapi.platform.integration.outer.IOutDpService;
import com.zatech.genesis.openapi.platform.share.SubsriptionConsts;
import com.zatech.genesis.openapi.platform.share.jsonschema.SwaggerJsonSchemaHelper;

import io.swagger.v3.oas.models.media.Schema;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ScenarioInstanceJsonSchemaBuilder(scenarioSchemaGroup = "pos", scenarioSchema = "cancellation", tags = {SubsriptionConsts.NANO,
    SubsriptionConsts.GRAPHENE})
public class PosCancellationInputJsonSchemaBuilder implements IScenarioInstanceInputJsonSchemaBuilder {

    @Autowired
    private IOutDpService iOutDpShareService;

    @Override
    public Schema buildInput(Long instanceId, ScenarioInstanceContext context) {

        String apiSchema = iOutDpShareService.getApiSchema("POS", "POS_CANCELLATION");
        System.out.println(apiSchema);
        return SwaggerJsonSchemaHelper.fromJsonStringToJsonSchema(apiSchema);
    }

}
