package com.zatech.genesis.openapi.hooks.plugin.nano.model;

import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.openapi.platform.share.json.Json;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * result of event policy.proposalCreate.result
 */
@Data
@Schema(name = "CreateProposalResultModel")
public class CreateProposalResultModel implements Json {
    @Schema(description = "Biz Apply No")
    private String bizApplyNo;

    @Schema(description = "A unique identifier associated with the issuance of an insurance policy.")
    private String issuanceNo;

    @Schema(description = "A unique identifier associated with the issuance of an insurance policy")
    private Long issuanceId;

    @Schema(description = "Pending Case Id")
    private Long pendingCaseId;

    @Schema(description = "Issuance Status")
    private IssuanceStatusEnum issuanceStatus;

}
