package com.zatech.genesis.openapi.hooks.plugin.nano.converter;

import com.zatech.genesis.openapi.hooks.plugin.nano.model.UnderwritingResultModel;
import com.zatech.genesis.openapi.platform.integration.policy.base.UwTaskDecisionInfo;

public class UnderwritingResultConverter {

    private UnderwritingResultConverter() {
    }

    public static UnderwritingResultModel convert(UwTaskDecisionInfo uwDecisionInfo) {
        UnderwritingResultModel model = new UnderwritingResultModel();
        model.setTaskId(uwDecisionInfo.getTaskId());
        model.setTaskNo(uwDecisionInfo.getTaskNo());
        model.setIssuanceNo(uwDecisionInfo.getApplicationNo());
        model.setPolicyNo(uwDecisionInfo.getPolicyNo());
        model.setTaskStatus(uwDecisionInfo.getTaskStatus());
        model.setHolderCustomerId(uwDecisionInfo.getHolderCustomerId());
        model.setUwDecisionId(uwDecisionInfo.getUwDecisionId());
        model.setUwDecision(uwDecisionInfo.getUwDecision());
        model.setTaskCategory(uwDecisionInfo.getTaskCategory());
        model.setWithOpenIssue(uwDecisionInfo.getWithOpenIssue());
        return model;
    }
}
