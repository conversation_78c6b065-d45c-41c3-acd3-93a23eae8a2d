package com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.samplebuilder;

import com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.context.model.ScenarioInstanceContext;
import com.zatech.genesis.openapi.platform.share.json.JsonMap;

/**
 * <AUTHOR>
 * @Date 2022/2/23
 * 构建输出示例数据
 **/
public interface IScenarioSchemaOutputSampleBuilder extends IScenarioSchemaSampleBuilder {

    JsonMap buildOutput(Long instanceId, ScenarioInstanceContext context);

}
