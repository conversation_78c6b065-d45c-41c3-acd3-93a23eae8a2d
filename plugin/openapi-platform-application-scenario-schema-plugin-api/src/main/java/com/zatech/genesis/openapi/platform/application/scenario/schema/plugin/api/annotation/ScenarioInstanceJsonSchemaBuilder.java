package com.zatech.genesis.openapi.platform.application.scenario.schema.plugin.api.annotation;

import com.zatech.genesis.openapi.platform.share.enums.ScenarioSchemaBindingEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2022/2/23
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface ScenarioInstanceJsonSchemaBuilder {

    String[] namespaces() default {};

    String scenarioSchemaGroup();

    String scenarioSchema();

    String[] tags();

    String[] tenants() default {};

    String version() default "v1";

    ScenarioSchemaBindingEnum binding() default ScenarioSchemaBindingEnum.Tenant;

}
