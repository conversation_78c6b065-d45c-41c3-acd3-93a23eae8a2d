package com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api;

import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureClaim;
import com.zatech.genesis.openapi.platform.knowledge.procedure.plugin.api.model.ProcedureExecutionContext;
import com.zatech.genesis.openapi.platform.share.json.Json;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @Date 2023/4/21
 **/
class AbstractProcedureTest {

    static class TestProcedure extends AbstractProcedure<Json, Json> {

        @Override
        public void init(ProcedureClaim claim) {

        }

        @Override
        public void execute(ProcedureExecutionContext context, Json input, Json output) {

        }
    }

    static class GenericT<T> implements Json {
    }

    static class TestGenericProcedure extends AbstractProcedure<GenericT<Json>, GenericT<Json>> {

        @Override
        public void init(ProcedureClaim claim) {

        }

        @Override
        public void execute(ProcedureExecutionContext context, GenericT<Json> input, GenericT<Json> output) {

        }
    }

    @Test
    public void testProcedureTypeArgument() {
        AbstractProcedure procedure1 = new TestProcedure();
        AbstractProcedure procedure2 = new TestGenericProcedure();

        Assertions.assertEquals(procedure1.inCls, Json.class);
        Assertions.assertEquals(procedure1.outCls, Json.class);
        Assertions.assertEquals(procedure2.inCls, GenericT.class);
        Assertions.assertEquals(procedure2.outCls, GenericT.class);
    }

}
