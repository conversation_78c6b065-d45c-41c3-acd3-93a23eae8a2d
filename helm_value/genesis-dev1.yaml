# Default values for user-auth.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
ingress:
  enabled: true
  hosts:
    - name: genesis-dev1-genesis-genesis-openapi-platform.dev1.eks.za-gj-aws.net
      servicePort: 8080
      serviceName: svc-genesis-dev1-genesis-openapi-platform
      path: /

statefulset:
  enabled: true

deployment:
  enabled: false

jobContainers:
  enabled: false

initContainers:
  enabled: false

replicaCount: 2

healthCheckPath: /actuator/health
healthCheckPort: 8080

imagePullSecrets:
- name: harbor-gj-public-aws-harbor

image:
#   repository: gj-public-ali-harbor.zatech.com
#   group: citest
  pullPolicy: Always

serviceAccount:
  create: false
  annotations: {}
  name: ""

maxUnavailable: {}

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

## By default deploymentStrategy is set to rollingUpdate with maxSurge of 25% and maxUnavailable of 25% .
## You can change type to `Recreate` or can uncomment `rollingUpdate` specification and adjust them to your usage.
deploymentStrategy:
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%
  type: RollingUpdate

service:
  type: ClusterIP

resources:
  limits:
    cpu: 1500m
    memory: 2048Mi
  requests:
    cpu: 300m
    memory: 512Mi

autoscaling:
  enabled: false

nodeSelector:
  com.zhonganinfo.bizcluster: genesis-dev1

tolerations: []

# affinity:
#   podAntiAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#       - labelSelector:
#           matchExpressions:
#             - key: app.kubernetes.io/name
#               operator: In
#               values:
#                 - citest-data
#         topologyKey: kubernetes.io/hostname

envs:
  - name: "DEPLOY_ENV"
    value: "dev1"
  - name: "CONFIG_SERVER_URL"
    value: "mscnew.za-gj-aws.net:8848"
  - name: "NACOS_GROUP"
    value: "baseline"
  - name: "NACOS_USER"
    value: "genesis"
  - name: "NACOS_PWD"
    value: "genesis"
  - name: "NACOS_ENABLE"
    value: "true"
  - name: "SPRING_DISCOVERY_ENABLE"
    value: "false"
  - name: "JVM_OPTS"
    value: "-Xms1024m -Xmx1024m"
  - name: "JAVA_OPTS"
    value: "-Dloader.path=./plugins -Xdebug -Xrunjdwp:transport=dt_socket,address=8788,server=y,suspend=n -Djava.security.egd=file:/dev/./urandom"
    
ports:
  - name: http
    containerPort: 8080
    protocol: TCP

volumeMounts:
  - name: localtime-volume
    mountPath: /etc/localtime
  - name: timezone-volume
    mountPath: /etc/timezone
  - name: applog-volume
    mountPath: /alidata1/admin/zatech-openapi-platform/logs

volumes:
  - name: localtime-volume
    hostPath:
      path: /etc/localtime
  - name: timezone-volume
    hostPath:
      path: /etc/timezone
  - name: applog-volume
    hostPath:
      path: /alidata1/admin/logs/zatech-openapi-platform
