package com.zatech.genesis.openapi.platform.api.resource.developercenter.mgmt.webhook.log.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/8/12 15:17
 */
@Data
public class WebHookPageableLogsResponse {

    private List<WebHookMessageLogSummary> data;

    @Schema(title = "Total record numbers", description = "total numbers of record")
    private long total;

    @Schema(title = "Page index", description = "index of current page,start from 0")
    private Integer pageIndex;

    @Schema(title = "Page size", description = "size of one page")
    private Integer pageSize;
}
