package com.zatech.genesis.openapi.platform.api.resource.dsl.model.schema;

import com.zatech.genesis.openapi.platform.api.resource.dsl.model.SpecBaseInfo;
import com.zatech.genesis.openapi.platform.api.resource.dsl.model.schemegroup.SchemaGroupDslTemplate;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2022/8/24
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class SchemaSpecInfo extends SpecBaseInfo {

    private SchemaGroupDslTemplate schemaGroup;

}
