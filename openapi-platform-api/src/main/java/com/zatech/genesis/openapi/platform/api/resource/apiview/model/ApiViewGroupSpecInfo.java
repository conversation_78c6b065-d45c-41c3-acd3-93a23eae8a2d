package com.zatech.genesis.openapi.platform.api.resource.apiview.model;

import com.zatech.genesis.openapi.platform.api.resource.dsl.model.SpecBaseInfo;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2022/8/24
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class ApiViewGroupSpecInfo extends SpecBaseInfo {

    private List<ApiViewGroupDslTemplate> groups;

    private List<ApiViewSchemaDslTemplate> schemas;

    private List<ApiViewDocumentDslTemplate> documents;

}
