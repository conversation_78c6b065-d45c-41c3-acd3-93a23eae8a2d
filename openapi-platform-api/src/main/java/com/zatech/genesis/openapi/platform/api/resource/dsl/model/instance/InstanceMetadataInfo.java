package com.zatech.genesis.openapi.platform.api.resource.dsl.model.instance;

import com.zatech.genesis.openapi.platform.api.resource.dsl.model.MetadataBaseInfo;
import com.zatech.genesis.openapi.platform.share.enums.AuthenticationType;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/8/17 15:06
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class InstanceMetadataInfo extends MetadataBaseInfo {

    private boolean validateFlag;

    private boolean activeFlag;

    private String deleted;

    private InstanceExtension extension;

    private DocInfo doc;

    private List<DocInfoI18n> docInfoI18ns;

    private BindFlow bindFlow;

    private boolean isCallback;

    private CallbackInfo callbackInfo;

    private List<AuthenticationType> authenticationTypes;

}


