package com.zatech.genesis.openapi.platform.api.resource.meta.scenario.instance.response;

import com.zatech.genesis.openapi.platform.share.enums.ScenarioSchemaBindingEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2022/1/22
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class BindingGoodsScenarioInstanceResponse extends ScenarioInstanceResponse {

    private Long goodsId;

    @Override
    public ScenarioSchemaBindingEnum getBinding() {

        return ScenarioSchemaBindingEnum.Goods;
    }

}
