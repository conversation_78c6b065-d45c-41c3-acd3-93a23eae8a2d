/*
 *
 *  * Copyright By ZATI
 *  * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 *  * All rights reserved.
 *  *
 *  * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 *  * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 *
 */

package com.zatech.genesis.openapi.platform.api.resource.apiview.document.response;

import com.zatech.genesis.openapi.platform.api.resource.meta.common.MetaBaseResponse;
import com.zatech.genesis.openapi.platform.share.enums.ApiViewDocLinkEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/8/14 16:53
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class ApiViewDocumentResponse extends MetaBaseResponse {

    private String link;

    private ApiViewDocLinkEnum linkType;

    private String docType;

    private Long boundCount;

}
