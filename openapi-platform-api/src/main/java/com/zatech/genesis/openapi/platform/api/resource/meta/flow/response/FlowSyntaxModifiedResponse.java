package com.zatech.genesis.openapi.platform.api.resource.meta.flow.response;

import com.zatech.genesis.openapi.platform.api.resource.meta.flow.FlowSyntaxCheckResult;

import java.util.Optional;

import lombok.Getter;

@Getter
public class FlowSyntaxModifiedResponse {

    private FlowSyntaxCheckResult flowSyntaxCheckResult;

    private Long flowSchemaId;

    private Boolean haveSyntaxError;

    public FlowSyntaxModifiedResponse(FlowSyntaxCheckResult flowSyntaxCheckResult, Long flowSchemaId) {
        this.flowSyntaxCheckResult = flowSyntaxCheckResult;
        this.flowSchemaId = flowSchemaId;
        this.haveSyntaxError = Optional.ofNullable(flowSyntaxCheckResult).map(w -> !w.pass()).orElse(false);
    }
}
