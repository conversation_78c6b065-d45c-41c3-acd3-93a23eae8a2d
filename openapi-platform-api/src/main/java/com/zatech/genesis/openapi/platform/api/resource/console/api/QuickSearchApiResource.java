package com.zatech.genesis.openapi.platform.api.resource.console.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @Date 2025/05/26
 */
@Tag(name = "Quick Search Related API")
@RequestMapping("/quick-search")
public interface QuickSearchApiResource {

    @Operation(summary = "Sync data into ES")
    @PostMapping("/sync")
    void sync();

}
