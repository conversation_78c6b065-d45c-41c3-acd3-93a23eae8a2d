package com.zatech.genesis.openapi.platform.api.resource.business.request;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@Data
@Schema(title = "更新接口权限请求")
public class UpdatePermissionRequest {

    @Schema(title = "需要授权的ScenarioInstance Id列表")
    private List<Long> permittedScenarioInstanceIds;

    @Schema(title = "需要限制的ScenarioInstance Id列表")
    private List<Long> deniedScenarioInstanceIds;

}
