package com.zatech.genesis.openapi.platform.api.resource.meta.modelmapping;

import com.zatech.genesis.openapi.platform.api.resource.meta.modelmapping.request.CreateModelMappingRequest;
import com.zatech.genesis.openapi.platform.api.resource.meta.modelmapping.response.ModelMappingDetailResponse;

import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2021/11/11 17:10
 **/
@RequestMapping("/openapi/mgmt/v1/meta/modelmapping")
public interface ModelMappingResource {

    @Operation(summary = "创建映射关系")
    @PostMapping
    @PreAuthorize("hasRole('ROLE_OPENAPI_EDIT')")
    Long createMapping(@RequestBody CreateModelMappingRequest request);

    @Operation(summary = "通过目标属性id获取映射源属性列表")
    @GetMapping("/by-target/{targetModelMetaId}")
    List<ModelMappingDetailResponse> querySourceModelMeta(@PathVariable("targetModelMetaId") Long targetModelMetaId);

    @Operation(summary = "通过源属性id获取目标属性列表")
    @GetMapping("/by-source/{sourceModelMetaId}")
    List<ModelMappingDetailResponse> queryTargetModelMeta(@PathVariable("sourceModelMetaId") Long sourceModelMetaId);

    @Operation(summary = "删除映射关系")
    @DeleteMapping("/{mappingId}")
    @PreAuthorize("hasRole('ROLE_OPENAPI_EDIT')")
    void deleteModelMapping(@PathVariable("mappingId") Long mappingId);

}
