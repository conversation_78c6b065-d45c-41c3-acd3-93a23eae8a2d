package com.zatech.genesis.openapi.platform.api.resource.console.mgmt.application.permission.openx;

import com.zatech.genesis.openapi.platform.api.resource.business.inner.permission.response.PermissionApiResponse;
import com.zatech.genesis.openapi.platform.api.resource.console.mgmt.application.permission.openx.request.UpdatePermissionRequest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @create 2024/10/9 11:35
 **/
@RequestMapping("/openapi/mgmt/v3/applications")
public interface OpenXPermissionMgmtResource {

    @Operation(summary = "获取所有带权限标记的api列表")
    @GetMapping("/{appId}/apis")
    PermissionApiResponse getPermissionApis(@Parameter(name = "应用id")
                                            @PathVariable("appId") String appId);

    @Operation(summary = "更新应用的api权限")
    @PutMapping("/{appId}/apis")
    @PreAuthorize("hasRole('ROLE_OPENAPI_EDIT')")
    void updateApiPermission(@Parameter(name = "应用id")
                             @PathVariable("appId") String appId,
                             @RequestBody UpdatePermissionRequest request);
}
