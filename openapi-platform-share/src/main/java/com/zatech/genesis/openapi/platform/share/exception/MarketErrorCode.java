package com.zatech.genesis.openapi.platform.share.exception;

import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.enums.ErrorTypeEnum;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.rest.ErrorTags;

import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.COLLECTION_CHECK;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.FACTOR;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.FORMULA_CHECK;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.GOODS_INFO_VALIDATE;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.GOODS_PLAN_CHECK;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.POLICY_V2;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.PRODUCT_LEVEL_CHECK;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.QUERY_PACKAGE;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.QUOTATION_SERVICE;
import static com.zatech.genesis.openapi.platform.share.ErrorConstant.Tag.RULE_ENGINE_ERROR;
import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.BusinessError$500;
import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.NotFound$404;
import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.ValidationError$400;

public enum MarketErrorCode implements IErrorCode {

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({GOODS_PLAN_CHECK})
    planId_not_match_goodsId,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({GOODS_PLAN_CHECK})
    plan_code_not_match_goodsId,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({FORMULA_CHECK})
    formula_code_not_found,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({FACTOR})
    factor_code_not_exists,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({COLLECTION_CHECK})
    collection_not_single,

    @StandardErrorCode(status = BusinessError$500)
    @ErrorTags({QUOTATION_SERVICE})
    liabilityId_not_match,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({QUOTATION_SERVICE})
    premium_discount_type_illegal,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({GOODS_INFO_VALIDATE})
    goodsCode_can_not_be_empty,

    @StandardErrorCode(status = NotFound$404)
    @ErrorTags({QUOTATION_SERVICE})
    product_id_not_exist,

    @StandardErrorCode(status = NotFound$404)
    @ErrorTags({QUOTATION_SERVICE})
    liability_id_not_exist,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({GOODS_INFO_VALIDATE})
    goods_info_is_not_present,

    @StandardErrorCode(status = NotFound$404, type = ErrorTypeEnum.internal)
    @ErrorTags({GOODS_INFO_VALIDATE})
    goods_not_found,

    @StandardErrorCode(status = NotFound$404, type = ErrorTypeEnum.internal)
    @ErrorTags({})
    product_not_found,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({GOODS_INFO_VALIDATE})
    goodsId_can_not_be_empty,

    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({GOODS_INFO_VALIDATE})
    package_product_and_liability,

    @StandardErrorCode(status = BusinessError$500)
    @ErrorTags({GOODS_INFO_VALIDATE, POLICY_V2})
    rider_product_configure_error,


    @StandardErrorCode(status = ValidationError$400)
    @ErrorTags({PRODUCT_LEVEL_CHECK})
    coveragePeriodType_or_value_cannot_be_empty,

    @StandardErrorCode(status = BusinessError$500)
    @ErrorTags({RULE_ENGINE_ERROR})
    rule_engine_error,

    @StandardErrorCode(status = NotFound$404)
    @ErrorTags({QUERY_PACKAGE})
    package_not_found,

    @StandardErrorCode(status = NotFound$404)
    @ErrorTags({QUOTATION_SERVICE})
    liability_not_found;

    MarketErrorCode() {
    }

    @Override
    public String getModuleName() {
        return "market";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
