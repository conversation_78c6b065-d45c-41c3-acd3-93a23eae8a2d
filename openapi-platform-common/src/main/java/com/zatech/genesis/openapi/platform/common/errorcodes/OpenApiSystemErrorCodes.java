package com.zatech.genesis.openapi.platform.common.errorcodes;


import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.enums.ErrorCategoryEnum;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.Forbidden$403;
import static com.zatech.genesis.portal.toolbox.exception.StandardErrorCode.HttpStatusCode.NotFound$404;

/**
 * <AUTHOR>
 * @create 2024/12/6 10:43
 **/
public enum OpenApiSystemErrorCodes implements IErrorCode {
    @StandardErrorCode(status = NotFound$404, category = ErrorCategoryEnum.system)
    application_not_found,

    @StandardErrorCode(status = Forbidden$403, category = ErrorCategoryEnum.system)
    application_not_permitted,

    @StandardErrorCode(status = Forbidden$403, category = ErrorCategoryEnum.system)
    api_url_not_permitted,

    @StandardErrorCode(status = NotFound$404, category = ErrorCategoryEnum.system)
    dashboard_name_not_found;

    @Override
    public String getModuleName() {
        return "system";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
