<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>unified-model</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <name>Genesis Unified Model - API</name>
    <artifactId>unified-model-api</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-enum-baseline</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-enum-product</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-enum-product-market</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-enum-fund</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>
</project>
