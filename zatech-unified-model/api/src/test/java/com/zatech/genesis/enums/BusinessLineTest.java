package com.zatech.genesis.enums;

import java.util.Comparator;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

@ExtendWith(MockitoExtension.class)
class BusinessLineTest {

    @Test
    void enums_should_be_sorted_in_alphabet_order() {
        final var sorted = Stream.of(BusinessLine.values())
                .sorted(Comparator.comparing(Enum::name))
                .toArray(BusinessLine[]::new);

        assertThat(sorted, is(BusinessLine.values()));
    }

}
