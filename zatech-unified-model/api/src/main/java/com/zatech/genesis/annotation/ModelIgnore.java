package com.zatech.genesis.annotation;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * This annotation is a hint for model validator to ignore the field or method.
 */
@UnifiedModel
@JsonIgnore
@Retention(RUNTIME)
@Target({FIELD, METHOD})
public @interface ModelIgnore {
}
