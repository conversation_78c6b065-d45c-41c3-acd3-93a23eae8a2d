package com.zatech.genesis.enums.business.policy;

/**
 * define the limit amount modes
 */
public enum LimitTypeEnum {

    /**
     * No limit
     */
    NO_LIMIT,

    /**
     * 每次事故限额
     */
    PER_INCIDENT_LIMIT,

    /**
     * 累计事故限额
     */
    ACCUMULATED_INCIDENT_LIMIT,

    /**
     * 每人每次事故限额
     */
    PER_PERSON_PER_TIME_INCIDENT_LIMIT,

    /**
     * 每日租金限额
     */
    DAILY_RENT_LIMIT

}
