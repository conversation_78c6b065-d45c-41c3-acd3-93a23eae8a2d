package com.zatech.genesis.annotation;

import com.zatech.gaia.resource.claim.LossPartyTypeEnum;
import com.zatech.gaia.resource.components.enums.claim.ClaimSectionEnum;
import com.zatech.gaia.resource.components.enums.claim.ClaimTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * claim type to indicate different claim application
 */
@UnifiedModel
@Target({ElementType.ANNOTATION_TYPE, ElementType.TYPE,
        ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ClaimModel {

    /**
     * list the claim types that indicates the classes and fields are used for
     * particular claim application.
     *
     * @return ClaimTypeEnum[]
     */
    ClaimTypeEnum[] value() default {};

    /**
     * list the claim loss party that indicates the classes and fields are used for
     * particular claim application.
     * @return ClaimLossPartyEnum[]
     */
    LossPartyTypeEnum[] lossParty() default {};

    /**
     * mark the sections on the class object level to indicates the object belongs to which section.
     * section can be configured by tenant by below relationships:
     * liability -> claim types
     * section -> claim types
     * these two mapping relationships are defined on product level.
     * @return ClaimSectionEnum[]
     */
    ClaimSectionEnum[] sections() default {};

    /**
     * description name used to explain the meaning of the field of model class.
     * It could be used in rule engine UI or DSL to tooltip the explanation of field.
     *
     * @return String
     */
    String description() default "";
}
