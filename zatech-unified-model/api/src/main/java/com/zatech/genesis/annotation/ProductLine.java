package com.zatech.genesis.annotation;

import com.zatech.genesis.enums.ApiTrait;
import com.zatech.genesis.enums.BusinessLine;

import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static com.zatech.genesis.enums.ApiTrait.KERNEL;
import static com.zatech.genesis.enums.BusinessLine.ALL;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * This annotation is used to tag the field, method or class that is specially used for some product line(s) only.
 * By default, value is ALL which means applying to every product line.
 */
@UnifiedModel
@Retention(RUNTIME)
@Target({FIELD, METHOD, TYPE})
@Repeatable(ProductLines.class)
public @interface ProductLine {

    BusinessLine[] value() default ALL;

    /**
     * The scope of property exposing to API
     */
    ApiTrait apiTrait() default KERNEL;

}
