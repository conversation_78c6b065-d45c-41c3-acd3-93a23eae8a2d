package com.zatech.genesis.annotation;

import com.google.common.annotations.Beta;
import com.zatech.genesis.enums.ApiTrait;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static com.zatech.genesis.enums.ApiTrait.KERNEL;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * This annotation is used for issuing policy scenario, marked the classes
 * and fields that necessary for policy issuance API.
 */
@UnifiedModel
@Retention(RUNTIME)
@Target({FIELD, METHOD, TYPE})
public @interface IssuanceModel {

    /**
     * indicator the polymorphism if the class is polymorphism to display the information for
     * policy issue API that gathering the information needed.
     * e.g. all the objects derived from insured object, this can indicate system to deal with polymorphism object list
     *
     * @return boolean
     */
    @Beta
    boolean polymorphism() default false;

    /**
     * Exclude unused properties from model hierarchy
     */
    String[] excludeBaseFields() default {};

    /**
     * The scope of property exposing to API
     */
    ApiTrait apiTrait() default KERNEL;

    int maxLength() default 999;

}
