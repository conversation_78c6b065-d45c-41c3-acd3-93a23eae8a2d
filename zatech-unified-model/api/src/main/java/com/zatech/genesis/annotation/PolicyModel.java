package com.zatech.genesis.annotation;

import com.google.common.annotations.Beta;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@UnifiedModel
@Retention(RUNTIME)
@Target({FIELD, METHOD, TYPE})
public @interface PolicyModel {

    /**
     * displayName is used to define the readable text for the field name of the model class.
     * It would be helpful for business users to read and use on the UI.
     *
     * @return String
     */
    String displayName() default "";

    /**
     * description name used to explain the meaning of the field of model class.
     * It could be used in rule engine UI or DSL to tooltip the explanation of field.
     *
     * @return String
     */
    String description() default "";

    /**
     * indicator the polymorphism if the class is polymorphism to display the information for
     * policy issue API that gathering the information needed.
     * e.g. all the objects derived from insured object, this can indicate system to deal with polymorphism object list
     *
     * @return boolean
     */
    @Beta
    boolean polymorphism() default false;

    /**
     * label function to indicator the function usage for special schemas or fields
     *
     * @return LabelEnum
     */
    @Beta
    LabelEnum label() default LabelEnum.NONE;

    /**
     * label type for property info annotation to distinguish the usage of fields on schema
     * the number of label type will be increase according to the functions supported.
     */
    enum LabelEnum {
        /**
         * premium calculation usage at fields level
         */
        PREMIUM_CALC,
        /**
         * none as the default of the label mark.
         */
        NONE
    }

}
