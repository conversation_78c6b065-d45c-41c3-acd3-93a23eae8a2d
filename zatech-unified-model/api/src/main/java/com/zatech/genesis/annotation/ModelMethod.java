package com.zatech.genesis.annotation;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Indicate the useful and aggregated built-in method of model
 * which may be used in universal pricing & risk scenario(s) etc.
 */
@UnifiedModel
@JsonIgnore
@Target(METHOD)
@Retention(RUNTIME)
public @interface ModelMethod {

    String description();

}
