package com.zatech.genesis.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@UnifiedModel
@Retention(RUNTIME)
@Target({FIELD, TYPE})
public @interface CustomizedModel {

    /**
     * whether the field is timezone insensitive or not we can use this to handle the timezone issue for the fields later on will move this to a
     * general place annotation for filed
     *
     * @return boolean
     */
    boolean timeZoneInsensitive() default false;

}
