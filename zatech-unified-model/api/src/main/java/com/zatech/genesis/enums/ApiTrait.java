package com.zatech.genesis.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Define all Graphene supported API trait
 */
@Getter
@AllArgsConstructor
public enum ApiTrait implements IEnum {

    FULL(true, true, "Expose to full scope"),
    KERNEL(true, false, "Expose to inbound integrator"),
    OPEN(false, true, "Expose to 3rd-party SI vendor");

    private final boolean kernelApi;

    private final boolean openApi;

    private final String description;

}
