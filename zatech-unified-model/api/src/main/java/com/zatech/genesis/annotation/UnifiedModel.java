package com.zatech.genesis.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * This annotation is a hint for all unified model.
 */
@Documented
@Inherited
@Retention(RUNTIME)
@Target(ANNOTATION_TYPE)
public @interface UnifiedModel {
}
