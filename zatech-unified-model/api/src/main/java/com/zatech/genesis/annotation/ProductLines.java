package com.zatech.genesis.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
/**
 * This annotation is used to tag the field, method or class that is specially used for some product line(s) only.
 * contain productLines annotation for product line and ApiTrait combine
 * @see ProductLine
 * @since 1.0.0
 */

@UnifiedModel
@Retention(RUNTIME)
@Target({FIELD, METHOD, TYPE})
public @interface ProductLines {

    ProductLine[] value();

}
