package com.zatech.genesis.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.zatech.genesis.enums.BusinessTrait.ACCIDENT_HEALTH;
import static com.zatech.genesis.enums.BusinessTrait.LIFE;
import static com.zatech.genesis.enums.BusinessTrait.OTHER;
import static com.zatech.genesis.enums.BusinessTrait.PROPERTY;
import static com.zatech.genesis.enums.BusinessTrait.VEHICLE;

/**
 * Define all Graphene supported insurance business lines in alphabet order
 */
@Getter
@AllArgsConstructor
public enum BusinessLine implements IEnum {

    ACCIDENT(ACCIDENT_HEALTH, ""),
    ALL(OTHER, ""),
    ANNUITY(LIFE, ""),
    CRITICAL_ILLNESS(ACCIDENT_HEALTH, ""),
    ENDOWMENT(LIFE, ""),
    ENGINEERING(OTHER, false, ""),
    EXPRESS_DELIVERY(OTHER, false, ""),
    EXTENDED_WARRANTY(PROPERTY, ""),
    GADGET(PROPERTY, ""),
    GROUP_EB(OTHER, false, ""),
    HOME_PROPERTY(PROPERTY, ""),
    INVESTMENT_LINKED(LIFE, ""),
    LIABILITY(PROPERTY, false, ""),
    MARINE_CARGO(OTHER, false, ""),
    MEDICAL(ACCIDENT_HEALTH, ""),
    MOTOR(VEHICLE, ""),
    MOTOR_JAPAN(VEHICLE, ""),
    PET(PROPERTY, ""),
    TERM_LIFE(LIFE, ""),
    TRAVEL(ACCIDENT_HEALTH, ""),
    UNIVERSAL_LIFE(LIFE, false, ""),
    WALLET_PROTECTION(PROPERTY, false, ""),
    WHOLE_LIFE(LIFE, "");

    private final BusinessTrait trait;

    private final boolean supported;

    private final String description;

    BusinessLine(BusinessTrait trait, String description) {
        this.trait = trait;
        this.supported = true;
        this.description = description;
    }

}
