/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.claim.caseinfo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.zatech.gaia.resource.claim.CaseDecisionEnum;
import com.zatech.gaia.resource.claim.LossPartyTypeEnum;
import com.zatech.gaia.resource.claim.PaymentStatusEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.genesis.model.claim.customer.Payee1;
import com.zatech.genesis.model.claim.documentation.Documentation;
import com.zatech.genesis.model.claim.incident.Incident;
import com.zatech.genesis.model.claim.incident.IncidentContent;
import com.zatech.genesis.model.claim.lossparty.LossParty;
import com.zatech.genesis.model.claim.matchedpolicy.PolicyMatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import lombok.Getter;
import lombok.Setter;

import org.springframework.util.CollectionUtils;

/**
 * Claim case base information
 */
@Getter
@Setter
@Schema(name = "Case")
public class Case {
    @Schema(description = "Collection of standardized reason codes explaining the circumstances and causes of the claim, used for categorization, statistics, and regulatory reporting purposes.", maxLength = 2000)
    private final List<String> reasonCodeList = Lists.newArrayList();

    @Schema(description = "Comprehensive list of insurance policies that potentially cover this claim, including policy details, coverage periods, and liability determinations for accurate claim processing.")
    private final List<PolicyMatch> policyMatchList = Lists.newArrayList();

    @Schema(description = "Complete list of individuals or organizations designated to receive claim payments, including their banking details, payment preferences, and legal authorization to receive funds.")
    private final List<Payee1> payeeList = Lists.newArrayList();

    @Schema(description = "Unique identifier assigned to this specific claim case, used for tracking, referencing, and communicating about the claim throughout its lifecycle.")
    private String caseNo;

    @Schema(description = "Reference number identifying the specific incident or event that triggered this claim, linking the case to the original occurrence report.", maxLength = 32, type = "string")
    private String incidentNo;

    @Schema(description = "Version control number tracking changes and updates made to this claim case, ensuring audit trail and preventing concurrent modification conflicts.")
    private Integer version;

    @Schema(description = "Current adjudication status of the claim case, such as APPROVED, DENIED, PENDING, or UNDER_INVESTIGATION, indicating the claim's processing stage and outcome.", maxLength = 32, type = "string")
    private CaseDecisionEnum decision;

    @Schema(description = "Detailed notes and observations about the claim case, including adjuster comments, special circumstances, and important considerations for claim processing.", maxLength = 4000, type = "string")
    private String comment;

    @Schema(description = "Final settlement amount to be paid out for this claim after currency conversion to the tenant's base currency, representing the actual monetary compensation.")
    private String payoutAmount;

    @Schema(description = "The currency of the base. This is a three character ISO 4217 currency code. For example, " +
        "USD for US Dollars, refer to the API reference. [Detail](/openx/docs/common/guide/reference/country-codes)",
        type = "string", example = "USD", pattern = "^[A-Z]{3}$", minLength = 3, maxLength = 8)
    private CurrencyEnum baseCurrency;

    @Schema(description = "Current status of payment processing for this claim, such as PENDING, PROCESSED, FAILED, or COMPLETED, indicating the payment workflow stage.", type = "string", maxLength = 32)
    private PaymentStatusEnum paymentStatus;

    @Schema(description = "Actual date and time when the claim settlement payment was processed and disbursed to the payee, marking the completion of the financial transaction.", type = "string", format = "date-time")
    private Date paymentDate;

    @Schema(description = "Timestamp of the most recent modification to this claim case record, used for audit trails and tracking case activity chronology.")
    private Date gmtModified;

    @Schema(description = "System identifier of the claims adjuster or operator currently assigned to handle this case, enabling workload management and responsibility tracking.")
    private Long currentOperatorId;

    @Schema(hidden = true, description = "Information about the party that suffered the loss or damage, including their relationship to the insured and liability assessment for claim settlement.")
    @Deprecated(since = "V2.29")
    private LossParty lossParty;

    /**
     * The loss parties (全自动理赔时非必填，默认取个单保单中被保人信息)
     */
    @Schema(description = "List of case loss party for insurance claim reporting submission")
    private List<LossParty> lossPartyList;

    /**
     * The incident information
     */
    @Schema(description = "Submitted incident base information for insurance claim reporting")
    private Incident incident;


    @Schema(description = "Detailed information about the incident that caused the claim, including circumstances, location, time, and contributing factors for accurate assessment.")
    private IncidentContent incidentContent;

    @Schema(description = "Complete collection of supporting documents, evidence, and paperwork related to this claim, including forms, photos, reports, and legal documentation.")
    private Documentation documentation;

    @JsonIgnore
    public List<LossParty> getLossParties() {
        if (CollectionUtils.isEmpty(lossPartyList) && null != lossParty) {
            LossParty lossParty = new LossParty();
            lossParty.setLossPartyType(LossPartyTypeEnum.PERSON);
            lossParty.setCustomer(this.lossParty.getCustomer());
            lossParty.setClaimTypeList(Optional.ofNullable(incident).orElse(new Incident()).getClaimTypeList());
            lossPartyList = Collections.singletonList(lossParty);
        }
        return Optional.ofNullable(lossPartyList).orElse(new ArrayList<>());
    }

}
