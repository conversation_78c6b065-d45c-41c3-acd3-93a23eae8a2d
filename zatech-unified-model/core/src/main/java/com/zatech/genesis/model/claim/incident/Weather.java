package com.zatech.genesis.model.claim.incident;

import com.zatech.gaia.resource.biz.WeatherTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * Claim incident weather information
 * weather type includes: TEMPERATURE, RAINFALL, WINDY, EARTHQUAKE
 */
@Getter
@Setter
@Schema(name = "Weather")
public class Weather {
    @Schema(description = "The type of weather")
    private WeatherTypeEnum weatherType;

    @Schema(description = "The name of area")
    private String areaName;

    @Schema(description = "The code of area")
    private String areaCode;

    @Schema(description = "The price of ticket")
    private String ticketPrice;

    @Schema(description = "The physical unit of weather")
    private String physicalUnit;

    @Schema(description = "Magnitude of earthquake")
    private String magnitude;

    @Schema(description = "Intensity of earthquake")
    private String intensity;

    @Schema(description = "value of a number")
    private String numericalValue;

    @Schema(description = "Currency, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)")
    private CurrencyEnum currency;

    @Schema(description = "Occurrence time")
    private Date occurrenceTime;

    @Schema(description = "Time zone id")
    private String zoneId;

}
