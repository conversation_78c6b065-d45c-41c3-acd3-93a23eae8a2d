package com.zatech.genesis.model.policy.customer.party;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.genesis.annotation.PolicyModel;
import com.zatech.genesis.model.UnifiedModel;
import com.zatech.genesis.model.util.CountryDeserialize;
import com.zatech.genesis.model.util.CountrySerializer;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@PolicyModel(displayName = "PartyTax", description = "PartyTax")
@Schema(name = "PartyTax")
public class PartyTax extends UnifiedModel {

    @PolicyModel(displayName = "Tax Id", description = "Tax Id")
    @Schema(description = "Tax Id")
    private Long taxId;

    @PolicyModel(displayName = "Tax No", description = "Tax No")
    @Schema(description = "Tax No")
    private String taxNo;

    @PolicyModel(displayName = "Tax Residential Country", description = "Tax Residential Country")
    @JsonDeserialize(using = CountryDeserialize.class)
    @JsonSerialize(using = CountrySerializer.class)
    @Schema(description = "Tax Residential Country, refer to the API reference. [Detail](/openx/docs/common/guide/reference/country-codes)", implementation = String.class)
    private CountryNationalityEnum taxResidentialCountry;

}
