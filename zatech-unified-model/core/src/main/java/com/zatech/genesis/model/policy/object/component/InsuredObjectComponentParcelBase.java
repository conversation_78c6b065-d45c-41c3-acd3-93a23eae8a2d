package com.zatech.genesis.model.policy.object.component;


import com.zatech.gaia.resource.components.enums.schema.ObjectComponent;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@Schema(title = "Insured object component parcel base", description = "Insured object component parcel base")
public class InsuredObjectComponentParcelBase extends InsuredObjectComponentBase {

    @Schema(description = "Returns the type of insured object component that this entity represents in the insurance system", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"PARCEL"})
    @Override
    public ObjectComponent getComponentType() {
        return super.getComponentType();
    }

    /**
     * 包裹号（trackNum）
     */
    @Schema(title = "Parcel number", description = "The unique number assigned to an insured parcel under a policy.")
    private String parcelNumber;

    /**
     * 订单号
     */
    @Schema(title = "Order ID", description = "The unique identifier assigned to an order or transaction, typically used in e-commerce or product insurance policies.")
    private String orderId;

    /**
     * 起运日期
     */
    @Schema(title = "Pick up date", description = "The date on which the insured parcel was picked up under a policy.")
    private Date pickUpDate;

    /**
     * 邮包等级 tier
     */
    @Schema(title = "Parcel tier", description = "The tier or level of the insured parcel under a policy.")
    private String parcelTier;

    /**
     * 运达日期
     */
    @Schema(title = "Delivered date", description = "The date on which the insured goods were delivered under a policy.")
    private Date deliveredDate;

    /**
     * 运单号,相当于一卡车
     */
    @Schema(title = "Transit ID", description = "The unique identifier for the transit of insured goods under a policy.")
    private String transitId;

    /**
     * 寄件人,shop_id
     */
    @Schema(title = "Sender", description = "The entity sending the insured goods under a policy.")
    private String sender;

    /**
     * 运费支付人,对应userId
     */
    @Schema(title = "Freight payer", description = "The entity responsible for paying freight charges under a policy.")
    private String freightPayer;

    /**
     * 订单价值
     */
    @Schema(title = "Order value", description = "The value of the insured order under a policy.")
    private String orderValue;

    /**
     * 承运人,third_party_logistics 运输相关第三方
     */
    @Schema(title = "Carrier", description = "The carrier responsible for transporting the insured goods under a policy.")
    private String carrier;

    /**
     * 邮包价值,insured_amount
     */
    @Schema(title = "Parcel value", description = "The value of the insured parcel under a policy.")
    private String parcelValue;

    /**
     * 收件人
     */
    @Schema(title = "Receiver", description = "The entity receiving the insured goods or services under a policy.")
    private String receiver;

    /**
     * 受托人
     */
    @Schema(title = "Depositary", description = "The entity responsible for holding or storing the insured goods under a policy.")
    private String depositary;

    /**
     * 邮包重量
     */
    @Schema(title = "Parcel weight", description = "The weight of the insured parcel under a policy.")
    private String parcelWeight;

    /**
     * 邮包体积
     */
    @Schema(title = "Parcel volume", description = "The volume of the insured parcel under a policy.")
    private String parcelVolume;

    /**
     * 货物名称及型号
     */
    @Schema(title = "Goods name and model", description = "The name and model of the insured goods under a policy.")
    private String goodsNameAndModel;

    /**
     * 起运地
     */
    @Schema(title = "Departure point", description = "The point of departure for the insured goods under a policy.")
    private String departurePoint;

    /**
     * 转运地
     */
    @Schema(title = "Transshipment point", description = "The point of transshipment for the insured goods under a policy.")
    private String transshipmentPoint;

    /**
     * 转运日期
     */
    @Schema(title = "Transshipment date", description = "The date of transshipment for the insured goods under a policy.")
    private Date transshipmentDate;

    /**
     * 目的地
     */
    @Schema(title = "Destination location", description = "The destination location of the insured goods under a policy.")
    private String destinationLocation;
}
