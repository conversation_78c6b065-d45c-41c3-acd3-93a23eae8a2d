/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.policy.object.liability;

import com.zatech.gaia.resource.components.enums.subject.SubjectTypeEnum;
import com.zatech.genesis.annotation.PolicyModel;
import com.zatech.genesis.model.policy.object.InsuredObject;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "EmployerLiability")
public class EmployerLiability extends InsuredObject {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"EMPLOYER_LIABILITY"})
    @Override
    public SubjectTypeEnum getInsuredType() {
        return super.getInsuredType();
    }

    @PolicyModel(description = "The number of total employee.")
    @Schema(description = "The number of total employee.")
    private Integer totalNumOfEmployee;

    @PolicyModel(description = "Is named or named basis.")
    @Schema(description = "Is named or named basis.")
    private Boolean unnamedBasisOrNamedBasis;

    @Schema(description = "Additional notes or comments regarding the employer's legal responsibility for injuries or illnesses suffered by employees during their employment. This field can be used to provide further details, clarifications, or specific conditions related to employer liability within the insurance context.")
    private String remarksOnEmployerLiability;

    @PolicyModel(description = "The employee information list.")
    private final List<EmployeeInfoBase> employeeInfoList = new ArrayList<>();
}
