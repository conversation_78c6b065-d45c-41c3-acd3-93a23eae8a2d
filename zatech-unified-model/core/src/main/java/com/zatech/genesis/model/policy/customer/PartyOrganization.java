package com.zatech.genesis.model.policy.customer;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.customer.PolicyCustomerEnum;
import com.zatech.gaia.resource.components.enums.schema.OrganizationIDTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.OrganizationTypeEnum;
import com.zatech.gaia.resource.graphene.common.CountryNationalityEnum;
import com.zatech.gaia.resource.graphene.customer.InvoiceTypeEnum;
import com.zatech.gaia.resource.graphene.customer.ReceiveInvoiceMethodEnum;
import com.zatech.genesis.annotation.IssuanceModel;
import com.zatech.genesis.annotation.PolicyModel;
import com.zatech.genesis.model.policy.customer.party.BaseParty;
import com.zatech.genesis.model.util.CountryDeserialize;
import com.zatech.genesis.model.util.CountrySerializer;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

import static com.zatech.genesis.enums.ApiTrait.FULL;

@Getter
@Setter
@PolicyModel(displayName = "PartyCompany", description = "Party Company")
@Schema(name = "PartyCompany")
@IssuanceModel
public class PartyOrganization extends BaseParty {

    /**
     * company id
     */
    @PolicyModel(displayName = "Company Id", description = "Company Id")
    @Schema(description = "Company Id")
    private Long companyId;

    /**
     * code
     */
    @PolicyModel(displayName = "Code", description = "Company code")
    @Schema(description = "Company code", example = "COMP123", maxLength = 128)
    private String code;

    /**
     * the type of ID
     */
    @PolicyModel(displayName = "Id Type", description = "The type of ID")
    @Schema(description = "The type of ID", example = "BUSINESS_LICENSE_NUMBER")
    @IssuanceModel(apiTrait = FULL)
    private OrganizationIDTypeEnum idType;

    /**
     * businessLicenseNumber same as organization id no
     */
    @PolicyModel(displayName = "Business License Number", description = "Business LicenseNumber same as organization id no")
    @Schema(description = "Business LicenseNumber same as organization id no", example = "BLN123456", maxLength = 128)
    @IssuanceModel(apiTrait = FULL)
    private String businessLicenseNumber;

    /**
     * companyName
     */
    @PolicyModel(displayName = "Company Name", description = "Company Name")
    @Schema(description = "Company Name", example = "Company Ltd.", maxLength = 256)
    @IssuanceModel(apiTrait = FULL)
    private String companyName;

    /**
     * companyName2
     */
    @PolicyModel(displayName = "Company Name2", description = "Company Name2")
    @Schema(description = "Company Name2", example = "Company Ltd. 2", maxLength = 256)
    private String companyName2;

    /**
     * industrialClassification
     */
    @PolicyModel(displayName = "Industrial Classification", description = "Industrial Classification")
    @Schema(description = "Industrial Classification", example = "Software", maxLength = 64)
    private String industrialClassification;

    /**
     * 保单角色:1-投保人,2-被保人,3-受益人,4-付款人
     */
    @PolicyModel(displayName = "Role Type", description = "Party Company")
    @Schema(description = "Party Company", example = "HOLDER")
    private PolicyCustomerEnum roleType;

    /**
     * 邮箱
     */
    @PolicyModel(displayName = "Email", description = "Company email")
    @Schema(description = "Company email", example = "<EMAIL>", maxLength = 128, format = "email")
    @IssuanceModel(apiTrait = FULL)
    private String email;

    /**
     * cifNumber
     */
    @PolicyModel(displayName = "Cif Number", description = "Cif Number")
    @Schema(description = "Cif Number", example = "CIF123456", maxLength = 128)
    private String cifNumber;

    /**
     * user Input Organization Type
     */
    @PolicyModel(displayName = "User Input Organization Type", description = "User Input Organization Type")
    @Schema(description = "User Input Organization Type", example = "IT", maxLength = 128)
    private String userInputOrganizationType;

    /**
     * registered Capital
     */
    @PolicyModel(displayName = "Registered Capital", description = "Registered Capital")
    @Schema(description = "Registered Capital", example = "1000000", format = "number")
    private Long registeredCapital;

    /**
     * registered Capital Currency
     */
    @PolicyModel(displayName = "Registered Capital Currency", description = "Registered Capital Currency")
    @Schema(description = "Registered Capital Currency, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)", example = "USD", maxLength = 8)
    private CurrencyEnum registeredCapitalCurrency;

    /**
     * employees Number
     */
    @PolicyModel(displayName = "Number Of Employees", description = "Employees Number")
    @Schema(description = "Employees Number", example = "29", format = "Integer")
    private Long numberOfEmployees;

    /**
     * legal Representative Id Type
     */
    @PolicyModel(displayName = "Legal Representative Id Type", description = "Legal Representative Id Type")
    @Schema(description = "Legal Representative Id Type, refer to the API reference. [Detail](/openx/docs/common/guide/reference/certificate-type)", example = "PASSPORT", maxLength = 10)
    private CertiTypeEnum legalRepresentativeIdType;

    /**
     * legal Representative Id No
     */
    @PolicyModel(displayName = "Legal Representative Id No", description = "Legal Representative Id No")
    @Schema(description = "Legal Representative Id No", example = "LRID123456", maxLength = 256)
    private String legalRepresentativeIdNo;

    /**
     * legal Representative Name
     */
    @PolicyModel(displayName = "Legal Representative Name", description = "Company legal Representative Name")
    @Schema(description = "Company legal Representative Name", example = "John Doe", maxLength = 256)
    private String legalRepresentativeName;

    /**
     * position
     */
    @PolicyModel(displayName = "Position", description = "Company position")
    @Schema(description = "Company position", example = "CEO", maxLength = 256)
    private String position;

    /**
     * taxpayer Identification Number
     */
    @PolicyModel(displayName = "Tax Payer Identification Number", description = "Tax payer Identification Number")
    @Schema(description = "Tax payer Identification Number", example = "TIN123456", maxLength = 256)
    private String taxpayerIdentificationNumber;

    /**
     * taxpayer Name
     */
    @PolicyModel(displayName = "Tax Payer Name", description = "Company taxpayer Name")
    @Schema(description = "Company taxpayer Name", example = "Company Taxpayer", maxLength = 256)
    private String taxpayerName;

    /**
     * registered Phone
     */
    @PolicyModel(displayName = "Registered Phone", description = "Company registeredPhone")
    @Schema(description = "Company registeredPhone", example = "******-123456", maxLength = 16)
    private String registeredPhone;

    /**
     * registration Date
     */
    @PolicyModel(displayName = "Registered Date", description = "Company registered Date")
    @Schema(description = "Company registered Date", example = "2023-01-01", format = "date")
    private Date registeredDate;

    /**
     * company Type
     */
    @PolicyModel(displayName = "Organization Type", description = "Company organizationType")
    @Schema(description = "Company organizationType", example = "ENTERPRISE", maxLength = 128)
    private OrganizationTypeEnum organizationType;

    /**
     * company Size
     */
    @PolicyModel(displayName = "Organization Size", description = "Company organization Size")
    @Schema(description = "Company organization Size", example = "Large", maxLength = 128)
    @IssuanceModel(apiTrait = FULL)
    private String organizationSize;

    /**
     * country
     */
    @PolicyModel(displayName = "Country", description = "Company country")
    @JsonDeserialize(using = CountryDeserialize.class)
    @JsonSerialize(using = CountrySerializer.class)
    @Schema(description = "Company country, refer to the API reference. [Detail](/openx/docs/common/guide/reference/country-codes)", example = "AF", implementation = String.class)
    private CountryNationalityEnum country;

    /**
     * industry Code
     */
    @PolicyModel(displayName = "Industry", description = "Industry Code")
    @Schema(description = "Industry Code", example = "Technology", maxLength = 64)
    private String industry;

    /**
     * certificate Expiry Date
     */
    @PolicyModel(displayName = "Certificate Expiry Date", description = "Certificate Expiry Date")
    @Schema(description = "Certificate Expiry Date", example = "2025-12-31", format = "date")
    private Date certificateExpiryDate;

    /**
     * organization Abbreviation Name
     */
    @PolicyModel(displayName = "Organization Abbreviation Name", description = "Organization Abbreviation Name")
    @Schema(description = "Organization Abbreviation Name", example = "CompLtd", maxLength = 128)
    private String organizationAbbreviationName;

    /**
     * registered Phone Country Code
     */
    @PolicyModel(displayName = "Registered Phone Country Code", description = "Registered Phone Country Code")
    @Schema(description = "Registered Phone Country Code", example = "+1", maxLength = 8)
    private String registeredPhoneCountryCode;

    /**
     * registered Address
     */
    @PolicyModel(displayName = "Registered Address", description = "Registered Address")
    @Schema(description = "Registered Address", example = "1234 Elm Street", maxLength = 256)
    private String registeredAddress;

    /**
     * Branch code
     */
    @PolicyModel(displayName = "Branch Code", description = "Branch code")
    @Schema(description = "Branch code", example = "BR123", maxLength = 128)
    private String branchCode;

    /**
     * preStored
     */
    @PolicyModel(displayName = "Is Pre Stored", description = "Whether preStored")
    @Schema(description = "Whether preStored", example = "false")
    private Boolean isPreStored;

    /**
     * Type of business
     */
    @PolicyModel(displayName = "Type Of Business", description = "Party Company")
    @Schema(description = "Party Company", example = "Software Development", maxLength = 128)
    @IssuanceModel(apiTrait = FULL)
    private String typeOfBusiness;

    /**
     * "Receive Invoice Method"
     */
    @PolicyModel(displayName = "Receive Invoice Method", description = "Receive Invoice Method")
    @Schema(description = "Receive Invoice Method", example = "EMAIL")
    @IssuanceModel(apiTrait = FULL)
    private ReceiveInvoiceMethodEnum receiveInvoiceMethod;

    /**
     * Invoice Type
     */
    @PolicyModel(displayName = "Invoice Type", description = "Invoice Type")
    @Schema(description = "Invoice Type", example = "INDIVIDUAL_INVOICE")
    @IssuanceModel(apiTrait = FULL)
    private InvoiceTypeEnum invoiceType;


}
