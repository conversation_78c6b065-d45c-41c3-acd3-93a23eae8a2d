package com.zatech.genesis.model.claim.incident;

import com.zatech.gaia.resource.claim.AccommodationOccurTypeEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * Claim incident accommodation information
 * accommodation type includes: CHANGE_OR_CANCEL, ACCOMMODATION_REIMBURSEMENT
 */
@Getter
@Setter
@Schema(name = "Accommodation")
public class Accommodation {
    @Schema(description = "Accommodation type eg. Accommodation Change or Cancel/Accommodation Reimbursement")
    private AccommodationOccurTypeEnum occurrenceType;

    @Schema(description = "Hotel name of accommodation")
    private String accommodationHotel;

    @Schema(description = "Room class of accommodation")
    private String roomClass;

    @Schema(description = "TimeZone of Start Date / End Date in accommodation")
    private String timeZoneId;

    @Schema(description = "Start date of accommodation,in change or cancel scenario : the start date of former " +
            "accommodation,in reimbursement scenario : the start date of extra accommodation")
    private Date startDate;

    @Schema(description = "End date of accommodation,in change or cancel scenario : the end date of former " +
            "accommodation,in reimbursement scenario : the end date of extra accommodation")
    private Date endDate;

    @Schema(description = "Accommodation days,generated automatically by (endDate - startDate) in frontend,can not be modified by user")
    private int accommodationDays;

    @Schema(description = "Accommodation amount per night")
    private String perAmount;

    @Schema(description = "Amount of accommodation fee,generated automatically by (accommodationDays * perAmount),can be modified by user")
    private String amount;

    @Schema(description = "The currency of amount and perAmount, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)")
    private CurrencyEnum currency;

    @Schema(description = "Assessment amount,automatically equals to amount, but can be modified by user")
    private String assessmentAmount;

    @Schema(description = "Assessment amount currency,automatically equals to amountCurrency, but can be modified by " +
        "user, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)")
    private CurrencyEnum assessmentCurrency;

    @Schema(description = "The timezone of booking date")
    private String bookingDateZoneId;

    @Schema(description = "Booking date")
    private Date bookingDate;

    @Schema(description = "The timezone of change date")
    private String changeDateZoneId;

    @Schema(description = "Change date")
    private Date changeDate;

    @Schema(description = "Change reason")
    private String changeReason;

}
