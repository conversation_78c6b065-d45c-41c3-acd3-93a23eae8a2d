package com.zatech.genesis.model.policy;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.CanIssueEventEnum;
import com.zatech.gaia.resource.components.enums.policy.ChangeSourceEnum;
import com.zatech.gaia.resource.components.enums.policy.DecisionTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyProductStatusChangeCauseEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyProductStatusEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyScenarioEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyStatusEnum;
import com.zatech.gaia.resource.components.enums.product.CoveragePeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PayPeriodTypeEnum;
import com.zatech.gaia.resource.components.enums.product.ProductTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.SaPremiumCalculationMethodEnum;
import com.zatech.gaia.resource.graphene.policy.ApplicationTypeEnum;
import com.zatech.gaia.resource.graphene.policy.PolicyDeliveryMethodEnum;
import com.zatech.gaia.resource.graphene.policy.SignOffStatusEnum;
import com.zatech.gaia.resource.graphene.renewal.RenewalSourceEnum;
import com.zatech.genesis.annotation.CustomizedModel;
import com.zatech.genesis.annotation.IssuanceModel;
import com.zatech.genesis.annotation.PolicyModel;
import com.zatech.genesis.annotation.ProductLine;
import com.zatech.genesis.annotation.RootModel;
import com.zatech.genesis.enums.ApiTrait;
import com.zatech.genesis.enums.BusinessLine;
import com.zatech.genesis.model.UnifiedModel;
import com.zatech.genesis.model.policy.base.Attachment;
import com.zatech.genesis.model.policy.base.ClaimHistory;
import com.zatech.genesis.model.policy.base.ClaimStack;
import com.zatech.genesis.model.policy.base.ClauseFile;
import com.zatech.genesis.model.policy.base.Comment;
import com.zatech.genesis.model.policy.base.CoveragePlan;
import com.zatech.genesis.model.policy.base.Currency;
import com.zatech.genesis.model.policy.base.IssuanceCustomerOption;
import com.zatech.genesis.model.policy.base.PaymentPlan;
import com.zatech.genesis.model.policy.base.PolicyRelationDetail;
import com.zatech.genesis.model.policy.base.PolicyVersion;
import com.zatech.genesis.model.policy.base.RiskInformation;
import com.zatech.genesis.model.policy.base.SalesChannel;
import com.zatech.genesis.model.policy.base.SpecialAgreement;
import com.zatech.genesis.model.policy.base.UnnamedInsured;
import com.zatech.genesis.model.policy.base.UserAuthInfo;
import com.zatech.genesis.model.policy.base.checkresult.ComplianceResult;
import com.zatech.genesis.model.policy.base.checkresult.UnderwritingResult;
import com.zatech.genesis.model.policy.base.checkresult.VerificationResult;
import com.zatech.genesis.model.policy.base.claim.ClaimStackConfig;
import com.zatech.genesis.model.policy.base.claim.ClaimStackTemplate;
import com.zatech.genesis.model.policy.base.fee.PremiumDetail;
import com.zatech.genesis.model.policy.campaign.CampaignInfo;
import com.zatech.genesis.model.policy.customer.Assignee;
import com.zatech.genesis.model.policy.customer.Beneficiary;
import com.zatech.genesis.model.policy.customer.Insured;
import com.zatech.genesis.model.policy.customer.Payee;
import com.zatech.genesis.model.policy.customer.Payer;
import com.zatech.genesis.model.policy.customer.PolicyHolder;
import com.zatech.genesis.model.policy.customer.SecondaryInsured;
import com.zatech.genesis.model.policy.customer.Trustee;
import com.zatech.genesis.model.policy.event.PolicyByEvent;
import com.zatech.genesis.model.policy.life.LoadingConclusion;
import com.zatech.genesis.model.policy.object.InsuredObject;
import com.zatech.genesis.model.policy.product.Product;
import com.zatech.genesis.model.policy.questionnaire.Questionnaire;
import com.zatech.genesis.model.policy.relation.CoverageInsuredRelation;
import com.zatech.genesis.model.policy.relation.IssuanceRelation;
import com.zatech.genesis.model.policy.relation.RelationPolicy;

import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections4.CollectionUtils;

import static com.zatech.genesis.enums.ApiTrait.FULL;
import static com.zatech.genesis.enums.ApiTrait.KERNEL;
import static com.zatech.genesis.enums.ApiTrait.OPEN;
import static com.zatech.genesis.enums.BusinessLine.ALL;
import static com.zatech.genesis.enums.BusinessLine.HOME_PROPERTY;
import static com.zatech.genesis.enums.BusinessLine.INVESTMENT_LINKED;
import static com.zatech.genesis.enums.BusinessLine.MOTOR;
import static com.zatech.genesis.enums.BusinessLine.TERM_LIFE;

@Getter
@Setter
@RootModel
@PolicyModel(displayName = "Policy", description = "A legally binding contract between an insurance company (insurer) and an individual or entity (policyholder)")
@Schema(name = "Policy",
    description = "A comprehensive insurance contract that outlines the terms, conditions, coverage details, and obligations between the insurance company and the policyholder. This document defines what is covered, policy limits, premiums, and claim procedures.")
public class Policy extends UnifiedModel {

    private static final EnumMap<PolicyProductStatusEnum, PolicyStatusEnum> PRODUCT_STATUS_TO_POLICY_STATUS_MAP = new EnumMap<>(
        PolicyProductStatusEnum.class);

    static {
        PRODUCT_STATUS_TO_POLICY_STATUS_MAP.put(PolicyProductStatusEnum.EFFECTIVE, PolicyStatusEnum.POLICY_EFFECT);
        PRODUCT_STATUS_TO_POLICY_STATUS_MAP.put(PolicyProductStatusEnum.TERMINATION, PolicyStatusEnum.TERMINATION);
        PRODUCT_STATUS_TO_POLICY_STATUS_MAP.put(PolicyProductStatusEnum.LAPSED, PolicyStatusEnum.LAPSED);
    }

    @PolicyModel(displayName = "Policy Id", description = "A unique identifier assigned to an insurance policy.")
    @Schema(description = "A unique system identifier for this insurance policy, automatically generated when the policy is created in our system.")
    private Long policyId;

    /**
     * policy no
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.policyNo}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Policy No", description = "Business policy No")
    @Schema(description = "The official policy number that customers will use to reference their insurance policy. This number is printed on policy documents and used for customer service inquiries.")
    @ProductLine(value = {ALL}, apiTrait = FULL)
    private String policyNo;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issueWithoutPayment}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Issue Without Payment", description = "Issue without payment")
    @Schema(description = "Indicates whether this policy can be issued without requiring immediate payment. When true, the policy becomes active even if premium payment is pending.")
    @ProductLine(ALL)
    private Boolean issueWithoutPayment;

    @IssuanceModel(apiTrait = FULL)
    @PolicyModel(displayName = "Extra Premium Due Days", description = "Extra premium due days")
    @Schema(description = "The grace period (in days) allowed for customers to pay any additional premium charges before the policy faces penalty or lapse. This typically applies when policy changes result in increased premiums.")
    @ProductLine(value = {BusinessLine.EXTENDED_WARRANTY}, apiTrait = FULL)
    @ProductLine(value = {MOTOR}, apiTrait = OPEN)
    private Integer extraPremiumDueDays;

    /**
     * issuance id
     */
    @PolicyModel(displayName = "Issuance Id", description = "A unique identifier associated with the issuance of an insurance policy")
    @Schema(description = "A unique system identifier that tracks the policy issuance process. This ID links the policy to its underwriting, approval, and issuance workflow.")
    @ProductLine(ALL)
    private Long issuanceId;

    @Schema(title = "Renewal source")
    private RenewalSourceEnum renewalSource;

    /**
     * proposal No = issuanceNo
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceNo}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Proposal No", description = "Proposal no")
    @Schema(description = "The application or proposal reference number used during the quote and underwriting process. This number connects the final policy to its original application.")
    @ProductLine(ALL)
    private String proposalNo;

    /**
     * goods id
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.goodsId}
     */
    @PolicyModel(displayName = "Goods Id", description = "Goods id")
    @Schema(description = "Internal system identifier for the insurance product being purchased. This links the policy to the specific product configuration and rules.")
    @ProductLine(ALL)
    private Long goodsId;

    /**
     * 是否法定受益人 默认：否
     */
    @PolicyModel(displayName = "Is Legal Beneficiary", description = "Is legal beneficiary")
    @Schema(description = "Indicates whether beneficiaries are determined by law (true) or specifically named by the policyholder (false). Legal beneficiaries typically follow statutory succession rules.")
    @ProductLine(ALL)
    private Boolean isLegalBeneficiary;

    /**
     * goods code
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.goodsCode}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Goods Code", description = "The code of goods that the customer wants to get the quote")
    @Schema(description = "The product code identifying the specific insurance product the customer wants to purchase. This code determines available coverages, pricing rules, and policy features.", maxLength = 64)
    @ProductLine(ALL)
    private String goodsCode;

    /**
     * goods version
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.goodsVersion}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Goods Version", description = "Goods version")
    @Schema(description = "The version of the insurance product being purchased. Different versions may have varying terms, conditions, or features while maintaining the same base product code.")
    @ProductLine(ALL)
    private String goodsVersion;

    /**
     * goods plan id
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.planId}
     */
    @PolicyModel(displayName = "Goods Plan Id", description = "Goods Plan ID")
    @Schema(description = "Internal identifier for the specific plan or package within the selected insurance product. Plans define coverage levels, deductibles, and premium structures.")
    @ProductLine(ALL)
    private Long goodsPlanId;

    /**
     * goods plan code
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.planCode}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Goods Plan Code", description = "The code of goods plan for sale.")
    @Schema(description = "`conditional required`\r- The plan code that identifies the specific coverage level and features within the selected product. Either planCode or packageCode must be provided to define the customer's chosen coverage option.", maxLength = 64)
    @ProductLine(ALL)
    private String planCode;

    /**
     * package id
     */
    @PolicyModel(displayName = "Package Id", description = "A unique identifier associated with the insurance package/plan of the policy.")
    @Schema(description = "System identifier for the insurance package or plan selected by the customer. Packages are pre-configured combinations of coverages designed for specific customer needs.")
    @ProductLine(ALL)
    private Long packageId;

    /**
     * package code
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.packageCode}
     */
    @PolicyModel(displayName = "Package Code", description = "A unique code associated with the insurance package/plan of the policy.")
    @Schema(description = "`conditional required`\r- The package code identifying a bundled set of coverages and benefits. Packages offer simplified product selection by combining related coverages into convenient options. Either packageCode or planCode must be provided.", maxLength = 128)
    @ProductLine(value = {ALL}, apiTrait = FULL)
    @IssuanceModel(apiTrait = FULL)
    private String packageCode;

    @PolicyModel(displayName = "By Pass Uw Check", description = "By pass uw check")
    @Schema(description = "Indicates whether automated underwriting rules were skipped during policy issuance. This may occur for pre-approved customers, group policies, or manual underwriting scenarios.")
    @IssuanceModel()
    @ProductLine(ALL)
    private Boolean byPassUwCheck;

    /**
     * fee calculation type
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Calculation Method", description = "Fee calculation type Method")
    @Schema(description = "The method used to calculate premiums and benefits for this policy. Different calculation methods may apply percentage-based, flat-rate, or tiered pricing structures.")
    @ProductLine(ALL)
    private SaPremiumCalculationMethodEnum calculationMethod;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.coveragePeriodType}
     */
    @IssuanceModel(apiTrait = ApiTrait.FULL)
    @PolicyModel(displayName = "Coverage Period Type", description = "Protection period type, such as year, month.refer to the API reference.")
    @Schema(description = "The time unit for the coverage period (e.g., YEARFULL for annual coverage, MONTH for monthly coverage). SUIFULL indicates age-based coverage that must be maintained within specific age ranges.", example = "YEARFULL")
    @ProductLine(ALL)
    private CoveragePeriodTypeEnum coveragePeriodType;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.coveragePeriod}
     */
    @IssuanceModel(apiTrait = ApiTrait.FULL)
    @PolicyModel(displayName = "Coverage Period Value", description = "The value of the coverage period,which works together with the coverage period type.")
    @Schema(description = "The numeric value that, combined with coverage period type, defines how long the insurance coverage remains active (e.g., '1' with 'YEARFULL' means one year of coverage).",
        minimum = "0", example = "1", type = "integer")
    @ProductLine(ALL)
    private String coveragePeriod;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceProductList}
     */
    @IssuanceModel(apiTrait = ApiTrait.FULL)
    @PolicyModel(displayName = "Payment Period Type", description = "The payment period type.")
    @ProductLine(ALL)
    @Schema(description = "How the customer will pay premiums over time (e.g., SINGLE for one-time payment, ANNUAL for yearly payments). This determines the payment schedule and frequency.\n`(Use \"premiumPeriodType\" as the key when querying enumeration values via metadata interface.)`", example = "SINGLE")
    private PayPeriodTypeEnum paymentPeriodType;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceProductList}
     */
    @IssuanceModel(apiTrait = ApiTrait.FULL)
    @PolicyModel(displayName = "Payment Period Value", description = "The period value is required working together with paymentPeriodType, except in cases where the paymentPeriodType is \"SINGLE\".")
    @Schema(example = "1", minimum = "0", type = "integer",
        description = "`conditional required`\r- The number of payment periods (e.g., '5' with 'ANNUAL' means 5 yearly payments). Not required when payment type is 'SINGLE' for one-time payments.")
    @ProductLine(ALL)
    private String paymentPeriod;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceProductList}
     */
    @IssuanceModel(apiTrait = ApiTrait.FULL)
    @PolicyModel(displayName = "Pay Frequency Type", description = "When the paymentPeriodType is SINGLE, the premiumFrequencyType should also be SINGLE in terms of business requirements.")
    @Schema(description = "`conditional required`\r- How often the customer pays premiums within the payment period (e.g., MONTHLY, QUARTERLY, ANNUALLY). Must be SINGLE when payment period type is SINGLE for one-time payments.", example = "SINGLE")
    @ProductLine(ALL)
    private PayFrequencyTypeEnum premiumFrequencyType;

    /**
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.policyType}
     */
    @PolicyModel(displayName = "Policy Type", description = "Policy type")
    @Schema(description = "`conditional required`\n The business scenario or purpose of this policy (e.g., NORMAL for standard policies, RENEWAL for policy renewals). Default value is NORMAL for new policy purchases.")
    @ProductLine(ALL)
    private PolicyScenarioEnum policyType;

    @PolicyModel(displayName = "Type Of Business", description = "The type of business or industry to which the insurance policy applies, such as retail, manufacturing, or services.")
    @Schema(description = "The business category or industry type of the policyholder's company or primary occupation. This helps determine risk levels and applicable coverage terms.")
    @ProductLine(ALL)
    private String typeOfBusiness;

    @PolicyModel(displayName = "Risk Classes", description = "The classification of risks associated with the policy, such as high, medium, or low risk.")
    @Schema(description = "The risk category assigned to this policy based on underwriting assessment (e.g., Low Risk, Standard, High Risk). This classification affects premium pricing and coverage terms.")
    @ProductLine(ALL)
    private String riskClasses;

    @PolicyModel(displayName = "Industry", description = "The industry or sector to which the policyholder or insured entity belongs.")
    @Schema(description = "The specific industry sector in which the policyholder operates their business or primary profession. Used for risk assessment and regulatory compliance.")
    @ProductLine(ALL)
    private String industry;

    /**
     * order no
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.orderNo}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Order No", description = "The field indicates payment transaction number, which is used by the system to create the actual payment invoice when using the payment gateway or finance API. `(Each request requires a new order number to be passed in through API.)`")
    @Schema(maxLength = 64, example = "z_329439235243",
        description =
            "`conditional required`\r- A unique order identifier for payment processing. This number tracks the payment transaction and is used to generate invoices through payment gateways. Each API request must include a new, unique order number.")
    @ProductLine(ALL)
    private String orderNo;

    /**
     * order no
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.quotationNo}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Quotation No", description = "QuotationNo input when issue. Scenario 1：set any value " +
        "as the associated number\rScenario 2: uses proposalNo in the response body of the Quote Creation Api as a value.")
    @Schema(maxLength = 64, example = "3294302904",
        description = "The quotation reference number that links this policy to its original price quote. You can either provide any tracking number for your records, or use the proposalNo returned from the Quote Creation API to maintain full traceability.")
    @ProductLine(ALL)
    private String quotationNo;

    /**
     * application no
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Application No", description = "Application No")
    @Schema(description = "The reference number assigned to the insurance application during the submission process. This tracks the customer's request through underwriting and approval stages.")
    @ProductLine(ALL)
    private String applicationNo;

    /**
     * trade no
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.tradeNo}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Trade No", description = "The field indicates payment transaction number, which is used by the system to create the actual payment invoice when using the payment gateway or finance API. \r`(Each request requires a new trade number to be passed in through API.)`")
    @Schema(maxLength = 64,
        description = "A unique trade identifier for payment processing, used to create payment invoices through financial systems and gateways. Each API request must include a new, unique trade number to ensure proper transaction tracking.", example = "p_329439235243")
    @ProductLine(ALL)
    private String tradeNo;

    @Schema(description = "A unique reference number for specific events that affect the policy, such as claims, amendments, or status changes. This helps track policy lifecycle events and their impact.")
    private String eventNo;

    /**
     * Event policy主保单号
     */
    @Schema(description = "The standard policy number for regular business operations, used when this policy is not part of an event-driven process.")
    private String normalPolicyNo;


    /**
     * policy status
     */
    @PolicyModel(displayName = "Policy Status", description = "The current condition or state of an insurance policy. It indicates whether the policy is active, lapsed, surrendered, terminated, etc.")
    @Schema(description = "The current status of the insurance policy (e.g., Active, Lapsed, Terminated). This determines whether coverage is in effect and what actions customers can take.")
    @ProductLine(ALL)
    private PolicyStatusEnum policyStatus;

    /**
     * policy status change reason, impact by main product
     */
    @PolicyModel(displayName = "Status Change Cause", description = "Policy status change reason, impact by main product ref PolicyProductStatusChangeCauseEnum")
    @Schema(description = "The reason why the policy status changed (e.g., Non-payment, Cancellation, Expiry). This is typically determined by the main product's status change.")
    @ProductLine(ALL)
    private PolicyProductStatusChangeCauseEnum statusChangeCause;

    /**
     * policy status change date
     */
    @PolicyModel(displayName = "Status Change Date", description = "Policy status change date")
    @Schema(description = "The date when the policy status was last modified. This tracks when changes like activation, lapse, or termination occurred.")
    @ProductLine(ALL)
    private Date statusChangeDate;

    /**
     * business change source
     */
    @PolicyModel(displayName = "Change Source", description = "Business change source ref ChangeSourceEnum")
    @Schema(description = "The origin or method through which policy changes were initiated (e.g., Customer Request, System Automatic, Agent Action). Helps track change accountability.")
    @ProductLine(ALL)
    private ChangeSourceEnum changeSource;

    /**
     * renewal policy source
     */
    @PolicyModel(displayName = "Renewal Source", description = "Renewal policy method ref RenewalSourceEnum")
    @Schema(description = "How the policy renewal was processed (e.g., Automatic Renewal, Manual Renewal, Customer Initiated). This indicates the renewal workflow used.")
    @IssuanceModel()
    @ProductLine(ALL)
    private RenewalSourceEnum renewalMethod;

    @Schema(description = "Additional comments, special instructions, or notes about this policy that may be relevant for customer service or claims processing.")
    private String remark;

    /**
     * 是否启用taylor校验
     */
    @Schema(description = "Whether advanced actuarial calculations using Taylor series expansion are enabled for this policy. This affects premium and benefit calculations.")
    private Boolean enableTaylor;

    /**
     * 出单流程是否同步处理 default value: No
     */
    @Schema(description = "Whether the policy issuance process runs synchronously (waiting for all steps to complete) or asynchronously (processing in background). Default is asynchronous (No).")
    private Boolean enableFullSync;

    @Schema(description = "How the final policy documents will be delivered to the customer (e.g., Email, Postal Mail, Digital Portal, SMS). This determines the delivery channel and format.")
    private PolicyDeliveryMethodEnum policyDeliveryMethod;

    @Schema(description = "The system or channel through which this policy was created or last modified (e.g., Agent, Portal, CHANNEL_API, GRAPHENE)")
    private String systemSource;

    @PolicyModel(displayName = "Application Type", description = "Application type")
    @Schema(description = "The category or type of insurance application (e.g., New Business, Renewal, Amendment). This determines the processing workflow and validation rules.")
    @IssuanceModel()
    @ProductLine(ALL)
    private ApplicationTypeEnum applicationType;

    /**
     * policy issue date
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Issue Date", description = "Policy issue Date")
    @Schema(description = "The official date when the insurance policy was issued and became legally effective. This is the starting point for coverage and policy terms.")
    @ProductLine(ALL)
    private Date issueDate;

    /**
     * policy sign off date, policy acknowledgement data
     */
    @PolicyModel(displayName = "Sign Off Date", description = "Policy sign off date, policy acknowledgement data")
    @Schema(description = "The date when the customer officially acknowledged receipt and acceptance of the policy terms. This confirms the customer's agreement to the policy conditions.")
    @ProductLine(ALL)
    private Date signOffDate;

    @PolicyModel(displayName = "Sign Off Status", description = "Sign Off Status")
    @Schema(description = "Whether the customer has acknowledged and accepted the policy (e.g., Signed, Pending, Not Required). This tracks customer confirmation status.")
    @ProductLine(ALL)
    private SignOffStatusEnum signOffStatus;

    /**
     * e policy dispatch date
     */
    @PolicyModel(displayName = "E Policy Dispatch Date", description = "E policy dispatch date")
    @Schema(description = "The date when electronic policy documents were sent to the customer via email or digital delivery. This tracks electronic document distribution.")
    @ProductLine(ALL)
    private Date epolicyDispatchDate;

    /**
     * Hard copy  policy dispatch date
     */
    @PolicyModel(displayName = "Policy Dispatch Date", description = "Hard copy policy dispatch date")
    @Schema(description = "The date when physical policy documents were mailed or delivered to the customer. This tracks physical document distribution for customers requiring hard copies.")
    @ProductLine(ALL)
    @CustomizedModel()
    private Date policyDispatchDate;

    /**
     * e policy dispatch date
     */
    @PolicyModel(displayName = "Proposal Creation Date", description = "Proposal creation date")
    @Schema(description = "The date when the initial insurance proposal or application was created in the system. This marks the beginning of the policy request process.")
    @ProductLine(ALL)
    @CustomizedModel()
    private Date proposalCreationDate;

    /**
     * proposal application date (insure date)
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Proposal Application Date", description = "Proposal application date")
    @Schema(description = "The date when the customer submitted their insurance application or when coverage was requested to begin. Also known as the desired effective date.")
    @ProductLine(ALL)
    @CustomizedModel()
    private Date proposalApplicationDate;

    /**
     * application date
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.insureDate}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Application Date", description = "The date that the application is submited.")
    @Schema(description = "`conditional required`\r- The date when the customer formally submitted their insurance application. This date is used for calculating coverage periods and premium rates.")
    @ProductLine(ALL)
    @CustomizedModel()
    private Date applicationDate;

    /**
     * proposal status
     */
    @PolicyModel(displayName = "Proposal Status", description = "Proposal status")
    @Schema(description = "The current processing status of the insurance proposal (e.g., Submitted, Under Review, Approved, Declined). This tracks progress through the underwriting process.")
    @ProductLine(ALL)
    private IssuanceStatusEnum proposalStatus;

    /**
     * Issuance customer option
     */
    @PolicyModel(displayName = "Issuance Customer Option", description = "Issuance customer option")
    @Schema(description = "Special customer preferences or options selected during policy issuance, such as delivery method, payment preferences, or service options.")
    @IssuanceModel
    private IssuanceCustomerOption issuanceCustomerOption;

    /**
     * agreement no
     */
    @PolicyModel(displayName = "Agreement No", description = "Agreement no")
    @Schema(description = "The reference number for any special agreements, riders, or endorsements attached to this policy. This links to additional terms or coverage modifications.")
    @IssuanceModel
    private String agreementNo;

    /**
     * original policy no ( free policy refer to the original one)
     */
    @PolicyModel(displayName = "Original Policy No", description = "Original policy no ( free policy refer to the original one)")
    @Schema(description = "For replacement or free policies, this contains the policy number of the original policy being replaced or referenced. Maintains linkage between related policies.")
    private String originalPolicyNo;

    /**
     * host transaction no
     */
    @PolicyModel(displayName = "Host Transaction No", description = "Host transaction no")
    @Schema(description = "The transaction identifier from the main insurance system or host platform. Used for system integration and transaction tracking across multiple platforms.")
    @ProductLine(ALL)
    private String hostTransactionNo;

    /**
     * third party transaction no
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.bizApplyNo}
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.tradeNo}
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.thirdPartyTransactionNo}
     */
    @PolicyModel(displayName = "Third Party Transaction No", description = "Third-party transaction number, we will use this field for idempotent processing of the API.\\r`(Each request requires a new third party transaction number to be passed in through API.)`")
    @Schema(description = "A unique transaction identifier provided by external systems or partners. This ensures idempotent API processing and prevents duplicate policy creation. Each API call must include a new, unique identifier.",
        maxLength = 64, example = "t_32943923243")
    @IssuanceModel()
    @ProductLine(ALL)
    private String thirdPartyTransactionNo;

    /**
     * currency information
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.salesCurrency 废弃}
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.multiCurrency}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Sales Currency", description = "The currency of the premium. This is a three character ISO 4217 currency code. For example, USD for US Dollars.")
    @Schema(description = "The currency used for all monetary amounts in this policy, including premiums, claims, and benefits. Uses standard ISO 4217 currency codes (e.g., USD, EUR, GBP).")
    @ProductLine(ALL)
    private Currency currency;

    /**
     * is renewable flag
     */
    @PolicyModel(displayName = "Renewal Policy", description = "Is renewable flag")
    @Schema(description = "Indicates whether this policy can be renewed when it expires. True means the customer is eligible for renewal, false means the policy will terminate at expiry.")
    @IssuanceModel()
    @ProductLine(ALL)
    private Boolean isRenewable;

    /**
     * timezone
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.zoneId}
     */
    @PolicyModel(displayName = "Zone Id", description = "By default, the system uses the configured time zone of tenant.")
    @Schema(maxLength = 64, example = "Europe/Zagreb",
        description = "`conditional required`\r- The time zone for interpreting all dates and times in this policy. If not specified, the system uses the tenant's default time zone configuration.")
    @IssuanceModel()
    @ProductLine(ALL)
    private String zoneId;

    /**
     * policy holder
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceHolder}
     */
    @IssuanceModel(apiTrait = KERNEL)
    @PolicyModel(displayName = "Policy Holder", description = "The policyHolder is an object that typically holds information about an individual who has a particular insurance policy. ")
    @Schema(description = "The person or organization that owns this insurance policy and is responsible for premium payments. The policyholder has the right to make changes to the policy and file claims.")
    @ProductLine(value = ALL, apiTrait = FULL)
    private PolicyHolder policyHolder;

    /**
     * policy insurant list
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceInsurantList}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Insured List", description = "Insurant of issue")
    @Schema(description = "List of individuals or entities who are covered under this insurance policy. These are the people or assets that will receive benefits in case of a covered event.")
    @ProductLine(ALL)
    private final List<Insured> insuredList = new ArrayList<>();

    /**
     * policy assignee
     */
    @Schema(description = "The person or entity designated to receive policy benefits or exercise certain policy rights on behalf of the beneficiary, typically used in trust arrangements or when beneficiaries are minors.")
    private Assignee assignee;

    /**
     * policy beneficiary list
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceInsurantList}
     */
    @PolicyModel(displayName = "Policy Beneficiary", description = "Policy Beneficiary")
    @Schema(description = "Individuals or organizations designated to receive benefits if the insured person dies or the policy matures. Beneficiaries are named by the policyholder.")
    private final List<Beneficiary> beneficiaryList = new ArrayList<>();

    /**
     * policy level fee info, only used for stamp duty now
     */
    @PolicyModel(displayName = "Premium Detail List", description = "Policy level fee info, only used for stamp duty now")
    @Schema(description = "Detailed breakdown of fees and charges applied at the policy level, such as government taxes, stamp duties, and administrative fees. Currently used primarily for stamp duty calculations.")
    private final List<PremiumDetail> premiumDetailList = new ArrayList<>();

    /**
     * policy by event list,
     * number size limitation:
     * The max size of event list is 100
     */
    private final List<PolicyByEvent> policyByEventList = new ArrayList<>();

    /**
     * policy coverage plan list
     * if there are mulitple plans for the goods, the value is not empty,
     * default is empty for single coverage plan
     */
    @PolicyModel(displayName = "Product List", description = "A collection of coverages/benefits on the policy for eb or group travel.")
    @Schema(description = "`conditional required`\n The insurance products and coverage options included in this policy. Each product defines specific benefits, limits, and terms that apply to different types of protection.")
    private final List<CoveragePlan> planList = new ArrayList<>();

    /**
     * product list
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceProductList}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Product List", description = "A collection of coverages/benefits on the policy.")
    @Schema(description = "Comprehensive list of all insurance products and coverages purchased under this policy, including main coverage and optional add-ons or riders.")
    @ProductLine(value = {ALL})
    private final List<Product> productList = new ArrayList<>();

    /**
     * policy version
     */
    @PolicyModel(displayName = "Version", description = "Policy version")
    @Schema(description = "Version control information for this policy, tracking changes and amendments made over time. Helps maintain audit trail of policy modifications.")
    private PolicyVersion version;

    /**
     * product, liability relationship with policy insurant or insured object
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.coverageRelationList}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Coverage Insured Relations", description = "Product, liability relationship with policy insurant or insured object")
    @Schema(description = "Defines which insurance products and coverage types apply to specific insured persons or objects. This mapping ensures proper coverage assignment and claims processing.")
    @ProductLine(value = ALL, apiTrait = KERNEL)
    private final List<CoverageInsuredRelation> insuredRelationList = new ArrayList<>();

    /**
     * insured object list
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.IssuanceInsuredObjectList}
     */
    @PolicyModel(displayName = "Insured Object List", description = "Insured object list")
    @Schema(description = "Physical items, properties, or assets covered under this policy such as vehicles, buildings, equipment, or personal belongings. Each object has specific coverage terms and limits.",
        extensions = {@Extension(properties = {@ExtensionProperty(name = "x-one-of-id-property", value = "insuredType")})})
    @IssuanceModel(apiTrait = FULL)
    @ProductLine(value = {ALL}, apiTrait = FULL)
    private final List<InsuredObject> insuredObjectList = new ArrayList<>();

    /**
     * policy payer
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuancePayerList}
     */
    @PolicyModel(displayName = "Payer List", description = "Policy Payer List")
    @Schema(description = "Individuals or organizations responsible for paying premiums for this policy. Payers may be different from policyholders in group insurance or sponsored policies.")
    @IssuanceModel
    private final List<Payer> payerList = new ArrayList<>();

    /**
     * policy payee
     */
    @PolicyModel(displayName = "Policy Payee List", description = "Policy payee list")
    @Schema(description = "Individuals or organizations designated to receive claim payments or benefits from this policy. Payees may be different from beneficiaries in certain policy types.")
    private final List<Payee> payeeList = new ArrayList<>();

    /**
     * goods name
     */
    @Schema(description = "The commercial name or marketing title of the insurance product being purchased. This is the user-friendly name customers see.")
    private String goodsName;

    /**
     * group policy no
     */
    @Schema(description = "For group insurance policies, this is the master policy number that covers multiple individuals under a single contract (e.g., employee group coverage).")
    private String groupPolicyNo;

    /**
     * relation policy
     */
    private final List<RelationPolicy> relationPolicyList = new ArrayList<>();

    /**
     * policy effective date
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.effectiveDate}
     */
    @IssuanceModel(apiTrait = FULL)
    @PolicyModel(displayName = "Effective Date", description = "The date when the quotation is effective. Refer to the API reference. ")
    @Schema(description = "`conditional required`\r- The date when insurance coverage begins. This is when the policy becomes active and claims can be filed.")
    @ProductLine(value = ALL, apiTrait = FULL)
    @CustomizedModel(timeZoneInsensitive = true)
    private Date effectiveDate;

    /**
     * policy expiry date
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.expiryDate}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Expiry Date", description = "The date that the policy expire date.")
    @Schema(description = "`conditional required`\r- The date when insurance coverage ends. After this date, no new claims can be filed unless the policy is renewed.")
    @CustomizedModel(timeZoneInsensitive = true)
    @ProductLine(value = ALL)
    private Date expiryDate;

    /**
     * tax exemption flag
     */
    @IssuanceModel
    @PolicyModel(displayName = "Tax Exemption", description = "Tax exemption flag")
    @Schema(description = "Indicates whether this policy qualifies for tax exemptions or special tax treatment. This affects premium calculations and tax reporting.")
    private Boolean taxExemption;

    /**
     * Policy campaign information
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.campaignCodeList}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Campaign List", description = "Campaign list that applied to the policy")
    @Schema(description = "Marketing campaigns, promotions, or discount programs applied to this policy. These may affect pricing, benefits, or terms.")
    @ProductLine(ALL)
    private final List<CampaignInfo> campaignList = new ArrayList<>();

    /**
     * loading conclusion for policy
     */
    @PolicyModel(displayName = "Loading Conclusions", description = "Loading Conclusions")
    @Schema(description = "Additional premium loadings or adjustments applied to the base rate due to increased risk factors, medical conditions, or lifestyle factors.")
    @IssuanceModel()
    @ProductLine(TERM_LIFE)
    private final List<LoadingConclusion> loadingConclusionList = new ArrayList<>();

    /**
     * auto renewal flag
     */
    @PolicyModel(displayName = "Auto Renewal", description = "Auto renewal flag")
    @Schema(description = "Whether the policy will automatically renew when it expires. True means the policy continues with updated terms, false means it will terminate unless manually renewed.")
    @IssuanceModel
    @ProductLine(ALL)
    private Boolean autoRenewal;

    /**
     * renewal times
     */
    @PolicyModel(displayName = "Renewal Times", description = "Renewal times")
    @Schema(description = "The number of times this policy has been renewed since its original issuance. Tracks the policy's renewal history and longevity.")
    @IssuanceModel
    @ProductLine(ALL)
    private Integer renewalTimes;

    /**
     * policy paid premium
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.premium}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Paid Premium", description = "The actual premium amount paid by the user.")
    @Schema(description = "The actual amount the customer has paid for this policy, which may differ from the calculated premium due to payments, refunds, or adjustments.", example = "500.55", type = "number", minimum = "0", hidden = true)
    @ProductLine(value = ALL, apiTrait = FULL)
    private String paidPremium;

    @PolicyModel(displayName = "Premium", description = "The payment that a policyholder makes to an insurance company to obtain and maintain an active insurance policy for a specified coverage period. It is essentially the price of insurance protection")
    @Schema(description = "The total premium amount calculated for this policy based on coverage selections, risk assessment, and applicable discounts or loadings. This represents the price of insurance protection before any payments or adjustments.")
    @ProductLine(value = ALL, apiTrait = FULL)
    private String premium;

    /**
     * questionnaire information
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.customerUwList}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Questionnaire", description = "The questionnaire associated with a policy or product.")
    @Schema(description = "Customer responses to underwriting questions used to assess risk, determine eligibility, and calculate appropriate premiums. This includes health, lifestyle, and risk-related information.")
    @ProductLine(INVESTMENT_LINKED)
    private Questionnaire questionnaire;

    /**
     * Whether it is renewal，Yes - Renew No - NB
     */
    @PolicyModel(displayName = "Is Renewal Policy", description = "Whether it is renewal，Yes - Renew No - NB")
    @Schema(description = "Indicates if this is a renewal of an existing policy (Yes) or a new business policy (No). This affects pricing, underwriting requirements, and policy terms.")
    @IssuanceModel
    @ProductLine(ALL)
    private Boolean isRenewalPolicy;

    /**
     * Policy payment plan
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.paymentPlan}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Payment Plan", description = "Policy payment plan")
    @Schema(description = "The customer's chosen payment schedule and method for premium payments, including frequency (monthly, annually), payment method, and any installment arrangements.")
    @ProductLine(ALL)
    private PaymentPlan paymentPlan;

    /**
     * Policy special agreement
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceSpecialAgreementList}
     */
    @IssuanceModel()
    @PolicyModel(displayName = "Special Agreement List", description = "Policy special agreement")
    @Schema(description = "Policy special agreement")
    @ProductLine({MOTOR, HOME_PROPERTY})
    private final List<SpecialAgreement> specialAgreementList = new ArrayList<>();

    /**
     * Whether policy has target filling product
     */
    @PolicyModel(displayName = "Has Target Filling Product", description = "Whether policy has target filling product")
    @Schema(description = "Indicates if this policy includes target-based or goal-oriented products, such as savings plans with specific financial targets or investment objectives.")
    private Boolean hasTargetFillingProduct;

    /**
     * Whether it has invested account
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.hasInvestAccount}
     */
    @PolicyModel(displayName = "Has Invest Account", description = "Whether it has invested account")
    @Schema(description = "For investment-linked policies, indicates if the policy includes an investment account where premiums are allocated to various investment funds chosen by the customer.")
    @ProductLine({INVESTMENT_LINKED})
    private Boolean hasInvestAccount;

    /**
     * Whether it has Exemption Indicator
     */
    @Schema(description = "Indicates whether this policy has exemption provisions that waive premium payments under certain conditions (e.g., disability, unemployment, critical illness).")
    private Boolean hasExemptionIndicator;

    /**
     * legal trustee flag
     */
    @Schema(description = "Indicates whether the trustee arrangement follows legal requirements rather than being specifically designated by the policyholder. Legal trustees are determined by statutory rules.")
    private Boolean isLegalTrustee;

    /**
     * 受信人
     */
    @Schema(description = "Issuance Trustee List, for example, the trustee of a trust fund.")
    private final List<Trustee> trusteeList = Lists.newArrayList();

    /**
     * secondary insurant list
     */
    @ProductLine({MOTOR, TERM_LIFE})
    @Schema(description = "Secondary insurant list")
    private final List<SecondaryInsured> secondaryInsurantList = new ArrayList<>();

    /**
     * Vesting Age
     */
    @Schema(description = "The age at which the policyholder or beneficiary gains full ownership rights to the policy benefits or investment values, typically applicable to endowment or investment-linked policies.", minimum = "0", maximum = "120")
    private Integer vestingAge;

    /**
     * Vesting Option
     */
    @Schema(description = "Whether the policyholder has elected vesting options that determine when and how policy benefits become fully owned and accessible.")
    private Boolean vestingOption;

    /**
     * renewal due date
     */
    @Schema(description = "The date by which the policy renewal must be processed to maintain continuous coverage without lapse. This is typically before the expiry date.")
    private Date renewalDueDate;

    /**
     * renewal issue date
     */
    @Schema(description = "The date when the renewed policy was officially issued and became effective for the new term.")
    private Date renewalIssueDate;

    /**
     * Is installment
     */
    @PolicyModel(displayName = "Is Installment", description = "If there are installments")
    @Schema(description = "Whether the premium payment is divided into multiple installments rather than a single payment. This affects payment schedules and processing.")
    @IssuanceModel
    @ProductLine
    private Boolean isInstallment;

    /**
     * underwriting result list
     */
    @PolicyModel(displayName = "Underwriting Result List", description = "Underwriting result list")
    @Schema(description = "Results from the underwriting assessment process, including risk evaluation, medical examinations, and any special conditions or requirements imposed on the policy.")
    private final List<UnderwritingResult> underwritingResultList = new ArrayList<>();

    /**
     * underwriting decision for the whole policy
     */
    @PolicyModel(displayName = "Underwriting Decision", description = "Underwriting decision for the whole policy")
    @Schema(description = "The final underwriting decision for the entire policy (e.g., Approved, Declined, Approved with Conditions). This determines if the policy can be issued.")
    private DecisionTypeEnum underwritingDecision;

    /**
     * verification result list
     */
    @PolicyModel(displayName = "Verification Result List", description = "Verification result list")
    @Schema(description = "Results from document and information verification processes, including identity verification, document authenticity checks, and data validation outcomes used to prevent fraud and ensure policy accuracy.")
    private final List<VerificationResult> verificationResultList = new ArrayList<>();

    /**
     * compliance result list
     */
    @PolicyModel(displayName = "Compliance Result List", description = "Compliance result list")
    @Schema(description = "Results from regulatory compliance checks, including anti-money laundering (AML) screening, sanctions list verification, and regulatory requirement validations to ensure policy meets legal standards.")
    private final List<ComplianceResult> complianceResultList = new ArrayList<>();

    /**
     * policy risk information
     */
    @PolicyModel(displayName = "Risk Information", description = "Risk Information")
    @Schema(description = "Comprehensive risk assessment information including risk factors, hazard evaluations, loss exposures, and risk mitigation measures that influence underwriting decisions and premium calculations.")
    private RiskInformation riskInformation;

    /**
     * policy attachment list
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.issuanceAttachmentList}
     */
    @IssuanceModel
    @PolicyModel(displayName = "Attachment List", description = "Policy attachment list")
    @Schema(description = "Supporting documents attached to the policy such as application forms, medical reports, identification documents, property valuations, or other required documentation for underwriting and claims processing.")
    private final List<Attachment> attachmentList = new ArrayList<>();

    /**
     * claim stack list
     */
    @PolicyModel(displayName = "Claim Stack List", description = "Claim stack list")
    @Schema(description = "Historical record of all claims filed under this policy, including claim details, status updates, settlement amounts, and processing history for tracking claim frequency and patterns.")
    @ProductLine
    private final List<ClaimStack> claimStackList = new ArrayList<>();

    /**
     * comment list
     */
    @PolicyModel(displayName = "Comment List", description = "Comment list")
    @Schema(description = "Notes, remarks, and comments added by underwriters, agents, or system processes during policy lifecycle, providing additional context for policy decisions and special considerations.")
    @IssuanceModel
    @ProductLine(ALL)
    private final List<Comment> commentList = new ArrayList<>();

    /**
     * Claim history list
     */
    @PolicyModel(displayName = "Claim History List", description = "Claim history list")
    @Schema(description = "Complete claims history including previous claims from prior policies or insurance carriers, used for risk assessment and to identify claim patterns that may affect pricing or coverage eligibility.")
    private final List<ClaimHistory> claimHistoryList = new ArrayList<>();

    /**
     * sales channel information list
     */
    @IssuanceModel
    @PolicyModel(displayName = "Sales Channel List", description = "Sales channel information list")
    @Schema(description = "Information about the distribution channels through which this policy was sold, including agent details, broker information, online platforms, or direct sales channels with associated commission and tracking data.")
    private final List<SalesChannel> salesChannelList = new ArrayList<>();

    /**
     * issuance Transaction Type
     */
    @PolicyModel(displayName = "Issuance Transaction Type", description = "Issuance Transaction Type ref IssuanceTransactionTypeEnum")
    @Schema(description = "Issuance Transaction Type ref IssuanceTransactionTypeEnum")
    @IssuanceModel
    private IssuanceTransactionTypeEnum issuanceTransactionType;

    @PolicyModel(displayName = "Update Ocr Result", description = "Update Ocr Result")
    @Schema(description = "Update Ocr Result")
    private Boolean updateOcrResult;

    @PolicyModel(displayName = "Public Tender", description = "Public Tender")
    @Schema(description = "Public Tender")
    private Boolean publicTender;

    @PolicyModel(displayName = "Public Tender No", description = "Public Tender No")
    @Schema(description = "Public Tender No")
    private String publicTenderNo;

    @PolicyModel(displayName = "Non Standard Tariff", description = "Non standard tariff")
    @Schema(description = "Non standard tariff")
    private Boolean nonStandardTariff;

    @PolicyModel(displayName = "Fronting", description = "Fronting")
    @Schema(description = "Fronting")
    private Boolean fronting;

    /**
     * Facultative insurance refers to the operation in which an insurer temporarily
     * agrees with another insurer to reinsurance part of its insurance business to another insurer.
     */
    @PolicyModel(displayName = "Facultative", description = "Facultative")
    @Schema(description = "Facultative")
    private Boolean facultative;

    @PolicyModel(displayName = "Number Of Objects", description = "Number of objects")
    @Schema(description = "Number of objects")
    private Integer numberOfObjects;

    @PolicyModel(displayName = "Previous Carrier", description = "Previous Carrier")
    @Schema(description = "Previous Carrier")
    private String previousCarrier;

    @PolicyModel(displayName = "Previous Carrier Period Value", description = "Previous Carrier Period Value")
    @Schema(description = "Previous Carrier Period Value")
    private Integer previousCoveragePeriodValue;

    @PolicyModel(displayName = "Previous Coverage Period Type", description = "Previous Coverage Period Type")
    @Schema(description = "Previous Coverage Period Type")
    private CoveragePeriodTypeEnum previousCoveragePeriodType;

    @PolicyModel(displayName = "Previous Effective Date", description = "Previous Effective Date")
    @Schema(description = "Previous Effective Date")
    private Date previousEffectiveDate;

    @PolicyModel(displayName = "Previous Expiration Date", description = "Previous Expiration Date")
    @Schema(description = "Previous Expiration Date")
    private Date previousExpirationDate;

    /**
     * 是否要出event policy
     * {com.zatech.genesis.openapi.platform.integration.policy.request.IssuanceRequest.createIssuanceByEvent}
     */
    @Schema(description = "Indicates whether the issuance is created based on a specific event, the default value is false.")
    private Boolean createIssuanceByEvent;


    @Schema(description = "Comprehensive list of policy relationships and dependencies, including connections to other policies, parent-child policy structures, endorsements, and cross-references that define how this policy interacts with other insurance contracts or agreements.")
    private final List<PolicyRelationDetail> relationDetailList = new ArrayList<>();

    /**
     * Event policy issue switch
     */
    @Schema(description = "The switch of event policy for pos")
    private CanIssueEventEnum canIssueEvent;

    @Schema(title = "HPS Exemption indicator", description = "A flag indicating whether the policy change is exempt from the Health Protection Scheme (HPS) requirements under POS operations.")
    private Boolean hpsExemptionIndicator;

    @Schema(title = "User auth info", description = "The authentication information of the user associated with an insurance policy.")
    private UserAuthInfo userAuthInfo;

    @Schema(title = "Issuance Unnamed Insured List", description = "The list of unnamed insured entities associated with the issuance of a policy.")
    private final List<UnnamedInsured> unnamedInsuredList = Lists.newArrayList();

    @Schema(description = "A list of clause files containing the terms and conditions or specific clauses that are part of an insurance policy or agreement.")
    private final List<ClauseFile> clauseFileList = Lists.newArrayList();

    @Schema(title = "Effective without pay")
    private Boolean effectiveWithoutPay;

    @Schema(description = "The person or entity responsible for creating the insurance policy or issuance.")
    private String insureCreator;

    /**
     * 见费出单支付时间
     */
    @Schema(title = "Collection Date", description = "The date on which a collection is made.")
    private Date collectionDate;

    @Schema(title = "Claim Stack Template List", description = "The list of claim stack templates in the system.")
    private final List<ClaimStackTemplate> claimStackTemplateList = Lists.newArrayList();

    @Schema(title = "Claim Stack Config List", description = "The list of claim stack configs in the system.")
    private final List<ClaimStackConfig> claimStackConfigList = Lists.newArrayList();

    @Schema(title = "Issuance rule decision.", description = "The list of rule decisions associated with the issuance of a policy.")
    private final List<IssuanceRelation> issuanceRelationList = Lists.newArrayList();

    /**
     * 获取终止日期
     * 如果产品列表为空，返回空
     * 克隆产品列表，并过滤掉状态变更日期为空的产品
     * 如果克隆的产品列表为空，返回空
     * 获取克隆的产品列表中，状态变更日期的最大值
     * 如果最大值存在，返回该日期
     * 如果最大值为空，返回空
     */
    @JsonIgnore
    public Date getTerminatedDate() {
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }
        List<Product> clonedPolicyProductList = productList.stream()
            .filter(policyProduct -> policyProduct.getStatusChangeDate() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(clonedPolicyProductList)) {
            return null;
        }
        return clonedPolicyProductList.stream().map(Product::getStatusChangeDate).distinct()
            .max(Comparator.comparing(Date::getTime)).orElse(null);
    }

    /**
     * Get the main product or the first rider if the main product is not present
     *
     * @return the main product or the first rider if the main product is not present
     */
    @JsonIgnore
    public Product getMainProductOrFirstRider() {
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }
        Product policyProduct = getMainProduct();
        //主险为空或主险是虚拟主险取附加险
        if (policyProduct == null || Boolean.TRUE == policyProduct.getIsVirtual()) {
            policyProduct = productList.stream().filter(x -> x.getProductType() == ProductTypeEnum.RIDER).findFirst().orElse(null);
        }
        return policyProduct;
    }

    /**
     * 获取主险
     */
    @JsonIgnore
    public Product getMainProduct() {
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }
        return productList.stream().filter(x -> x.getProductType() == ProductTypeEnum.MAIN).findFirst().orElse(null);
    }

}
