package com.zatech.genesis.model.policy.customer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PolicyCustomerEnum;
import com.zatech.genesis.annotation.IssuanceModel;
import com.zatech.genesis.annotation.PolicyModel;
import com.zatech.genesis.model.UnifiedModel;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@PolicyModel(displayName = "Customer", description = "customer base info")
@Schema(name = "Customer")
public class Customer extends UnifiedModel {

    /**
     * Serial No to support distinguish multiple customers
     * it refers to a new customer
     */
    @PolicyModel(displayName = "Serial No", description = "Serial No")
    @Schema(description = "Unique sequential identifier used to distinguish between multiple customer records within the same policy when a customer has multiple roles or instances.")
    private String serialNo;

    @PolicyModel(displayName = "Customer Id", description = "Customer ID, the same value as party ID")
    @Schema(description = "Unique system identifier for this customer record, matching the party ID from the customer management system to ensure data consistency across platforms.")
    private Long customerId;

    @PolicyModel(displayName = "Customer Name", description = "Customer Name")
    @Schema(description = "Full name of the customer, automatically derived VARIABLE_DEF from individual full name for persons or company name for organizations involved in the insurance policy.")
    private String name;

    @PolicyModel(displayName = "Party Type", description = "Party Type")
    @Schema(description = "Classification of the customer entity type, such as INDIVIDUAL for natural persons or COMPANY for business organizations and legal entities.")
    private PartyTypeEnum customerType;

    /**
     * policy role:
     * 1- policyholder,
     * 2- policy insurant,
     * 3- beneficiary,
     * 4- payer
     *
     * @see PolicyCustomerEnum
     */
    @PolicyModel(displayName = "Role Type", description = "Role Type")
    @Schema(description = "The specific role this customer plays in the insurance policy, such as HOLDER (policy owner), INSURANT (covered person), BENEFICIARY (benefit recipient), or PAYER (premium payer).")
    private PolicyCustomerEnum roleType;

    @PolicyModel(displayName = "Key Word", description = "Key Word List")
    @Schema(description = "Collection of searchable keywords or tags associated with this customer for quick identification, categorization, and filtering purposes in customer management systems.")
    private final List<String> keyWordList = new ArrayList<>();

    @IssuanceModel
    @PolicyModel(displayName = "Individual", description = "Person type of policy related roles")
    @Schema(description = "`conditional required`\r- Person type of policy related roles. If individual information " +
        "is not provided, organization information must be filled in.")
    private PartyIndividual individual;

    @IssuanceModel
    @PolicyModel(displayName = "Organization", description = "Organization")
    @Schema(description = "`conditional required`\r- Organization type of policy related roles. If the organization " +
        "information is not provided, individual information must be filled in.")
    private PartyOrganization organization;

    public Customer(PartyOrganization partyCompany) {
        this.setOrganization(partyCompany);
        this.setCustomerId(partyCompany.getPartyId());
        this.setCustomerType(PartyTypeEnum.COMPANY);
    }

    public Customer(PartyIndividual partyCustomer) {
        this.setIndividual(partyCustomer);
        this.setCustomerId(partyCustomer.getPartyId());
        this.setCustomerType(PartyTypeEnum.INDIVIDUAL);
    }

    @JsonIgnore
    public String getName() {
        if (PartyTypeEnum.INDIVIDUAL == getCustomerType()) {
            return this.getIndividual().getFullName();
        } else if (PartyTypeEnum.COMPANY == getCustomerType()) {
            return this.getOrganization().getCompanyName();
        } else {
            return "";
        }
    }

    public Long getCustomerId() {
        if (PartyTypeEnum.INDIVIDUAL == getCustomerType()) {
            return this.getIndividual() != null ? this.getIndividual().getPartyId() : null;
        } else if (PartyTypeEnum.COMPANY == getCustomerType()) {
            return this.getOrganization() != null ? this.getOrganization().getPartyId() : null;
        } else {
            return null;
        }
    }
}
