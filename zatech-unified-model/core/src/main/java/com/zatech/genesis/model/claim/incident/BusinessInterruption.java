/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.claim.incident;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

@Data
public class BusinessInterruption {

    private Long id;

    @Schema(description = "The unique system-generation ID of a loss party, which is used to identify and retrieve the loss party details.")
    private Long lossPartyId;

    @Schema(description = "The start date of interruption")
    private Date startDate;

    @Schema(description = "The end date of interruption")
    private Date endDate;

    @Schema(description = "The unique ID of the time zone, which is used to identify the rules used to convert between an Instant and a LocalDateTime. For example, Asia/Shanghai.")
    private String zoneId;

    @Schema(description = "The days of interruption")
    private Integer interruptionDays;

    @Schema(description = "Daily average income")
    private String dailyAverageIncomeAmount;

    @Schema(description = "Actual daily income during the interruption period")
    private String actualDailyIncomeAmount;

    @Schema(description = "The standard and default currency the organization uses for its financial transactions, reporting, and accounting purposes across all its operations. It is configured at the tenant level in Configuration Center >Tenant Data Configuration > Basic Rule.")
    private CurrencyEnum currency;

    @Schema(description = "Profit rate")
    private String profitRate;

    @Schema(description = "Adjustment coefficient")
    private String adjustmentCoefficient;

    @Schema(description = "Additional costs")
    private String additionalCosts;

}
