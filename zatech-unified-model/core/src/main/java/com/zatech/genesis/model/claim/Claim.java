package com.zatech.genesis.model.claim;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.claim.CaseTypeEnum;
import com.zatech.gaia.resource.claim.ClaimStatusEnum;
import com.zatech.gaia.resource.claim.PreAuthorizationCaseEnum;
import com.zatech.gaia.resource.components.enums.claim.ClaimChannelEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.genesis.annotation.RootModel;
import com.zatech.genesis.model.UnifiedModel;
import com.zatech.genesis.model.claim.caseinfo.Case;
import com.zatech.genesis.model.claim.customer.AuthorizedParticipant;
import com.zatech.genesis.model.claim.customer.Claimant;
import com.zatech.genesis.model.policy.customer.Customer;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * Claim information
 */
@Getter
@Setter
@RootModel
@Schema(description = "Create case input", name = "Claim")
public class Claim extends UnifiedModel {

    @Schema(description = "List of case types")
    private final List<CaseTypeEnum> caseTypeList = Lists.newArrayList();

    @Schema(description = "List of cases")
    private final List<Case> caseList = Lists.newArrayList();

    @Schema(hidden = true, description = "List of insured customers, primarily required for group policies")
    private final List<Customer> insuredList = Lists.newArrayList();

    @Schema(description = "List of AuthorizedParticipant")
    private final List<AuthorizedParticipant> authorizedParticipantList = Lists.newArrayList();

    @Schema(description = "Claim application number", maxLength = 32, type = "string")
    private String applicationNo;

    @Schema(description = "The date of application", format = "date-time", type = "string")
    private Date applicationDate;

    @Schema(description = "reported policy number", maxLength = 32, type = "string")
    private String policyNo;

    @Schema(description = "The current status of the claim application")
    private ClaimStatusEnum applicationStatus;

    @Schema(description = "The full name of the user who applies the claim case.", maxLength = 128, type = "string")
    private String appliedBy;

    @Schema(description = "Third party transaction number", maxLength = 64, type = "string")
    private String thirdPartyTransactionNo;

    @Schema(description = "The application source")
    private ClaimChannelEnum source;

    @Schema(description = "Submission channel")
    private String channelCode;

    @Deprecated
    @Schema(description = "Pre-authorized flag")
    private Boolean preAuthorized;

    @Schema(description = "The flag used to indicate whether the claim case is a pre-authorization case: Non-pre-authorized or Pre-authorized or Pre-authorization in Progress.")
    private PreAuthorizationCaseEnum preAuthorizationCase;

    @Schema(description = "Customer claim amount")
    private String claimAmount;

    @Schema(description = "The currency of claim amount, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)")
    private CurrencyEnum currency;

    @Schema(description = "Individual or entity submitting the insurance claim, containing their personal information, contact details, and relationship to the insured party for claim processing and verification purposes.")
    private Claimant claimant;

    @Schema(description = "Third party claim No., support for externally specified case No.")
    private String reportNo;

}
