/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.claim.incident;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "VehicleTotalLoss")
public class VehicleTotalLoss {

    @Schema(description = "Vehicle loss id")
    private Long vehicleLossId;

    @Schema(description = "Object Value")
    private String objectValue;

    @Schema(description = "Years Used")
    private String yearsUsed;

    @Schema(description = "Total Depreciation Ratio")
    private String totalDepreciationRatio;

    @Schema(description = "Amortization Value")
    private String amortizationValue;

    @Schema(description = "Salvage Amount")
    private String salvageAmount;

    @Schema(description = "Estimation Amount")
    private String estimationAmount;

}
