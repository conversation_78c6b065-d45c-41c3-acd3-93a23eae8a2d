package com.zatech.genesis.model.claim.incident;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * Claim vehicle damage estimation information
 */
@Getter
@Setter
@Schema(name = "VehicleEstimation")
public class VehicleEstimation {
    @Schema(description = "Tax")
    private String tax;

    @Schema(description = "Tax Rate")
    private String taxRate;

    @Schema(description = "The rate of vehicle part total amount deduction(-) or increase(+)")
    private String partFloatRate;

    @Schema(description = "Total Estimation Amount")
    private String totalEstimationAmount;

    @Schema(description = "Currency, refer to the API reference. [Detail](/openx/docs/common/guide/reference/currency-codes)")
    private CurrencyEnum currency;

    @Schema(description = "Vehicle Total Loss")
    private VehicleTotalLoss vehicleTotalLoss;

    @Schema(description = "List of Vehicle Partial Loss Part")
    private List<VehiclePartialLossPart> vehiclePartialLossParts;

    @Schema(description = "List of Vehicle Partial Loss Labor")
    private List<VehiclePartialLossLabor> vehiclePartialLossLabors;

    @Schema(description = "List of Vehicle Partial Loss Material")
    private List<VehiclePartialLossMaterial> vehiclePartialLossMaterials;

    @Schema(description = "List of Vehicle Loss Sundry")
    private List<VehiclePartialLossSundry> vehicleLossSundries;

}
