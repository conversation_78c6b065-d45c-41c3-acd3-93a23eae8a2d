/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.claim.incident;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * @Author: liyadong002
 * @Date: 2023/3/23 16:19
 * @Description:
 */
@Data
@Schema(name = "VehicleLossTotal")
public class VehicleLossTotal {


    @Schema(description = "ID")
    private Long id;

    /**
     * Vehicle loss id
     */
    @Schema(description = "Vehicle loss ID")
    private Long vehicleLossId;

    /**
     * The global case no
     */
    @Schema(description = "The global case No.")
    private String caseNo;

    /**
     * Object Value
     */
    @Schema(description = "Object value")
    private String objectValue;

    /**
     * Years Used
     */
    @Schema(description = "Years used")
    private String yearsUsed;

    /**
     * Total Depreciation Ratio
     */
    @Schema(description = "Total depreciation ratio")
    private String totalDepreciationRatio;

    /**
     * Amortization Value
     */
    @Schema(description = "Amortization value")
    private String amortizationValue;

    /**
     * Salvage Amount
     */
    @Schema(description = "Salvage amount")
    private String salvageAmount;

    /**
     * Estimation Amount
     */
    @Schema(description = "Estimation amount")
    private String estimationAmount;
}
