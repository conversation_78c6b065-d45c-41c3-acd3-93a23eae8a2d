package com.zatech.genesis.model.claim.lossparty;

import com.zatech.gaia.resource.claim.LossPartyTypeEnum;
import com.zatech.gaia.resource.components.enums.claim.ClaimTypeEnum;
import com.zatech.gaia.resource.components.enums.subject.SubjectTypeEnum;
import com.zatech.genesis.annotation.ClaimModel;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * Claim loss party information
 * which includes: PERSON, VEHICLE, PROPERTY, ANIMAL
 */
@Getter
@Setter
@Schema(name = "LossParty")
public class LossParty {
    @Schema(description = "The relationship between the property and the insured object, indicating the property is an insured object or a third-party property." +
        "When lossPartyType is PERSON, the following values can be passed: DRIVER, INSURED, PASSENGER, RENTER, THIRD_PARTY. When lossPartyType is VEHICLE, ANIMAL, or PROPERTY, the following values can be passed: INSURED_OBJECT, THIRD_PARTY.")
    private String lossPartyRole;

    @Schema(description = "Loss party type,indicating the type of loss party. The following values can be passed: PERSON, VEHICLE, PROPERTY, ANIMAL.")
    private LossPartyTypeEnum lossPartyType;

    @Schema(description = "Identifier of loss parties, used to payee/incidentContent binding loss parties")
    private String identifier;

    @Schema(description = "When loss party type is PERSON, customer information")
    @ClaimModel(lossParty = {LossPartyTypeEnum.PERSON})
    private LossPartyCustomer customer;

    @Schema(description = "When loss party type is PROPERTY, property information")
    @ClaimModel(lossParty = {LossPartyTypeEnum.PROPERTY})
    private LossPartyProperty property;

    @Schema(description = "When loss party type is VEHICLE, vehicle information")
    @ClaimModel(lossParty = {LossPartyTypeEnum.VEHICLE})
    private LossPartyVehicle vehicle;

    @Schema(description = "When loss party type is ANIMAL, animal information")
    @ClaimModel(lossParty = {LossPartyTypeEnum.ANIMAL})
    private LossPartyAnimal animal;

    @Schema(description = "Insured object type, When the loss party type is person, refer to the API reference. [Detail](/openx/docs/common/guide/reference/subject-type)")
    private SubjectTypeEnum insuredObjectType;

    @Schema(description = "Insured object number, When the loss party type is person")
    private String insuredObjectNo;

    @Schema(description = "The insured object number bound with loss party")
    private List<LossPartyInsuredObjectRelation> lossPartyInsuredObjectRelationList;

    @Schema(description = "The pre-defined specific category or classification of an insurance claim based on the nature of the loss or event being reported.")
    private List<ClaimTypeEnum> claimTypeList;

}
