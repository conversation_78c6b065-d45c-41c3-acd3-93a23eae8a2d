package com.zatech.genesis.model.policy.base;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.biz.AppliedUnitEnum;
import com.zatech.gaia.resource.biz.ClaimPeriodTypeEnum;
import com.zatech.gaia.resource.biz.ClaimTimesTypeEnum;
import com.zatech.gaia.resource.biz.MainConditionTypeEnum;
import com.zatech.gaia.resource.biz.StackTypeEnum;
import com.zatech.gaia.resource.biz.StackUnitEnum;
import com.zatech.gaia.resource.components.enums.policy.ValueTypeEnum;
import com.zatech.genesis.annotation.PolicyModel;
import com.zatech.genesis.model.UnifiedModel;
import com.zatech.genesis.model.policy.base.claim.ConditionValue;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@PolicyModel(displayName = "ClaimStack", description = "policy ClaimStack")
@Schema(name = "ClaimStack")
public class ClaimStack extends UnifiedModel {

    @Schema(title = "The claim stack id", description = "The system-generated unique identifier for the record.")
    private Long id;

    //这个暂时先隐藏
    @Schema(title = "The claim stack instance id", description = "The unique identifier for an instance of an entity in the system.", hidden = true)
    private Long instanceId;

    /**
     * The stack code
     */
    @PolicyModel(displayName = "Stack Code", description = "The stack code")
    private String stackCode;

    /**
     * The stack value
     */
    @PolicyModel(displayName = "Stack Value", description = "The stack value")
    private String stackValue;


    @Schema(title = "The stack value", description = "The representation form of the claim stack value, such as fixed value, enumeration, formula, range, and user input, which determines how the value of the claim stack is defined and specified.")
    private ValueTypeEnum stackValueType;

    @Schema(title = "Stack Type", description = "A specific category or classification that determines how different types of benefit limits, such as claim limits, deductibles, and co-pays, are applied and interact with insurance claims. The three common stack types: limit, deduction, and % of co-pay.")
    private StackTypeEnum stackType;

    @Schema(title = "Main Condition Type", description = "The primary condition type associated with a policy or transaction.")
    private MainConditionTypeEnum mainConditionType;

    @Schema(title = "Period Type", description = "The type of period (e.g., monthly, yearly, etc.).")
    private ClaimPeriodTypeEnum periodType;

    @Schema(title = "Before Days", description = "The number of days before a specific event or date.")
    private Integer beforeDays;

    @Schema(title = "After Days", description = "The number of days after a specific event or date.")
    private Integer afterDays;

    @Schema(title = "Stack Unit", description = "The unit of claim stack, which is used to quantify the limitation metric in the claim stack definitions. Valid values:No. of Days,No. of Times,Amount,Number,Percentage,Comparison of Deductible Amount and Percentage")
    private StackUnitEnum stackUnit;

    @Schema(title = "Applied Unit", description = "The unit applied to a calculation or value.")
    private AppliedUnitEnum appliedUnit;

    /**
     * {@link ClaimTimesTypeEnum}
     */
    @Schema(title = "Unit Type", description = "The type of unit used in the system (e.g., currency, time, etc.).")
    private String unitType;

    @Schema(title = "Condition Values", description = "The values associated with specific conditions.")
    private final List<ConditionValue> conditionValueList = Lists.newArrayList();

}
