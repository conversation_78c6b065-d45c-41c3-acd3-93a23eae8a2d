/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.policy.relation;

import com.zatech.gaia.resource.graphene.policy.PolicyRelationSubTypeEnum;
import com.zatech.gaia.resource.graphene.policy.PolicyRelationTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by yaoxiaolong, 2023/5/31 18:44
 * <p>
 * description
 */
@Setter
@Getter
public class IssuanceRelation implements Serializable {

    @Schema(description = "The unique identifier assigned to a specific relationship record. This number is used to link and track the association between different entities or documents within the insurance policy issuance process, such as the relationship between a policy and related business documents, or between different parties involved in the policy.")
    private String relationNo;

    @Schema(description = "The classification of the relationship. This field specifies the nature of the association being defined, such as the type of party relationship (e.g., policyholder-insured, policyholder-beneficiary) or the type of document relationship (e.g., policy-endorsement, policy-claim). The valid values are typically defined in Configuration Center > Tenant Data Configuration > Enumerated Values.")
    private PolicyRelationTypeEnum type;

    @Schema(description = "A further categorization of the relationship type, providing a more specific classification when the 'type' field is insufficient to fully describe the relationship. For instance, if the 'type' is 'Policyholder-Insured Relationship', the 'subType' could specify 'Primary Insured', 'Secondary Insured', etc. The valid values are typically defined in Configuration Center > Tenant Data Configuration > Enumerated Values, and are dependent on the selected 'type'.")
    private PolicyRelationSubTypeEnum subType;
}
