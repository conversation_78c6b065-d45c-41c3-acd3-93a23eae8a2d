/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.model.pos;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zatech.gaia.resource.components.enums.bcp.PayMethodEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(title = "POS payment account information", description = "POS payment account information")
public class PosCasePaymentAccountInfo extends BaseCommon {

    /**
     * payee的partyId
     */
    @Schema(title = "The partyID of payee", description = "The unique identifier of the payee associated with the POS case payment account.")
    private Long policyPayeeId;

    /**
     * account ID
     */
    @Schema(title = "The ID of account", description = "The unique identifier for the payment account associated with the POS transaction.")
    private Long payAccountId;

    /**
     * 支付方式
     */
    @Schema(title = "The method of payment", description = "The payment method used for the POS transaction.")
    private PayMethodEnum paymentMethod;

    /**
     * bankCode
     */
    @Schema(title = "The code of bank", description = "The unique identification number assigned to financial institutions by regulatory authorities, used for POS payment account verification and processing.")
    private String bankCode;

    /**
     * bankBranchCode
     */
    @Schema(title = "The code of bank branch", description = "The unique identifier assigned to a specific branch of a bank, used to distinguish between different branches within the same bank for POS transactions. Configured in the Account section of customer details in Customer Center.")
    private String bankBranchCode;

    /**
     * 账户类型
     */
    @Schema(title = "The type of bank account", description = "The type of account used for POS payment transactions.")
    private AccountTypeEnum accountType;

    /**
     * 账户子类型
     */
    @Schema(title = "The subtype of bank account", description = "The sub-account type representing the middle level in the cascade account hierarchy defined in the POS Payment/Collection Method & Account configuration.")
    private AccountSubTypeEnum accountSubType;

    /**
     * expiryDate
     */
    @Schema(title = "The expiryDate of bank card", description = "The date on which the POS case payment account becomes invalid or expires.")
    private LocalDate expiryDate;

    /**
     * 手机号
     */
    @Schema(title = "The mobile number of holder", description = "The unique numerical sequence assigned to a mobile device for communication purposes, used to identify the policyholders contact number in POS transactions.")
    private String mobileNo;

    /**
     * 银行名称
     */
    @JsonProperty("bank")
    @JsonAlias("bankName")
    @Schema(title = "The name of bank", description = "The financial institution associated with the POS payment account.")
    private String bank;

    /**
     * 银行名称
     */
    @Schema(title = "The name of bank", description = "The name of the customers bank as configured in the account details section of the POS system.")
    private String bankName;

    /**
     * 银行城市
     */
    @Schema(title = "Bank city", description = "The city where the bank associated with the policyholders payment account is located.")
    private String bankCity;

    /**
     * 银行分行
     */
    @JsonProperty("bankBranch")
    @JsonAlias("bankBranchName")
    @Schema(title = "The name of bank branch", description = "The bank branch information associated with the payment account for POS transactions.")
    private String bankBranch;

    /**
     * 银行分行
     */
    @Schema(title = "The name of bank branch", description = "The specific designation or title of a bank branch, used to distinguish between different branches within the same bank network for POS payment account operations.")
    private String bankBranchName;

    /**
     * 银行账户
     */
    @JsonProperty("accountNumber")
    @JsonAlias("cardNumber")
    @Schema(title = "The number of account", description = "The account number associated with the POS payment transaction.")
    private String accountNumber;

    /**
     * 银行账户
     */
    @Schema(title = "The number of account", description = "The unique numerical identifier assigned to a payment card, used to link transactions to the cardholders account in POS operations.")
    private String cardNumber;

    /**
     * 账户人
     */
    @JsonProperty("accountName")
    @JsonAlias("cardHolderName")
    @Schema(title = "The name of account", description = "The name of the account associated with the POS payment transaction.")
    private String accountName;

    /**
     * 账户人
     */
    @Schema(title = "The name of account", description = "The name of the cardholder associated with the payment account used for POS transactions in insurance operations.")
    private String cardHolderName;

    /**
     * 地址11
     */
    @Schema(title = "Line 1 of first address", description = "Extension field for line 1 of the first address, typically used to store supplemental address information such as province or state details.")
    private String address11;

    /**
     * 地址12
     */
    @Schema(title = "Line 2 of first address", description = "Extension field for line 2 of the first address, typically used to store additional address details such as district or locality information.")
    private String address12;

    /**
     * 地址13
     */
    @Schema(title = "Line 3 of first address", description = "Extension field for line 3 of the first address, typically used for additional district or locality information.")
    private String address13;

    /**
     * 地址14
     */
    @Schema(title = "Line 4 of first address", description = "Extension field for line 4 of the address, typically used for additional location details such as district or neighborhood information.")
    private String address14;

    /**
     * IBAN
     */
    @Schema(title = "IBAN", description = "International Bank Account Number, a standardized code of up to 34 alphanumeric characters that uniquely identifies a bank account for cross-border transactions.")
    private String iban;

    /**
     * Third party pay voucher
     */
    @Schema(title = "Third party pay voucher", description = "A voucher or proof of payment from a third-party payment system used in POS transactions.")
    private String thirdPartyPayVoucher;

    /**
     * Bank address
     */
    @Schema(title = "Bank address", description = "The physical location of the bank associated with the POS payment account, including street address, city, region, postal code, and country.")
    private String bankAddress;

    /**
     * Bank branch address
     */
    @Schema(title = "Bank branch address", description = "The physical address of the bank branch associated with the payment account, including street, city, region, postal code, and country details.")
    private String bankBranchAddress;

    /**
     * SWIFT code
     */
    @Schema(title = "SWIFT code", description = "The SWIFT code (Business Identifier Code or BIC) uniquely identifies financial institutions for international transactions, managed by the Society for Worldwide Interbank Financial Telecommunications.")
    private String swiftCode;

    /**
     * 邮编
     */
    @Schema(title = "The code of postal", description = "The postal code associated with the policyholders address in the POS case payment account information.")
    private String postalCode;

    /**
     * 币种转换后金额
     */
    @Schema(title = "The fee of total", description = "Total fee amount for the POS transaction, represented as a string value.")
    private String totalFee;

    /**
     * 币种
     */
    @Schema(title = "The currency of total", description = "The in which the POS transaction amount is denominated.")
    private CurrencyEnum currency;

    /**
     * 币种转换后金额
     */
    @Schema(title = "The fee of base currency total", description = "The total base fee amount for the POS transaction, before any adjustments or additional charges.")
    private String baseTotalFee;

    /**
     * 币种
     */
    @Schema(title = "The base currency of total", description = "The standard currency configured at the tenant level used for POS financial transactions and reporting within the organization.")
    private CurrencyEnum baseCurrency;

    @Schema(title = "POS payment account information", description = "POS payment account information")
    private List<PosBillSummaryInfo> posBillSummaryInfoList;

    @Schema(title = "The extension list of POS payment account", description = "Base for POS operations in the insurance domain.")
    private Map<String, String> extensions;
}
