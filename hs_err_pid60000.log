#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 37748736 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3557), pid=60000, tid=72560
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.6+10) (build 17.0.6+10-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.6+10-LTS, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:58343,suspend=y,server=n -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.profiles.active=local1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2022.1\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture.props -Dfile.encoding=UTF-8 com.zatech.genesis.openapi.platform.OpenapiApplication

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.2364)
Time: Thu Oct 26 17:48:55 2023  Windows 10 , 64 bit Build 19041 (10.0.19041.2364) elapsed time: 83.501540 seconds (0d 0h 1m 23s)

---------------  T H R E A D  ---------------

Current thread (0x000001b2d235adb0):  VMThread "VM Thread" [stack: 0x000000aa58a00000,0x000000aa58b00000] [id=72560]

Stack: [0x000000aa58a00000,0x000000aa58b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x64b39a]
V  [jvm.dll+0x7ff044]
V  [jvm.dll+0x8007fe]
V  [jvm.dll+0x800e53]
V  [jvm.dll+0x235848]
V  [jvm.dll+0x6482fa]
V  [jvm.dll+0x63ca0a]
V  [jvm.dll+0x2f3d59]
V  [jvm.dll+0x2faee6]
V  [jvm.dll+0x34755a]
V  [jvm.dll+0x34779f]
V  [jvm.dll+0x2ca128]
V  [jvm.dll+0x2cd296]
V  [jvm.dll+0x2d7adc]
V  [jvm.dll+0x30b340]
V  [jvm.dll+0x80528b]
V  [jvm.dll+0x805f68]
V  [jvm.dll+0x806495]
V  [jvm.dll+0x806864]
V  [jvm.dll+0x80692e]
V  [jvm.dll+0x7b1d6a]
V  [jvm.dll+0x64a265]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526a1]

VM_Operation (0x000000aa586ffa70): G1Concurrent, mode: safepoint, requested by thread 0x000001b2bbaab9d0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001b2e08e25a0, length=139, elements={
0x000001b2d235d750, 0x000001b2d235e210, 0x000001b2d301c4c0, 0x000001b2d301ed90,
0x000001b2bba18050, 0x000001b2bba176b0, 0x000001b2d302d350, 0x000001b2d302dc50,
0x000001b2bba16d10, 0x000001b2d317aaf0, 0x000001b2d317bb80, 0x000001b2d317e1e0,
0x000001b2bba18520, 0x000001b2bba159d0, 0x000001b2d4cb8610, 0x000001b2d4cb77a0,
0x000001b2d4cb42b0, 0x000001b2d4cb6460, 0x000001b2d4cb6e00, 0x000001b2d4cb72d0,
0x000001b2d4cb7c70, 0x000001b2d4cb8ae0, 0x000001b2d4cb5ac0, 0x000001b2d63f7fe0,
0x000001b2d63f9320, 0x000001b2d63f97f0, 0x000001b2d63f7170, 0x000001b2d63fb4d0,
0x000001b2d63fc340, 0x000001b2d63f8e50, 0x000001b2d63f6300, 0x000001b2d63f67d0,
0x000001b2d63fc810, 0x000001b2d63f5960, 0x000001b2d63f5e30, 0x000001b2d63f6ca0,
0x000001b2d4cb4780, 0x000001b2d4cba7c0, 0x000001b2d4cb8140, 0x000001b2d4cb9950,
0x000001b2d4cb9e20, 0x000001b2d4cba2f0, 0x000001b2d4cb6930, 0x000001b2d4cb4c50,
0x000001b2d4cb9480, 0x000001b2d4cbb160, 0x000001b2d4cb5120, 0x000001b2bba15500,
0x000001b2de3215e0, 0x000001b2de31c8e0, 0x000001b2de31ea90, 0x000001b2de31c410,
0x000001b2de322450, 0x000001b2de31d280, 0x000001b2d4548010, 0x000001b2d4548a30,
0x000001b2de31ef60, 0x000001b2de320770, 0x000001b2de31ba70, 0x000001b2de320c40,
0x000001b2de3202a0, 0x000001b2de31cdb0, 0x000001b2de321f80, 0x000001b2de322920,
0x000001b2de31f430, 0x000001b2de322df0, 0x000001b2d4cb55f0, 0x000001b2d4cbac90,
0x000001b2d4cbb630, 0x000001b2d4cb3de0, 0x000001b2d63fa190, 0x000001b2d63fb9a0,
0x000001b2e0399360, 0x000001b2e0399830, 0x000001b2e0395e70, 0x000001b2e03971b0,
0x000001b2e0394660, 0x000001b2e0396ce0, 0x000001b2e0398020, 0x000001b2e03959a0,
0x000001b2e03984f0, 0x000001b2e0396340, 0x000001b2e0394190, 0x000001b2e0398e90,
0x000001b2e03989c0, 0x000001b2e0396810, 0x000001b2e0394b30, 0x000001b2e0395000,
0x000001b2e03954d0, 0x000001b2e0397680, 0x000001b2e0397b50, 0x000001b2e0399d00,
0x000001b2e039a1d0, 0x000001b2e039a6a0, 0x000001b2e0392e50, 0x000001b2e0393320,
0x000001b2e03937f0, 0x000001b2e0393cc0, 0x000001b2d4cb8fb0, 0x000001b2e05e5dd0,
0x000001b2e05e62a0, 0x000001b2e05e10d0, 0x000001b2e05e2db0, 0x000001b2e05e3c20,
0x000001b2e05e4f60, 0x000001b2e05e6770, 0x000001b2e05e7ab0, 0x000001b2e05e8920,
0x000001b2e05e3280, 0x000001b2e05e5900, 0x000001b2e05e28e0, 0x000001b2e05e6c40,
0x000001b2e05e40f0, 0x000001b2de3bc4d0, 0x000001b2e05e7110, 0x000001b2e05e75e0,
0x000001b2e05e7f80, 0x000001b2e05e4a90, 0x000001b2e05e45c0, 0x000001b2e05e8450,
0x000001b2e05e5430, 0x000001b2e05e1a70, 0x000001b2e05e1f40, 0x000001b2e05e2410,
0x000001b2de31fdd0, 0x000001b2e081ac20, 0x000001b2e08150b0, 0x000001b2e0815580,
0x000001b2e081a280, 0x000001b2e081a750, 0x000001b2e0817730, 0x000001b2e081b0f0,
0x000001b2e081ba90, 0x000001b2e0817c00, 0x000001b2e081bf60, 0x000001b2e081c430,
0x000001b2e081c900, 0x000001b2e0815a50, 0x000001b2e08198e0
}

Java Threads: ( => current thread )
  0x000001b2d235d750 JavaThread "Reference Handler" daemon [_thread_blocked, id=64924, stack(0x000000aa58b00000,0x000000aa58c00000)]
  0x000001b2d235e210 JavaThread "Finalizer" daemon [_thread_blocked, id=75720, stack(0x000000aa58c00000,0x000000aa58d00000)]
  0x000001b2d301c4c0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=60640, stack(0x000000aa58d00000,0x000000aa58e00000)]
  0x000001b2d301ed90 JavaThread "Attach Listener" daemon [_thread_blocked, id=76900, stack(0x000000aa58e00000,0x000000aa58f00000)]
  0x000001b2bba18050 JavaThread "Service Thread" daemon [_thread_blocked, id=15056, stack(0x000000aa58f00000,0x000000aa59000000)]
  0x000001b2bba176b0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=74144, stack(0x000000aa59000000,0x000000aa59100000)]
  0x000001b2d302d350 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=73888, stack(0x000000aa59100000,0x000000aa59200000)]
  0x000001b2d302dc50 JavaThread "Sweeper thread" daemon [_thread_blocked, id=76556, stack(0x000000aa59200000,0x000000aa59300000)]
  0x000001b2bba16d10 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=67576, stack(0x000000aa59300000,0x000000aa59400000)]
  0x000001b2d317aaf0 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=72772, stack(0x000000aa59600000,0x000000aa59700000)]
  0x000001b2d317bb80 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=76596, stack(0x000000aa59700000,0x000000aa59800000)]
  0x000001b2d317e1e0 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=77020, stack(0x000000aa59800000,0x000000aa59900000)]
  0x000001b2bba18520 JavaThread "Notification Thread" daemon [_thread_blocked, id=74888, stack(0x000000aa59500000,0x000000aa59600000)]
  0x000001b2bba159d0 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=22268, stack(0x000000aa5a300000,0x000000aa5a400000)]
  0x000001b2d4cb8610 JavaThread "com.alibaba.nacos.client.auth.ram.identify.watcher.0" daemon [_thread_blocked, id=22632, stack(0x000000aa5ab00000,0x000000aa5ac00000)]
  0x000001b2d4cb77a0 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=65928, stack(0x000000aa5ac00000,0x000000aa5ad00000)]
  0x000001b2d4cb42b0 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=69168, stack(0x000000aa5ad00000,0x000000aa5ae00000)]
  0x000001b2d4cb6460 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=71896, stack(0x000000aa5ae00000,0x000000aa5af00000)]
  0x000001b2d4cb6e00 JavaThread "nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent" daemon [_thread_blocked, id=64692, stack(0x000000aa5af00000,0x000000aa5b000000)]
  0x000001b2d4cb72d0 JavaThread "com.alibaba.nacos.client.remote.worker" daemon [_thread_blocked, id=73908, stack(0x000000aa5b000000,0x000000aa5b100000)]
  0x000001b2d4cb7c70 JavaThread "com.alibaba.nacos.client.remote.worker" daemon [_thread_blocked, id=75376, stack(0x000000aa5b100000,0x000000aa5b200000)]
  0x000001b2d4cb8ae0 JavaThread "grpc-nio-worker-ELG-1-1" daemon [_thread_in_native, id=20756, stack(0x000000aa5b200000,0x000000aa5b300000)]
  0x000001b2d4cb5ac0 JavaThread "grpc-nio-worker-ELG-1-2" daemon [_thread_in_native, id=9068, stack(0x000000aa5b600000,0x000000aa5b700000)]
  0x000001b2d63f7fe0 JavaThread "AsyncAppender-Worker-infoAppenderAsync" daemon [_thread_blocked, id=52708, stack(0x000000aa5a500000,0x000000aa5a600000)]
  0x000001b2d63f9320 JavaThread "AsyncAppender-Worker-requestInfoAppenderAsync" daemon [_thread_blocked, id=71948, stack(0x000000aa5a600000,0x000000aa5a700000)]
  0x000001b2d63f97f0 JavaThread "AsyncAppender-Worker-errorAppenderAsync" daemon [_thread_blocked, id=76064, stack(0x000000aa5a700000,0x000000aa5a800000)]
  0x000001b2d63f7170 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=19560, stack(0x000000aa5c500000,0x000000aa5c600000)]
  0x000001b2d63fb4d0 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=68776, stack(0x000000aa5c600000,0x000000aa5c700000)]
  0x000001b2d63fc340 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=72084, stack(0x000000aa5a200000,0x000000aa5a300000)]
  0x000001b2d63f8e50 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=71424, stack(0x000000aa5b700000,0x000000aa5b800000)]
  0x000001b2d63f6300 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=77356, stack(0x000000aa5b800000,0x000000aa5b900000)]
  0x000001b2d63f67d0 JavaThread "genesis-mds-0 housekeeper" daemon [_thread_blocked, id=66156, stack(0x000000aa5bb00000,0x000000aa5bc00000)]
  0x000001b2d63fc810 JavaThread "muangthai-mds-0 housekeeper" daemon [_thread_blocked, id=77004, stack(0x000000aa5bc00000,0x000000aa5bd00000)]
  0x000001b2d63f5960 JavaThread "test-mds-0 housekeeper" daemon [_thread_blocked, id=69384, stack(0x000000aa5bd00000,0x000000aa5be00000)]
  0x000001b2d63f5e30 JavaThread "asuransiastra-mds-0 housekeeper" daemon [_thread_blocked, id=46936, stack(0x000000aa5be00000,0x000000aa5bf00000)]
  0x000001b2d63f6ca0 JavaThread "prudential-mds-0 housekeeper" daemon [_thread_blocked, id=64788, stack(0x000000aa5bf00000,0x000000aa5c000000)]
  0x000001b2d4cb4780 JavaThread "cdg-mds-0 housekeeper" daemon [_thread_blocked, id=64636, stack(0x000000aa5c000000,0x000000aa5c100000)]
  0x000001b2d4cba7c0 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=76988, stack(0x000000aa5c100000,0x000000aa5c200000)]
  0x000001b2d4cb8140 JavaThread "zurich-mds-0 housekeeper" daemon [_thread_blocked, id=77692, stack(0x000000aa5c200000,0x000000aa5c300000)]
  0x000001b2d4cb9950 JavaThread "kwiasia-mds-0 housekeeper" daemon [_thread_blocked, id=58876, stack(0x000000aa5c300000,0x000000aa5c400000)]
  0x000001b2d4cb9e20 JavaThread "doc-mds-0 housekeeper" daemon [_thread_blocked, id=48372, stack(0x000000aa5c400000,0x000000aa5c500000)]
  0x000001b2d4cba2f0 JavaThread "test2-mds-0 housekeeper" daemon [_thread_blocked, id=75956, stack(0x000000aa5c700000,0x000000aa5c800000)]
  0x000001b2d4cb6930 JavaThread "moovaz-mds-0 housekeeper" daemon [_thread_blocked, id=76644, stack(0x000000aa5b500000,0x000000aa5b600000)]
  0x000001b2d4cb4c50 JavaThread "msigasia-mds-0 housekeeper" daemon [_thread_blocked, id=46104, stack(0x000000aa5ca00000,0x000000aa5cb00000)]
  0x000001b2d4cb9480 JavaThread "air-mds-0 housekeeper" daemon [_thread_blocked, id=7064, stack(0x000000aa5ba00000,0x000000aa5bb00000)]
  0x000001b2d4cbb160 JavaThread "sli-mds-0 housekeeper" daemon [_thread_blocked, id=70432, stack(0x000000aa5cb00000,0x000000aa5cc00000)]
  0x000001b2d4cb5120 JavaThread "uniqa-mds-0 housekeeper" daemon [_thread_blocked, id=50260, stack(0x000000aa5cd00000,0x000000aa5ce00000)]
  0x000001b2bba15500 JavaThread "fquality-mds-0 housekeeper" daemon [_thread_blocked, id=23632, stack(0x000000aa5cf00000,0x000000aa5d000000)]
  0x000001b2de3215e0 JavaThread "carro-mds-0 housekeeper" daemon [_thread_blocked, id=59364, stack(0x000000aa5d100000,0x000000aa5d200000)]
  0x000001b2de31c8e0 JavaThread "zamy-mds-0 housekeeper" daemon [_thread_blocked, id=73520, stack(0x000000aa5d300000,0x000000aa5d400000)]
  0x000001b2de31ea90 JavaThread "arkana-mds-0 housekeeper" daemon [_thread_blocked, id=62904, stack(0x000000aa5d500000,0x000000aa5d600000)]
  0x000001b2de31c410 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=58100, stack(0x000000aa5d600000,0x000000aa5d700000)]
  0x000001b2de322450 JavaThread "incomecore-mds-0 housekeeper" daemon [_thread_blocked, id=21360, stack(0x000000aa5d800000,0x000000aa5d900000)]
  0x000001b2de31d280 JavaThread "lifecn-mds-0 housekeeper" daemon [_thread_blocked, id=53920, stack(0x000000aa5da00000,0x000000aa5db00000)]
  0x000001b2d4548010 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=10272, stack(0x000000aa59900000,0x000000aa59a00000)]
  0x000001b2d4548a30 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=67372, stack(0x000000aa5b900000,0x000000aa5ba00000)]
  0x000001b2de31ef60 JavaThread "Catalina-utility-1" [_thread_blocked, id=56616, stack(0x000000aa58100000,0x000000aa58200000)]
  0x000001b2de320770 JavaThread "Catalina-utility-2" [_thread_blocked, id=77572, stack(0x000000aa58200000,0x000000aa58300000)]
  0x000001b2de31ba70 JavaThread "container-0" [_thread_blocked, id=10968, stack(0x000000aa58300000,0x000000aa58400000)]
  0x000001b2de320c40 JavaThread "Thread-28" daemon [_thread_in_native, id=77496, stack(0x000000aa5ce00000,0x000000aa5cf00000)]
  0x000001b2de3202a0 JavaThread "lettuce-eventExecutorLoop-1-1" daemon [_thread_blocked, id=77516, stack(0x000000aa5d000000,0x000000aa5d100000)]
  0x000001b2de31cdb0 JavaThread "lettuce-timer-3-1" daemon [_thread_blocked, id=77472, stack(0x000000aa5d200000,0x000000aa5d300000)]
  0x000001b2de321f80 JavaThread "spring.cloud.inetutils" daemon [_thread_blocked, id=47492, stack(0x000000aa5aa00000,0x000000aa5ab00000)]
  0x000001b2de322920 JavaThread "nacos-grpc-client-executor-mscnew.za-gj-aws.net-27" daemon [_thread_blocked, id=71636, stack(0x000000aa5b400000,0x000000aa5b500000)]
  0x000001b2de31f430 JavaThread "nacos-grpc-client-executor-mscnew.za-gj-aws.net-28" daemon [_thread_blocked, id=61504, stack(0x000000aa5d400000,0x000000aa5d500000)]
  0x000001b2de322df0 JavaThread "http-nio-8080-exec-1" daemon [_thread_blocked, id=70416, stack(0x000000aa5e600000,0x000000aa5e700000)]
  0x000001b2d4cb55f0 JavaThread "http-nio-8080-exec-2" daemon [_thread_blocked, id=66848, stack(0x000000aa5e700000,0x000000aa5e800000)]
  0x000001b2d4cbac90 JavaThread "http-nio-8080-exec-3" daemon [_thread_blocked, id=67252, stack(0x000000aa5e800000,0x000000aa5e900000)]
  0x000001b2d4cbb630 JavaThread "http-nio-8080-exec-4" daemon [_thread_blocked, id=71224, stack(0x000000aa5e900000,0x000000aa5ea00000)]
  0x000001b2d4cb3de0 JavaThread "http-nio-8080-exec-5" daemon [_thread_blocked, id=69992, stack(0x000000aa5ea00000,0x000000aa5eb00000)]
  0x000001b2d63fa190 JavaThread "http-nio-8080-exec-6" daemon [_thread_blocked, id=75268, stack(0x000000aa5eb00000,0x000000aa5ec00000)]
  0x000001b2d63fb9a0 JavaThread "http-nio-8080-exec-7" daemon [_thread_blocked, id=2060, stack(0x000000aa5ec00000,0x000000aa5ed00000)]
  0x000001b2e0399360 JavaThread "http-nio-8080-exec-8" daemon [_thread_blocked, id=23824, stack(0x000000aa5ed00000,0x000000aa5ee00000)]
  0x000001b2e0399830 JavaThread "http-nio-8080-exec-9" daemon [_thread_blocked, id=46156, stack(0x000000aa5ee00000,0x000000aa5ef00000)]
  0x000001b2e0395e70 JavaThread "http-nio-8080-exec-10" daemon [_thread_blocked, id=12596, stack(0x000000aa5ef00000,0x000000aa5f000000)]
  0x000001b2e03971b0 JavaThread "http-nio-8080-Poller" daemon [_thread_in_native, id=26728, stack(0x000000aa5f000000,0x000000aa5f100000)]
  0x000001b2e0394660 JavaThread "http-nio-8080-Acceptor" daemon [_thread_in_native, id=21596, stack(0x000000aa5f100000,0x000000aa5f200000)]
  0x000001b2e0396ce0 JavaThread "lettuce-nioEventLoop-4-1" daemon [_thread_in_native, id=56328, stack(0x000000aa5b300000,0x000000aa5b400000)]
  0x000001b2e0398020 JavaThread "scheduled-dns-resolve-thread" daemon [_thread_blocked, id=70992, stack(0x000000aa5cc00000,0x000000aa5cd00000)]
  0x000001b2e03959a0 JavaThread "lettuce-nioEventLoop-4-2" daemon [_thread_in_native, id=77308, stack(0x000000aa5f200000,0x000000aa5f300000)]
  0x000001b2e03984f0 JavaThread "scheduled-dns-resolve-thread" daemon [_thread_blocked, id=65492, stack(0x000000aa5f300000,0x000000aa5f400000)]
  0x000001b2e0396340 JavaThread "pool-9-thread-1" [_thread_blocked, id=74748, stack(0x000000aa5f400000,0x000000aa5f500000)]
  0x000001b2e0394190 JavaThread "pool-9-thread-2" [_thread_blocked, id=50756, stack(0x000000aa5f500000,0x000000aa5f600000)]
  0x000001b2e0398e90 JavaThread "KMPoll_T0" [_thread_blocked, id=76776, stack(0x000000aa5f600000,0x000000aa5f700000)]
  0x000001b2e03989c0 JavaThread "KMPoll_T1" [_thread_blocked, id=75104, stack(0x000000aa5f700000,0x000000aa5f800000)]
  0x000001b2e0396810 JavaThread "KMPoll_T2" [_thread_blocked, id=69280, stack(0x000000aa5f800000,0x000000aa5f900000)]
  0x000001b2e0394b30 JavaThread "KMPoll_T3" [_thread_in_native, id=68168, stack(0x000000aa5f900000,0x000000aa5fa00000)]
  0x000001b2e0395000 JavaThread "KMPoll_T4" [_thread_in_native, id=77204, stack(0x000000aa5fa00000,0x000000aa5fb00000)]
  0x000001b2e03954d0 JavaThread "KMPoll_T5" [_thread_in_native, id=77544, stack(0x000000aa5fb00000,0x000000aa5fc00000)]
  0x000001b2e0397680 JavaThread "KMPoll_T6" [_thread_in_native, id=56608, stack(0x000000aa5fc00000,0x000000aa5fd00000)]
  0x000001b2e0397b50 JavaThread "KMPoll_T7" [_thread_in_native, id=69104, stack(0x000000aa5fd00000,0x000000aa5fe00000)]
  0x000001b2e0399d00 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=74616, stack(0x000000aa5fe00000,0x000000aa5ff00000)]
  0x000001b2e039a1d0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=74236, stack(0x000000aa5ff00000,0x000000aa60000000)]
  0x000001b2e039a6a0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=65936, stack(0x000000aa60000000,0x000000aa60100000)]
  0x000001b2e0392e50 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=70640, stack(0x000000aa60100000,0x000000aa60200000)]
  0x000001b2e0393320 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=74348, stack(0x000000aa60200000,0x000000aa60300000)]
  0x000001b2e03937f0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=76364, stack(0x000000aa60300000,0x000000aa60400000)]
  0x000001b2e0393cc0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=34608, stack(0x000000aa60400000,0x000000aa60500000)]
  0x000001b2d4cb8fb0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=75820, stack(0x000000aa60500000,0x000000aa60600000)]
  0x000001b2e05e5dd0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=25312, stack(0x000000aa60600000,0x000000aa60700000)]
  0x000001b2e05e62a0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=65776, stack(0x000000aa60700000,0x000000aa60800000)]
  0x000001b2e05e10d0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=19872, stack(0x000000aa60800000,0x000000aa60900000)]
  0x000001b2e05e2db0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=55356, stack(0x000000aa60900000,0x000000aa60a00000)]
  0x000001b2e05e3c20 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=64232, stack(0x000000aa60a00000,0x000000aa60b00000)]
  0x000001b2e05e4f60 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=76460, stack(0x000000aa60b00000,0x000000aa60c00000)]
  0x000001b2e05e6770 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=73976, stack(0x000000aa60c00000,0x000000aa60d00000)]
  0x000001b2e05e7ab0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=10052, stack(0x000000aa60d00000,0x000000aa60e00000)]
  0x000001b2e05e8920 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=69588, stack(0x000000aa60e00000,0x000000aa60f00000)]
  0x000001b2e05e3280 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=74480, stack(0x000000aa60f00000,0x000000aa61000000)]
  0x000001b2e05e5900 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=77800, stack(0x000000aa61000000,0x000000aa61100000)]
  0x000001b2e05e28e0 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=76264, stack(0x000000aa61100000,0x000000aa61200000)]
  0x000001b2e05e6c40 JavaThread "ThreadPoolTaskExecutor-1" [_thread_blocked, id=59780, stack(0x000000aa61200000,0x000000aa61300000)]
  0x000001b2e05e40f0 JavaThread "DestroyJavaVM" [_thread_blocked, id=66792, stack(0x000000aa58400000,0x000000aa58500000)]
  0x000001b2de3bc4d0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=77556, stack(0x000000aa61300000,0x000000aa61400000)]
  0x000001b2e05e7110 JavaThread "MillisecondClock" [_thread_blocked, id=6680, stack(0x000000aa61400000,0x000000aa61500000)]
  0x000001b2e05e75e0 JavaThread "kafka-coordinator-heartbeat-thread | openapi-hooks-case" daemon [_thread_blocked, id=77292, stack(0x000000aa61500000,0x000000aa61600000)]
  0x000001b2e05e7f80 JavaThread "kafka-coordinator-heartbeat-thread | openpai-hooks-felton" daemon [_thread_blocked, id=72736, stack(0x000000aa61600000,0x000000aa61700000)]
  0x000001b2e05e4a90 JavaThread "kafka-coordinator-heartbeat-thread | openapi-hooks-application" daemon [_thread_blocked, id=23820, stack(0x000000aa61700000,0x000000aa61800000)]
  0x000001b2e05e45c0 JavaThread "kafka-coordinator-heartbeat-thread | openapi-work-order-default" daemon [_thread_blocked, id=77056, stack(0x000000aa61800000,0x000000aa61900000)]
  0x000001b2e05e8450 JavaThread "kafka-coordinator-heartbeat-thread | zatech-openapi-platform-SH-NB-038" daemon [_thread_blocked, id=56352, stack(0x000000aa61900000,0x000000aa61a00000)]
  0x000001b2e05e5430 JavaThread "kafka-coordinator-heartbeat-thread | openpai-hooks" daemon [_thread_blocked, id=48056, stack(0x000000aa61a00000,0x000000aa61b00000)]
  0x000001b2e05e1a70 JavaThread "kafka-coordinator-heartbeat-thread | openpai-hooks-mine" daemon [_thread_blocked, id=69680, stack(0x000000aa61b00000,0x000000aa61c00000)]
  0x000001b2e05e1f40 JavaThread "kafka-coordinator-heartbeat-thread | procedure-mine" daemon [_thread_blocked, id=76732, stack(0x000000aa61c00000,0x000000aa61d00000)]
  0x000001b2e05e2410 JavaThread "nacos-grpc-client-executor-mscnew.za-gj-aws.net-29" daemon [_thread_blocked, id=73440, stack(0x000000aa59400000,0x000000aa59500000)]
  0x000001b2de31fdd0 JavaThread "nacos-grpc-client-executor-mscnew.za-gj-aws.net-30" daemon [_thread_blocked, id=69936, stack(0x000000aa61d00000,0x000000aa61e00000)]
  0x000001b2e081ac20 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=17732, stack(0x000000aa61e00000,0x000000aa61f00000)]
  0x000001b2e08150b0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=61144, stack(0x000000aa61f00000,0x000000aa62000000)]
  0x000001b2e0815580 JavaThread "KMP-openapi-platform-genesis-dev1-procedure-mine_T0" [_thread_blocked, id=77508, stack(0x000000aa62100000,0x000000aa62200000)]
  0x000001b2e081a280 JavaThread "KMP-policy-genesis-dev1-openpai-hooks-mine_T0" [_thread_blocked, id=8968, stack(0x000000aa62200000,0x000000aa62300000)]
  0x000001b2e081a750 JavaThread "KMP-openapi-platform-genesis-dev1-procedure-mine_T1" [_thread_blocked, id=77300, stack(0x000000aa62300000,0x000000aa62400000)]
  0x000001b2e0817730 JavaThread "KMP-policy-genesis-dev1-openpai-hooks-mine_T1" [_thread_blocked, id=59048, stack(0x000000aa62400000,0x000000aa62500000)]
  0x000001b2e081b0f0 JavaThread "KMP-openapi-platform-genesis-dev1-procedure-mine_T2" [_thread_blocked, id=66036, stack(0x000000aa62500000,0x000000aa62600000)]
  0x000001b2e081ba90 JavaThread "KMP-policy-genesis-dev1-openpai-hooks-mine_T2" [_thread_blocked, id=77676, stack(0x000000aa62600000,0x000000aa62700000)]
  0x000001b2e0817c00 JavaThread "KMP-posonline-genesis-dev1-openpai-hooks-mine_T0" [_thread_blocked, id=7272, stack(0x000000aa62700000,0x000000aa62800000)]
  0x000001b2e081bf60 JavaThread "KMP-posonline-genesis-dev1-openpai-hooks-mine_T1" [_thread_blocked, id=77444, stack(0x000000aa62800000,0x000000aa62900000)]
  0x000001b2e081c430 JavaThread "KMP-posonline-genesis-dev1-openpai-hooks-mine_T2" [_thread_blocked, id=50836, stack(0x000000aa62900000,0x000000aa62a00000)]
  0x000001b2e081c900 JavaThread "KMP-__consumer_command_request-zatech-openapi-platform-SH-NB-038_T0" [_thread_blocked, id=77436, stack(0x000000aa62a00000,0x000000aa62b00000)]
  0x000001b2e0815a50 JavaThread "KMP-__consumer_command_request-zatech-openapi-platform-SH-NB-038_T1" [_thread_blocked, id=66304, stack(0x000000aa62b00000,0x000000aa62c00000)]
  0x000001b2e08198e0 JavaThread "KMP-__consumer_command_request-zatech-openapi-platform-SH-NB-038_T2" [_thread_blocked, id=77256, stack(0x000000aa62c00000,0x000000aa62d00000)]

Other Threads:
=>0x000001b2d235adb0 VMThread "VM Thread" [stack: 0x000000aa58a00000,0x000000aa58b00000] [id=72560]
  0x000001b2bbab7b60 WatcherThread [stack: 0x000000aa5a400000,0x000000aa5a500000] [id=76688]
  0x000001b2bba99ea0 GCTaskThread "GC Thread#0" [stack: 0x000000aa58500000,0x000000aa58600000] [id=64444]
  0x000001b2d4356940 GCTaskThread "GC Thread#1" [stack: 0x000000aa59a00000,0x000000aa59b00000] [id=73312]
  0x000001b2d4356bf0 GCTaskThread "GC Thread#2" [stack: 0x000000aa59b00000,0x000000aa59c00000] [id=74256]
  0x000001b2d435aeb0 GCTaskThread "GC Thread#3" [stack: 0x000000aa59c00000,0x000000aa59d00000] [id=77296]
  0x000001b2d435b160 GCTaskThread "GC Thread#4" [stack: 0x000000aa59d00000,0x000000aa59e00000] [id=76428]
  0x000001b2d435f420 GCTaskThread "GC Thread#5" [stack: 0x000000aa59e00000,0x000000aa59f00000] [id=76400]
  0x000001b2d4385f00 GCTaskThread "GC Thread#6" [stack: 0x000000aa59f00000,0x000000aa5a000000] [id=76000]
  0x000001b2d43861b0 GCTaskThread "GC Thread#7" [stack: 0x000000aa5a000000,0x000000aa5a100000] [id=77756]
  0x000001b2bbaab9d0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000aa58600000,0x000000aa58700000] [id=29056]
  0x000001b2bbaac2f0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000aa58700000,0x000000aa58800000] [id=72928]
  0x000001b2d32f2df0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000aa5a900000,0x000000aa5aa00000] [id=72792]
  0x000001b2bbaedf80 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000aa58800000,0x000000aa58900000] [id=68324]
  0x000001b2d4386460 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000aa5a100000,0x000000aa5a200000] [id=44792]
  0x000001b2e004b660 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000aa5e500000,0x000000aa5e600000] [id=75640]
  0x000001b2dec18770 ConcurrentGCThread "G1 Refine#3" [stack: 0x000000aa62000000,0x000000aa62100000] [id=76836]
  0x000001b2bbaee7b0 ConcurrentGCThread "G1 Service" [stack: 0x000000aa58900000,0x000000aa58a00000] [id=76296]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001b2bba2a4c0] Threads_lock - owner thread: 0x000001b2d235adb0
[0x000001b2bba2b090] Heap_lock - owner thread: 0x000001b2bbaab9d0

Heap address: 0x0000000703200000, size: 4046 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bc0000-0x0000000800bc0000), size 12320768, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 8 total, 8 available
 Memory: 16181M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4046M
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 772096K, used 479871K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 65 young (133120K), 15 survivors (30720K)
 Metaspace       used 151732K, committed 153088K, reserved 1187840K
  class space    used 19472K, committed 20096K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| O|  |TAMS 0x0000000703400000, 0x0000000703200000| Untracked 
|   1|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000, 0x0000000703400000| Untracked 
|   2|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703600000| Untracked 
|   3|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703800000| Untracked 
|   4|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703c00000, 0x0000000703a00000| Untracked 
|   5|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703c00000| Untracked 
|   6|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000704000000, 0x0000000703e00000| Untracked 
|   7|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704000000| Untracked 
|   8|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704200000| Untracked 
|   9|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704600000, 0x0000000704400000| Untracked 
|  10|0x0000000704600000, 0x0000000704800000, 0x0000000704800000|100%| O|  |TAMS 0x0000000704800000, 0x0000000704600000| Untracked 
|  11|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704a00000, 0x0000000704800000| Updating 
|  12|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704c00000, 0x0000000704a00000| Untracked 
|  13|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704e00000, 0x0000000704c00000| Updating 
|  14|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000705000000, 0x0000000704e00000| Updating 
|  15|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705200000, 0x0000000705000000| Updating 
|  16|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705400000, 0x0000000705200000| Updating 
|  17|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705600000, 0x0000000705400000| Updating 
|  18|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705800000, 0x0000000705600000| Untracked 
|  19|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705800000| Untracked 
|  20|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705a00000| Untracked 
|  21|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705c00000| Updating 
|  22|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000706000000, 0x0000000705e00000| Updating 
|  23|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706200000, 0x0000000706000000| Updating 
|  24|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706400000, 0x0000000706200000| Updating 
|  25|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706600000, 0x0000000706400000| Updating 
|  26|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706800000, 0x0000000706600000| Updating 
|  27|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706a00000, 0x0000000706800000| Updating 
|  28|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706c00000, 0x0000000706a00000| Updating 
|  29|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706e00000, 0x0000000706c00000| Updating 
|  30|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000707000000, 0x0000000706e00000| Updating 
|  31|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707200000, 0x0000000707000000| Updating 
|  32|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707400000, 0x0000000707200000| Updating 
|  33|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707600000, 0x0000000707400000| Updating 
|  34|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707800000, 0x0000000707600000| Updating 
|  35|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707a00000, 0x0000000707800000| Updating 
|  36|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707c00000, 0x0000000707a00000| Untracked 
|  37|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707e00000, 0x0000000707c00000| Updating 
|  38|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000708000000, 0x0000000707e00000| Updating 
|  39|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708200000, 0x0000000708000000| Updating 
|  40|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708400000, 0x0000000708200000| Updating 
|  41|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708600000, 0x0000000708400000| Updating 
|  42|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708800000, 0x0000000708600000| Untracked 
|  43|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708a00000, 0x0000000708800000| Updating 
|  44|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708c00000, 0x0000000708a00000| Updating 
|  45|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708e00000, 0x0000000708c00000| Updating 
|  46|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|  |TAMS 0x0000000709000000, 0x0000000708e00000| Updating 
|  47|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| O|  |TAMS 0x0000000709200000, 0x0000000709000000| Updating 
|  48|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709400000, 0x0000000709200000| Untracked 
|  49|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709600000, 0x0000000709400000| Untracked 
|  50|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709800000, 0x0000000709600000| Untracked 
|  51|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709a00000, 0x0000000709800000| Untracked 
|  52|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709c00000, 0x0000000709a00000| Updating 
|  53|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| O|  |TAMS 0x0000000709e00000, 0x0000000709c00000| Updating 
|  54|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| O|  |TAMS 0x000000070a000000, 0x0000000709e00000| Updating 
|  55|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| O|  |TAMS 0x000000070a200000, 0x000000070a000000| Untracked 
|  56|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| O|  |TAMS 0x000000070a400000, 0x000000070a200000| Untracked 
|  57|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| O|  |TAMS 0x000000070a600000, 0x000000070a400000| Untracked 
|  58|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a800000, 0x000000070a600000| Untracked 
|  59|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| O|  |TAMS 0x000000070aa00000, 0x000000070a800000| Untracked 
|  60|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|  |TAMS 0x000000070ac00000, 0x000000070aa00000| Updating 
|  61|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ae00000, 0x000000070ac00000| Updating 
|  62|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070b000000, 0x000000070ae00000| Updating 
|  63|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| O|  |TAMS 0x000000070b200000, 0x000000070b000000| Untracked 
|  64|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| O|  |TAMS 0x000000070b400000, 0x000000070b200000| Updating 
|  65|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| O|  |TAMS 0x000000070b600000, 0x000000070b400000| Updating 
|  66|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%| O|  |TAMS 0x000000070b800000, 0x000000070b600000| Updating 
|  67|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| O|  |TAMS 0x000000070ba00000, 0x000000070b800000| Updating 
|  68|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| O|  |TAMS 0x000000070bc00000, 0x000000070ba00000| Updating 
|  69|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| O|  |TAMS 0x000000070be00000, 0x000000070bc00000| Untracked 
|  70|0x000000070be00000, 0x000000070c000000, 0x000000070c000000|100%| O|  |TAMS 0x000000070c000000, 0x000000070be00000| Updating 
|  71|0x000000070c000000, 0x000000070c200000, 0x000000070c200000|100%| O|  |TAMS 0x000000070c200000, 0x000000070c000000| Untracked 
|  72|0x000000070c200000, 0x000000070c400000, 0x000000070c400000|100%| O|  |TAMS 0x000000070c400000, 0x000000070c200000| Updating 
|  73|0x000000070c400000, 0x000000070c600000, 0x000000070c600000|100%| O|  |TAMS 0x000000070c600000, 0x000000070c400000| Updating 
|  74|0x000000070c600000, 0x000000070c800000, 0x000000070c800000|100%| O|  |TAMS 0x000000070c800000, 0x000000070c600000| Untracked 
|  75|0x000000070c800000, 0x000000070ca00000, 0x000000070ca00000|100%| O|  |TAMS 0x000000070ca00000, 0x000000070c800000| Untracked 
|  76|0x000000070ca00000, 0x000000070cc00000, 0x000000070cc00000|100%| O|  |TAMS 0x000000070cc00000, 0x000000070ca00000| Untracked 
|  77|0x000000070cc00000, 0x000000070ce00000, 0x000000070ce00000|100%| O|  |TAMS 0x000000070ce00000, 0x000000070cc00000| Untracked 
|  78|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%| O|  |TAMS 0x000000070d000000, 0x000000070ce00000| Untracked 
|  79|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%| O|  |TAMS 0x000000070d200000, 0x000000070d000000| Untracked 
|  80|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%| O|  |TAMS 0x000000070d400000, 0x000000070d200000| Updating 
|  81|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%| O|  |TAMS 0x000000070d600000, 0x000000070d400000| Updating 
|  82|0x000000070d600000, 0x000000070d800000, 0x000000070d800000|100%| O|  |TAMS 0x000000070d800000, 0x000000070d600000| Updating 
|  83|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| O|  |TAMS 0x000000070da00000, 0x000000070d800000| Updating 
|  84|0x000000070da00000, 0x000000070dc00000, 0x000000070dc00000|100%| O|  |TAMS 0x000000070dc00000, 0x000000070da00000| Updating 
|  85|0x000000070dc00000, 0x000000070de00000, 0x000000070de00000|100%| O|  |TAMS 0x000000070de00000, 0x000000070dc00000| Untracked 
|  86|0x000000070de00000, 0x000000070e000000, 0x000000070e000000|100%|HS|  |TAMS 0x000000070e000000, 0x000000070de00000| Complete 
|  87|0x000000070e000000, 0x000000070e200000, 0x000000070e200000|100%|HC|  |TAMS 0x000000070e200000, 0x000000070e000000| Complete 
|  88|0x000000070e200000, 0x000000070e400000, 0x000000070e400000|100%|HC|  |TAMS 0x000000070e400000, 0x000000070e200000| Complete 
|  89|0x000000070e400000, 0x000000070e600000, 0x000000070e600000|100%|HS|  |TAMS 0x000000070e600000, 0x000000070e400000| Complete 
|  90|0x000000070e600000, 0x000000070e800000, 0x000000070e800000|100%|HC|  |TAMS 0x000000070e800000, 0x000000070e600000| Complete 
|  91|0x000000070e800000, 0x000000070ea00000, 0x000000070ea00000|100%|HC|  |TAMS 0x000000070ea00000, 0x000000070e800000| Complete 
|  92|0x000000070ea00000, 0x000000070ec00000, 0x000000070ec00000|100%| O|  |TAMS 0x000000070ec00000, 0x000000070ea00000| Untracked 
|  93|0x000000070ec00000, 0x000000070ee00000, 0x000000070ee00000|100%| O|  |TAMS 0x000000070ee00000, 0x000000070ec00000| Untracked 
|  94|0x000000070ee00000, 0x000000070f000000, 0x000000070f000000|100%| O|  |TAMS 0x000000070f000000, 0x000000070ee00000| Untracked 
|  95|0x000000070f000000, 0x000000070f200000, 0x000000070f200000|100%| O|  |TAMS 0x000000070f200000, 0x000000070f000000| Untracked 
|  96|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| O|  |TAMS 0x000000070f400000, 0x000000070f200000| Untracked 
|  97|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| O|  |TAMS 0x000000070f600000, 0x000000070f400000| Untracked 
|  98|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%|HS|  |TAMS 0x000000070f600000, 0x000000070f600000| Complete 
|  99|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%|HC|  |TAMS 0x000000070f800000, 0x000000070f800000| Complete 
| 100|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%|HS|  |TAMS 0x000000070fa00000, 0x000000070fa00000| Complete 
| 101|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%|HC|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Complete 
| 102|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%|HC|  |TAMS 0x000000070fe00000, 0x000000070fe00000| Complete 
| 103|0x0000000710000000, 0x0000000710200000, 0x0000000710200000|100%|HC|  |TAMS 0x0000000710000000, 0x0000000710000000| Complete 
| 104|0x0000000710200000, 0x0000000710400000, 0x0000000710400000|100%|HS|  |TAMS 0x0000000710400000, 0x0000000710200000| Complete 
| 105|0x0000000710400000, 0x0000000710600000, 0x0000000710600000|100%|HC|  |TAMS 0x0000000710600000, 0x0000000710400000| Complete 
| 106|0x0000000710600000, 0x0000000710800000, 0x0000000710800000|100%|HC|  |TAMS 0x0000000710800000, 0x0000000710600000| Complete 
| 107|0x0000000710800000, 0x0000000710a00000, 0x0000000710a00000|100%|HC|  |TAMS 0x0000000710a00000, 0x0000000710800000| Complete 
| 108|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000, 0x0000000710a00000| Untracked 
| 109|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000, 0x0000000710c00000| Untracked 
| 110|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000, 0x0000000710e00000| Untracked 
| 111|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000, 0x0000000711000000| Untracked 
| 112|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%|HS|  |TAMS 0x0000000711400000, 0x0000000711200000| Complete 
| 113|0x0000000711400000, 0x0000000711600000, 0x0000000711600000|100%|HC|  |TAMS 0x0000000711600000, 0x0000000711400000| Complete 
| 114|0x0000000711600000, 0x0000000711800000, 0x0000000711800000|100%|HC|  |TAMS 0x0000000711800000, 0x0000000711600000| Complete 
| 115|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%|HC|  |TAMS 0x0000000711a00000, 0x0000000711800000| Complete 
| 116|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| O|  |TAMS 0x0000000711c00000, 0x0000000711a00000| Untracked 
| 117|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| O|  |TAMS 0x0000000711e00000, 0x0000000711c00000| Untracked 
| 118|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| O|  |TAMS 0x0000000712000000, 0x0000000711e00000| Untracked 
| 119|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| O|  |TAMS 0x0000000712200000, 0x0000000712000000| Untracked 
| 120|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| O|  |TAMS 0x0000000712400000, 0x0000000712200000| Untracked 
| 121|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| O|  |TAMS 0x0000000712600000, 0x0000000712400000| Untracked 
| 122|0x0000000712600000, 0x0000000712800000, 0x0000000712800000|100%| O|  |TAMS 0x0000000712800000, 0x0000000712600000| Updating 
| 123|0x0000000712800000, 0x0000000712a00000, 0x0000000712a00000|100%| O|  |TAMS 0x0000000712a00000, 0x0000000712800000| Updating 
| 124|0x0000000712a00000, 0x0000000712c00000, 0x0000000712c00000|100%| O|  |TAMS 0x0000000712c00000, 0x0000000712a00000| Updating 
| 125|0x0000000712c00000, 0x0000000712e00000, 0x0000000712e00000|100%|HS|  |TAMS 0x0000000712e00000, 0x0000000712c00000| Complete 
| 126|0x0000000712e00000, 0x0000000713000000, 0x0000000713000000|100%|HC|  |TAMS 0x0000000713000000, 0x0000000712e00000| Complete 
| 127|0x0000000713000000, 0x0000000713200000, 0x0000000713200000|100%|HC|  |TAMS 0x0000000713200000, 0x0000000713000000| Complete 
| 128|0x0000000713200000, 0x0000000713400000, 0x0000000713400000|100%|HC|  |TAMS 0x0000000713400000, 0x0000000713200000| Complete 
| 129|0x0000000713400000, 0x0000000713400000, 0x0000000713600000|  0%| F|  |TAMS 0x0000000713400000, 0x0000000713400000| Untracked 
| 130|0x0000000713600000, 0x0000000713600000, 0x0000000713800000|  0%| F|  |TAMS 0x0000000713600000, 0x0000000713600000| Untracked 
| 131|0x0000000713800000, 0x0000000713a00000, 0x0000000713a00000|100%|HS|  |TAMS 0x0000000713a00000, 0x0000000713800000| Complete 
| 132|0x0000000713a00000, 0x0000000713c00000, 0x0000000713c00000|100%|HC|  |TAMS 0x0000000713c00000, 0x0000000713a00000| Complete 
| 133|0x0000000713c00000, 0x0000000713e00000, 0x0000000713e00000|100%|HC|  |TAMS 0x0000000713e00000, 0x0000000713c00000| Complete 
| 134|0x0000000713e00000, 0x0000000714000000, 0x0000000714000000|100%|HC|  |TAMS 0x0000000714000000, 0x0000000713e00000| Complete 
| 135|0x0000000714000000, 0x0000000714200000, 0x0000000714200000|100%| O|  |TAMS 0x0000000714000000, 0x0000000714000000| Untracked 
| 136|0x0000000714200000, 0x0000000714400000, 0x0000000714400000|100%| O|  |TAMS 0x0000000714200000, 0x0000000714200000| Untracked 
| 137|0x0000000714400000, 0x0000000714600000, 0x0000000714600000|100%| O|  |TAMS 0x0000000714400000, 0x0000000714400000| Untracked 
| 138|0x0000000714600000, 0x0000000714800000, 0x0000000714800000|100%| O|  |TAMS 0x0000000714600000, 0x0000000714600000| Untracked 
| 139|0x0000000714800000, 0x0000000714a00000, 0x0000000714a00000|100%| O|  |TAMS 0x0000000714800000, 0x0000000714800000| Untracked 
| 140|0x0000000714a00000, 0x0000000714a00000, 0x0000000714c00000|  0%| F|  |TAMS 0x0000000714a00000, 0x0000000714a00000| Untracked 
| 141|0x0000000714c00000, 0x0000000714c00000, 0x0000000714e00000|  0%| F|  |TAMS 0x0000000714c00000, 0x0000000714c00000| Untracked 
| 142|0x0000000714e00000, 0x0000000715000000, 0x0000000715000000|100%| O|  |TAMS 0x0000000715000000, 0x0000000714e00000| Untracked 
| 143|0x0000000715000000, 0x0000000715200000, 0x0000000715200000|100%| O|  |TAMS 0x0000000715200000, 0x0000000715000000| Untracked 
| 144|0x0000000715200000, 0x0000000715400000, 0x0000000715400000|100%| O|  |TAMS 0x0000000715400000, 0x0000000715200000| Untracked 
| 145|0x0000000715400000, 0x0000000715600000, 0x0000000715600000|100%| O|  |TAMS 0x0000000715600000, 0x0000000715400000| Untracked 
| 146|0x0000000715600000, 0x0000000715800000, 0x0000000715800000|100%| O|  |TAMS 0x0000000715800000, 0x0000000715600000| Untracked 
| 147|0x0000000715800000, 0x0000000715a00000, 0x0000000715a00000|100%| O|  |TAMS 0x0000000715a00000, 0x0000000715800000| Untracked 
| 148|0x0000000715a00000, 0x0000000715c00000, 0x0000000715c00000|100%| O|  |TAMS 0x0000000715c00000, 0x0000000715a00000| Untracked 
| 149|0x0000000715c00000, 0x0000000715e00000, 0x0000000715e00000|100%| O|  |TAMS 0x0000000715e00000, 0x0000000715c00000| Untracked 
| 150|0x0000000715e00000, 0x0000000716000000, 0x0000000716000000|100%| O|  |TAMS 0x0000000716000000, 0x0000000715e00000| Untracked 
| 151|0x0000000716000000, 0x0000000716200000, 0x0000000716200000|100%| O|  |TAMS 0x0000000716200000, 0x0000000716000000| Untracked 
| 152|0x0000000716200000, 0x0000000716400000, 0x0000000716400000|100%| O|  |TAMS 0x0000000716400000, 0x0000000716200000| Untracked 
| 153|0x0000000716400000, 0x0000000716600000, 0x0000000716600000|100%| O|  |TAMS 0x0000000716600000, 0x0000000716400000| Untracked 
| 154|0x0000000716600000, 0x0000000716800000, 0x0000000716800000|100%| O|  |TAMS 0x0000000716800000, 0x0000000716600000| Untracked 
| 155|0x0000000716800000, 0x0000000716a00000, 0x0000000716a00000|100%| O|  |TAMS 0x0000000716a00000, 0x0000000716800000| Untracked 
| 156|0x0000000716a00000, 0x0000000716c00000, 0x0000000716c00000|100%| O|  |TAMS 0x0000000716c00000, 0x0000000716a00000| Updating 
| 157|0x0000000716c00000, 0x0000000716e00000, 0x0000000716e00000|100%| O|  |TAMS 0x0000000716e00000, 0x0000000716c00000| Untracked 
| 158|0x0000000716e00000, 0x0000000717000000, 0x0000000717000000|100%| O|  |TAMS 0x0000000717000000, 0x0000000716e00000| Untracked 
| 159|0x0000000717000000, 0x0000000717200000, 0x0000000717200000|100%| O|  |TAMS 0x0000000717200000, 0x0000000717000000| Untracked 
| 160|0x0000000717200000, 0x0000000717400000, 0x0000000717400000|100%| O|  |TAMS 0x0000000717400000, 0x0000000717200000| Untracked 
| 161|0x0000000717400000, 0x0000000717600000, 0x0000000717600000|100%| O|  |TAMS 0x0000000717600000, 0x0000000717400000| Untracked 
| 162|0x0000000717600000, 0x0000000717800000, 0x0000000717800000|100%| O|  |TAMS 0x0000000717800000, 0x0000000717600000| Untracked 
| 163|0x0000000717800000, 0x0000000717a00000, 0x0000000717a00000|100%| O|  |TAMS 0x0000000717a00000, 0x0000000717800000| Untracked 
| 164|0x0000000717a00000, 0x0000000717c00000, 0x0000000717c00000|100%| O|  |TAMS 0x0000000717c00000, 0x0000000717a00000| Updating 
| 165|0x0000000717c00000, 0x0000000717e00000, 0x0000000717e00000|100%| O|  |TAMS 0x0000000717c00000, 0x0000000717c00000| Untracked 
| 166|0x0000000717e00000, 0x0000000718000000, 0x0000000718000000|100%| O|  |TAMS 0x0000000717e00000, 0x0000000717e00000| Untracked 
| 167|0x0000000718000000, 0x0000000718200000, 0x0000000718200000|100%| O|  |TAMS 0x0000000718000000, 0x0000000718000000| Untracked 
| 168|0x0000000718200000, 0x0000000718400000, 0x0000000718400000|100%| O|  |TAMS 0x0000000718200000, 0x0000000718200000| Untracked 
| 169|0x0000000718400000, 0x0000000718600000, 0x0000000718600000|100%| O|  |TAMS 0x0000000718400000, 0x0000000718400000| Untracked 
| 170|0x0000000718600000, 0x0000000718800000, 0x0000000718800000|100%| O|  |TAMS 0x0000000718600000, 0x0000000718600000| Untracked 
| 171|0x0000000718800000, 0x0000000718a00000, 0x0000000718a00000|100%| O|  |TAMS 0x0000000718800000, 0x0000000718800000| Untracked 
| 172|0x0000000718a00000, 0x0000000718c00000, 0x0000000718c00000|100%| O|  |TAMS 0x0000000718a00000, 0x0000000718a00000| Untracked 
| 173|0x0000000718c00000, 0x0000000718e00000, 0x0000000718e00000|100%| O|  |TAMS 0x0000000718c00000, 0x0000000718c00000| Untracked 
| 174|0x0000000718e00000, 0x0000000719000000, 0x0000000719000000|100%| O|  |TAMS 0x0000000718e00000, 0x0000000718e00000| Untracked 
| 175|0x0000000719000000, 0x0000000719200000, 0x0000000719200000|100%| O|  |TAMS 0x0000000719000000, 0x0000000719000000| Untracked 
| 176|0x0000000719200000, 0x0000000719400000, 0x0000000719400000|100%| O|  |TAMS 0x0000000719200000, 0x0000000719200000| Untracked 
| 177|0x0000000719400000, 0x0000000719600000, 0x0000000719600000|100%| O|  |TAMS 0x0000000719400000, 0x0000000719400000| Untracked 
| 178|0x0000000719600000, 0x0000000719800000, 0x0000000719800000|100%| O|  |TAMS 0x0000000719600000, 0x0000000719600000| Untracked 
| 179|0x0000000719800000, 0x00000007199a3a00, 0x0000000719a00000| 81%| O|  |TAMS 0x0000000719800000, 0x0000000719800000| Updating 
| 180|0x0000000719a00000, 0x0000000719a00000, 0x0000000719c00000|  0%| F|  |TAMS 0x0000000719a00000, 0x0000000719a00000| Untracked 
| 181|0x0000000719c00000, 0x0000000719c00000, 0x0000000719e00000|  0%| F|  |TAMS 0x0000000719c00000, 0x0000000719c00000| Untracked 
| 182|0x0000000719e00000, 0x0000000719e00000, 0x000000071a000000|  0%| F|  |TAMS 0x0000000719e00000, 0x0000000719e00000| Untracked 
| 183|0x000000071a000000, 0x000000071a000000, 0x000000071a200000|  0%| F|  |TAMS 0x000000071a000000, 0x000000071a000000| Untracked 
| 184|0x000000071a200000, 0x000000071a200000, 0x000000071a400000|  0%| F|  |TAMS 0x000000071a200000, 0x000000071a200000| Untracked 
| 185|0x000000071a400000, 0x000000071a400000, 0x000000071a600000|  0%| F|  |TAMS 0x000000071a400000, 0x000000071a400000| Untracked 
| 186|0x000000071a600000, 0x000000071a600000, 0x000000071a800000|  0%| F|  |TAMS 0x000000071a600000, 0x000000071a600000| Untracked 
| 187|0x000000071a800000, 0x000000071a800000, 0x000000071aa00000|  0%| F|  |TAMS 0x000000071a800000, 0x000000071a800000| Untracked 
| 188|0x000000071aa00000, 0x000000071aa00000, 0x000000071ac00000|  0%| F|  |TAMS 0x000000071aa00000, 0x000000071aa00000| Untracked 
| 189|0x000000071ac00000, 0x000000071ac00000, 0x000000071ae00000|  0%| F|  |TAMS 0x000000071ac00000, 0x000000071ac00000| Untracked 
| 190|0x000000071ae00000, 0x000000071ae00000, 0x000000071b000000|  0%| F|  |TAMS 0x000000071ae00000, 0x000000071ae00000| Untracked 
| 191|0x000000071b000000, 0x000000071b000000, 0x000000071b200000|  0%| F|  |TAMS 0x000000071b000000, 0x000000071b000000| Untracked 
| 192|0x000000071b200000, 0x000000071b200000, 0x000000071b400000|  0%| F|  |TAMS 0x000000071b200000, 0x000000071b200000| Untracked 
| 193|0x000000071b400000, 0x000000071b400000, 0x000000071b600000|  0%| F|  |TAMS 0x000000071b400000, 0x000000071b400000| Untracked 
| 194|0x000000071b600000, 0x000000071b600000, 0x000000071b800000|  0%| F|  |TAMS 0x000000071b600000, 0x000000071b600000| Untracked 
| 195|0x000000071b800000, 0x000000071b800000, 0x000000071ba00000|  0%| F|  |TAMS 0x000000071b800000, 0x000000071b800000| Untracked 
| 196|0x000000071ba00000, 0x000000071ba00000, 0x000000071bc00000|  0%| F|  |TAMS 0x000000071ba00000, 0x000000071ba00000| Untracked 
| 197|0x000000071bc00000, 0x000000071bc00000, 0x000000071be00000|  0%| F|  |TAMS 0x000000071bc00000, 0x000000071bc00000| Untracked 
| 198|0x000000071be00000, 0x000000071be00000, 0x000000071c000000|  0%| F|  |TAMS 0x000000071be00000, 0x000000071be00000| Untracked 
| 199|0x000000071c000000, 0x000000071c000000, 0x000000071c200000|  0%| F|  |TAMS 0x000000071c000000, 0x000000071c000000| Untracked 
| 200|0x000000071c200000, 0x000000071c200000, 0x000000071c400000|  0%| F|  |TAMS 0x000000071c200000, 0x000000071c200000| Untracked 
| 201|0x000000071c400000, 0x000000071c400000, 0x000000071c600000|  0%| F|  |TAMS 0x000000071c400000, 0x000000071c400000| Untracked 
| 202|0x000000071c600000, 0x000000071c600000, 0x000000071c800000|  0%| F|  |TAMS 0x000000071c600000, 0x000000071c600000| Untracked 
| 203|0x000000071c800000, 0x000000071c800000, 0x000000071ca00000|  0%| F|  |TAMS 0x000000071c800000, 0x000000071c800000| Untracked 
| 204|0x000000071ca00000, 0x000000071ca00000, 0x000000071cc00000|  0%| F|  |TAMS 0x000000071ca00000, 0x000000071ca00000| Untracked 
| 205|0x000000071cc00000, 0x000000071cc00000, 0x000000071ce00000|  0%| F|  |TAMS 0x000000071cc00000, 0x000000071cc00000| Untracked 
| 206|0x000000071ce00000, 0x000000071ce00000, 0x000000071d000000|  0%| F|  |TAMS 0x000000071ce00000, 0x000000071ce00000| Untracked 
| 207|0x000000071d000000, 0x000000071d000000, 0x000000071d200000|  0%| F|  |TAMS 0x000000071d000000, 0x000000071d000000| Untracked 
| 208|0x000000071d200000, 0x000000071d200000, 0x000000071d400000|  0%| F|  |TAMS 0x000000071d200000, 0x000000071d200000| Untracked 
| 209|0x000000071d400000, 0x000000071d400000, 0x000000071d600000|  0%| F|  |TAMS 0x000000071d400000, 0x000000071d400000| Untracked 
| 210|0x000000071d600000, 0x000000071d600000, 0x000000071d800000|  0%| F|  |TAMS 0x000000071d600000, 0x000000071d600000| Untracked 
| 211|0x000000071d800000, 0x000000071d800000, 0x000000071da00000|  0%| F|  |TAMS 0x000000071d800000, 0x000000071d800000| Untracked 
| 212|0x000000071da00000, 0x000000071da00000, 0x000000071dc00000|  0%| F|  |TAMS 0x000000071da00000, 0x000000071da00000| Untracked 
| 213|0x000000071dc00000, 0x000000071dc00000, 0x000000071de00000|  0%| F|  |TAMS 0x000000071dc00000, 0x000000071dc00000| Untracked 
| 214|0x000000071de00000, 0x000000071de00000, 0x000000071e000000|  0%| F|  |TAMS 0x000000071de00000, 0x000000071de00000| Untracked 
| 215|0x000000071e000000, 0x000000071e000000, 0x000000071e200000|  0%| F|  |TAMS 0x000000071e000000, 0x000000071e000000| Untracked 
| 216|0x000000071e200000, 0x000000071e200000, 0x000000071e400000|  0%| F|  |TAMS 0x000000071e200000, 0x000000071e200000| Untracked 
| 217|0x000000071e400000, 0x000000071e400000, 0x000000071e600000|  0%| F|  |TAMS 0x000000071e400000, 0x000000071e400000| Untracked 
| 218|0x000000071e600000, 0x000000071e600000, 0x000000071e800000|  0%| F|  |TAMS 0x000000071e600000, 0x000000071e600000| Untracked 
| 219|0x000000071e800000, 0x000000071e800000, 0x000000071ea00000|  0%| F|  |TAMS 0x000000071e800000, 0x000000071e800000| Untracked 
| 220|0x000000071ea00000, 0x000000071ea00000, 0x000000071ec00000|  0%| F|  |TAMS 0x000000071ea00000, 0x000000071ea00000| Untracked 
| 221|0x000000071ec00000, 0x000000071ec00000, 0x000000071ee00000|  0%| F|  |TAMS 0x000000071ec00000, 0x000000071ec00000| Untracked 
| 222|0x000000071ee00000, 0x000000071ee00000, 0x000000071f000000|  0%| F|  |TAMS 0x000000071ee00000, 0x000000071ee00000| Untracked 
| 223|0x000000071f000000, 0x000000071f000000, 0x000000071f200000|  0%| F|  |TAMS 0x000000071f000000, 0x000000071f000000| Untracked 
| 224|0x000000071f200000, 0x000000071f200000, 0x000000071f400000|  0%| F|  |TAMS 0x000000071f200000, 0x000000071f200000| Untracked 
| 225|0x000000071f400000, 0x000000071f400000, 0x000000071f600000|  0%| F|  |TAMS 0x000000071f400000, 0x000000071f400000| Untracked 
| 226|0x000000071f600000, 0x000000071f600000, 0x000000071f800000|  0%| F|  |TAMS 0x000000071f600000, 0x000000071f600000| Untracked 
| 227|0x000000071f800000, 0x000000071f800000, 0x000000071fa00000|  0%| F|  |TAMS 0x000000071f800000, 0x000000071f800000| Untracked 
| 228|0x000000071fa00000, 0x000000071fa00000, 0x000000071fc00000|  0%| F|  |TAMS 0x000000071fa00000, 0x000000071fa00000| Untracked 
| 229|0x000000071fc00000, 0x000000071fc00000, 0x000000071fe00000|  0%| F|  |TAMS 0x000000071fc00000, 0x000000071fc00000| Untracked 
| 230|0x000000071fe00000, 0x000000071fe00000, 0x0000000720000000|  0%| F|  |TAMS 0x000000071fe00000, 0x000000071fe00000| Untracked 
| 231|0x0000000720000000, 0x0000000720000000, 0x0000000720200000|  0%| F|  |TAMS 0x0000000720000000, 0x0000000720000000| Untracked 
| 232|0x0000000720200000, 0x0000000720200000, 0x0000000720400000|  0%| F|  |TAMS 0x0000000720200000, 0x0000000720200000| Untracked 
| 233|0x0000000720400000, 0x0000000720400000, 0x0000000720600000|  0%| F|  |TAMS 0x0000000720400000, 0x0000000720400000| Untracked 
| 234|0x0000000720600000, 0x0000000720600000, 0x0000000720800000|  0%| F|  |TAMS 0x0000000720600000, 0x0000000720600000| Untracked 
| 235|0x0000000720800000, 0x0000000720800000, 0x0000000720a00000|  0%| F|  |TAMS 0x0000000720800000, 0x0000000720800000| Untracked 
| 236|0x0000000720a00000, 0x0000000720a00000, 0x0000000720c00000|  0%| F|  |TAMS 0x0000000720a00000, 0x0000000720a00000| Untracked 
| 237|0x0000000720c00000, 0x0000000720c00000, 0x0000000720e00000|  0%| F|  |TAMS 0x0000000720c00000, 0x0000000720c00000| Untracked 
| 238|0x0000000720e00000, 0x0000000720e00000, 0x0000000721000000|  0%| F|  |TAMS 0x0000000720e00000, 0x0000000720e00000| Untracked 
| 239|0x0000000721000000, 0x0000000721000000, 0x0000000721200000|  0%| F|  |TAMS 0x0000000721000000, 0x0000000721000000| Untracked 
| 240|0x0000000721200000, 0x0000000721200000, 0x0000000721400000|  0%| F|  |TAMS 0x0000000721200000, 0x0000000721200000| Untracked 
| 241|0x0000000721400000, 0x0000000721400000, 0x0000000721600000|  0%| F|  |TAMS 0x0000000721400000, 0x0000000721400000| Untracked 
| 242|0x0000000721600000, 0x0000000721600000, 0x0000000721800000|  0%| F|  |TAMS 0x0000000721600000, 0x0000000721600000| Untracked 
| 243|0x0000000721800000, 0x0000000721800000, 0x0000000721a00000|  0%| F|  |TAMS 0x0000000721800000, 0x0000000721800000| Untracked 
| 244|0x0000000721a00000, 0x0000000721a00000, 0x0000000721c00000|  0%| F|  |TAMS 0x0000000721a00000, 0x0000000721a00000| Untracked 
| 245|0x0000000721c00000, 0x0000000721c00000, 0x0000000721e00000|  0%| F|  |TAMS 0x0000000721c00000, 0x0000000721c00000| Untracked 
| 246|0x0000000721e00000, 0x0000000721e00000, 0x0000000722000000|  0%| F|  |TAMS 0x0000000721e00000, 0x0000000721e00000| Untracked 
| 247|0x0000000722000000, 0x0000000722000000, 0x0000000722200000|  0%| F|  |TAMS 0x0000000722000000, 0x0000000722000000| Untracked 
| 248|0x0000000722200000, 0x0000000722200000, 0x0000000722400000|  0%| F|  |TAMS 0x0000000722200000, 0x0000000722200000| Untracked 
| 249|0x0000000722400000, 0x0000000722400000, 0x0000000722600000|  0%| F|  |TAMS 0x0000000722400000, 0x0000000722400000| Untracked 
| 250|0x0000000722600000, 0x0000000722600000, 0x0000000722800000|  0%| F|  |TAMS 0x0000000722600000, 0x0000000722600000| Untracked 
| 251|0x0000000722800000, 0x0000000722800000, 0x0000000722a00000|  0%| F|  |TAMS 0x0000000722800000, 0x0000000722800000| Untracked 
| 252|0x0000000722a00000, 0x0000000722a00000, 0x0000000722c00000|  0%| F|  |TAMS 0x0000000722a00000, 0x0000000722a00000| Untracked 
| 253|0x0000000722c00000, 0x0000000722c00000, 0x0000000722e00000|  0%| F|  |TAMS 0x0000000722c00000, 0x0000000722c00000| Untracked 
| 254|0x0000000722e00000, 0x0000000722e00000, 0x0000000723000000|  0%| F|  |TAMS 0x0000000722e00000, 0x0000000722e00000| Untracked 
| 255|0x0000000723000000, 0x0000000723000000, 0x0000000723200000|  0%| F|  |TAMS 0x0000000723000000, 0x0000000723000000| Untracked 
| 256|0x0000000723200000, 0x0000000723200000, 0x0000000723400000|  0%| F|  |TAMS 0x0000000723200000, 0x0000000723200000| Untracked 
| 257|0x0000000723400000, 0x0000000723400000, 0x0000000723600000|  0%| F|  |TAMS 0x0000000723400000, 0x0000000723400000| Untracked 
| 258|0x0000000723600000, 0x0000000723600000, 0x0000000723800000|  0%| F|  |TAMS 0x0000000723600000, 0x0000000723600000| Untracked 
| 259|0x0000000723800000, 0x0000000723800000, 0x0000000723a00000|  0%| F|  |TAMS 0x0000000723800000, 0x0000000723800000| Untracked 
| 260|0x0000000723a00000, 0x0000000723a00000, 0x0000000723c00000|  0%| F|  |TAMS 0x0000000723a00000, 0x0000000723a00000| Untracked 
| 261|0x0000000723c00000, 0x0000000723c00000, 0x0000000723e00000|  0%| F|  |TAMS 0x0000000723c00000, 0x0000000723c00000| Untracked 
| 262|0x0000000723e00000, 0x0000000723e00000, 0x0000000724000000|  0%| F|  |TAMS 0x0000000723e00000, 0x0000000723e00000| Untracked 
| 263|0x0000000724000000, 0x0000000724000000, 0x0000000724200000|  0%| F|  |TAMS 0x0000000724000000, 0x0000000724000000| Untracked 
| 264|0x0000000724200000, 0x0000000724200000, 0x0000000724400000|  0%| F|  |TAMS 0x0000000724200000, 0x0000000724200000| Untracked 
| 265|0x0000000724400000, 0x0000000724400000, 0x0000000724600000|  0%| F|  |TAMS 0x0000000724400000, 0x0000000724400000| Untracked 
| 266|0x0000000724600000, 0x0000000724600000, 0x0000000724800000|  0%| F|  |TAMS 0x0000000724600000, 0x0000000724600000| Untracked 
| 267|0x0000000724800000, 0x0000000724800000, 0x0000000724a00000|  0%| F|  |TAMS 0x0000000724800000, 0x0000000724800000| Untracked 
| 268|0x0000000724a00000, 0x0000000724a00000, 0x0000000724c00000|  0%| F|  |TAMS 0x0000000724a00000, 0x0000000724a00000| Untracked 
| 269|0x0000000724c00000, 0x0000000724c00000, 0x0000000724e00000|  0%| F|  |TAMS 0x0000000724c00000, 0x0000000724c00000| Untracked 
| 270|0x0000000724e00000, 0x0000000724e00000, 0x0000000725000000|  0%| F|  |TAMS 0x0000000724e00000, 0x0000000724e00000| Untracked 
| 271|0x0000000725000000, 0x0000000725000000, 0x0000000725200000|  0%| F|  |TAMS 0x0000000725000000, 0x0000000725000000| Untracked 
| 272|0x0000000725200000, 0x0000000725200000, 0x0000000725400000|  0%| F|  |TAMS 0x0000000725200000, 0x0000000725200000| Untracked 
| 273|0x0000000725400000, 0x0000000725400000, 0x0000000725600000|  0%| F|  |TAMS 0x0000000725400000, 0x0000000725400000| Untracked 
| 274|0x0000000725600000, 0x0000000725600000, 0x0000000725800000|  0%| F|  |TAMS 0x0000000725600000, 0x0000000725600000| Untracked 
| 275|0x0000000725800000, 0x0000000725800000, 0x0000000725a00000|  0%| F|  |TAMS 0x0000000725800000, 0x0000000725800000| Untracked 
| 276|0x0000000725a00000, 0x0000000725a00000, 0x0000000725c00000|  0%| F|  |TAMS 0x0000000725a00000, 0x0000000725a00000| Untracked 
| 277|0x0000000725c00000, 0x0000000725c00000, 0x0000000725e00000|  0%| F|  |TAMS 0x0000000725c00000, 0x0000000725c00000| Untracked 
| 278|0x0000000725e00000, 0x0000000725e00000, 0x0000000726000000|  0%| F|  |TAMS 0x0000000725e00000, 0x0000000725e00000| Untracked 
| 279|0x0000000726000000, 0x0000000726000000, 0x0000000726200000|  0%| F|  |TAMS 0x0000000726000000, 0x0000000726000000| Untracked 
| 280|0x0000000726200000, 0x0000000726200000, 0x0000000726400000|  0%| F|  |TAMS 0x0000000726200000, 0x0000000726200000| Untracked 
| 281|0x0000000726400000, 0x0000000726400000, 0x0000000726600000|  0%| F|  |TAMS 0x0000000726400000, 0x0000000726400000| Untracked 
| 282|0x0000000726600000, 0x0000000726600000, 0x0000000726800000|  0%| F|  |TAMS 0x0000000726600000, 0x0000000726600000| Untracked 
| 283|0x0000000726800000, 0x0000000726800000, 0x0000000726a00000|  0%| F|  |TAMS 0x0000000726800000, 0x0000000726800000| Untracked 
| 284|0x0000000726a00000, 0x0000000726a00000, 0x0000000726c00000|  0%| F|  |TAMS 0x0000000726a00000, 0x0000000726a00000| Untracked 
| 285|0x0000000726c00000, 0x0000000726c00000, 0x0000000726e00000|  0%| F|  |TAMS 0x0000000726c00000, 0x0000000726c00000| Untracked 
| 286|0x0000000726e00000, 0x0000000726e00000, 0x0000000727000000|  0%| F|  |TAMS 0x0000000726e00000, 0x0000000726e00000| Untracked 
| 287|0x0000000727000000, 0x0000000727000000, 0x0000000727200000|  0%| F|  |TAMS 0x0000000727000000, 0x0000000727000000| Untracked 
| 288|0x0000000727200000, 0x0000000727200000, 0x0000000727400000|  0%| F|  |TAMS 0x0000000727200000, 0x0000000727200000| Untracked 
| 289|0x0000000727400000, 0x0000000727400000, 0x0000000727600000|  0%| F|  |TAMS 0x0000000727400000, 0x0000000727400000| Untracked 
| 290|0x0000000727600000, 0x0000000727600000, 0x0000000727800000|  0%| F|  |TAMS 0x0000000727600000, 0x0000000727600000| Untracked 
| 291|0x0000000727800000, 0x0000000727800000, 0x0000000727a00000|  0%| F|  |TAMS 0x0000000727800000, 0x0000000727800000| Untracked 
| 292|0x0000000727a00000, 0x0000000727a00000, 0x0000000727c00000|  0%| F|  |TAMS 0x0000000727a00000, 0x0000000727a00000| Untracked 
| 293|0x0000000727c00000, 0x0000000727c00000, 0x0000000727e00000|  0%| F|  |TAMS 0x0000000727c00000, 0x0000000727c00000| Untracked 
| 294|0x0000000727e00000, 0x0000000727e00000, 0x0000000728000000|  0%| F|  |TAMS 0x0000000727e00000, 0x0000000727e00000| Untracked 
| 295|0x0000000728000000, 0x0000000728000000, 0x0000000728200000|  0%| F|  |TAMS 0x0000000728000000, 0x0000000728000000| Untracked 
| 296|0x0000000728200000, 0x0000000728200000, 0x0000000728400000|  0%| F|  |TAMS 0x0000000728200000, 0x0000000728200000| Untracked 
| 297|0x0000000728400000, 0x0000000728400000, 0x0000000728600000|  0%| F|  |TAMS 0x0000000728400000, 0x0000000728400000| Untracked 
| 298|0x0000000728600000, 0x0000000728600000, 0x0000000728800000|  0%| F|  |TAMS 0x0000000728600000, 0x0000000728600000| Untracked 
| 299|0x0000000728800000, 0x0000000728800000, 0x0000000728a00000|  0%| F|  |TAMS 0x0000000728800000, 0x0000000728800000| Untracked 
| 300|0x0000000728a00000, 0x0000000728a00000, 0x0000000728c00000|  0%| F|  |TAMS 0x0000000728a00000, 0x0000000728a00000| Untracked 
| 301|0x0000000728c00000, 0x0000000728c00000, 0x0000000728e00000|  0%| F|  |TAMS 0x0000000728c00000, 0x0000000728c00000| Untracked 
| 302|0x0000000728e00000, 0x0000000728e00000, 0x0000000729000000|  0%| F|  |TAMS 0x0000000728e00000, 0x0000000728e00000| Untracked 
| 303|0x0000000729000000, 0x0000000729000000, 0x0000000729200000|  0%| F|  |TAMS 0x0000000729000000, 0x0000000729000000| Untracked 
| 304|0x0000000729200000, 0x0000000729200000, 0x0000000729400000|  0%| F|  |TAMS 0x0000000729200000, 0x0000000729200000| Untracked 
| 305|0x0000000729400000, 0x0000000729400000, 0x0000000729600000|  0%| F|  |TAMS 0x0000000729400000, 0x0000000729400000| Untracked 
| 306|0x0000000729600000, 0x0000000729600000, 0x0000000729800000|  0%| F|  |TAMS 0x0000000729600000, 0x0000000729600000| Untracked 
| 307|0x0000000729800000, 0x0000000729800000, 0x0000000729a00000|  0%| F|  |TAMS 0x0000000729800000, 0x0000000729800000| Untracked 
| 308|0x0000000729a00000, 0x0000000729a00000, 0x0000000729c00000|  0%| F|  |TAMS 0x0000000729a00000, 0x0000000729a00000| Untracked 
| 309|0x0000000729c00000, 0x0000000729c00000, 0x0000000729e00000|  0%| F|  |TAMS 0x0000000729c00000, 0x0000000729c00000| Untracked 
| 310|0x0000000729e00000, 0x0000000729e00000, 0x000000072a000000|  0%| F|  |TAMS 0x0000000729e00000, 0x0000000729e00000| Untracked 
| 311|0x000000072a000000, 0x000000072a000000, 0x000000072a200000|  0%| F|  |TAMS 0x000000072a000000, 0x000000072a000000| Untracked 
| 312|0x000000072a200000, 0x000000072a2a68b8, 0x000000072a400000| 32%| E|  |TAMS 0x000000072a200000, 0x000000072a200000| Complete 
| 313|0x000000072a400000, 0x000000072a600000, 0x000000072a600000|100%| E|CS|TAMS 0x000000072a400000, 0x000000072a400000| Complete 
| 314|0x000000072a600000, 0x000000072a800000, 0x000000072a800000|100%| E|CS|TAMS 0x000000072a600000, 0x000000072a600000| Complete 
| 315|0x000000072a800000, 0x000000072aa00000, 0x000000072aa00000|100%| E|CS|TAMS 0x000000072a800000, 0x000000072a800000| Complete 
| 316|0x000000072aa00000, 0x000000072ac00000, 0x000000072ac00000|100%| E|CS|TAMS 0x000000072aa00000, 0x000000072aa00000| Complete 
| 317|0x000000072ac00000, 0x000000072ae00000, 0x000000072ae00000|100%| E|CS|TAMS 0x000000072ac00000, 0x000000072ac00000| Complete 
| 318|0x000000072ae00000, 0x000000072b000000, 0x000000072b000000|100%| E|CS|TAMS 0x000000072ae00000, 0x000000072ae00000| Complete 
| 319|0x000000072b000000, 0x000000072b200000, 0x000000072b200000|100%| E|CS|TAMS 0x000000072b000000, 0x000000072b000000| Complete 
| 320|0x000000072b200000, 0x000000072b400000, 0x000000072b400000|100%| E|CS|TAMS 0x000000072b200000, 0x000000072b200000| Complete 
| 321|0x000000072b400000, 0x000000072b600000, 0x000000072b600000|100%| E|CS|TAMS 0x000000072b400000, 0x000000072b400000| Complete 
| 322|0x000000072b600000, 0x000000072b800000, 0x000000072b800000|100%| E|CS|TAMS 0x000000072b600000, 0x000000072b600000| Complete 
| 323|0x000000072b800000, 0x000000072ba00000, 0x000000072ba00000|100%| E|CS|TAMS 0x000000072b800000, 0x000000072b800000| Complete 
| 324|0x000000072ba00000, 0x000000072bc00000, 0x000000072bc00000|100%| E|CS|TAMS 0x000000072ba00000, 0x000000072ba00000| Complete 
| 325|0x000000072bc00000, 0x000000072be00000, 0x000000072be00000|100%| E|CS|TAMS 0x000000072bc00000, 0x000000072bc00000| Complete 
| 326|0x000000072be00000, 0x000000072c000000, 0x000000072c000000|100%| E|CS|TAMS 0x000000072be00000, 0x000000072be00000| Complete 
| 327|0x000000072c000000, 0x000000072c200000, 0x000000072c200000|100%| E|  |TAMS 0x000000072c000000, 0x000000072c000000| Complete 
| 328|0x000000072c200000, 0x000000072c400000, 0x000000072c400000|100%| E|CS|TAMS 0x000000072c200000, 0x000000072c200000| Complete 
| 329|0x000000072c400000, 0x000000072c600000, 0x000000072c600000|100%| E|CS|TAMS 0x000000072c400000, 0x000000072c400000| Complete 
| 330|0x000000072c600000, 0x000000072c800000, 0x000000072c800000|100%| E|CS|TAMS 0x000000072c600000, 0x000000072c600000| Complete 
| 331|0x000000072c800000, 0x000000072ca00000, 0x000000072ca00000|100%| E|CS|TAMS 0x000000072c800000, 0x000000072c800000| Complete 
| 332|0x000000072ca00000, 0x000000072cc00000, 0x000000072cc00000|100%| E|CS|TAMS 0x000000072ca00000, 0x000000072ca00000| Complete 
| 333|0x000000072cc00000, 0x000000072ce00000, 0x000000072ce00000|100%| E|CS|TAMS 0x000000072cc00000, 0x000000072cc00000| Complete 
| 334|0x000000072ce00000, 0x000000072d000000, 0x000000072d000000|100%| E|CS|TAMS 0x000000072ce00000, 0x000000072ce00000| Complete 
| 335|0x000000072d000000, 0x000000072d200000, 0x000000072d200000|100%| E|CS|TAMS 0x000000072d000000, 0x000000072d000000| Complete 
| 336|0x000000072d200000, 0x000000072d400000, 0x000000072d400000|100%| E|CS|TAMS 0x000000072d200000, 0x000000072d200000| Complete 
| 337|0x000000072d400000, 0x000000072d600000, 0x000000072d600000|100%| E|CS|TAMS 0x000000072d400000, 0x000000072d400000| Complete 
| 338|0x000000072d600000, 0x000000072d800000, 0x000000072d800000|100%| E|CS|TAMS 0x000000072d600000, 0x000000072d600000| Complete 
| 339|0x000000072d800000, 0x000000072da00000, 0x000000072da00000|100%| E|CS|TAMS 0x000000072d800000, 0x000000072d800000| Complete 
| 340|0x000000072da00000, 0x000000072dc00000, 0x000000072dc00000|100%| E|CS|TAMS 0x000000072da00000, 0x000000072da00000| Complete 
| 341|0x000000072dc00000, 0x000000072de00000, 0x000000072de00000|100%| E|CS|TAMS 0x000000072dc00000, 0x000000072dc00000| Complete 
| 342|0x000000072de00000, 0x000000072e000000, 0x000000072e000000|100%| E|CS|TAMS 0x000000072de00000, 0x000000072de00000| Complete 
| 343|0x000000072e000000, 0x000000072e200000, 0x000000072e200000|100%| E|CS|TAMS 0x000000072e000000, 0x000000072e000000| Complete 
| 344|0x000000072e200000, 0x000000072e400000, 0x000000072e400000|100%| E|CS|TAMS 0x000000072e200000, 0x000000072e200000| Complete 
| 345|0x000000072e400000, 0x000000072e600000, 0x000000072e600000|100%| E|CS|TAMS 0x000000072e400000, 0x000000072e400000| Complete 
| 346|0x000000072e600000, 0x000000072e800000, 0x000000072e800000|100%| E|CS|TAMS 0x000000072e600000, 0x000000072e600000| Complete 
| 347|0x000000072e800000, 0x000000072ea00000, 0x000000072ea00000|100%| E|CS|TAMS 0x000000072e800000, 0x000000072e800000| Complete 
| 348|0x000000072ea00000, 0x000000072eafc560, 0x000000072ec00000| 49%| S|CS|TAMS 0x000000072ea00000, 0x000000072ea00000| Complete 
| 349|0x000000072ec00000, 0x000000072ee00000, 0x000000072ee00000|100%| S|CS|TAMS 0x000000072ec00000, 0x000000072ec00000| Complete 
| 350|0x000000072ee00000, 0x000000072f000000, 0x000000072f000000|100%| S|CS|TAMS 0x000000072ee00000, 0x000000072ee00000| Complete 
| 351|0x000000072f000000, 0x000000072f200000, 0x000000072f200000|100%| S|CS|TAMS 0x000000072f000000, 0x000000072f000000| Complete 
| 352|0x000000072f200000, 0x000000072f400000, 0x000000072f400000|100%| S|CS|TAMS 0x000000072f200000, 0x000000072f200000| Complete 
| 353|0x000000072f400000, 0x000000072f600000, 0x000000072f600000|100%| S|CS|TAMS 0x000000072f400000, 0x000000072f400000| Complete 
| 354|0x000000072f600000, 0x000000072f800000, 0x000000072f800000|100%| S|CS|TAMS 0x000000072f600000, 0x000000072f600000| Complete 
| 355|0x000000072f800000, 0x000000072fa00000, 0x000000072fa00000|100%| S|CS|TAMS 0x000000072f800000, 0x000000072f800000| Complete 
| 356|0x000000072fa00000, 0x000000072fc00000, 0x000000072fc00000|100%| S|CS|TAMS 0x000000072fa00000, 0x000000072fa00000| Complete 
| 357|0x000000072fc00000, 0x000000072fe00000, 0x000000072fe00000|100%| S|CS|TAMS 0x000000072fc00000, 0x000000072fc00000| Complete 
| 358|0x000000072fe00000, 0x0000000730000000, 0x0000000730000000|100%| S|CS|TAMS 0x000000072fe00000, 0x000000072fe00000| Complete 
| 359|0x0000000730000000, 0x0000000730200000, 0x0000000730200000|100%| S|CS|TAMS 0x0000000730000000, 0x0000000730000000| Complete 
| 360|0x0000000730200000, 0x0000000730400000, 0x0000000730400000|100%| S|CS|TAMS 0x0000000730200000, 0x0000000730200000| Complete 
| 361|0x0000000730400000, 0x0000000730600000, 0x0000000730600000|100%| S|CS|TAMS 0x0000000730400000, 0x0000000730400000| Complete 
| 362|0x0000000730600000, 0x0000000730800000, 0x0000000730800000|100%| S|CS|TAMS 0x0000000730600000, 0x0000000730600000| Complete 
| 363|0x0000000730800000, 0x0000000730a00000, 0x0000000730a00000|100%| E|CS|TAMS 0x0000000730800000, 0x0000000730800000| Complete 
| 364|0x0000000730a00000, 0x0000000730c00000, 0x0000000730c00000|100%| E|CS|TAMS 0x0000000730a00000, 0x0000000730a00000| Complete 
| 365|0x0000000730c00000, 0x0000000730e00000, 0x0000000730e00000|100%| E|CS|TAMS 0x0000000730c00000, 0x0000000730c00000| Complete 
| 366|0x0000000730e00000, 0x0000000731000000, 0x0000000731000000|100%| E|CS|TAMS 0x0000000730e00000, 0x0000000730e00000| Complete 
| 367|0x0000000731000000, 0x0000000731200000, 0x0000000731200000|100%| E|CS|TAMS 0x0000000731000000, 0x0000000731000000| Complete 
| 368|0x0000000731200000, 0x0000000731400000, 0x0000000731400000|100%| E|CS|TAMS 0x0000000731200000, 0x0000000731200000| Complete 
| 369|0x0000000731400000, 0x0000000731600000, 0x0000000731600000|100%| E|CS|TAMS 0x0000000731400000, 0x0000000731400000| Complete 
| 370|0x0000000731600000, 0x0000000731800000, 0x0000000731800000|100%| E|CS|TAMS 0x0000000731600000, 0x0000000731600000| Complete 
| 371|0x0000000731800000, 0x0000000731a00000, 0x0000000731a00000|100%| E|CS|TAMS 0x0000000731800000, 0x0000000731800000| Complete 
| 372|0x0000000731a00000, 0x0000000731c00000, 0x0000000731c00000|100%| E|CS|TAMS 0x0000000731a00000, 0x0000000731a00000| Complete 
| 373|0x0000000731c00000, 0x0000000731e00000, 0x0000000731e00000|100%| E|CS|TAMS 0x0000000731c00000, 0x0000000731c00000| Complete 
| 374|0x0000000731e00000, 0x0000000732000000, 0x0000000732000000|100%| E|CS|TAMS 0x0000000731e00000, 0x0000000731e00000| Complete 
| 375|0x0000000732000000, 0x0000000732200000, 0x0000000732200000|100%| E|CS|TAMS 0x0000000732000000, 0x0000000732000000| Complete 
| 376|0x0000000732200000, 0x0000000732400000, 0x0000000732400000|100%| E|CS|TAMS 0x0000000732200000, 0x0000000732200000| Complete 

Card table byte_map: [0x000001b2c6e30000,0x000001b2c7620000] _byte_map_base: 0x000001b2c3617000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001b2bba9a400, (CMBitMap*) 0x000001b2bba9a3c0
 Prev Bits: [0x000001b2cbd50000, 0x000001b2cfc88000)
 Next Bits: [0x000001b2c7e10000, 0x000001b2cbd48000)

Polling page: 0x000001b2b9a10000

Metaspace:

Usage:
  Non-class:    129.16 MB used.
      Class:     19.02 MB used.
       Both:    148.18 MB used.

Virtual space:
  Non-class space:      136.00 MB reserved,     129.88 MB ( 95%) committed,  17 nodes.
      Class space:        1.00 GB reserved,      19.63 MB (  2%) committed,  1 nodes.
             Both:        1.13 GB reserved,     149.50 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  1.84 MB
       Class:  302.00 KB
        Both:  2.14 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 176.38 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 2164.
num_arena_deaths: 132.
num_vsnodes_births: 18.
num_vsnodes_deaths: 0.
num_space_committed: 2392.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 170.
num_chunks_taken_from_freelist: 8153.
num_chunk_merges: 62.
num_chunk_splits: 5675.
num_chunks_enlarged: 4106.
num_purges: 6.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=33857Kb max_used=33857Kb free=15294Kb
 bounds [0x000001b2c3530000, 0x000001b2c5650000, 0x000001b2c6530000]
 total_blobs=17931 nmethods=17032 adapters=822
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 83.418 Thread 0x000001b2de3bc4d0 17333       1       org.apache.kafka.common.MetricNameTemplate::group (5 bytes)
Event: 83.418 Thread 0x000001b2d4548a30 17332       1       org.apache.kafka.common.MetricNameTemplate::name (5 bytes)
Event: 83.418 Thread 0x000001b2d4548a30 nmethod 17332 0x000001b2c563d810 code [0x000001b2c563d9a0, 0x000001b2c563da78]
Event: 83.418 Thread 0x000001b2de3bc4d0 nmethod 17333 0x000001b2c563db10 code [0x000001b2c563dca0, 0x000001b2c563dd78]
Event: 83.418 Thread 0x000001b2d4548a30 17334       1       org.apache.kafka.common.MetricNameTemplate::description (5 bytes)
Event: 83.418 Thread 0x000001b2d4548010 nmethod 17331 0x000001b2c563de10 code [0x000001b2c563dfa0, 0x000001b2c563e078]
Event: 83.418 Thread 0x000001b2d302d350 nmethod 17330 0x000001b2c563e110 code [0x000001b2c563e2a0, 0x000001b2c563e378]
Event: 83.418 Thread 0x000001b2d4548a30 nmethod 17334 0x000001b2c563e410 code [0x000001b2c563e5a0, 0x000001b2c563e678]
Event: 83.423 Thread 0x000001b2d4548a30 17335       1       sun.net.util.IPAddressUtil::digit (18 bytes)
Event: 83.423 Thread 0x000001b2d4548a30 nmethod 17335 0x000001b2c563e710 code [0x000001b2c563e8a0, 0x000001b2c563e968]
Event: 83.423 Thread 0x000001b2d302d350 17336       1       sun.nio.ch.SocketChannelImpl::ensureOpen (16 bytes)
Event: 83.423 Thread 0x000001b2d302d350 nmethod 17336 0x000001b2c563ea10 code [0x000001b2c563ebc0, 0x000001b2c563ed08]
Event: 83.429 Thread 0x000001b2d4548a30 17337       1       org.apache.kafka.common.Node::hashCode (90 bytes)
Event: 83.429 Thread 0x000001b2d4548a30 nmethod 17337 0x000001b2c563f410 code [0x000001b2c563f5c0, 0x000001b2c563f898]
Event: 83.434 Thread 0x000001b2d4548a30 17338       1       sun.nio.ch.SocketChannelImpl::isUnixSocket (16 bytes)
Event: 83.435 Thread 0x000001b2d4548a30 nmethod 17338 0x000001b2c563fa90 code [0x000001b2c563fc20, 0x000001b2c563fd18]
Event: 83.436 Thread 0x000001b2de3bc4d0 17339       1       org.apache.kafka.common.utils.Timer::update (14 bytes)
Event: 83.436 Thread 0x000001b2de3bc4d0 nmethod 17339 0x000001b2c563fd90 code [0x000001b2c563ff20, 0x000001b2c5640058]
Event: 83.437 Thread 0x000001b2d302d350 17340       1       org.apache.kafka.common.Node::isEmpty (30 bytes)
Event: 83.437 Thread 0x000001b2d302d350 nmethod 17340 0x000001b2c5640190 code [0x000001b2c5640320, 0x000001b2c5640458]

GC Heap History (20 events):
Event: 62.174 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 337920K, used 180458K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 5 survivors (10240K)
 Metaspace       used 107919K, committed 108800K, reserved 1146880K
  class space    used 13776K, committed 14208K, reserved 1048576K
}
Event: 62.178 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 337920K, used 172794K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 107919K, committed 108800K, reserved 1146880K
  class space    used 13776K, committed 14208K, reserved 1048576K
}
Event: 62.644 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 337920K, used 293626K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 60 young (122880K), 1 survivors (2048K)
 Metaspace       used 109045K, committed 109952K, reserved 1146880K
  class space    used 13913K, committed 14336K, reserved 1048576K
}
Event: 62.648 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 772096K, used 173987K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 109045K, committed 109952K, reserved 1146880K
  class space    used 13913K, committed 14336K, reserved 1048576K
}
Event: 64.523 GC heap before
{Heap before GC invocations=50 (full 0):
 garbage-first heap   total 772096K, used 632739K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 226 young (462848K), 2 survivors (4096K)
 Metaspace       used 114108K, committed 114944K, reserved 1155072K
  class space    used 14537K, committed 14912K, reserved 1048576K
}
Event: 64.530 GC heap after
{Heap after GC invocations=51 (full 0):
 garbage-first heap   total 772096K, used 181732K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 6 survivors (12288K)
 Metaspace       used 114108K, committed 114944K, reserved 1155072K
  class space    used 14537K, committed 14912K, reserved 1048576K
}
Event: 66.421 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 772096K, used 632292K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 226 young (462848K), 6 survivors (12288K)
 Metaspace       used 117233K, committed 118144K, reserved 1155072K
  class space    used 14823K, committed 15232K, reserved 1048576K
}
Event: 66.431 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 772096K, used 189749K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 10 survivors (20480K)
 Metaspace       used 117233K, committed 118144K, reserved 1155072K
  class space    used 14823K, committed 15232K, reserved 1048576K
}
Event: 68.412 GC heap before
{Heap before GC invocations=52 (full 0):
 garbage-first heap   total 772096K, used 632117K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 226 young (462848K), 10 survivors (20480K)
 Metaspace       used 121198K, committed 122176K, reserved 1163264K
  class space    used 15355K, committed 15808K, reserved 1048576K
}
Event: 68.428 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 772096K, used 203369K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 16 survivors (32768K)
 Metaspace       used 121198K, committed 122176K, reserved 1163264K
  class space    used 15355K, committed 15808K, reserved 1048576K
}
Event: 73.786 GC heap before
{Heap before GC invocations=53 (full 0):
 garbage-first heap   total 772096K, used 631401K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 226 young (462848K), 16 survivors (32768K)
 Metaspace       used 128302K, committed 129344K, reserved 1163264K
  class space    used 16318K, committed 16832K, reserved 1048576K
}
Event: 73.807 GC heap after
{Heap after GC invocations=54 (full 0):
 garbage-first heap   total 772096K, used 216555K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 21 young (43008K), 21 survivors (43008K)
 Metaspace       used 128302K, committed 129344K, reserved 1163264K
  class space    used 16318K, committed 16832K, reserved 1048576K
}
Event: 78.971 GC heap before
{Heap before GC invocations=54 (full 0):
 garbage-first heap   total 772096K, used 648683K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 226 young (462848K), 21 survivors (43008K)
 Metaspace       used 148384K, committed 149440K, reserved 1179648K
  class space    used 18738K, committed 19264K, reserved 1048576K
}
Event: 78.998 GC heap after
{Heap after GC invocations=55 (full 0):
 garbage-first heap   total 772096K, used 242528K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 21 young (43008K), 21 survivors (43008K)
 Metaspace       used 148384K, committed 149440K, reserved 1179648K
  class space    used 18738K, committed 19264K, reserved 1048576K
}
Event: 82.410 GC heap before
{Heap before GC invocations=55 (full 0):
 garbage-first heap   total 772096K, used 547680K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 154 young (315392K), 21 survivors (43008K)
 Metaspace       used 151429K, committed 152704K, reserved 1187840K
  class space    used 19422K, committed 20032K, reserved 1048576K
}
Event: 82.442 GC heap after
{Heap after GC invocations=56 (full 0):
 garbage-first heap   total 772096K, used 287840K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 28 young (57344K), 28 survivors (57344K)
 Metaspace       used 151429K, committed 152704K, reserved 1187840K
  class space    used 19422K, committed 20032K, reserved 1048576K
}
Event: 83.092 GC heap before
{Heap before GC invocations=56 (full 0):
 garbage-first heap   total 772096K, used 541792K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 125 young (256000K), 28 survivors (57344K)
 Metaspace       used 151526K, committed 152768K, reserved 1187840K
  class space    used 19446K, committed 20032K, reserved 1048576K
}
Event: 83.122 GC heap after
{Heap after GC invocations=57 (full 0):
 garbage-first heap   total 772096K, used 320512K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 25 young (51200K), 25 survivors (51200K)
 Metaspace       used 151526K, committed 152768K, reserved 1187840K
  class space    used 19446K, committed 20032K, reserved 1048576K
}
Event: 83.194 GC heap before
{Heap before GC invocations=57 (full 0):
 garbage-first heap   total 772096K, used 402432K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 39 young (79872K), 25 survivors (51200K)
 Metaspace       used 151526K, committed 152768K, reserved 1187840K
  class space    used 19446K, committed 20032K, reserved 1048576K
}
Event: 83.215 GC heap after
{Heap after GC invocations=58 (full 0):
 garbage-first heap   total 772096K, used 369279K [0x0000000703200000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 15 survivors (30720K)
 Metaspace       used 151526K, committed 152768K, reserved 1187840K
  class space    used 19446K, committed 20032K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.011 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\java.dll
Event: 0.011 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\zip.dll
Event: 0.020 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\jsvml.dll
Event: 3.064 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\zip.dll
Event: 3.070 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\instrument.dll
Event: 3.071 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\jimage.dll
Event: 3.090 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\net.dll
Event: 3.091 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\nio.dll
Event: 3.615 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\management.dll
Event: 3.709 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\management_ext.dll
Event: 13.768 Loaded shared library D:\jdks\17\jdk17.0.6_10\bin\sunmscapi.dll
Event: 65.823 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\librocksdbjni14145711817872284057.dll

Deoptimization events (20 events):
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT PACKING pc=0x000001b2c4fd2d1c sp=0x000000aa612f92e0
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa612f8798 mode 1
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT PACKING pc=0x000001b2c49036e4 sp=0x000000aa612f91e0
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa612f8680 mode 1
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT PACKING pc=0x000001b2c4fd9fe4 sp=0x000000aa612f92c0
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa612f8780 mode 1
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT PACKING pc=0x000001b2c4f2d044 sp=0x000000aa612f9330
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa612f8868 mode 1
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT PACKING pc=0x000001b2c4f2fd6c sp=0x000000aa612f9490
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa612f8930 mode 1
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT PACKING pc=0x000001b2c4fc005c sp=0x000000aa612f94d0
Event: 82.645 Thread 0x000001b2e05e6c40 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa612f89f0 mode 1
Event: 82.689 Thread 0x000001b2e05e3c20 DEOPT PACKING pc=0x000001b2c52a19dc sp=0x000000aa60afbf60
Event: 82.689 Thread 0x000001b2e05e3c20 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa60afb3d8 mode 0
Event: 83.003 Thread 0x000001b2e05e5dd0 DEOPT PACKING pc=0x000001b2c52a19dc sp=0x000000aa606f9ea0
Event: 83.003 Thread 0x000001b2e05e5dd0 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa606f9318 mode 0
Event: 83.060 Thread 0x000001b2e05e28e0 DEOPT PACKING pc=0x000001b2c52a19dc sp=0x000000aa611f9730
Event: 83.060 Thread 0x000001b2e05e28e0 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa611f8ba8 mode 0
Event: 83.060 Thread 0x000001b2e039a1d0 DEOPT PACKING pc=0x000001b2c52a19dc sp=0x000000aa5fff9ac0
Event: 83.060 Thread 0x000001b2e039a1d0 DEOPT UNPACKING pc=0x000001b2c358ace3 sp=0x000000aa5fff8f38 mode 0

Classes unloaded (20 events):
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801f1c800 'java/lang/invoke/LambdaForm$DMH+0x0000000801f1c800'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801f1c000 'java/lang/invoke/LambdaForm$DMH+0x0000000801f1c000'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec9c00 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec9c00'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec9800 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec9800'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec8000 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec8000'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec8400 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec8400'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec9000 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec9000'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec9400 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec9400'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ec8c00 'java/lang/invoke/LambdaForm$DMH+0x0000000801ec8c00'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebdc00 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebdc00'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebd800 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebd800'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebd000 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebd000'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebc800 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebc800'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebd400 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebd400'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebc400 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebc400'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801ebcc00 'java/lang/invoke/LambdaForm$DMH+0x0000000801ebcc00'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801e59000 'java/lang/invoke/LambdaForm$DMH+0x0000000801e59000'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801e58400 'java/lang/invoke/LambdaForm$DMH+0x0000000801e58400'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801e58c00 'java/lang/invoke/LambdaForm$DMH+0x0000000801e58c00'
Event: 83.485 Thread 0x000001b2d235adb0 Unloading class 0x0000000801e34800 'jdk/internal/reflect/GeneratedMethodAccessor122'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 78.775 Thread 0x000001b2e0396810 Exception <a 'java/lang/NoSuchMethodError'{0x000000071c18e588}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, long)'> (0x000000071c18e588) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 79.796 Thread 0x000001b2e05e5dd0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007305aab60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007305aab60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 79.796 Thread 0x000001b2e05e28e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000730587c78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000730587c78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 79.796 Thread 0x000001b2e039a1d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000073051db18}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000073051db18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.250 Thread 0x000001b2e039a1d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072abd40d8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072abd40d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.250 Thread 0x000001b2e05e28e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072ab74cb8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072ab74cb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.250 Thread 0x000001b2e05e5dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072abf8b28}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072abf8b28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.251 Thread 0x000001b2e039a1d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072abdcff0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072abdcff0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.251 Thread 0x000001b2e05e28e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072ab7dbb0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072ab7dbb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.251 Thread 0x000001b2e05e5dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072a8021e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072a8021e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.391 Thread 0x000001b2e039a1d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000729e190d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x0000000729e190d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.391 Thread 0x000001b2e05e5dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072a1d74c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x000000072a1d74c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 80.392 Thread 0x000001b2e05e28e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000729e5f210}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x0000000729e5f210) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 81.014 Thread 0x000001b2e039a1d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000727b36fb8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000727b36fb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 81.014 Thread 0x000001b2e05e28e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000727bd3e08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000727bd3e08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 81.014 Thread 0x000001b2e05e5dd0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000727b8b428}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000727b8b428) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 82.129 Thread 0x000001b2d4cb8610 Exception <a 'java/io/FileNotFoundException'{0x0000000723613408}> (0x0000000723613408) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 82.130 Thread 0x000001b2d4cb8610 Exception <a 'java/io/FileNotFoundException'{0x0000000723613bd0}> (0x0000000723613bd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 83.355 Thread 0x000001b2e03989c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072d9a7970}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, int)'> (0x000000072d9a7970) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 83.360 Thread 0x000001b2e0396810 Exception <a 'java/lang/NoSuchMethodError'{0x000000072d47e5e8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x000000072d47e5e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 80.888 Executing VM operation: HandshakeAllThreads done
Event: 80.890 Executing VM operation: HandshakeAllThreads
Event: 80.890 Executing VM operation: HandshakeAllThreads done
Event: 81.792 Executing VM operation: ICBufferFull
Event: 81.792 Executing VM operation: ICBufferFull done
Event: 81.792 Executing VM operation: ICBufferFull
Event: 81.792 Executing VM operation: ICBufferFull done
Event: 82.410 Executing VM operation: G1TryInitiateConcMark
Event: 82.442 Executing VM operation: G1TryInitiateConcMark done
Event: 82.642 Executing VM operation: HandshakeAllThreads
Event: 82.642 Executing VM operation: HandshakeAllThreads done
Event: 82.651 Executing VM operation: HandshakeAllThreads
Event: 82.652 Executing VM operation: HandshakeAllThreads done
Event: 82.656 Executing VM operation: HandshakeAllThreads
Event: 82.656 Executing VM operation: HandshakeAllThreads done
Event: 83.092 Executing VM operation: G1TryInitiateConcMark
Event: 83.122 Executing VM operation: G1TryInitiateConcMark done
Event: 83.194 Executing VM operation: G1TryInitiateConcMark
Event: 83.215 Executing VM operation: G1TryInitiateConcMark done
Event: 83.484 Executing VM operation: G1Concurrent

Events (20 events):
Event: 83.425 loading class org/apache/kafka/common/requests/FetchRequest$PartitionData
Event: 83.425 loading class org/apache/kafka/common/requests/FetchRequest$PartitionData done
Event: 83.425 loading class java/util/concurrent/ConcurrentLinkedQueue$CLQSpliterator
Event: 83.428 loading class java/util/concurrent/ConcurrentLinkedQueue$CLQSpliterator done
Event: 83.428 loading class org/apache/kafka/clients/consumer/internals/Fetcher
Event: 83.428 loading class org/apache/kafka/clients/consumer/internals/Fetcher done
Event: 83.428 loading class org/apache/kafka/clients/consumer/internals/Fetcher$CompletedFetch
Event: 83.428 loading class org/apache/kafka/clients/consumer/internals/Fetcher$CompletedFetch done
Event: 83.428 loading class org/apache/kafka/clients/consumer/internals/Fetcher$CompletedFetch
Event: 83.428 loading class org/apache/kafka/clients/consumer/internals/Fetcher$CompletedFetch done
Event: 83.429 loading class org/apache/kafka/clients/FetchSessionHandler$FetchRequestData
Event: 83.429 loading class org/apache/kafka/clients/FetchSessionHandler$FetchRequestData done
Event: 83.430 loading class org/apache/kafka/common/requests/FetchRequest$Builder
Event: 83.430 loading class org/apache/kafka/common/requests/FetchRequest$Builder done
Event: 83.432 loading class org/apache/kafka/clients/consumer/internals/Fetcher$CompletedFetch
Event: 83.432 loading class org/apache/kafka/clients/consumer/internals/Fetcher$CompletedFetch done
Event: 83.432 loading class org/apache/kafka/clients/consumer/internals/Fetcher$1
Event: 83.432 loading class org/apache/kafka/clients/consumer/internals/Fetcher$1 done
Event: 83.433 loading class org/apache/kafka/clients/consumer/ConsumerRecords
Event: 83.433 loading class org/apache/kafka/clients/consumer/ConsumerRecords done


Dynamic libraries:
0x00007ff74ea60000 - 0x00007ff74ea6d000 	D:\jdks\17\jdk17.0.6_10\bin\java.exe
0x00007ffc8f4d0000 - 0x00007ffc8f6c8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffc8eb80000 - 0x00007ffc8ec3f000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffc8d0c0000 - 0x00007ffc8d392000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffc8cc40000 - 0x00007ffc8cd40000 	C:\Windows\System32\ucrtbase.dll
0x00007ffc75510000 - 0x00007ffc75527000 	D:\jdks\17\jdk17.0.6_10\bin\jli.dll
0x00007ffc71c60000 - 0x00007ffc71c75000 	D:\jdks\17\jdk17.0.6_10\bin\VCRUNTIME140.dll
0x00007ffc8d8a0000 - 0x00007ffc8da41000 	C:\Windows\System32\USER32.dll
0x00007ffc8ce50000 - 0x00007ffc8ce72000 	C:\Windows\System32\win32u.dll
0x00007ffc8eeb0000 - 0x00007ffc8eedb000 	C:\Windows\System32\GDI32.dll
0x00007ffc73700000 - 0x00007ffc7399a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007ffc8cd40000 - 0x00007ffc8ce4f000 	C:\Windows\System32\gdi32full.dll
0x00007ffc8e940000 - 0x00007ffc8e9de000 	C:\Windows\System32\msvcrt.dll
0x00007ffc8d3d0000 - 0x00007ffc8d46d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffc8dab0000 - 0x00007ffc8dae2000 	C:\Windows\System32\IMM32.DLL
0x00007ffc4caa0000 - 0x00007ffc4cb3b000 	D:\jdks\17\jdk17.0.6_10\bin\msvcp140.dll
0x00007ffc1cf90000 - 0x00007ffc1dba8000 	D:\jdks\17\jdk17.0.6_10\bin\server\jvm.dll
0x00007ffc8e750000 - 0x00007ffc8e7fe000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffc8e350000 - 0x00007ffc8e3ec000 	C:\Windows\System32\sechost.dll
0x00007ffc8ec50000 - 0x00007ffc8ed75000 	C:\Windows\System32\RPCRT4.dll
0x00007ffc63590000 - 0x00007ffc635b7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffc75ce0000 - 0x00007ffc75ce9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffc83a70000 - 0x00007ffc83a7a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffc8eee0000 - 0x00007ffc8ef4b000 	C:\Windows\System32\WS2_32.dll
0x00007ffc8b310000 - 0x00007ffc8b322000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffc83380000 - 0x00007ffc8338a000 	D:\jdks\17\jdk17.0.6_10\bin\jimage.dll
0x00007ffc89910000 - 0x00007ffc89af4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffc749a0000 - 0x00007ffc749d5000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffc8ce80000 - 0x00007ffc8cf02000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffc6bc90000 - 0x00007ffc6bcc9000 	D:\jdks\17\jdk17.0.6_10\bin\jdwp.dll
0x00007ffc769a0000 - 0x00007ffc769ae000 	D:\jdks\17\jdk17.0.6_10\bin\instrument.dll
0x00007ffc6ca80000 - 0x00007ffc6caa5000 	D:\jdks\17\jdk17.0.6_10\bin\java.dll
0x00007ffc6ad30000 - 0x00007ffc6ad48000 	D:\jdks\17\jdk17.0.6_10\bin\zip.dll
0x00007ffc4c9c0000 - 0x00007ffc4ca96000 	D:\jdks\17\jdk17.0.6_10\bin\jsvml.dll
0x00007ffc8db50000 - 0x00007ffc8e294000 	C:\Windows\System32\SHELL32.dll
0x00007ffc8ab20000 - 0x00007ffc8b2b2000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffc8d4e0000 - 0x00007ffc8d835000 	C:\Windows\System32\combase.dll
0x00007ffc8c5f0000 - 0x00007ffc8c620000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffc8e2a0000 - 0x00007ffc8e34d000 	C:\Windows\System32\SHCORE.dll
0x00007ffc8da50000 - 0x00007ffc8daa5000 	C:\Windows\System32\shlwapi.dll
0x00007ffc8cad0000 - 0x00007ffc8caef000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffc76990000 - 0x00007ffc7699c000 	D:\jdks\17\jdk17.0.6_10\bin\dt_socket.dll
0x00007ffc8bff0000 - 0x00007ffc8c02b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffc63610000 - 0x00007ffc6378a000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\SangforTcpX64.dll
0x00007ffc8e800000 - 0x00007ffc8e92a000 	C:\Windows\System32\ole32.dll
0x00007ffc8e3f0000 - 0x00007ffc8e4bd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffc8d470000 - 0x00007ffc8d4d9000 	C:\Windows\System32\WINTRUST.dll
0x00007ffc8cf60000 - 0x00007ffc8d0b6000 	C:\Windows\System32\CRYPT32.dll
0x00007ffc8c780000 - 0x00007ffc8c792000 	C:\Windows\SYSTEM32\MSASN1.dll
0x00007ffc8c350000 - 0x00007ffc8c3ba000 	C:\Windows\system32\mswsock.dll
0x00007ffc6adb0000 - 0x00007ffc6adc9000 	D:\jdks\17\jdk17.0.6_10\bin\net.dll
0x00007ffc8bb80000 - 0x00007ffc8bc8c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffc6ad50000 - 0x00007ffc6ad65000 	D:\jdks\17\jdk17.0.6_10\bin\nio.dll
0x00007ffc6c2c0000 - 0x00007ffc6c30b000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\1_SangforNspX64.dll
0x00007ffc8f450000 - 0x00007ffc8f458000 	C:\Windows\System32\PSAPI.DLL
0x00007ffc8c040000 - 0x00007ffc8c10a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffc8ec40000 - 0x00007ffc8ec48000 	C:\Windows\System32\NSI.dll
0x00007ffc85090000 - 0x00007ffc8509a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffc87fe0000 - 0x00007ffc88060000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffc8d3a0000 - 0x00007ffc8d3c7000 	C:\Windows\System32\bcrypt.dll
0x00007ffc83c30000 - 0x00007ffc83c39000 	D:\jdks\17\jdk17.0.6_10\bin\management.dll
0x00007ffc83c20000 - 0x00007ffc83c2b000 	D:\jdks\17\jdk17.0.6_10\bin\management_ext.dll
0x00007ffc8c540000 - 0x00007ffc8c558000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffc8baf0000 - 0x00007ffc8bb24000 	C:\Windows\system32\rsaenh.dll
0x00007ffc8ca50000 - 0x00007ffc8ca7e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffc8c560000 - 0x00007ffc8c56c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffc87ec0000 - 0x00007ffc87ed7000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffc87e00000 - 0x00007ffc87e1d000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffc6c3b0000 - 0x00007ffc6c3c7000 	C:\Windows\system32\napinsp.dll
0x00007ffc6c390000 - 0x00007ffc6c3ab000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffc888a0000 - 0x00007ffc888b5000 	C:\Windows\system32\wshbth.dll
0x00007ffc89180000 - 0x00007ffc8919d000 	C:\Windows\system32\NLAapi.dll
0x00007ffc6c370000 - 0x00007ffc6c382000 	C:\Windows\System32\winrnr.dll
0x00007ffc76500000 - 0x00007ffc7650e000 	D:\jdks\17\jdk17.0.6_10\bin\sunmscapi.dll
0x00007ffc8c660000 - 0x00007ffc8c687000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffc8c620000 - 0x00007ffc8c65b000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffc83c10000 - 0x00007ffc83c17000 	C:\Windows\system32\wshunix.dll
0x00007ffc42840000 - 0x00007ffc42ccf000 	C:\Users\<USER>\AppData\Local\Temp\librocksdbjni14145711817872284057.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\jdks\17\jdk17.0.6_10\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;D:\jdks\17\jdk17.0.6_10\bin\server;C:\Program Files (x86)\Sangfor\SSL\ClientComponent;C:\Users\<USER>\AppData\Local\Temp

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:58343,suspend=y,server=n -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.profiles.active=local1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2022.1\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture.props -Dfile.encoding=UTF-8 
java_command: com.zatech.genesis.openapi.platform.OpenapiApplication
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\classpath167519030.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4242538496                                {product} {ergonomic}
   size_t MaxNewSize                               = 2543845376                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4242538496                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\jdks\17\jdk17.0.6_10
CLASSPATH=.;D:\jdks\17\jdk17.0.6_10\lib\dt.jar;D:\jdks\17\jdk17.0.6_10\lib\tools.jar
PATH=D:\jdks\17\jdk17.0.6_10\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\nodejs\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\soft\Git\cmd;"D:\jdks\17\jdk17.0.6_10\bin;D:\jdks\17\jdk17.0.6_10\jre\bin";D:\soft\scala\bin;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
USERNAME=jiaming.lin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.2364)
OS uptime: 2 days 8:27 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x8a, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi

Memory: 4k page, system-wide physical 16181M (580M free)
TotalPageFile size 31541M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 1041M, peak: 1050M
current process commit charge ("private bytes"): 1170M, peak: 1205M

vm_info: OpenJDK 64-Bit Server VM (17.0.6+10-LTS) for windows-amd64 JRE (17.0.6+10-LTS), built on Jan 13 2023 22:57:04 by "Administrator" with MS VC++ 15.9 (VS2017)

END.
