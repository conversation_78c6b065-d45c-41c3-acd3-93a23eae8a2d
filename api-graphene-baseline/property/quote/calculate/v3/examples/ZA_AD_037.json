{"request": {"tradeNo": "T8306674446814534", "riskClasses": "01", "goodsCode": "ZA_AD_037", "packageCode": "ADP01", "planCode": "ADP01", "coveragePeriod": 365, "coveragePeriodType": "DAY", "premiumFrequencyType": "SINGLE", "zoneId": "Europe/Zagreb", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "effectiveDate": "2025-03-01T17:21:58.299+08:00", "expiryDate": "2025-03-01T17:21:58.299+08:00", "applicationDate": "2025-04-22T17:21:58.299+08:00", "paidPremium": 219.42, "payerList": [{"individual": {"holderRelation": "SPOUSE", "certiType": "IDCARD", "certiNo": "43543546", "firstName": "ken", "lastName": "peter", "fullName": "ken peter", "birthPlace": "Shanghai", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "address": "state city district town"}], "middleName": "middle", "formattedLastName": "peter", "formattedMiddleName": "middle", "formattedFirstName": "ken", "formattedFullName": "ken middle peter", "fullName2": "ken peter", "lastName2": "peter2", "middleName2": "middle2", "firstName2": "ken2", "formattedLastName2": "peter2", "formattedMiddleName2": "middle2", "formattedFirstName2": "ken2", "formattedFullName2": "ken2 middle2 peter2", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "SE", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2020-01-01", "height": "180", "weight": "75", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "1001", "channelCode": "ZA002MYSHO", "channelUserCodePartyId": "CUP1001", "isCustomer": true, "countryCode": "US", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2025-01-01", "position": "Developer", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "TN123456", "blackStatus": false, "workingPlace": "Company", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "Office", "payerType": "FIRST", "extraInfo": "Some extra info"}, "payerType": "FIRST", "payMethod": "CASH"}], "insuredObjectList": [{"insuredType": "INSURED_DEVICE", "deviceNo": "3294039243234", "deviceSellerId": "3294039243234", "deviceBuyerId": "3294039243234", "deviceName": "iPhone 15", "deviceBrand": "Apple", "deviceModel": "Apple15", "deviceCategory": "PHONE", "deviceCategoryName": "Smartphone", "devicePrice": "5000", "deviceMarketValue": 5000, "deviceBuyerReviewScore": "5", "deviceSellerReviewScore": "5", "warrantyPeriod": "24 months", "warrantyPeriodCustomer": "<PERSON>", "devicePurchaseTime": "2023-08-16T17:43:02.857+08:00", "applicablePeriod": "36 months", "deviceUser": "<PERSON>", "deviceManufacturer": "Apple Inc.", "deviceDesc": "Latest Apple iPhone with advanced features.", "deviceImgUrl": "http://example.com/device.jpg", "premiumCode": "PC123456", "deviceStatus": "Brand", "extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "extraInfo": "Some extra information", "serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "category1": "ew1", "category2": "ew2", "category3": "ew-c3"}], "productList": [{"productCode": "ZA_PROD_EP", "sumInsured": 300000, "productLiabilityList": [{"liabilityCode": "700000002", "liabilityId": "700000002", "sumInsured": 300000, "serialNo": "21f392be-d31e-463f-9a59-965c1583fa4a"}], "serialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "policyHolder": {"individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "gender": "MALE", "certiNo": "43543546", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ZA002MYSHO", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "payerType": "FIRST", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address": "province city street house"}]}}, "insuredList": [{"insuredOrder": 1, "mainInsurantRelation": "MAIN_INSURANT", "relationshipWithMainInsured": "SELF", "serialNo": "2198d953-f51f-4563-8165-44e80ddae71d", "status": "VALID", "individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "gender": "MALE", "certiNo": "43543546", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ZA002MYSHO", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "payerType": "FIRST", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address": "province city street house"}]}}], "paymentPlan": {"samePaymentMethodForAllInstallments": true, "payMethod": "CASH"}}, "response": {"200": {"standardGrossPremium": "1000.00", "totalPremium": "1100.00", "loading": "50.00", "discount": "20.00", "standardNetPremium": "1030.00", "campaignDiscount": "10.00", "taxList": [{"taxType": "vat", "tax": "70.00"}], "installmentList": [{"installmentNo": 1, "standardNetPremiumIncCampaign": "515.00", "taxList": [{"taxType": "vat", "tax": "35.00"}]}, {"installmentNo": 2, "standardNetPremiumIncCampaign": "515.00", "taxList": [{"taxType": "vat", "tax": "35.00"}]}], "productList": [{"productCode": "P001", "standardGrossPremium": "500.00", "loading": "25.00", "discount": "10.00", "standardNetPremium": "515.00", "taxList": [{"taxType": "vat", "tax": "35.00"}]}, {"productCode": "P002", "standardGrossPremium": "500.00", "loading": "25.00", "discount": "10.00", "standardNetPremium": "515.00", "taxList": [{"taxType": "vat", "tax": "35.00"}]}], "totalCommission": "50.00", "commissionList": [{"commission": "30.00", "partnerCode": "PARTNER001"}, {"commission": "20.00", "partnerCode": "PARTNER002"}], "totalServiceFee": "20.00", "serviceFeeList": [{"serviceFee": "10.00", "partnerCode": "SERVICE001"}, {"serviceFee": "10.00", "partnerCode": "SERVICE002"}]}}}