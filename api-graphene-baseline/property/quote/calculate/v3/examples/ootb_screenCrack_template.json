{"request": {"coveragePeriod": 1, "coveragePeriodType": "YEARFULL", "premiumFrequencyType": "SINGLE", "planCode": "ScreenCrack_1Y_Template", "policyHolder": {"individual": {"lastName": "SSAA", "firstName": "ken", "fullName": "ken peter", "birthday": "2004-11-06", "gender": "MALE", "certiNo": "611001197612294277", "certiType": "IDCARD", "nationality": "SG", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}, "insuredList": [{"insuredOrder": 1, "relationshipWithPolicyholder": "SELF", "individual": {"lastName": "SSAA", "firstName": "ken", "fullName": "ken peter", "birthday": "2004-11-06", "gender": "MALE", "certiNo": "611001197612294277", "certiType": "IDCARD", "nationality": "SG", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}], "insuredRelationList": [{"serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["2b58aa27-0492-42a9-ae9b-dc6d18816b2a"]}], "insuredObjectList": [{"serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "insuredType": "INSURED_DEVICE", "extensions": {"E_IsDiplomat": "NO"}, "insuredComponentList": [{"componentType": "DEVICE", "deviceId": "deviceId123", "deviceName": "iPhone 16", "devicePrice": "5000", "deviceCategory": "PHONE", "deviceBrand": "Apple", "deviceSellerId": "1231212313", "warrantyPeriod": "24 months", "extensions": {"E_invoiceNo": "1112312321"}}], "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "extraInfo": "Some extra information", "category1": "ew1", "category2": "ew2", "category3": "ew-c3"}], "zoneId": "Europe/Zagreb", "goodsCode": "ScreenCrack_Template", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "effectiveDate": "2025-11-01T17:21:58.299+08:00", "productList": [{"serialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "productCode": "ScreenCrack_Template", "sumInsured": 5000, "productLiabilityList": [{"serialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b2a", "liabilityCode": "1726", "sumInsured": 5000}]}], "applicationDate": "2025-11-01T17:21:58.299+08:00"}, "response": {"totalTax": "6", "taxList": [{"taxType": "SERVICE_TAX", "tax": "6"}], "standardGrossPremium": "100", "baseCurrencyResult": {"installmentList": [{"productList": [{"productSaPremium": {"taxValues": {"taxValueList": [{"pureTaxRate": "0.06", "tax": "24.06", "productTaxType": "SERVICE_TAX", "taxRate": "6", "rateType": "PERCENTAGE", "coverageTotalTax": "24.06"}]}, "tax": "24.06", "finalPremium": "425.06", "standardPremium": "401", "finalSumInsured": "20050", "netPremium": 401, "sumInsured": "20050"}, "expiryDate": "2026-11-01T17:21:57.299+08", "effectiveDate": "2025-11-01T17:21:58.299+08", "productId": 2450831852994566, "liabilityList": [{"liabilityCode": "1726", "liabilityId": 396, "liabilitySaPremium": {"standardPremium": "401", "finalSumInsured": "20050", "netPremium": 401, "sumInsured": "20050"}}], "productCode": "ScreenCrack_Template"}], "isPeriodInInitialPeriod": true, "standardGrossPremium": "401", "dueEndDate": "2025-11-01T17:21:58.299+08", "totalPremium": "425.06", "dueDate": "2025-11-01T17:21:58.299+08", "currency": "ZAR", "totalTax": "24.06", "standardNetPremium": "401", "taxList": [{"taxType": "SERVICE_TAX", "tax": "24.06"}], "periodNo": 1}], "productList": [{"productSaPremium": {"taxValues": {"taxValueList": [{"pureTaxRate": "0.06", "tax": "24.06", "productTaxType": "SERVICE_TAX", "taxRate": "6", "rateType": "PERCENTAGE", "coverageTotalTax": "24.06"}]}, "tax": "24.06", "finalPremium": "425.06", "standardPremium": "401", "finalSumInsured": "20050", "netPremium": 401, "sumInsured": "20050"}, "expiryDate": "2026-11-01T17:21:57.299+08", "effectiveDate": "2025-11-01T17:21:58.299+08", "productId": 2450831852994566, "liabilityList": [{"liabilityCode": "1726", "liabilityId": 396, "liabilitySaPremium": {"standardPremium": "401", "finalSumInsured": "20050", "netPremium": 401, "sumInsured": "20050"}}], "productCode": "ScreenCrack_Template"}], "standardGrossPremium": "401", "taxList": [{"taxType": "SERVICE_TAX", "tax": "24.06"}], "installmentCalculationMethod": "PRE_CALCULATE_PREMIUM_FOR_ALL_INSTALLMENTS", "totalPremium": "425.06", "currency": "ZAR", "totalTax": "24.06", "standardNetPremium": "401"}, "totalPremium": "106", "currency": "MYR", "standardNetPremium": "100", "installmentCalculationMethod": "PRE_CALCULATE_PREMIUM_FOR_ALL_INSTALLMENTS", "installmentList": [{"productList": [{"productSaPremium": {"taxValues": {"taxValueList": [{"pureTaxRate": "0.06", "tax": "6", "productTaxType": "SERVICE_TAX", "taxRate": "6", "rateType": "PERCENTAGE", "coverageTotalTax": "6"}]}, "tax": "6", "finalPremium": "106", "standardPremium": "100", "finalSumInsured": "5000", "netPremium": 100, "sumInsured": "5000"}, "expiryDate": "2026-11-01T17:21:57.299+08", "effectiveDate": "2025-11-01T17:21:58.299+08", "productId": 2450831852994566, "liabilityList": [{"liabilityCode": "1726", "liabilityId": 396, "liabilitySaPremium": {"standardPremium": "100", "finalSumInsured": "5000", "netPremium": 100, "sumInsured": "5000"}}], "productCode": "ScreenCrack_Template"}], "isPeriodInInitialPeriod": true, "standardGrossPremium": "100", "dueEndDate": "2025-11-01T17:21:58.299+08", "totalPremium": "106", "dueDate": "2025-11-01T17:21:58.299+08", "currency": "MYR", "totalTax": "6", "standardNetPremium": "100", "taxList": [{"taxType": "SERVICE_TAX", "tax": "6"}], "periodNo": 1}], "productList": [{"productSaPremium": {"taxValues": {"taxValueList": [{"pureTaxRate": "0.06", "tax": "6", "productTaxType": "SERVICE_TAX", "taxRate": "6", "rateType": "PERCENTAGE", "coverageTotalTax": "6"}]}, "tax": "6", "finalPremium": "106", "standardPremium": "100", "finalSumInsured": "5000", "netPremium": 100, "sumInsured": "5000"}, "expiryDate": "2026-11-01T17:21:57.299+08", "effectiveDate": "2025-11-01T17:21:58.299+08", "productId": 2450831852994566, "liabilityList": [{"liabilityCode": "1726", "liabilityId": 396, "liabilitySaPremium": {"standardPremium": "100", "finalSumInsured": "5000", "netPremium": 100, "sumInsured": "5000"}}], "productCode": "ScreenCrack_Template"}]}}