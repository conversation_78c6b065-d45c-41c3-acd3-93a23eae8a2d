{"request": {"policyDeliveryMethod": "E_POLICY", "renewalMethod": "NONE_RENEWAL", "salesChannelList": [{"channelRole": "NON_OFFICE", "salesAgreementCode": "salesChannelName"}], "systemSource": "systemSource", "enableFullSync": false, "remark": "remark", "*goodsCode": "EDI_v1", "temporary": false, "currency": {"premiumCurrency": "SGD", "saCurrency": "SGD"}, "issuanceRelationList": [{"type": "RENEWAL_POLICY", "subType": "HOST_RELATION_NO", "relationNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "planCode": "All_risk", "riskClasses": "All_risk", "typeOfBusiness": "All_risk", "packageCode": "All_risk", "*thirdPartyTransactionNo": "All_risk", "premium": 11, "tradeNo": "1", "*coveragePeriodType": "YEARFULL", "calculationMethod": "CALCULATE_PREMIUM", "*coveragePeriod": 1, "*paymentPeriodType": "YEARFULL", "paymentPeriod": 1, "premiumFrequencyType": "YEAR", "orderNo": "orderNo", "quotationNo": "quotationNo", "applicationDate": "2024-04-22T17:21:58.299+08:00", "effectiveDate": "2024-05-01T17:21:58.299+08:00", "expiryDate": "2024-05-01T17:21:58.299+08:00", "zoneId": "Europe/Zagreb", "*policyHolder": {"individual": {"attachmentList": [{"refId": 1, "refType": "POLICY_HOLDER", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}]}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "isPromotional": true, "isCrossSell": true, "businessLicenseNumber": "BLN123456", "*companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "+1-**********", "registeredDate": "2023-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE"}}, "paymentPlan": {"*payMethod": "CASH", "*samePaymentMethodForAllInstallments": true, "installmentList": [{"*paymentNum": 1, "premium": "500.00", "paymentDate": "2024-06-26T00:00:00Z", "dueDate": "2024-07-26T00:00:00Z", "*payMethod": "CASH", "paymentStatus": "NORMAL"}], "extensions": {"extensions": "extensions"}}, "*insuredList": [{"insuredOrder": 1, "virtualKidNumber": 0, "virtualAdultNumber": 0, "virtualTotalNumber": 0, "mainInsurantRelation": "MAIN_INSURANT", "relationshipWithMainInsured": "SELF", "occupationClass": "Software Engineer", "insuredEffectiveDate": "2024-10-27T22:55:47.333+08:00", "insuredExpiryDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationReason": "DUE_TO_POS_INSURED", "status": "VALID", "individual": {"attachmentList": [{"refId": 1, "refType": "POLICY_HOLDER", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}]}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "isPromotional": true, "isCrossSell": true, "businessLicenseNumber": "BLN123456", "companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "+1-**********", "registeredDate": "2023-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE"}}], "payerList": [{"individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "*companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "+1-**********", "registeredDate": "2023-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE"}, "payMethod": "CASH", "payerType": "FIRST", "autoDeduction": true}], "payeeList": [{"autoDeduction": true, "mainInsuredRelation": "SELF", "payMethod": "CASH", "payeeType": "SB", "sharePercentage": "50%", "*individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "productList": [{"*productCode": "EDI_v1", "sumInsured": 112.32, "productLiabilityList": [{"liabilityCode": "1002", "sumInsured": 112.32, "loadingList": "*", "discountList": "*"}]}], "campaignList": [{"campaignCode": "campaignCode", "periodNo": "1", "promotionCode": "promotionCode", "campaignPayerList": "*"}], "attachmentList": [{"*refType": "DEVICE", "*attachmentTypeCode": "attachmentTypeCode", "*attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "*attachmentName": "Idcard.jpg"}], "*insuredObjectList": [{"serialNo": "7665de1f-72cf-497c-b30a-bb7f85e04f1b", "insuredType": "ORDER", "extensions": {"2": "2"}, "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "ORDER", "bookingNumber": "p18589017836", "orderNumber": "p18547975564", "orderType": "MALE", "numberOfOrder": 1, "orderDate": "2025-02-21T18:37:34.126+08:00", "orderCurrency": "1", "orderPrice": "1", "effectiveDate": "2025-02-21T18:37:34.126+08:00", "effectiveDateTimeZone": "2025-02-21T18:37:34.126+08:00", "expiryDate": "2025-02-21T18:37:34.126+08:00", "expiryDateTimeZone": "Asia/Shanghai", "eventCode": "eventCode", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "INSURED_BUILDING", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "GROSS_PROFIT", "insuredNo": "1", "insuredObjectName": "1", "averageAnnualIncomeInPast3Y": 1, "averageVariableCostsInPast3Y": 3, "projectedAnnualIncomeCurrentYear": 3, "variableCostsCurrentYear": 3}, {"extensions": {"E_IsDiplomat": "NO"}, "componentType": "LOSS_OF_RENT", "insuredNo": "1", "insuredObjectName": "1", "averageRentalIncomeInPast3Y": 1, "projectedRentalIncomeCurrentYear": 3}, {"extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "insuredNo", "componentType": "BUILDING", "country": "country", "zipCode": "zipCode", "longitude": "longitude", "latitude": "latitude", "builtYear": "2025-05-01T17:21:58.299+08:00", "structure": "structure", "buildingType": "buildingType", "buildingSize": "buildingSize", "claimHistory": "claimHistory", "fireAlarm": "fireAlarm", "burglarAlarm": "burglar<PERSON>larm", "address1": "address1", "address2": "address2", "address3": "address3", "address4": "address4", "address5": "address5", "buildingIndustryCategory": "buildingIndustryCategory", "floodZone": "floodZone", "earthquakeZone": "earthquakeZone", "gpsCoordinateDegree": "gpsCoordinateDegree", "gpsCoordinateMinute": "gpsCoordinateMinute", "gpsCoordinateSecond": "gpsCoordinateSecond", "numberOfFloor": "numberOfFloor", "buildingArea": "buildingArea", "buildingOccupiedYears": "buildingOccupiedYears", "rentedArea": "rentedArea", "buildingAge": "buildingAge", "riskQuality": "riskQuality", "damageability": "damageability", "combustibility": "combustibility", "buildingOccupation": "buildingOccupation", "securitySystem": "securitySystem", "occupiedAfterBusinessHours": "occupiedAfterBusinessHours", "buildingFrame": "buildingFrame", "roofStructure": "roofStructure", "wallStructure": "wallStructure", "floorStructure": "floorStructure", "adjoiningLeftBuildingOccupation": "adjoiningLeftBuildingOccupation", "adjoiningLeftBuildingDistance": "adjoiningLeftBuildingDistance", "adjoiningRightBuildingOccupation": "adjoiningRightBuildingOccupation", "adjoiningRightBuildingDistance": "adjoiningRightBuildingDistance", "adjoiningFrontBuildingOccupation": "adjoiningFrontBuildingOccupation", "adjoiningFrontBuildingDistance": "adjoiningFrontBuildingDistance", "adjoiningRearBuildingOccupation": "adjoiningRearBuildingOccupation", "adjoiningRearBuildingDistance": "adjoiningRearBuildingDistance", "ownershipStatus": "ownershipStatus", "subBuildingType": "subBuildingType", "phHolderRoleOfBuilding": "phHolderRoleOfBuilding", "constructionType": "constructionType", "housingLoan": "housingLoan", "bank": "bank", "loanNo": "loanNo", "sumInsuredForBuilding": "sumInsuredForBuilding", "natureOfBusiness": "natureOfBusiness", "mainUseOfBuilding": "mainUseOfBuilding", "insuredObjectName": "insuredObjectName", "estimatedValue": "estimatedValue", "heightOfBuilding": "heightOfBuilding", "insuredInBasement": "insuredInBasement"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "LOCATION_INSURED", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "ADDRESS", "zipCode": "2000002", "address1": "provincex", "address2": "cityx", "address3": "streetx", "longitude": "33", "latitude": "44", "floodZone": "lowRisk", "earthquakeZone": "lowRisk", "refObjectSerialsNoList": ["1", "1"]}, {"extensions": {"E_IsDiplomat": "NO"}, "componentType": "GROSS_PROFIT", "insuredNo": "1", "insuredObjectName": "1", "averageAnnualIncomeInPast3Y": 1, "averageVariableCostsInPast3Y": 3, "projectedAnnualIncomeCurrentYear": 3, "variableCostsCurrentYear": 3}, {"extensions": {"E_IsDiplomat": "NO"}, "componentType": "LOSS_OF_RENT", "insuredNo": "1", "insuredObjectName": "1", "averageRentalIncomeInPast3Y": 1, "projectedRentalIncomeCurrentYear": 3}, {"extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "insuredNo", "componentType": "BUILDING", "country": "country", "zipCode": "zipCode", "longitude": "longitude", "latitude": "latitude", "builtYear": "2025-05-01T17:21:58.299+08:00", "structure": "structure", "buildingType": "buildingType", "buildingSize": "buildingSize", "claimHistory": "claimHistory", "fireAlarm": "fireAlarm", "burglarAlarm": "burglar<PERSON>larm", "address1": "address1", "address2": "address2", "address3": "address3", "address4": "address4", "address5": "address5", "buildingIndustryCategory": "buildingIndustryCategory", "floodZone": "floodZone", "earthquakeZone": "earthquakeZone", "gpsCoordinateDegree": "gpsCoordinateDegree", "gpsCoordinateMinute": "gpsCoordinateMinute", "gpsCoordinateSecond": "gpsCoordinateSecond", "numberOfFloor": "numberOfFloor", "buildingArea": "buildingArea", "buildingOccupiedYears": "buildingOccupiedYears", "rentedArea": "rentedArea", "buildingAge": "buildingAge", "riskQuality": "riskQuality", "damageability": "damageability", "combustibility": "combustibility", "buildingOccupation": "buildingOccupation", "securitySystem": "securitySystem", "occupiedAfterBusinessHours": "occupiedAfterBusinessHours", "buildingFrame": "buildingFrame", "roofStructure": "roofStructure", "wallStructure": "wallStructure", "floorStructure": "floorStructure", "adjoiningLeftBuildingOccupation": "adjoiningLeftBuildingOccupation", "adjoiningLeftBuildingDistance": "adjoiningLeftBuildingDistance", "adjoiningRightBuildingOccupation": "adjoiningRightBuildingOccupation", "adjoiningRightBuildingDistance": "adjoiningRightBuildingDistance", "adjoiningFrontBuildingOccupation": "adjoiningFrontBuildingOccupation", "adjoiningFrontBuildingDistance": "adjoiningFrontBuildingDistance", "adjoiningRearBuildingOccupation": "adjoiningRearBuildingOccupation", "adjoiningRearBuildingDistance": "adjoiningRearBuildingDistance", "ownershipStatus": "ownershipStatus", "subBuildingType": "subBuildingType", "phHolderRoleOfBuilding": "phHolderRoleOfBuilding", "constructionType": "constructionType", "housingLoan": "housingLoan", "bank": "bank", "loanNo": "loanNo", "sumInsuredForBuilding": "sumInsuredForBuilding", "natureOfBusiness": "natureOfBusiness", "mainUseOfBuilding": "mainUseOfBuilding", "insuredObjectName": "insuredObjectName", "estimatedValue": "estimatedValue", "heightOfBuilding": "heightOfBuilding", "insuredInBasement": "insuredInBasement"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "ADDRESS", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "ADDRESS", "zipCode": "2000002", "address1": "provincex", "address2": "cityx", "address3": "streetx", "longitude": "33", "latitude": "44", "floodZone": "lowRisk", "earthquakeZone": "lowRisk", "refObjectSerialsNoList": ["1", "1"]}]}, {"extensions": {"E_IsDiplomat": "NO"}, "shareFlag": true, "serialNo": "SN*********0", "insuredType": "INSURED_DEVICE", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "DEVICE", "deviceNo": "3294039243234", "deviceName": "iPhone 15", "deviceBrand": "Apple", "deviceModel": "Apple15", "deviceCategory": "PHONE", "deviceCategoryName": "Smartphone", "deviceMarketValue": 5000, "deviceBuyerReviewScore": "5", "deviceSellerReviewScore": "5", "warrantyPeriod": "24 months", "warrantyPeriodCustomer": "<PERSON>", "devicePurchaseTime": "2023-08-16T17:43:02.857+08:00", "applicablePeriod": "36 months", "deviceUser": "<PERSON>", "deviceManufacturer": "Apple Inc.", "deviceDesc": "Latest Apple iPhone with advanced features.", "deviceImgUrl": "http://example.com/device.jpg", "premiumCode": "PC123456", "deviceStatus": "Brand", "deviceSellerId": "11", "deviceId": "1"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "GROSS_PROFIT", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "GROSS_PROFIT", "insuredNo": "1", "insuredObjectName": "1", "averageAnnualIncomeInPast3Y": 1, "averageVariableCostsInPast3Y": 3, "projectedAnnualIncomeCurrentYear": 3, "variableCostsCurrentYear": 3}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "LOSS_OF_RENT", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "LOSS_OF_RENT", "insuredNo": "1", "insuredObjectName": "1", "averageAnnualIncomeInPast3Y": 1, "averageVariableCostsInPast3Y": 3, "projectedAnnualIncomeCurrentYear": 3, "variableCostsCurrentYear": 3}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "AUXILIARY_BUILDINGS", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "insuredNo", "componentType": "BUILDING", "country": "country", "zipCode": "zipCode", "longitude": "longitude", "latitude": "latitude", "builtYear": "2025-05-01T17:21:58.299+08:00", "structure": "structure", "buildingType": "buildingType", "buildingSize": "buildingSize", "claimHistory": "claimHistory", "fireAlarm": "fireAlarm", "burglarAlarm": "burglar<PERSON>larm", "address1": "address1", "address2": "address2", "address3": "address3", "address4": "address4", "address5": "address5", "buildingIndustryCategory": "buildingIndustryCategory", "floodZone": "floodZone", "earthquakeZone": "earthquakeZone", "gpsCoordinateDegree": "gpsCoordinateDegree", "gpsCoordinateMinute": "gpsCoordinateMinute", "gpsCoordinateSecond": "gpsCoordinateSecond", "numberOfFloor": "numberOfFloor", "buildingArea": "buildingArea", "buildingOccupiedYears": "buildingOccupiedYears", "rentedArea": "rentedArea", "buildingAge": "buildingAge", "riskQuality": "riskQuality", "damageability": "damageability", "combustibility": "combustibility", "buildingOccupation": "buildingOccupation", "securitySystem": "securitySystem", "occupiedAfterBusinessHours": "occupiedAfterBusinessHours", "buildingFrame": "buildingFrame", "roofStructure": "roofStructure", "wallStructure": "wallStructure", "floorStructure": "floorStructure", "adjoiningLeftBuildingOccupation": "adjoiningLeftBuildingOccupation", "adjoiningLeftBuildingDistance": "adjoiningLeftBuildingDistance", "adjoiningRightBuildingOccupation": "adjoiningRightBuildingOccupation", "adjoiningRightBuildingDistance": "adjoiningRightBuildingDistance", "adjoiningFrontBuildingOccupation": "adjoiningFrontBuildingOccupation", "adjoiningFrontBuildingDistance": "adjoiningFrontBuildingDistance", "adjoiningRearBuildingOccupation": "adjoiningRearBuildingOccupation", "adjoiningRearBuildingDistance": "adjoiningRearBuildingDistance", "ownershipStatus": "ownershipStatus", "subBuildingType": "subBuildingType", "phHolderRoleOfBuilding": "phHolderRoleOfBuilding", "constructionType": "constructionType", "housingLoan": "housingLoan", "bank": "bank", "loanNo": "loanNo", "sumInsuredForBuilding": "sumInsuredForBuilding", "natureOfBusiness": "natureOfBusiness", "mainUseOfBuilding": "mainUseOfBuilding", "insuredObjectName": "insuredObjectName", "estimatedValue": "estimatedValue", "heightOfBuilding": "heightOfBuilding", "insuredInBasement": "insuredInBasement"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "ENGINEERING_STRUCTURES", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "STRUCTURE", "insuredObjectName": "1", "structureType": "structureType", "constructionDetail": "constructionDetail"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "REFRIGERATED_GOODS", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "shareFlag": true, "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "REFRIGERATED_PRODUCT", "insuredObjectName": "1", "remarksOnInsuredObject": "remarksOnInsuredObject", "coldRoomBuildingArea": "coldRoomBuildingArea", "coolantDescription": "coolantDescription"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "SWIMMING_POOLS", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "POOL", "insuredObjectName": "1", "poolType": "poolType", "remarksOnInsuredObject": "remarksOnInsuredObject"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "RAW_MATERIALS_PRODUCTS_MERCHANDISES", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "PRODUCT", "componentInfoList": [{"productType": "productType", "insuredNo": "insuredNo", "productSubType": "productSubType", "productName": "A", "brand": "BRAND", "model": "MODEL", "exportationProducts": "SSA", "salesQuantity": "BEST", "unitPrice": "10", "estimatedSalesRevenue": "estimatedSalesRevenue"}]}]}, {"extensions": {"E_IsDiplomat": "NO"}, "serialNo": "serialNo", "insuredType": "OUTDOOR_SUPPLIES", "category1": "category1", "category2": "category2", "category3": "category3", "insuredNo": "insuredNo", "remark": "remark", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "PRODUCT", "componentInfoList": [{"productType": "productType", "insuredNo": "insuredNo", "productSubType": "productSubType", "productName": "A", "brand": "BRAND", "model": "MODEL", "exportationProducts": "SSA", "salesQuantity": "BEST", "unitPrice": "10", "estimatedSalesRevenue": "estimatedSalesRevenue"}]}]}], "insuredRelationList": [{"serialNo": "7665de1f-72cf-497c-b30a-bb7f85e04f1b", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["1", "1"]}], "extensions": {"E_IsDiplomat": "NO"}, "userAuthInfo": "*", "claimStackTemplateList": "*", "claimStackConfigList": "*"}, "response": {"policies": [{"thirdPartyTransactionNo": "B34905543", "orderNo": "123", "quotationNo": "133", "proposalNo": "133", "policyNo": "123", "proposalStatus": "33211109011", "ruleValidateResult": {"decision": "DECLINE", "details": [{"ruleCode": "MSIG_VN_Travel_manualUW", "decisionType": "DECLINE", "msgValue": "pls error", "msgCode": "pls error"}]}}]}}