{"request": {"tradeNo": "tradeno-123131437", "thirdPartyTransactionNo": "third-1231412537", "coveragePeriod": 1, "coveragePeriodType": "YEARFULL", "premiumFrequencyType": "SINGLE", "planCode": "ScreenCrack_1Y_Template", "policyHolder": {"individual": {"lastName": "SSAA", "firstName": "ken", "fullName": "ken peter", "birthday": "2004-11-06", "gender": "MALE", "certiNo": "611001197612294277", "certiType": "IDCARD", "nationality": "SG", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}, "insuredList": [{"insuredOrder": 1, "relationshipWithPolicyholder": "SELF", "individual": {"lastName": "SSAA", "firstName": "ken", "fullName": "ken peter", "birthday": "2004-11-06", "gender": "MALE", "certiNo": "611001197612294277", "certiType": "IDCARD", "nationality": "SG", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}], "insuredRelationList": [{"serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["2b58aa27-0492-42a9-ae9b-dc6d18816b2a"]}], "insuredObjectList": [{"serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "insuredType": "INSURED_DEVICE", "extensions": {"E_IsDiplomat": "NO"}, "insuredComponentList": [{"componentType": "DEVICE", "deviceId": "deviceId123", "deviceName": "iPhone 16", "devicePrice": "5000", "deviceCategory": "PHONE", "deviceBrand": "Apple", "deviceSellerId": "1231212313", "warrantyPeriod": "24 months", "extensions": {"E_invoiceNo": "1112312321"}}], "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "extraInfo": "Some extra information", "category1": "ew1", "category2": "ew2", "category3": "ew-c3"}], "paymentPlan": {"samePaymentMethodForAllInstallments": true, "payMethod": "CASH"}, "paidPremium": 106, "zoneId": "Europe/Zagreb", "goodsCode": "ScreenCrack_Template", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "effectiveDate": "2025-11-01T17:21:58.299+08:00", "productList": [{"serialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "productCode": "ScreenCrack_Template", "sumInsured": 5000, "productLiabilityList": [{"serialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b2a", "liabilityCode": "1726", "sumInsured": 5000}]}], "applicationDate": "2025-11-01T17:21:58.299+08:00"}, "response": {"policies": [{"proposalNo": "20251023775864", "quotationNo": "20251023775864", "policyNo": "102025116741", "thirdPartyTransactionNo": "third-1231412537", "ruleValidateResult": {"decision": "ACCEPT", "details": [{"decisionType": "ACCEPT", "ruleCode": "ScreenCrack_UW_devicePrice"}]}, "proposalStatus": "WAITING_FOR_ISSUANCE"}]}}