{"request": {"tradeNo": "T@natural", "coveragePeriod": 6, "coveragePeriodType": "MONTH", "premiumFrequencyType": "SINGLE", "thirdPartyTransactionNo": "thrid@natural", "payerList": [{"individual": {"holderRelation": "SPOUSE", "certiType": "IDCARD", "certiNo": "43543546", "firstName": "ken", "lastName": "peter", "fullName": "ken peter", "birthPlace": "Shanghai", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town"}], "middleName": "middle", "formattedLastName": "peter", "formattedMiddleName": "middle", "formattedFirstName": "ken", "formattedFullName": "ken middle peter", "fullName2": "ken peter", "lastName2": "peter2", "middleName2": "middle2", "firstName2": "ken2", "formattedLastName2": "peter2", "formattedMiddleName2": "middle2", "formattedFirstName2": "ken2", "formattedFullName2": "ken2 middle2 peter2", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "SE", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2020-01-01", "height": "180", "weight": "75", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "1001", "channelCode": "CC", "channelUserCodePartyId": "CUP1001", "isCustomer": true, "countryCode": "US", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2025-01-01", "position": "Developer", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "TN123456", "blackStatus": false, "workingPlace": "Company", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "Office", "payerType": "FIRST", "extraInfo": "Some extra info"}, "payerType": "FIRST", "payMethod": "CASH"}], "paidPremium": 53, "planCode": "OOTB_ZA_6M_ScreenCrack", "attachmentList": [{"refType": "DEVICE", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}], "insuredObjectList": [{"insuredType": "INSURED_DEVICE", "deviceNo": "3294039243234", "deviceName": "iPhone 15", "deviceBrand": "Apple", "deviceModel": "Apple15", "deviceCategory": "PHONE", "deviceCategoryName": "Smartphone", "devicePrice": "5000", "deviceMarketValue": 5000, "deviceBuyerReviewScore": "5", "deviceSellerReviewScore": "5", "warrantyPeriod": "24 months", "warrantyPeriodCustomer": "<PERSON>", "devicePurchaseTime": "2023-08-16T17:43:02.857+08:00", "applicablePeriod": "36 months", "deviceUser": "<PERSON>", "deviceManufacturer": "Apple Inc.", "deviceDesc": "Latest Apple iPhone with advanced features.", "deviceImgUrl": "http://example.com/device.jpg", "premiumCode": "PC123456", "deviceStatus": "Brand", "extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "extraInfo": "Some extra information", "serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "category1": "ew1", "category2": "ew2", "category3": "ew-c3"}], "zoneId": "Europe/Zagreb", "goodsCode": "OOTB_ZA_ScreenCrack", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "effectiveDate": "2024-05-01T17:21:58.299+08:00", "productList": [{"productCode": "OOTB_ZA_ScreenCrack", "sumInsured": 5000, "productLiabilityList": [{"liabilityCode": "1726", "sumInsured": 5000}]}], "applicationDate": "2024-04-22T17:21:58.299+08:00", "policyHolder": {"individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "gender": "MALE", "certiNo": "43543546", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "payerType": "FIRST", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house"}]}}}, "response": {"200": {"success": "TRUE"}}}