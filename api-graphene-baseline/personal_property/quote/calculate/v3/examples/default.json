{"request": {"coveragePeriod": 6, "coveragePeriodType": "MONTH", "premiumFrequencyType": "SINGLE", "insuredObjectList": [{"insuredType": "INSURED_DEVICE", "deviceNo": "3294039243234", "deviceName": "iPhone 15", "devicePrice": "5000", "deviceBrand": "Apple", "deviceModel": "Apple15", "deviceCategory": "PHONE", "deviceCategoryName": "Smartphone", "deviceMarketValue": 5000, "deviceBuyerReviewScore": "5", "deviceSellerReviewScore": "5", "warrantyPeriod": "24 months", "warrantyPeriodCustomer": "<PERSON>", "devicePurchaseTime": "2023-08-16T17:43:02.857+08:00", "applicablePeriod": "36 months", "deviceUser": "<PERSON>", "deviceManufacturer": "Apple Inc.", "deviceDesc": "Latest Apple iPhone with advanced features.", "deviceImgUrl": "http://example.com/device.jpg", "premiumCode": "PC123456", "deviceStatus": "Brand", "extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "extraInfo": "Some extra information"}], "zoneId": "Europe/Zagreb", "goodsCode": "OOTB_ZA_ScreenCrack", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "planCode": "OOTB_ZA_6M_ScreenCrack", "effectiveDate": "2024-10-27T22:55:47.333+00:00", "productList": [{"productCode": "OOTB_ZA_ScreenCrack", "sumInsured": 5000, "productLiabilityList": [{"liabilityCode": "1726", "sumInsured": 5000}]}], "applicationDate": "2024-10-27T22:55:47.333+00:00"}, "response": {"200": {"value": {"taxList": [{"taxType": "SERVICE_TAX", "tax": "3"}], "standardGrossPremium": "50", "totalPremium": "53", "standardNetPremium": "50"}}}}