{"request": {"issueWithoutPayment": true, "isLegalBeneficiary": false, "policyDeliveryMethod": "E_POLICY", "renewalMethod": "NONE_RENEWAL", "insureCreator": "1", "salesChannelList": [{"channelRole": "NON_OFFICE", "agentCode": "1", "branchCode": "1", "agentPathCode": "1", "salesAgreementCode": "salesChannelName"}], "systemSource": "systemSource", "enableFullSync": false, "remark": "remark", "temporary": true, "*goodsCode": "EDI_v1", "currency": {"premiumCurrency": "SGD", "saCurrency": "SGD"}, "planCode": "All_risk", "questionnaire": {"applyNo": "*********", "purpose": "GENERAL_DECLARATION"}, "agreementNo": "1313112313123", "*thirdPartyTransactionNo": "issue@natural", "premium": 6.739, "tradeNo": "T@natural", "coveragePeriodType": "YEARFULL", "coveragePeriod": 1, "paymentPeriodType": "YEARFULL", "paymentPeriod": 1, "premiumFrequencyType": "YEAR", "orderNo": "10000000000000000000000000000000000000000000000000000000000", "quotationNo": "121", "applicationDate": "2024-04-22T17:21:58.299+08:00", "effectiveDate": "2024-05-01T17:21:58.299+08:00", "expiryDate": "2024-05-01T17:21:58.299+08:00", "zoneId": "Europe/Zagreb", "policyHolder": {"individual": {"issueWithoutPayment": true, "attachmentList": [{"refId": 1, "refType": "POLICY_HOLDER", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "extensions": {"q243": "234"}}, "organization": {"issueWithoutPayment": true, "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "+1-**********", "registeredDate": "2023-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE"}}, "insuredList": [{"insuredOrder": 1, "virtualKidNumber": 0, "virtualAdultNumber": 0, "virtualTotalNumber": 0, "mainInsurantRelation": "MAIN_INSURANT", "relationshipWithMainInsured": "SELF", "occupationClass": "Software Engineer", "insuredEffectiveDate": "2024-10-27T22:55:47.333+08:00", "insuredExpiryDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationReason": "DUE_TO_POS_INSURED", "status": "VALID", "individual": {"attachmentList": [{"refId": 1, "refType": "POLICY_HOLDER", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}]}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "+1-**********", "registeredDate": "2023-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE"}, "extensions": {"234": "234"}}], "payerList": [{"individual": {"issueWithoutPayment": true, "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "extensions": {"3": "123"}}, "payMethod": "CASH", "payerType": "FIRST", "autoDeduction": true}], "payeeList": [{"autoDeduction": true, "mainInsuredRelation": "SELF", "payMethod": "CASH", "payeeType": "SB", "sharePercentage": "50%", "individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}, "extensions": {"342": "243"}}], "productList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0432b", "*productCode": "EDI_v1", "sumInsured": 112.32, "coveragePlanCode": "Single_Adult_Upgraded", "productLiabilityList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0433b", "*liabilityCode": "1002", "sumInsured": 112.32, "loadingList": "*", "discountList": "*"}]}], "planList": [{"extensions": {}, "*planName": "Manager Plan", "*planCode": "001", "*planType": "PRE_DEFINED_PLAN"}], "unnamedInsuredList": [{"averageAge": "35", "averageSalary": "50000", "occupationCategory": "developer", "numberOfInsured": 100, "malePercentage": "60.5", "planCode": "PLAN001", "status": "ACTIVE"}], "campaignList": [{"campaignCode": "campaignCode", "periodNo": "1", "promotionCode": "promotionCode", "campaignPayerList": "*"}], "attachmentList": [{"refType": "DEVICE", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}], "insuredRelationList": [{"serialNo": "7665de1f-72cf-497c-b30a-bb7f85e04f1b", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["1", "1"]}], "issuanceRelationList": [{"type": "RENEWAL_POLICY", "subType": "HOST_RELATION_NO", "relationNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "insuredObjectList": [{"serialNo": "7665de1f-72cf-497c-b30a-bb7f85e04f1b", "insuredType": "TRAVEL", "extensions": {"E_IsDiplomat": "NO"}, "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "insuredId": "insuredId", "insuredNo": "insuredNo", "componentType": "TRAVEL", "travelType": "SINGLE", "travelStartDate": "2023-08-16T17:43:02.857+08:00", "travelEndDate": "2023-08-19T17:43:02.857+08:00", "destinationCountry": "ANGUILLA", "deductibleAmount": 11, "adultNumber": 11, "childrenNumber": 11, "daysInAdvance": 11, "maxTemperature": 11, "departureTimeZone": "11", "destinationTimeZone": "11", "destinationCity": "11", "departureCity": "11", "travelOrderType": "11", "travelExpense": "11", "travelOrderNumber": "11", "bookingNumber": "11", "bookingTime": "2023-08-19", "travelAgency": "1", "destinationRegion": "1", "departureCountry": "1", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "insuredId": "insuredId", "insuredNo": "insuredNo", "componentType": "TRIP", "tripType": "SINGLE", "transportationNumber": "202409", "arrivalAirport": "Pudong", "destinationCity": "destinationCity", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "insuredId": "insuredId", "insuredNo": "insuredNo", "componentType": "FELLOW_TRAVELLER", "fellowTravellerName": "morgan", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "insuredId": "insuredId", "insuredNo": "insuredNo", "componentType": "HOTEL", "hotelName": "morganHotel", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}, {"extensions": {"E_IsDiplomat": "NO"}, "insuredId": "insuredId", "insuredNo": "insuredNo", "componentType": "TICKET", "ticketType": "ticket@natural", "ticketNumber": "ticket@natural", "entryTime": "2023-01-0", "ticketPrice": 13, "placeOfInterest": "placeOfInterest", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}]}], "extensions": {"E_IsDiplomat": "NO"}, "beneficiaryList": [{"benefitRatio": "SELF", "insurantRelation": ["SELF", "SPOUSE", "CHILDREN", "PARENTS", "OTHER"], "benefitType": ["NORMAL", "IRREVOCABLE", "DEFAULT"], "beneficiaryOrder": ["PRIMARY", "SECONDARY", "THIRD"], "individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelCode": "English", "channelUserNo": "English", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": ["ADDRESS", "HA", "CA", "DELIVERY", "OVERSEAS", "CITIZENID", "CONTACT", "PERMANENT", "RESIDENTIAL", "MAILING", "BILLING"], "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "userAuthInfo": "*", "claimStackTemplateList": "*", "claimStackConfigList": "*"}, "response": {"policies": [{"thirdPartyTransactionNo": "B34905543", "orderNo": "123", "quotationNo": "133", "policyNo": "123", "proposalNo": "123", "tradeNo": "***********", "proposalStatus": "***********", "ruleValidateResult": {"decision": "DECLINE", "details": [{"ruleCode": "MSIG_VN_Travel_manualUW", "decisionType": "DECLINE", "msgValue": "pls error", "msgCode": "pls error"}]}}]}}