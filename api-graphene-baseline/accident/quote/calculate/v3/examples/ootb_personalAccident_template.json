{"request": {"goodsCode": "PersonalAccident_Template", "coveragePeriodType": "DAY", "coveragePeriod": 1, "premiumFrequencyType": "SINGLE", "paymentPeriodType": "SINGLE", "paymentPeriod": 1, "premiumFrequency": 1, "issueDate": "2025-10-22T08:37:49.761+08:00", "applicationDate": "2025-10-21T08:37:49.761+08:00", "zoneId": "Asia/Shanghai", "planCode": "PersonalAccident_02_Template", "insuredRelationList": [{"serialNo": "insured-ci2025101514150001-001", "relationType": "PRODUCT_INSURED", "productSerialNo": "3e7e5391-2a37-4d23-a3de-b56c27f74420"}], "policyHolder": {"individual": {"fullName": "ken peter", "lastName": "peter", "firstName": "ken", "birthday": "1995-06-06", "gender": "MALE", "certiNo": "43543546-89984894", "certiType": "IDCARD", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "industryCode": "IT", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 10, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}, "insuredList": [{"serialNo": "insured-ci2025101514150001-001", "insuredOrder": 1, "relationshipWithPolicyholder": "SELF", "individual": {"fullName": "ken peter", "lastName": "peter", "firstName": "ken", "birthday": "1995-06-06", "gender": "MALE", "certiNo": "43543546-89984894", "certiType": "IDCARD", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "industryCode": "IT", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 10, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}], "productList": [{"serialNo": "3e7e5391-2a37-4d23-a3de-b56c27f74420", "productCode": "PersonalAccident_Template", "effectiveDate": "2025-10-22T08:37:49.761+08:00", "sumInsured": 100000, "coveragePeriodType": "DAY", "coveragePeriod": "1", "premiumFrequencyType": "SINGLE", "paymentPeriodType": "SINGLE", "productLiabilityList": [{"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230a", "sumInsured": 100000, "liabilityCode": "1002"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230b", "sumInsured": 100000, "liabilityCode": "1101"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230c", "sumInsured": 20000, "liabilityCode": "1359"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230d", "sumInsured": 50000, "liabilityCode": "3302"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230e", "sumInsured": 800, "liabilityCode": "2125"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230f", "sumInsured": 50000, "liabilityCode": "1029"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230g", "sumInsured": 50000, "liabilityCode": "1007"}]}], "effectiveDate": "2025-10-22T08:37:49.761+08:00"}, "response": {"200": {"totalTax": "0.027", "taxList": [{"taxType": "GST", "tax": "0.027"}], "standardGrossPremium": "0.273", "baseCurrencyResult": {"installmentList": [{"productList": [{"productSaPremium": {"annualNetPremium": "0.200", "standardPremiumIncTax": "0.220", "finalPremium": "0.220", "annualStandardPremium": "0.200", "standardPremium": "0.200", "finalSumInsured": "180.564", "netPremiumIncTax": 0.22, "taxValues": {"taxValueList": [{"pureTaxRate": "0.1", "annualTax": "0.020", "tax": "0.020", "productTaxType": "GST", "taxRate": "10", "rateType": "PERCENTAGE"}]}, "tax": "0.020", "annualNetPremiumIncTax": "0.220", "annualTotalTax": "0", "annualStandardPremiumIncTax": "0.220", "netPremium": 0.2, "sumInsured": "180.564"}, "expiryDate": "2026-07-11T06:55:46.333+08", "effectiveDate": "2025-07-16T06:55:47.333+08", "productId": 2395562854858758, "liabilityList": [{"liabilityCode": "1002", "liabilityId": 102, "liabilitySaPremium": {"netPremiumIncTax": 0.22, "standardPremiumIncTax": "0.220", "annualStandardPremiumIncTax": "0.220", "standardPremium": "0.200", "finalSumInsured": "180.564", "netPremium": 0.2, "sumInsured": "180.564"}}, {"liabilityCode": "1101", "liabilityId": 115, "liabilitySaPremium": {"sumInsured": "180.564", "finalSumInsured": "180.564"}}, {"liabilityCode": "1330", "liabilityId": 417, "liabilitySaPremium": {"sumInsured": "180.564", "finalSumInsured": "180.564"}}, {"liabilityCode": "1331", "liabilityId": 418, "liabilitySaPremium": {"sumInsured": "180.564", "finalSumInsured": "180.564"}}], "productCode": "Snack_Accident_MSIG"}], "standardGrossPremium": "0.200", "totalPremium": "0.220", "currency": "USD", "totalTax": "0.020", "standardNetPremium": "0.200", "taxList": [{"taxType": "GST", "tax": "0.020"}], "periodNo": 1}], "productList": [{"productSaPremium": {"annualNetPremium": "0.200", "standardPremiumIncTax": "0.220", "finalPremium": "0.220", "annualStandardPremium": "0.200", "standardPremium": "0.200", "finalSumInsured": "180.564", "netPremiumIncTax": 0.22, "taxValues": {"taxValueList": [{"pureTaxRate": "0.1", "annualTax": "0.020", "tax": "0.020", "productTaxType": "GST", "taxRate": "10", "rateType": "PERCENTAGE"}]}, "tax": "0.020", "annualNetPremiumIncTax": "0.220", "annualTotalTax": "0", "annualStandardPremiumIncTax": "0.220", "netPremium": 0.2, "sumInsured": "180.564"}, "expiryDate": "2026-07-11T06:55:46.333+08", "effectiveDate": "2025-07-16T06:55:47.333+08", "productId": 2395562854858758, "liabilityList": [{"liabilityCode": "1002", "liabilityId": 102, "liabilitySaPremium": {"netPremiumIncTax": 0.22, "standardPremiumIncTax": "0.220", "annualStandardPremiumIncTax": "0.220", "standardPremium": "0.200", "finalSumInsured": "180.564", "netPremium": 0.2, "sumInsured": "180.564"}}, {"liabilityCode": "1101", "liabilityId": 115, "liabilitySaPremium": {"sumInsured": "180.564", "finalSumInsured": "180.564"}}, {"liabilityCode": "1330", "liabilityId": 417, "liabilitySaPremium": {"sumInsured": "180.564", "finalSumInsured": "180.564"}}, {"liabilityCode": "1331", "liabilityId": 418, "liabilitySaPremium": {"sumInsured": "180.564", "finalSumInsured": "180.564"}}], "productCode": "Snack_Accident_MSIG"}], "standardGrossPremium": "0.200", "taxList": [{"taxType": "GST", "tax": "0.020"}], "installmentCalculationMethod": "RE_CALCULATE_PREMIUM_FOR_EACH_INSTALLMENT", "totalPremium": "0.220", "currency": "USD", "totalTax": "0.020", "standardNetPremium": "0.200"}, "totalPremium": "0.300", "currency": "SGD", "standardNetPremium": "0.273", "installmentCalculationMethod": "RE_CALCULATE_PREMIUM_FOR_EACH_INSTALLMENT", "installmentList": [{"productList": [{"productSaPremium": {"annualNetPremium": "0.273", "standardPremiumIncTax": "0.3", "finalPremium": "0.300", "annualStandardPremium": "0.273", "standardPremium": "0.273", "finalSumInsured": "246", "netPremiumIncTax": 0.3, "taxValues": {"taxValueList": [{"pureTaxRate": "0.1", "annualTax": "0.027", "tax": "0.027", "productTaxType": "GST", "taxRate": "10", "rateType": "PERCENTAGE"}]}, "tax": "0.027", "annualNetPremiumIncTax": "0.300", "annualTotalTax": "0", "annualStandardPremiumIncTax": "0.300", "netPremium": 0.273, "sumInsured": "246"}, "expiryDate": "2026-07-11T06:55:46.333+08", "effectiveDate": "2025-07-16T06:55:47.333+08", "productId": 2395562854858758, "liabilityList": [{"liabilityCode": "1002", "liabilityId": 102, "liabilitySaPremium": {"netPremiumIncTax": 0.3, "standardPremiumIncTax": "0.3", "annualStandardPremiumIncTax": "0.300", "standardPremium": "0.273", "finalSumInsured": "246", "netPremium": 0.273, "sumInsured": "246"}}, {"liabilityCode": "1101", "liabilityId": 115, "liabilitySaPremium": {"sumInsured": "246", "finalSumInsured": "246"}}, {"liabilityCode": "1330", "liabilityId": 417, "liabilitySaPremium": {"sumInsured": "246", "finalSumInsured": "246"}}, {"liabilityCode": "1331", "liabilityId": 418, "liabilitySaPremium": {"sumInsured": "246", "finalSumInsured": "246"}}], "productCode": "Snack_Accident_MSIG"}], "standardGrossPremium": "0.273", "totalPremium": "0.300", "currency": "SGD", "totalTax": "0.027", "standardNetPremium": "0.273", "taxList": [{"taxType": "GST", "tax": "0.027"}], "periodNo": 1}], "productList": [{"productSaPremium": {"annualNetPremium": "0.273", "standardPremiumIncTax": "0.3", "finalPremium": "0.300", "annualStandardPremium": "0.273", "standardPremium": "0.273", "finalSumInsured": "246", "netPremiumIncTax": 0.3, "taxValues": {"taxValueList": [{"pureTaxRate": "0.1", "annualTax": "0.027", "tax": "0.027", "productTaxType": "GST", "taxRate": "10", "rateType": "PERCENTAGE"}]}, "tax": "0.027", "annualNetPremiumIncTax": "0.300", "annualTotalTax": "0", "annualStandardPremiumIncTax": "0.300", "netPremium": 0.273, "sumInsured": "246"}, "expiryDate": "2026-07-11T06:55:46.333+08", "effectiveDate": "2025-07-16T06:55:47.333+08", "productId": 2395562854858758, "liabilityList": [{"liabilityCode": "1002", "liabilityId": 102, "liabilitySaPremium": {"netPremiumIncTax": 0.3, "standardPremiumIncTax": "0.3", "annualStandardPremiumIncTax": "0.300", "standardPremium": "0.273", "finalSumInsured": "246", "netPremium": 0.273, "sumInsured": "246"}}, {"liabilityCode": "1101", "liabilityId": 115, "liabilitySaPremium": {"sumInsured": "246", "finalSumInsured": "246"}}, {"liabilityCode": "1330", "liabilityId": 417, "liabilitySaPremium": {"sumInsured": "246", "finalSumInsured": "246"}}, {"liabilityCode": "1331", "liabilityId": 418, "liabilitySaPremium": {"sumInsured": "246", "finalSumInsured": "246"}}], "productCode": "Snack_Accident_MSIG"}]}}}