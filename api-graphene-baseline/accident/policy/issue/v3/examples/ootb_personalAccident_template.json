{"request": {"tradeNo": "accident-T@natural-001", "thirdPartyTransactionNo": "accident-T@natural-001", "goodsCode": "PersonalAccident_Template", "coveragePeriodType": "DAY", "coveragePeriod": 1, "premiumFrequencyType": "SINGLE", "paymentPeriodType": "SINGLE", "paymentPeriod": 1, "premiumFrequency": 1, "issueDate": "2025-10-22T08:37:49.761+08:00", "applicationDate": "2025-10-21T08:37:49.761+08:00", "zoneId": "Asia/Shanghai", "planCode": "PersonalAccident_02_Template", "insuredRelationList": [{"serialNo": "insured-ci2025101514150001-001", "relationType": "PRODUCT_INSURED", "productSerialNo": "3e7e5391-2a37-4d23-a3de-b56c27f74420"}], "policyHolder": {"individual": {"fullName": "ken peter", "lastName": "peter", "firstName": "ken", "birthday": "1995-06-06", "gender": "MALE", "certiNo": "43543546-89984894", "certiType": "IDCARD", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "industryCode": "IT", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 10, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}, "insuredList": [{"serialNo": "insured-ci2025101514150001-001", "insuredOrder": 1, "relationshipWithPolicyholder": "SELF", "individual": {"fullName": "ken peter", "lastName": "peter", "firstName": "ken", "birthday": "1995-06-06", "gender": "MALE", "certiNo": "43543546-89984894", "certiType": "IDCARD", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "industryCode": "IT", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 10, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "StreetName", "address12": "StreetNo", "address13": "Town", "address15": "Country"}]}}], "productList": [{"serialNo": "3e7e5391-2a37-4d23-a3de-b56c27f74420", "productCode": "PersonalAccident_Template", "effectiveDate": "2025-10-22T08:37:49.761+08:00", "sumInsured": 100000, "coveragePeriodType": "DAY", "coveragePeriod": "1", "premiumFrequencyType": "SINGLE", "paymentPeriodType": "SINGLE", "productLiabilityList": [{"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230a", "sumInsured": 100000, "liabilityCode": "1002"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230b", "sumInsured": 100000, "liabilityCode": "1101"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230c", "sumInsured": 20000, "liabilityCode": "1359"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230d", "sumInsured": 50000, "liabilityCode": "3302"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230e", "sumInsured": 800, "liabilityCode": "2125"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230f", "sumInsured": 50000, "liabilityCode": "1029"}, {"serialNo": "6360facb-1d21-47c6-8be2-dcbac0c2230g", "sumInsured": 50000, "liabilityCode": "1007"}]}], "payerList": [{"individual": {"holderRelation": "SPOUSE", "certiType": "IDCARD", "certiNo": "43543546", "firstName": "ken", "lastName": "peter", "fullName": "ken peter", "birthPlace": "Shanghai", "sex": "MALE", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town"}], "middleName": "middle", "formattedLastName": "peter", "formattedMiddleName": "middle", "formattedFirstName": "ken", "formattedFullName": "ken middle peter", "fullName2": "ken peter", "lastName2": "peter2", "middleName2": "middle2", "firstName2": "ken2", "formattedLastName2": "peter2", "formattedMiddleName2": "middle2", "formattedFirstName2": "ken2", "formattedFullName2": "ken2 middle2 peter2", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "SE", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2020-01-01", "height": "180", "weight": "75", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "1001", "channelCode": "CC", "channelUserCodePartyId": "CUP1001", "isCustomer": true, "countryCode": "US", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2025-01-01", "position": "Developer", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "TN123456", "blackStatus": false, "workingPlace": "Company", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "Office", "extraInfo": "Some extra info"}, "payerType": "FIRST", "payMethod": "CASH"}], "paymentPlan": {"extensions": {}, "installmentList": [], "payMethod": "CASH", "samePaymentMethodForAllInstallments": true}, "effectiveDate": "2025-10-22T08:37:49.761+08:00"}, "response": {"200": {"policies": [{"proposalNo": "20251022774860", "policyNo": "102025115738", "thirdPartyTransactionNo": "accident-T@natural-001"}]}}}