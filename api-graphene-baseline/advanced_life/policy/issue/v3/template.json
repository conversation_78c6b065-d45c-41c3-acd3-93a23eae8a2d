{"request": {"isLegalBeneficiary": false, "policyDeliveryMethod": "E_POLICY", "renewalMethod": "NONE_RENEWAL", "insureCreator": "1", "salesChannelList": [{"channelRole": "NON_OFFICE", "agentCode": "1", "branchCode": "1", "agentPathCode": "1", "salesAgreementCode": "salesChannelName"}], "issuanceRelationList": [{"type": "RENEWAL_POLICY", "subType": "HOST_RELATION_NO", "relationNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "systemSource": "systemSource", "enableFullSync": false, "remark": "remark", "*goodsCode": "exampleGoodsCode", "currency": {"premiumCurrency": "USD", "saCurrency": "USD"}, "*planCode": "examplePlanCode", "questionnaire": {"*applyNo": "202501151727053447213914243565158452", "*purpose": "POLICYHOLDER_DECLARATION"}, "*thirdPartyTransactionNo": "t_32943923243", "premium": 500.55, "tradeNo": "p_329439235243", "coveragePeriodType": "YEARFULL", "coveragePeriod": 1, "paymentPeriodType": "SINGLE", "paymentPeriod": 1, "premiumFrequencyType": "SINGLE", "orderNo": "z_329439235243", "quotationNo": "**********", "applicationDate": "2024-06-26T00:00:00Z", "effectiveDate": "2024-06-26T00:00:00Z", "expiryDate": "2030-01-01T00:00:00Z", "zoneId": "Europe/Zagreb", "hasInvestAccount": true, "*policyHolder": {"*individual": {"attachmentList": [{"refId": 1, "refType": "POLICY_HOLDER", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": ["TR", "NL", "HR", "RS", "IE"], "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address": "province city street house"}]}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "*companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "******-123456", "registeredDate": "2023-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE"}}, "insuredList": [{"insuredOrder": 1, "virtualKidNumber": 0, "virtualAdultNumber": 0, "virtualTotalNumber": 0, "mainInsurantRelation": "MAIN_INSURANT", "relationshipWithMainInsured": ["SELF", "SPOUSE", "CHILDREN", "PARENTS", "SIBLINGS", "OTHER"], "occupationClass": "Software Engineer", "insuredEffectiveDate": "2024-10-27T22:55:47.333+08:00", "insuredExpiryDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationReason": "DUE_TO_POS_INSURED", "status": "VALID", "*individual": {"attachmentList": [{"refId": 1, "refType": "POLICY_HOLDER", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelCode": "English", "channelUserNo": "English", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}]}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 29, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "******-123456", "registeredDate": "2023-01-01", "organizationType": ["ENTERPRISE", "OFFICIAL_ORGAN", "INSTITUTION_ORGANIZATION"], "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2025-12-31", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "isPreStored": false, "typeOfBusiness": "Software Development", "receiveInvoiceMethod": ["EMAIL", "MAIL"], "invoiceType": ["GROUP_INVOICE", "INDIVIDUAL_INVOICE"]}, "extensions": {"123": "234"}}], "payerList": [{"*payMethod": "CASH", "payerType": "FIRST", "autoDeduction": true, "*individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelCode": "English", "channelUserNo": "English", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "beneficiaryList": [{"benefitRatio": "SELF", "insurantRelation": ["SELF", "SPOUSE", "CHILDREN", "PARENTS", "OTHER"], "benefitType": ["NORMAL", "IRREVOCABLE", "DEFAULT"], "beneficiaryOrder": ["PRIMARY", "SECONDARY", "THIRD"], "*individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelCode": "English", "channelUserNo": "English", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": ["ADDRESS", "HA", "CA", "DELIVERY", "OVERSEAS", "CITIZENID", "CONTACT", "PERMANENT", "RESIDENTIAL", "MAILING", "BILLING"], "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "trusteeList": [{"insurantRelation": "*", "*individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "certiNo": "********", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelCode": "English", "channelUserNo": "English", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"*addressType": ["ADDRESS", "HA", "CA", "DELIVERY", "OVERSEAS", "CITIZENID", "CONTACT", "PERMANENT", "RESIDENTIAL", "MAILING", "BILLING"], "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "productList": [{"*productCode": "product_gemini_ul_20221122_main_2", "calculateMethod": "CALCULATE_PREMIUM", "extraCalculateMethod": "CALCULATE_EXTRA_LOADING_BASED_ON_UW_DECISION", "sumInsured": 1000000, "premiumPeriod": "1000000", "periodStandardPremium": "223", "periodPremium": "123", "premiumType": "PLANNED_PREMIUM", "productInvestmentPlanList": [{"*amount": 10000.0, "premiumType": "PLANNED_PREMIUM", "switchType": "AMOUNT", "paymentFrequencyType": "MONTH", "paymentFrequencyValue": "12", "nextDueDate": "2023-06-15T00:00:00.000+08:00", "singleTopUpType": null, "startDate": "2023-05-15T00:00:00.000+08:00", "endDate": "2033-05-15T00:00:00.000+08:00", "investmentPlanStatus": "ACTIVE", "fundAppointmentType": "A_LA_CARTE_FUND_APPOINTMENT", "modelPortfolioCode": null, "productFundPlanList": [{"*fundCode": "FUND003", "fundName": "11", "*fundAllocation": "30%", "fundAmount": 3000.0, "startDate": "2023-05-15T00:00:00.000+08:00", "endDate": "2033-05-15T00:00:00.000+08:00", "distributionIndicator": false, "distributionMethod": "REINVESTMENT"}]}], "productBonusList": [{"bonusType": "CASH_BONUS", "bonusStatus": "ACTIVE", "cbPaymentOption": 1, "depositAccountBalance": "5000.00", "extensions": {"E_BonusRate": "3.5%", "E_LastPaymentDate": "2023-04-15"}}], "productLiabilityList": [{"liabilityCode": "1021", "sumInsured": 1000000, "loadingList": "*", "discountList": "*", "liabilityBenefit": "*"}], "plannedPremiumCalMethod": 3}], "campaignList": [{"campaignCode": "campaignCode", "periodNo": "1", "promotionCode": "promotionCode", "campaignPayerList": "*"}], "*paymentPlan": {"*payMethod": "CASH", "*samePaymentMethodForAllInstallments": true, "installmentList": [{"*paymentNum": 1, "premium": "500.00", "paymentDate": "2024-06-26T00:00:00Z", "dueDate": "2024-07-26T00:00:00Z", "*payMethod": "CASH", "paymentStatus": "NORMAL"}]}, "extensions": {"E_IsDiplomat": "NO"}, "userAuthInfo": "*", "claimStackTemplateList": "*", "claimStackConfigList": "*"}, "response": {"policies": [{"thirdPartyTransactionNo": "t_32943923243", "orderNo": "z_*********", "quotationNo": "**********", "policyNo": "POL123456", "proposalNo": "PROP123456", "proposalStatus": "EFFECTIVE", "ruleValidateResult": {"decision": "DECLINE", "details": [{"ruleCode": "MSIG_VN_Travel_manualUW", "decisionType": "DECLINE", "msgValue": "pls error", "msgCode": "pls error"}]}}]}}