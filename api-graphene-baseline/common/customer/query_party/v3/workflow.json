{"activities": [{"name": "Start", "type": "Start", "dependEvents": ["Start"]}, {"name": "query<PERSON><PERSON>y", "type": "Procedure", "procedures": [{"tag": "nano", "name": "query<PERSON><PERSON>y", "type": "RestfulService", "domain": "customer"}], "dependActivities": ["Start"]}, {"name": "End", "type": "End", "using": [{"direction": "OUT", "procedure": "query<PERSON><PERSON>y"}], "dependActivities": ["query<PERSON><PERSON>y"]}]}