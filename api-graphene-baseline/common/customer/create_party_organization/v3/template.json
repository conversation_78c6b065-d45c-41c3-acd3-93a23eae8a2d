{"request": {"*transNo": "uuidqw124531", "*transType": "DIRECT_USER_CREATE", "*roleType": "HOLDER", "code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "!*companyName": "Company Ltd.", "companyName2": "Company Ltd. 2", "industrialClassification": "Software", "cifNumber": "CIF123456", "userInputOrganizationType": "IT", "registeredCapital": 1000000, "registeredCapitalCurrency": "USD", "numberOfEmployees": 500, "legalRepresentativeIdType": "PASSPORT", "legalRepresentativeIdNo": "LRID123456", "legalRepresentativeName": "<PERSON>", "position": "CEO", "taxpayerIdentificationNumber": "TIN123456", "taxpayerName": "Company Taxpayer", "registeredPhone": "******-123456", "registeredDate": "2000-01-01", "organizationType": "ENTERPRISE", "organizationSize": "Large", "country": "UNITED_STATES", "industry": "Technology", "certificateExpiryDate": "2030-01-01", "organizationAbbreviationName": "CompLtd", "registeredPhoneCountryCode": "+1", "registeredAddress": "1234 Elm Street", "branchCode": "BR123", "typeOfBusiness": "Software Development", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneType": "PHONE", "phoneNo": "*********", "countryCode": "+86", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town1", "address15": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "state city district town"}]}, "response": {"partyId": 259792306208776}}