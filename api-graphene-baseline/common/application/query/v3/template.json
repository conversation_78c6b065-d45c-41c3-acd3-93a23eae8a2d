{"response": {"relationDetailList": [{"relationPolicyNo": "relationPolicyNo"}], "agreementNo": "agreementNo", "issueWithoutPayment": true, "systemSource": "systemSource", "insureCreator": "1", "beneficiaryList": [{"benefitRatio": "SELF", "insurantRelation": ["SELF", "SPOUSE", "CHILDREN", "PARENTS", "OTHER"], "benefitType": ["NORMAL", "IRREVOCABLE", "DEFAULT"], "beneficiaryOrder": ["PRIMARY", "SECONDARY", "THIRD"], "*individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "sex": "MALE", "gender": "MALE", "certiNo": "43543546", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelCode": "English", "channelUserNo": "English", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "extraInfo": "Some extra information", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 1, "*addressType": ["ADDRESS", "HA", "CA", "DELIVERY", "OVERSEAS", "CITIZENID", "CONTACT", "PERMANENT", "RESIDENTIAL", "MAILING", "BILLING"], "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "remark": "remark", "proposalNo": "proposalNo_0d5c6224ecce", "proposalStatus": "EFFECTIVE", "goodsCode": "goodsCode_79a3a7292e41", "goodsVersion": "goodsVersion_f7dfddb40e56", "goodsPlanCode": "goodsPlanCode_6e23dcdfcecb", "packageCode": "packageCode_e04ef9dfac48", "orderNo": "orderNo_242852baf61d", "applicationNo": "applicationNo_46e4ba5fa445", "tradeNo": "tradeNo_dc13cbcc1580", "policyStatus": "POLICY_EFFECT", "statusChangeCause": "POS_NO_REASON_RESCISSION", "premiumFrequencyType": "SINGLE", "paymentPeriodType": "SINGLE", "statusChangeDate": "2024-03-27 17:29:24", "changeSource": "POS_ONLINE", "applicationDate": "2024-03-27 17:29:24", "signOffDate": "2024-03-27 17:29:24", "signOffStatus": "PENDING_CONFIRMATION", "epolicyDispatchDate": "2024-03-27 17:29:24", "campaignList": "*", "actualPremium": 1, "thirdPartyTransactionNo": "thirdPartyTransactionNo_1d19757c3147", "planCode": "3323B", "paidPremium": "300", "coveragePeriod": "exampleCoveragePeriod", "coveragePeriodType": "DAY", "renewalMethod": "NONE_RENEWAL", "policyDeliveryMethod": "E_POLICY", "channelRole": "NON_OFFICE", "salesAgreementCode": "salesChannelName", "issuanceStatus": "EFFECTIVE", "isRenewalPolicy": false, "currency": "*", "zoneId": "zoneId_593e8c1a4235", "policyNo": "*********", "policyHolder": {"individual": {"fullName": "fullName_e7c207ee1b79", "lastName": "lastName_8d7f2b0914a1", "middleName": "middleName_b7a39150dd81", "firstName": "firstName_00973bbff5dd", "formattedLastName": "formattedLastName_402cdf5e50de", "formattedMiddleName": "formattedMiddleName_a3b752ee7f6b", "formattedFirstName": "formattedFirstName_5c89ddbbc4fb", "formattedFullName": "formattedFullName_2c90a070cff7", "fullName2": "fullName2_1f1318e8bb3f", "lastName2": "lastName2_962b089be7ae", "middleName2": "middleName2_2e3c16defe05", "firstName2": "firstName2_195377acd3df", "formattedLastName2": "formattedLastName2_2cc9254ed9f2", "formattedMiddleName2": "formattedMiddleName2_26fdc14d090f", "formattedFirstName2": "formattedFirstName2_d427fa6f3cf1", "formattedFullName2": "formattedFullName2_1d679084c6f7", "birthday": "2024-03-27", "sex": "MALE", "gender": "MALE", "certiNo": "certiNo_af01355e00d1", "certiType": "IDCARD", "residenceCountry": "TR", "customerGrade": "customerGrade_e0c7c586b2e5", "isPromotional": false, "socialSecurity": false, "industryCode": "industryCode_1814b15248f4", "occupationCode": "occupationCode_70a448faefc5", "nationality": "AFGHANISTAN", "secondNationality": "AFGHANISTAN", "education": "BACHELOR", "income": "income_8e1c2fe3f62b", "smoke": false, "smokeGetRidOfDate": "2024-03-27", "height": "height_eb21cfd5d125", "weight": "weight_b7664e22242e", "birthPlace": "birthPlace_797ed3bae9d3", "race": "race_00936b9a2556", "marriageStatus": "UNMARRIED", "occupationStatus": "UNEMPLOYED", "channelUserNo": "channelUserNo_019579ecf4e7", "channelCode": "channelCode_434eaa48cc14", "channelUserCodePartyId": "channelUserCodePartyId_d5c973f6710a", "isCustomer": false, "countryCode": "countryCode_47cf85d2d17b", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2024-03-27", "ckaExpiryDate": "2024-03-27", "position": "position_f178bc947519", "title": "MR", "residentialStatus": "UNKNOWN", "deathOrNot": false, "dateOfDeath": "2024-03-27", "taxNo": "taxNo_de2827f91da9", "blackStatus": false, "workingPlace": "workingPlace_5fdc794c8ea3", "issuanceDateOfCertificate": "2024-03-27", "issuancePlaceOfCertificate": "issuancePlaceOfCertificate_0c7a6b1e593c", "extraInfo": "extraInfo_f092f13120dc", "partyId": 0, "partyType": "INDIVIDUAL", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 1, "addressType": "ADDRESS", "address11": "address11_815a99ba7cfd", "address12": "address12_96f8c9c45660", "address13": "address13_1e16cb9b67e9", "address14": "address14_eb3de42333ac", "address15": "address15_eff313b7d9b7", "address16": "address16", "address21": "address21_0c25d91bfdd3", "address22": "address22_981aeff1e82c", "address23": "address23_fc79c8181a3b", "address24": "address24_546bb7c07a88", "address25": "address25_efc19f7fe1de", "zipCode": "zipCode_7bec402c3154", "gisCode": "gisCode_da25a17f1016", "organizationAddressType": "REGISTER", "serialNo": "serialNo_1ff96e26e255"}], "attachmentList": "*", "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "holderRelation": "SELF", "roleType": "HOLDER", "serialNo": "serialNo_ac6f741a242c", "extensions": {}}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "code_3667ea49bb97", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "businessLicenseNumber_3216614b5d7c", "companyName": "companyName_e49f0a18178c", "companyName2": "companyName2_e1c0cab2d144", "industrialClassification": "industrialClassification_3c0f02f9af2d", "roleType": "HOLDER", "email": "email_0c3e1e26a337", "cifNumber": "cifNumber_8955f80b56c7", "userInputOrganizationType": "userInputOrganizationType_0a066e831093", "registeredCapital": 0, "registeredCapitalCurrency": "USD", "numberOfEmployees": 0, "legalRepresentativeIdType": "IDCARD", "legalRepresentativeIdNo": "legalRepresentativeIdNo_b9cd84a3d135", "legalRepresentativeName": "legalRepresentativeName_850090ecb265", "position": "position_6d5a71666ea3", "taxpayerIdentificationNumber": "taxpayerIdentificationNumber_9f816dc27c37", "taxpayerName": "taxpayerName_0d0e4318f929", "registeredPhone": "registeredPhone_23e2e08cee81", "registeredDate": "2024-03-27 17:29:24", "organizationType": "ENTERPRISE", "organizationSize": "organizationSize_e98e1d916112", "country": "AFGHANISTAN", "industry": "industry_84a360d67ebc", "certificateExpiryDate": "2024-03-27 17:29:24", "organizationAbbreviationName": "organizationAbbreviationName_4169b37df895", "registeredPhoneCountryCode": "registeredPhoneCountryCode_698f098a35f2", "registeredAddress": "registeredAddress_c776813e10fe", "branchCode": "branchCode_afb974936855", "isPreStored": false, "typeOfBusiness": "typeOfBusiness_9007d968e2c9", "receiveInvoiceMethod": "EMAIL", "invoiceType": "GROUP_INVOICE", "partyType": "INDIVIDUAL", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 1, "addressType": "ADDRESS", "address11": "address11_11ad7781e891", "address12": "address12_63cae06518ea", "address13": "address13_9d01144eba20", "address14": "address14_ded7d4733f38", "address15": "address15_1e77ab1fe946", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "zipCode": "zipCode_2e3fb1e67151", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "serialNo": "serialNo_eb58e01293b1", "extensions": {}}], "serialNo": "serialNo_8b16e4543b43", "extensions": {}}, "serialNo": "serialNo_185c4e76b658", "extensions": {}}, "insuredList": [{"insuredEffectiveDate": "2024-03-27 17:29:24", "insuredExpiryDate": "2024-03-27 17:29:24", "insuredTerminationDate": "2024-03-27 17:29:24", "insuredTerminationReason": "DUE_TO_POS_INSURED", "status": "VALID", "individual": {"fullName": "fullName_b37d77d20cec", "lastName": "lastName_841c7e297aa8", "middleName": "middleName_94ef09b1abd0", "firstName": "firstName_cb7f76031826", "formattedLastName": "formattedLastName_cd9ce46109b3", "formattedMiddleName": "formattedMiddleName_109646ea8b53", "formattedFirstName": "formattedFirstName_6e9fca5407e7", "formattedFullName": "formattedFullName_27fcfabe7383", "fullName2": "fullName2_fa329230d7d2", "lastName2": "lastName2_82997322ff35", "middleName2": "middleName2_a2131b37030c", "firstName2": "firstName2_f58d846a2104", "formattedLastName2": "formattedLastName2_8223ff9126f0", "formattedMiddleName2": "formattedMiddleName2_6faf07c0dad0", "formattedFirstName2": "formattedFirstName2_2674dfff9965", "formattedFullName2": "formattedFullName2_30743eb29add", "birthday": "2024-03-27", "holderRelation": "SELF", "sex": "MALE", "gender": "MALE", "certiNo": "certiNo_815a1dc5ccfa", "certiType": "IDCARD", "residenceCountry": "TR", "customerGrade": "customerGrade_36a4ab552107", "isPromotional": false, "socialSecurity": false, "industryCode": "industryCode_bb9da0386d65", "occupationCode": "occupationCode_b06b81d3c8ef", "nationality": "AFGHANISTAN", "secondNationality": "AFGHANISTAN", "education": "BACHELOR", "income": "income_9fc08323d6d7", "smoke": false, "smokeGetRidOfDate": "2024-03-27", "height": "height_0dc6da9cc851", "weight": "weight_0d0fa8b340ba", "birthPlace": "birthPlace_2711dd9649b7", "race": "race_77bc267f33ba", "marriageStatus": "UNMARRIED", "occupationStatus": "UNEMPLOYED", "channelUserNo": "channelUserNo_b411afd16dcb", "channelCode": "channelCode_d6bc7ad9f50e", "channelUserCodePartyId": "channelUserCodePartyId_c0e1845c86f0", "countryCode": "countryCode_9a87f6ca1dfc", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2024-03-27", "ckaExpiryDate": "2024-03-27", "position": "position_3c9603a80898", "title": "MR", "residentialStatus": "UNKNOWN", "deathOrNot": false, "dateOfDeath": "2024-03-27", "taxNo": "taxNo_db466251829e", "blackStatus": false, "workingPlace": "workingPlace_aca49447c5f6", "issuanceDateOfCertificate": "2024-03-27", "issuancePlaceOfCertificate": "issuancePlaceOfCertificate_f82074e39473", "extraInfo": "extraInfo_470034ebf4ec", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 0, "addressType": "ADDRESS", "address11": "address11_8bb62f403b7b", "address12": "address12_bdef203dd372", "address13": "address13_804ab5f22b2f", "address14": "address14_514f13a58a19", "address15": "address15_82fc711152d2", "address16": "address16", "address21": "address21_6ef1c3afee34", "address22": "address22_ab094b4972fc", "address23": "address23_099aab9c0ff5", "address24": "address24_a0ded45d5bbe", "address25": "address25_396f1ce19a32", "zipCode": "zipCode_a15fc0728410", "gisCode": "gisCode_f73f235c2f7c", "organizationAddressType": "REGISTER", "serialNo": "serialNo_a3e0d332ac7b", "extensions": {}}], "attachmentList": "*", "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "serialNo": "serialNo_9e2b3619a6c0", "extensions": {}}, "organization": {"accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "622202*********0123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN*********", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "code": "code_666b7f3b562e", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "businessLicenseNumber_018643b176b0", "companyName": "companyName_b186df87d83f", "companyName2": "companyName2_25efdc339b54", "industrialClassification": "industrialClassification_92c82cc40596", "roleType": "HOLDER", "email": "email_6a286bec53af", "cifNumber": "cifNumber_e98643c62569", "userInputOrganizationType": "userInputOrganizationType_e95d180d8bf8", "registeredCapital": 0, "registeredCapitalCurrency": "USD", "numberOfEmployees": 0, "legalRepresentativeIdType": "IDCARD", "legalRepresentativeIdNo": "legalRepresentativeIdNo_e5a05ac7446d", "legalRepresentativeName": "legalRepresentativeName_480867d24d4b", "position": "position_bc9cb7625174", "taxpayerIdentificationNumber": "taxpayerIdentificationNumber_7814e78f3cc3", "taxpayerName": "taxpayerName_c21b1a831e2a", "registeredPhone": "registeredPhone_29d6ac356e4d", "registeredDate": "2024-03-27 17:29:24", "organizationType": "ENTERPRISE", "organizationSize": "organizationSize_253702e0b5e9", "country": "AFGHANISTAN", "industry": "industry_947343550bf8", "certificateExpiryDate": "2024-03-27 17:29:24", "organizationAbbreviationName": "organizationAbbreviationName_a2b750289c79", "registeredPhoneCountryCode": "registeredPhoneCountryCode_f55d04d7890d", "registeredAddress": "registeredAddress_5ae6a6653f9e", "branchCode": "branchCode_a3c6b9010b18", "isPreStored": false, "typeOfBusiness": "typeOfBusiness_9789943eba8e", "receiveInvoiceMethod": "EMAIL", "invoiceType": "GROUP_INVOICE", "partyType": "INDIVIDUAL", "serialNo": "serialNo_5207b0330ddb", "extensions": {}}, "serialNo": "serialNo_cd091ffea4bb", "extensions": {}}], "productList": [{"productCode": "productCode_2a7cef46c9e9", "productVersion": "productVersion_ce2b194fe400", "productCategory": "TRADITIONAL", "underwriteDecision": "NORMAL", "premiumFrequencyType": "SINGLE", "premiumFrequencyValue": "1", "mainProductId": 0, "productType": "MAIN", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "riskStartDate": "2024-03-27 17:29:25", "unit": 0, "sumInsured": "sumInsured_bb7c12f3f758", "totalSumInsured": "totalSumInsured_d97c18936bec", "riskSa": "riskSa_7f6820930072", "lastPremiumDueDate": "2024-03-27 17:29:25", "dueDate": "2024-03-27 17:29:25", "currentIndex": 0, "coverageTotalDeltaNetPremium": "123", "totalInstallPremium": "totalInstallPremium_acc34b34bec5", "periodPremium": "periodPremium_9696b2c4acae", "policyFee": "policyFee_e9fabf277c1d", "status": "EFFECTIVE", "statusName": "statusName_0e82e4a85274", "statusChangeCause": "POS_NO_REASON_RESCISSION", "cancelOption": "IMMEDIATELY", "statusChangeDate": "2024-03-27 17:29:25", "currency": "USD", "coveragePeriodType": "DAY", "coveragePeriod": "coveragePeriod_b1f11087d08a", "paymentPeriodType": "YEARFULL", "paymentPeriod": "paymentPeriod_f706f0a54103", "canRenewal": "ENSURE", "issuanceProductId": 0, "pickupType": "CASH_COLLECTION", "pickupPayAccountId": 0, "benefitLevel": 0, "policyScenario": "NORMAL", "productName": "productName_1f108c930bb7", "includingTax": false, "tax": "tax_ba3697a97e11", "taxType": "GST", "premiumAfterDiscount": "premiumAfterDiscount_547767b08ac6", "premiumDiscount": "premiumDiscount_6d4241118996", "annualPremium": "annualPremium_4f582a7cdae2", "actualPremium": "actualPremium_5795479c92b7", "annualExtraPremium": "annualExtraPremium_10be670a2401", "annualExtraPremiumIncTax": "annualExtraPremiumIncTax_c82f5debe7ba", "annualDiscountPremium": "annualDiscountPremium_bfa6e8ec09de", "annualDiscountPremiumIncTax": "annualDiscountPremiumIncTax_89c6b0516040", "coverageTotalNoClaimDiscount": "coverageTotalNoClaimDiscount_9524650924b7", "annualTotalTax": "annualTotalTax_9808f9774f04", "productLiabilityList": [{"productLiabilityId": 0, "liabilityCode": "liabilityCode_583cf4fc01b9", "liabilityId": 0, "annuityDeferPeriodType": "annuityDeferPeriodType_97e4952e52c6", "annuityDeferPeriod": "annuityDeferPeriod_b9266d9c7391", "annuityPaymentPeriodType": "annuityPaymentPeriodType_02f4243f20aa", "annuityPaymentPeriod": "annuityPaymentPeriod_9dc9e75ea256", "annuityGuaranteePeriodType": "annuityGuaranteePeriodType_fd401d63a536", "annuityGuaranteePeriod": "annuityGuaranteePeriod_dd9cf8cc795b", "annuityPaymentFrequencyType": "annuityPaymentFrequencyType_655a3f42629b", "annuityPMTModalFactor": "annuityPMTModalFactor_f3cf39d1ca0e", "annuityPaymentOption": "annuityPaymentOption_52b50004118e", "claimStackCodeList": ["claimStackCodeList_ff0000fff1ca"], "deductible": {"deductibleType": "NO_DEDUCTIBLE", "amount": "amount_6254dafe1c7f", "valueType": "AMOUNT", "serialNo": "serialNo_76a0a3f0c886", "extensions": {}}, "limit": {"limitType": "NO_LIMIT", "valueType": "AMOUNT", "amount": "amount_c72b388f3128", "serialNo": "serialNo_d0d2dd6b3e0a", "extensions": {}}, "premiumDetailList": [{"periodNo": "1", "periodFinalPremium": "1", "periodStandardPremiumIncTax": "1", "periodExtraPremium": "1", "periodExtraPremiumIncTax": "1", "premiumLoadingList": "*", "periodDiscountPremium": "1", "periodDiscountPremiumIncTax": "1", "premiumDiscountList": "*", "periodNetPremium": "1", "periodNetPremiumIncTax": "1", "periodNoClaimDiscount": "1", "periodNoClaimDiscountIncTax": "1", "noClaimDiscountRate": "1", "periodTotalTax": "1", "periodLevy": "1", "periodServiceFee": "1", "serviceFeeList": "*", "periodCommission": "1", "periodCampaignDiscount": "1", "actualPremium": "1", "premiumDeltaRate": "1", "multiCurrencyPremiumList": "*"}], "liabilityBenefitList": [{"productLiabilityBenefitId": 0, "productId": 0, "bonusType": "CASH_BONUS", "policyProductLiabilityId": 0, "policyProductId": 0, "liabilityId": 0, "benefitType": "ANNUITY", "sbPaymentOption": "RECEIVE_BENEFIT_IN_CASH", "sbPaymentFrequencyType": "SINGLE", "paymentFrequencyValue": "paymentFrequencyValue_e79601c53da6", "paymentDeferPeriodType": "IMMEDIATE_PAYMENT", "paymentDeferPeriodValue": "paymentDeferPeriodValue_72a1d58dde3b", "paymentPeriodType": "WHOLE_LIFE", "paymentPeriodValue": "paymentPeriodValue_912c4a3e23b0", "guaranteePeriodType": false, "guaranteePeriodValue": "guaranteePeriodValue_23eb6a0fd597", "depositAccountBalance": "depositAccountBalance_d2ac831045b8", "benefitStatus": "benefitStatus_1a72bb7ee3c9", "serialNo": "serialNo_199d3a0d8068", "extensions": {}}], "liabilityRiskList": [{"riskSa": "riskSa_d7d092051c1a", "riskCategoryCode": "riskCategoryCode_040f51e63487", "riskSubCategoryCode": "riskSubCategoryCode_69ca7b5e0c7d", "serialNo": "serialNo_37729b27f572", "extensions": {}}], "interestList": [{"interestId": 0, "interestName": "interestName_93bcf567de69", "premiumDetailList": [{"periodNo": 0, "stampDuty": "stampDuty_dcf4d18151e2", "periodStandardPremium": "periodStandardPremium_ebdb8d236b4e", "periodStandardPremiumIncTax": "periodStandardPremiumIncTax_5394c9d338ed", "periodExtraPremium": "periodExtraPremium_013be47d818c", "periodExtraPremiumIncTax": "periodExtraPremiumIncTax_d3fb62159635", "premiumLoadingList": [{"periodPremium": "periodPremium_dd2d15e8ce38", "periodPremiumTax": "periodPremiumTax_779edbd34db3", "loadingType": "HEALTH", "loadingMethod": "FIXED_AMOUNT", "loadingValue": "loadingValue_98993f5d9735", "loadingPeriod": 0, "loadingPeriodType": "PERMANENT", "extraRate": "extraRate_a15bc59324d3", "serialNo": "serialNo_18f463eee9a5", "extensions": {}}], "periodDiscountPremium": "periodDiscountPremium_275946d162ca", "periodDiscountPremiumIncTax": "periodDiscountPremiumIncTax_6759ca4641c9", "premiumDiscountList": [{"premiumDiscountType": 0, "periodDiscountPremium": "periodDiscountPremium_04f9968e46ec", "discountRate": "discountRate_08526d192ed6", "serialNo": "serialNo_92da60508b36", "extensions": {}}], "periodNetPremium": "periodNetPremium_1a3a06d5be28", "periodNetPremiumIncTax": "periodNetPremiumIncTax_956596928613", "periodNoClaimDiscount": "periodNoClaimDiscount_30e7d86fb8a3", "periodNoClaimDiscountIncTax": "periodNoClaimDiscountIncTax_c117342a26e7", "noClaimDiscountRate": "noClaimDiscountRate_6fba9ee159d0", "periodTotalTax": "periodTotalTax_6db863548b18", "taxList": [{"feeType": "ORDER_TEMPORARILY", "amount": "amount_1c173458393e", "bizFeeCategory": "TAX", "accumulated": false, "taxList": [{"taxType": "GST", "rateType": "PERCENTAGE", "tax": "tax_ccda66be0168", "annualTax": "annualTax_a2710ad1ca68", "policyTaxId": 0, "promotionDiscountOnTax": "promotionDiscountOnTax_bee984984f40", "taxRate": "taxRate_28b8399ffc34", "serialNo": "serialNo_b6468e468b25", "extensions": {}}], "serialNo": "serialNo_dae452d777c9", "extensions": {}}], "periodLevy": "periodLevy_ebcfacbbe7b3", "periodServiceFee": "periodServiceFee_4191da48b883", "serviceFeeList": [{"amount": "amount_09a0306bd3cf", "partnerCode": "partnerCode_fd26cba3adaf", "serialNo": "serialNo_0f2ad69040f3", "extensions": {}}], "periodCommission": "periodCommission_08effd92c670", "commissionList": [{"amount": "amount_4803a8c27fb7", "channelCode": "channelCode_ab3a5284baa9", "serialNo": "serialNo_cfb9415ca5c2", "extensions": {}}], "periodCampaignDiscount": "periodCampaignDiscount_2501dcf3c8e3", "actualPremium": "actualPremium_370f321b9bba", "premiumDeltaRate": "premiumDeltaRate_579a70efb866", "premiumId": 0, "premiumLevel": "POLICY", "correlationId": "correlationId_f2aeeedbe9be", "serialNo": "serialNo_a737d4e2e4fc", "extensions": {}}], "unit": 0, "sumInsured": "sumInsured_e1ca8502bd5c", "campaignFreeSumInsured": "campaignFreeSumInsured_ef7c435e82cd", "sumInsuredDownSell": "sumInsuredDownSell_6c1b5646e544", "totalSumInsured": "totalSumInsured_89d5d7ed56cd", "riskSa": "riskSa_08e1314be1cb", "freeAmountOfLiabilitySA": "freeAmountOfLiabilitySA_6d2fffd19e52", "discountSumInsured": "discountSumInsured_5fa4bc76d237", "periodPremium": "periodPremium_d2463c3fa346", "periodStandardPremiumIncTax": "periodStandardPremiumIncTax_a164d7815d1a", "annualPremium": "annualPremium_7569d03cc91b", "isOption": false, "underwritingPassed": false, "status": "TERMINATION", "liabilityCode": "liabilityCode_d156709f1bcc", "includingTax": false, "premiumIncTax": "premiumIncTax_c08425b8fc05", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "statusChangeCause": "POS_NO_REASON_RESCISSION", "statusChangeDate": "2024-03-27 17:29:25", "noClaimDiscountRate": "noClaimDiscountRate_45c9e5639e1c", "liabilityCategoryCode": "liabilityCategoryCode_66fe20fc88a1", "sbPaymentFrequencyType": "sbPaymentFrequencyType_17504237475e", "sbModalFactor": "sbModalFactor_45825503628e", "sbPaymentOption": "sbPaymentOption_fe96939deec1", "deductibleAmount": "deductibleAmount_62719aef6e1f", "serialNo": "serialNo_259f8ba8435d", "extensions": {}}], "calculateNcd": false, "unit": 0, "sumInsured": "sumInsured_dcb10255f36d", "campaignFreeSumInsured": "campaignFreeSumInsured_2840b94f9590", "sumInsuredDownSell": "sumInsuredDownSell_f331873983b9", "totalSumInsured": "totalSumInsured_436422e236b9", "riskSa": "riskSa_f1b78f42afeb", "freeAmountOfLiabilitySA": "freeAmountOfLiabilitySA_d68ce67210c9", "discountSumInsured": "discountSumInsured_c296245dd18b", "periodPremium": "periodPremium_f1063e527ae1", "periodStandardPremiumIncTax": "periodStandardPremiumIncTax_a1d02f9e3780", "annualPremium": "annualPremium_f7a7b03a163a", "isOption": false, "underwritingPassed": false, "status": "TERMINATION", "includingTax": false, "premiumIncTax": "premiumIncTax_2c94df72960d", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "statusChangeCause": "POS_NO_REASON_RESCISSION", "statusChangeDate": "2024-03-27 17:29:25", "noClaimDiscountRate": "noClaimDiscountRate_3885249c3629", "liabilityCategoryCode": "liabilityCategoryCode_6558e876c6d1", "sbPaymentFrequencyType": "sbPaymentFrequencyType_71f23be50e4a", "sbModalFactor": "sbModalFactor_2f41e1566927", "sbPaymentOption": "sbPaymentOption_1c9e60aab5ce", "deductibleAmount": "deductibleAmount_cfe57c5c9f2a", "serialNo": "serialNo_9cb3e79ebf75", "extensions": {}}], "premiumPeriod": "premiumPeriod_95e2ee14513f", "premiumType": "PLANNED_PREMIUM", "premiumStartDate": "2024-03-27 17:29:25", "premiumExpiryDate": "2024-03-27 17:29:25", "premiumDeductionType": "CASH_PREMIUM", "gracePeriod": 0, "attachedToProductId": 0, "attachedToProductCode": "attachedToProductCode_017dcd0985a4", "waiverStartDate": "2024-03-27 17:29:25", "waiverEndDate": "2024-03-27 17:29:25", "estimateLapseDate": "2024-03-27 17:29:25", "installmentType": "RENEW", "claimRatio": "claimRatio_ef5a0b48cadc", "calculateMethod": "CALCULATE_PREMIUM", "extraCalculateMethod": "CALCULATE_EXTRA_LOADING_BASED_ON_UW_DECISION", "plannedPremiumCalMethod": 0, "premiumDeltaRate": "premiumDeltaRate_d40311993f79", "adjustedAnnualStandardPremium": "adjustedAnnualStandardPremium_19ceac28cbff", "adjustedAnnualStandardPremiumRate": "adjustedAnnualStandardPremiumRate_ab357fa67416", "minimumInvestmentPeriodType": "WHOLE_COVERAGE_PERIOD", "minimumInvestmentPeriodValue": "minimumInvestmentPeriodValue_d4a0fa8ba243", "minimumInvestmentStartDate": "2024-03-27 17:29:25", "minimumInvestmentEndDate": "2024-03-27 17:29:25", "nlgBenefitBefore": false, "nlgBenefitAfter": false, "benefitOptionCode": false, "withinFreeLookPeriod": false, "shortRateCalcMethod": "BY_SHORT_RATE", "sumInsuredMultiplier": "sumInsuredMultiplier_c45cff180e83", "serialNo": "serialNo_c3503f6ff178", "premiumDetailList": [{"periodNo": "1", "periodStandardPremium": "1", "periodStandardPremiumIncTax": "1", "periodExtraPremium": "1", "periodExtraPremiumIncTax": "1", "premiumLoadingList": "*", "periodDiscountPremium": "1", "periodDiscountPremiumIncTax": "1", "premiumDiscountList": "*", "periodNetPremium": "1", "periodNetPremiumIncTax": "1", "periodNoClaimDiscount": "1", "periodNoClaimDiscountIncTax": "1", "noClaimDiscountRate": "1", "periodTotalTax": "1", "periodLevy": "1", "periodServiceFee": "1", "serviceFeeList": "*", "periodCommission": "1", "periodCampaignDiscount": "1", "actualPremium": "1", "premiumDeltaRate": "1", "periodFinalPremium": "1", "multiCurrencyPremiumList": "*"}], "salesChannelList": "*", "extensions": {}}], "insuredObjectList": [{"insuredType": "INSURED_AUTO", "policyInsuredObjectId": 1001, "serialNo": "AUTO001", "category1": "Vehicle", "category2": "Private Car", "category3": "Sedan", "insuredNo": "AUTO-2024-001", "shareFlag": false, "objectId": "OBJ-AUTO-001", "remark": "Family car for daily commute", "extraInfo": "Additional driver certification required", "insuredName": "Toyota Camry 2023", "relationComponentList": [], "insuredComponentList": [], "autoDriverList": [], "policyAdditionalEquipmentList": [], "autoModel": "Cam<PERSON>", "plateRegistrationZone": "Shanghai", "vehicleBrand": "Toyota", "motorNcd": null, "carOwner": null, "carryingCapacity": "500", "carryingGoodsType": "Personal Items", "chassisNo": "JTDKN3DU5E3123456", "claimExperienceInfo": null, "colorOfPlateNo": "Blue", "deviceAbs": "Yes", "deviceAeb": "Yes", "deviceAirbag": "Front and Side", "deviceAlarm": "Factory Standard", "deviceGearOrSteeringLock": "Electronic", "deviceGps": "Built-in", "deviceImmobiliser": "Yes", "deviceInstalled": "Complete", "deviceTracking": "GPS Enabled", "distanceConfirmationDate": "2024-01-15T10:30:00Z", "drivingDistance": "15000", "averageDrivingDistance": "12000", "lastYearDrivingDistance": "11500", "engineCapacity": "2.5L", "engineNo": "2AR-FE-123456", "plateNo": "沪A12345", "purchaseDate": "2023-03-15T00:00:00Z", "purchasePrice": "280000", "vehicleColor": "<PERSON>", "vehicleEngine": "NON_EV", "vehicleLeasing": false, "engineType": "Gasoline", "vehicleMake": "Toyota", "vehicleModel": "Camry 2.5L", "vehicleType": "Sedan", "vehicleUsage": "Private", "vinNo": "JTDKN3DU5E3123456", "yearOfManufacturing": 2023, "vehicleGroup": "Medium Sedan", "vehicleSubGroup": "Family Car", "enginePower": "203", "engineVolume": "2487", "vehicleInvoiceValue": "280000", "loan": null, "extraEquipmentValue": 5000, "extraEquipmentName": "Premium Sound System", "existHistoryPolicy": false}, {"insuredType": "TRAVEL", "policyInsuredObjectId": 1002, "serialNo": "TRAVEL001", "category1": "Travel", "category2": "International", "category3": "Leisure", "insuredNo": "TRAVEL-2024-001", "shareFlag": false, "objectId": "OBJ-TRAVEL-001", "remark": "Annual family vacation", "extraInfo": "Pre-existing medical conditions covered", "insuredName": "European Tour 2024", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_DEVICE", "policyInsuredObjectId": 1003, "serialNo": "DEVICE001", "category1": "Electronics", "category2": "Mobile", "category3": "Smartphone", "insuredNo": "DEVICE-2024-001", "shareFlag": false, "objectId": "OBJ-DEVICE-001", "remark": "Primary business phone", "extraInfo": "Extended warranty purchased", "insuredName": "iPhone 15 Pro Max", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PRODUCT_LIABILITY", "policyInsuredObjectId": 1004, "serialNo": "LIABILITY001", "category1": "Liability", "category2": "Product", "category3": "Manufacturing", "insuredNo": "LIABILITY-2024-001", "shareFlag": false, "objectId": "OBJ-LIABILITY-001", "remark": "Product liability coverage for electronics", "extraInfo": "Global coverage included", "insuredName": "Consumer Electronics Product Line", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PUBLIC_LIABILITY", "policyInsuredObjectId": 1005, "serialNo": "PUBLIC001", "category1": "Liability", "category2": "Public", "category3": "General", "insuredNo": "PUBLIC-2024-001", "shareFlag": false, "objectId": "OBJ-PUBLIC-001", "remark": "General public liability coverage", "extraInfo": "Professional indemnity included", "insuredName": "Restaurant Public Liability", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "EMPLOYER_LIABILITY", "policyInsuredObjectId": 1006, "serialNo": "EMPLOYER001", "category1": "Liability", "category2": "Employer", "category3": "Workplace", "insuredNo": "EMPLOYER-2024-001", "shareFlag": false, "objectId": "OBJ-EMPLOYER-001", "remark": "Employer liability for workplace injuries", "extraInfo": "Covers all employees including contractors", "insuredName": "Manufacturing Company Employer Liability", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "BUS", "policyInsuredObjectId": 1007, "serialNo": "BUS001", "category1": "Transport", "category2": "Public", "category3": "Bus", "insuredNo": "BUS-2024-001", "shareFlag": false, "objectId": "OBJ-BUS-001", "remark": "City bus delay coverage", "extraInfo": "Peak hour coverage included", "insuredName": "City Bus Route 101", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "ORDER", "policyInsuredObjectId": 1008, "serialNo": "ORDER001", "category1": "Commerce", "category2": "Order", "category3": "E-commerce", "insuredNo": "ORDER-2024-001", "shareFlag": false, "objectId": "OBJ-ORDER-001", "remark": "E-commerce order protection", "extraInfo": "International shipping covered", "insuredName": "Online Purchase Order #ORD-12345", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_CONTRACT", "policyInsuredObjectId": 1009, "serialNo": "CONTRACT001", "category1": "Legal", "category2": "Contract", "category3": "Commercial", "insuredNo": "CONTRACT-2024-001", "shareFlag": false, "objectId": "OBJ-CONTRACT-001", "remark": "Business contract protection", "extraInfo": "Multi-party contract coverage", "insuredName": "Software Development Contract", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_BUILDING", "policyInsuredObjectId": 1010, "serialNo": "BUILDING001", "category1": "Property", "category2": "Building", "category3": "Commercial", "insuredNo": "BUILDING-2024-001", "shareFlag": false, "objectId": "OBJ-BUILDING-001", "remark": "Office building coverage", "extraInfo": "Earthquake and flood coverage included", "insuredName": "Downtown Office Complex", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "LOCATION_INSURED", "policyInsuredObjectId": 1011, "serialNo": "LOCATION001", "category1": "Property", "category2": "Location", "category3": "Multiple", "insuredNo": "LOCATION-2024-001", "shareFlag": false, "objectId": "OBJ-LOCATION-001", "remark": "Multi-location business coverage", "extraInfo": "All subsidiary locations included", "insuredName": "Retail Chain Locations", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "OTHER_PROPERTIES", "policyInsuredObjectId": 1012, "serialNo": "PROPERTY001", "category1": "Property", "category2": "Other", "category3": "Miscellaneous", "insuredNo": "PROPERTY-2024-001", "shareFlag": false, "objectId": "OBJ-PROPERTY-001", "remark": "Miscellaneous property coverage", "extraInfo": "Includes outdoor equipment and fixtures", "insuredName": "Outdoor Equipment and Fixtures", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PET", "policyInsuredObjectId": 1013, "serialNo": "PET001", "category1": "Animal", "category2": "Pet", "category3": "Domestic", "insuredNo": "PET-2024-001", "shareFlag": false, "objectId": "OBJ-PET-001", "remark": "Family pet health coverage", "extraInfo": "Routine checkups and vaccinations covered", "insuredName": "Golden Retriever - Max", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_PERSON", "policyInsuredObjectId": 1014, "serialNo": "PERSON001", "category1": "Person", "category2": "Individual", "category3": "Life", "insuredNo": "PERSON-2024-001", "shareFlag": false, "objectId": "OBJ-PERSON-001", "remark": "Life insurance coverage", "extraInfo": "Critical illness benefit included", "insuredName": "<PERSON>", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PARCEL", "policyInsuredObjectId": 1015, "serialNo": "PARCEL001", "category1": "Cargo", "category2": "<PERSON><PERSON><PERSON>", "category3": "Express", "insuredNo": "PARCEL-2024-001", "shareFlag": false, "objectId": "OBJ-PARCEL-001", "remark": "Express parcel delivery coverage", "extraInfo": "International shipping protection", "insuredName": "Express Delivery Package", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "FLIGHT", "policyInsuredObjectId": 1016, "serialNo": "FLIGHT001", "category1": "Transport", "category2": "Aviation", "category3": "Commercial", "insuredNo": "FLIGHT-2024-001", "shareFlag": false, "objectId": "OBJ-FLIGHT-001", "remark": "Flight delay compensation coverage", "extraInfo": "Weather-related delays covered", "insuredName": "Flight MU5735 Shanghai to Beijing", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "TAKEAWAY", "policyInsuredObjectId": 1017, "serialNo": "TAKEAWAY001", "category1": "Food", "category2": "Delivery", "category3": "Express", "insuredNo": "TAKEAWAY-2024-001", "shareFlag": false, "objectId": "OBJ-TAKEAWAY-001", "remark": "Food delivery delay coverage", "extraInfo": "Hot food guarantee included", "insuredName": "McDonald's Delivery Order", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "LOAN_GUARANTEE", "policyInsuredObjectId": 1018, "serialNo": "LOAN001", "category1": "Financial", "category2": "Loan", "category3": "Guarantee", "insuredNo": "LOAN-2024-001", "shareFlag": false, "objectId": "OBJ-LOAN-001", "remark": "Loan guarantee protection", "extraInfo": "Default risk coverage", "insuredName": "Home Mortgage Loan Guarantee", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "GROSS_PROFIT", "policyInsuredObjectId": 1019, "serialNo": "PROFIT001", "category1": "Business", "category2": "Income", "category3": "Profit", "insuredNo": "PROFIT-2024-001", "shareFlag": false, "objectId": "OBJ-PROFIT-001", "remark": "Business interruption profit coverage", "extraInfo": "Loss of revenue protection", "insuredName": "Restaurant Business Profit", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "LOSS_OF_RENT", "policyInsuredObjectId": 1020, "serialNo": "RENT001", "category1": "Property", "category2": "Rental", "category3": "Income", "insuredNo": "RENT-2024-001", "shareFlag": false, "objectId": "OBJ-RENT-001", "remark": "Loss of rental income coverage", "extraInfo": "Tenant default protection", "insuredName": "Apartment Building Rental Income", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "AUXILIARY_BUILDINGS", "policyInsuredObjectId": 1021, "serialNo": "AUXILIARY001", "category1": "Property", "category2": "Building", "category3": "Auxiliary", "insuredNo": "AUXILIARY-2024-001", "shareFlag": false, "objectId": "OBJ-AUXILIARY-001", "remark": "Auxiliary building structures coverage", "extraInfo": "Garage, shed, and outbuildings included", "insuredName": "Property Auxiliary Structures", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "ENGINEERING_STRUCTURES", "policyInsuredObjectId": 1022, "serialNo": "ENGINEERING001", "category1": "Infrastructure", "category2": "Engineering", "category3": "Civil", "insuredNo": "ENGINEERING-2024-001", "shareFlag": false, "objectId": "OBJ-ENGINEERING-001", "remark": "Civil engineering structures coverage", "extraInfo": "Bridges, tunnels, and infrastructure", "insuredName": "Highway Bridge Construction", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "REFRIGERATED_GOODS", "policyInsuredObjectId": 1023, "serialNo": "REFRIGERATED001", "category1": "Cargo", "category2": "Food", "category3": "Cold Chain", "insuredNo": "REFRIGERATED-2024-001", "shareFlag": false, "objectId": "OBJ-REFRIGERATED-001", "remark": "Temperature-controlled goods coverage", "extraInfo": "Cold chain integrity protection", "insuredName": "Frozen Food Shipment", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "SWIMMING_POOLS", "policyInsuredObjectId": 1024, "serialNo": "POOL001", "category1": "Property", "category2": "Recreation", "category3": "Pool", "insuredNo": "POOL-2024-001", "shareFlag": false, "objectId": "OBJ-POOL-001", "remark": "Swimming pool and spa coverage", "extraInfo": "Equipment and liability included", "insuredName": "Residential Swimming Pool", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "RAW_MATERIALS_PRODUCTS_MERCHANDISES", "policyInsuredObjectId": 1025, "serialNo": "MATERIALS001", "category1": "Inventory", "category2": "Materials", "category3": "Raw", "insuredNo": "MATERIALS-2024-001", "shareFlag": false, "objectId": "OBJ-MATERIALS-001", "remark": "Raw materials and finished goods coverage", "extraInfo": "Work in progress included", "insuredName": "Manufacturing Inventory Stock", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "OUTDOOR_SUPPLIES", "policyInsuredObjectId": 1026, "serialNo": "OUTDOOR001", "category1": "Equipment", "category2": "Outdoor", "category3": "Recreation", "insuredNo": "OUTDOOR-2024-001", "shareFlag": false, "objectId": "OBJ-OUTDOOR-001", "remark": "Outdoor recreational equipment coverage", "extraInfo": "Camping and sports gear included", "insuredName": "Outdoor Recreation Equipment", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PROFESSIONAL_EMPLOYEES_PERSON_INFO", "policyInsuredObjectId": 1027, "serialNo": "PROFESSIONAL001", "category1": "Personnel", "category2": "Professional", "category3": "Employee", "insuredNo": "PROFESSIONAL-2024-001", "shareFlag": false, "objectId": "OBJ-PROFESSIONAL-001", "remark": "Professional employee coverage", "extraInfo": "Key person insurance included", "insuredName": "Senior Software Engineer Coverage", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PUBLIC_PLACES", "policyInsuredObjectId": 1028, "serialNo": "PUBLICPLACE001", "category1": "Location", "category2": "Public", "category3": "Venue", "insuredNo": "PUBLICPLACE-2024-001", "shareFlag": false, "objectId": "OBJ-PUBLICPLACE-001", "remark": "Public venue liability coverage", "extraInfo": "Event and gathering protection", "insuredName": "Community Center Public Venue", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PRODUCT_GOODS_EQUIPMENTS", "policyInsuredObjectId": 1029, "serialNo": "EQUIPMENT001", "category1": "Equipment", "category2": "Business", "category3": "Industrial", "insuredNo": "EQUIPMENT-2024-001", "shareFlag": false, "objectId": "OBJ-EQUIPMENT-001", "remark": "Business equipment and machinery coverage", "extraInfo": "Breakdown and theft protection", "insuredName": "Manufacturing Equipment Suite", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "OPERATION_LIABILITY", "policyInsuredObjectId": 1030, "serialNo": "OPERATION001", "category1": "Liability", "category2": "Operations", "category3": "Business", "insuredNo": "OPERATION-2024-001", "shareFlag": false, "objectId": "OBJ-OPERATION-001", "remark": "Business operations liability coverage", "extraInfo": "Third-party damage protection", "insuredName": "Construction Operations Liability", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "EMPLOYEES_PERSONAL_EFFECTS", "policyInsuredObjectId": 1031, "serialNo": "EFFECTS001", "category1": "Property", "category2": "Personal", "category3": "Employee", "insuredNo": "EFFECTS-2024-001", "shareFlag": false, "objectId": "OBJ-EFFECTS-001", "remark": "Employee personal belongings coverage", "extraInfo": "Workplace theft and damage protection", "insuredName": "Employee Personal Effects Coverage", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "ROADS_AND_PATHWAYS", "policyInsuredObjectId": 1032, "serialNo": "ROADS001", "category1": "Infrastructure", "category2": "Transportation", "category3": "Roads", "insuredNo": "ROADS-2024-001", "shareFlag": false, "objectId": "OBJ-ROADS-001", "remark": "Roads and pathways infrastructure coverage", "extraInfo": "Maintenance and liability protection", "insuredName": "Municipal Road Network", "relationComponentList": "*", "insuredComponentList": "*"}], "payerList": [{"autoDeduction": false, "holderRelation": "SELF", "name": "name_d89ff32606b6", "payAccountId": 0, "payMethod": "SBPS_CREDIT_CARD", "policyByEventId": 0, "policyPayerId": 0, "posPolicyPayerId": 0, "sharePercentage": "sharePercentage_e3af103741b8", "authAgreementSigningDate": "2024-03-27", "authAgreementNo": "authAgreementNo_e11335c072e5", "branchCode": "branchCode_f6f4a745ca65", "payerAccountId": 0, "customerId": 0, "roleType": "HOLDER", "keyWordList": ["keyWordList_63038081b641"], "individual": {"personId": 0, "fullName": "fullName_2ff8029b5c2e", "lastName": "lastName_65bcb4e197e2", "middleName": "middleName_e99cac966158", "firstName": "firstName_ebbff07688ef", "formattedLastName": "formattedLastName_08bc7548b13d", "formattedMiddleName": "formattedMiddleName_522b8cbde8b1", "formattedFirstName": "formattedFirstName_b7d25b24a98d", "formattedFullName": "formattedFullName_389370968aef", "fullName2": "fullName2_f7319214628d", "lastName2": "lastName2_ebddf9de2d9e", "middleName2": "middleName2_4c2f5bbc1601", "firstName2": "firstName2_1f94f7390e2f", "formattedLastName2": "formattedLastName2_cdfdf4c35b20", "formattedMiddleName2": "formattedMiddleName2_35a5be10a8eb", "formattedFirstName2": "formattedFirstName2_2ad8c439b95c", "formattedFullName2": "formattedFullName2_c3523bf8a193", "birthday": "2024-03-27", "sex": "MALE", "gender": "MALE", "certiNo": "certiNo_ca5bac8b6eb9", "certiType": "IDCARD", "residenceCountry": "TR", "customerGrade": "customerGrade_3ac13db76c8b", "isPromotional": false, "socialSecurity": false, "industryCode": "industryCode_9eb2d15f53d1", "occupationCode": "occupationCode_748a85bbaf78", "nationality": "AFGHANISTAN", "secondNationality": "AFGHANISTAN", "education": "BACHELOR", "income": "income_db6ecaea8f05", "smoke": false, "smokeGetRidOfDate": "2024-03-27", "height": "height_4de185707c3c", "weight": "weight_4e00c6a6ba39", "birthPlace": "birthPlace_99fb4d2d065f", "race": "race_f193176e8701", "marriageStatus": "UNMARRIED", "occupationStatus": "UNEMPLOYED", "channelUserNo": "channelUserNo_1062b296db8e", "channelCode": "channelCode_08deb7011115", "channelUserCodePartyId": "channelUserCodePartyId_178c7af41322", "isCustomer": false, "countryCode": "countryCode_fb3b203b476c", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2024-03-27", "ckaExpiryDate": "2024-03-27", "position": "position_d76d6e96f4b0", "title": "MR", "residentialStatus": "UNKNOWN", "deathOrNot": false, "dateOfDeath": "2024-03-27", "taxNo": "taxNo_92d5c279e698", "blackStatus": false, "workingPlace": "workingPlace_85ac729239dd", "issuanceDateOfCertificate": "2024-03-27", "issuancePlaceOfCertificate": "issuancePlaceOfCertificate_ea6081c945c3", "extraInfo": "extraInfo_d1f611fe3cfe", "partyId": 0, "partyType": "INDIVIDUAL", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 0, "addressType": "ADDRESS", "address11": "address11_3ef43fa23e2c", "address12": "address12_d765549ea7bd", "address13": "address13_c008287570c5", "address14": "address14_479012ef5b26", "address15": "address15_72aa02f76b0a", "address16": "address16", "address21": "address21_e1275c5a159d", "address22": "address22_bb9ea452e0a0", "address23": "address23_171e56d0e8d2", "address24": "address24_1ad40aa476d8", "address25": "address25_13eca08cc387", "zipCode": "zipCode_653b85fc10f5", "gisCode": "gisCode_e85a101739c5", "organizationAddressType": "REGISTER", "serialNo": "serialNo_9719817dd69c", "extensions": {}}], "accountList": [{"accountId": 0, "bankCode": "bankCode_246376503df6", "bankBranchCode": "bankBranchCode_a9d3f97aff84", "bankBranchName": "bankBranchName_69ed94df41ba", "accountType": "DEBIT_CARD", "accountSubType": "VISA_CARD", "cardHolderName": "cardHolderName_901d97d2d2dc", "cardNumber": "cardNumber_399a82a47142", "expiryDate": "2024-03-27", "mobileNo": "mobileNo_5be4faf40a22", "safeNo": "safeNo_eb7b4519d59a", "bankName": "bankName_33cc9a640859", "bankCity": "bankCity_57b132b5c87a", "payAccountId": 0, "payMethod": "SBPS_CREDIT_CARD", "status": 0, "thirdPartyPayVoucher": "thirdPartyPayVoucher_b036a94bf442", "partyType": "INDIVIDUAL", "isModified": false, "iban": "iban_9f6f1df2f94f", "summaryId": 0, "accountUniqueKey": "accountUniqueKey_10ef24e1420e", "swiftCode": "swiftCode_3f940dbd461a", "bankAddress": "bankAddress_5218cb2685b7", "bankBranchAddress": "bankBranchAddress_31c5799ef8ab", "serialNo": "serialNo_9ee1fcf98d03", "extensions": {}}], "socialAccountList": [{"socialAccountId": 0, "socialAccountType": "socialAccountType_4033347f984a", "socialAccountNumber": "socialAccountNumber_2a9d84e33b9a", "serialNo": "serialNo_a2c3f97f57e0", "extensions": {}}], "contactPersonList": [{"contactPersonId": 0, "contactPersonName": "contactPersonName_72284488fb7e", "countryCode": "countryCode_102b15a8d260", "contactPersonPhoneNumber": "contactPersonPhoneNumber_6a86d38a285c", "contactPersonEmail": "contactPersonEmail_f6cb6ce758ad", "position": "position_fb364a7dcfcf", "certiType": "IDCARD", "certiNo": "certiNo_3fcfe5c90f11", "isDefault": false, "remark": "remark_49188a77f800", "serialNo": "serialNo_3f1e3c1f8cb1", "extensions": {}}], "taxList": [{"taxId": 0, "taxNo": "taxNo_09ed50c66b97", "taxResidentialCountry": "AFGHANISTAN", "serialNo": "serialNo_20e4a457027b", "extensions": {}}], "issueWithoutPayment": false, "holderRelation": "SELF", "roleType": "HOLDER", "serialNo": "serialNo_aeaba8ec589d", "extensions": {}}, "organization": {"companyId": 0, "code": "code_73a1c1aa7898", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "businessLicenseNumber_5bf337fcfc74", "companyName": "companyName_b9a4f4fa9699", "companyName2": "companyName2_85b39d57d315", "industrialClassification": "industrialClassification_ff014bd630cf", "roleType": "HOLDER", "email": "email_ae5cf4a85b71", "cifNumber": "cifNumber_9bedbe3cb8a4", "userInputOrganizationType": "userInputOrganizationType_b2f62bf6245f", "registeredCapital": 0, "registeredCapitalCurrency": "USD", "numberOfEmployees": 0, "legalRepresentativeIdType": "IDCARD", "legalRepresentativeIdNo": "legalRepresentativeIdNo_27790ebaf35d", "legalRepresentativeName": "legalRepresentativeName_2cd201304623", "position": "position_a708600bcdac", "taxpayerIdentificationNumber": "taxpayerIdentificationNumber_827bb3a5f776", "taxpayerName": "taxpayerName_02075b72c699", "registeredPhone": "registeredPhone_429bb9215f79", "registeredDate": "2024-03-27 17:29:25", "organizationType": "ENTERPRISE", "organizationSize": "organizationSize_33b4750f2934", "country": "AFGHANISTAN", "industry": "industry_2172bcf5fa16", "certificateExpiryDate": "2024-03-27 17:29:25", "organizationAbbreviationName": "organizationAbbreviationName_5def4dce11ba", "registeredPhoneCountryCode": "registeredPhoneCountryCode_f3641d97b9db", "registeredAddress": "registeredAddress_0dd132b5885b", "branchCode": "branchCode_2e1051ff3ed8", "isPreStored": false, "typeOfBusiness": "typeOfBusiness_31f678f59367", "receiveInvoiceMethod": "EMAIL", "invoiceType": "GROUP_INVOICE", "partyId": 0, "partyType": "INDIVIDUAL", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 0, "addressType": "ADDRESS", "address11": "address11_7c55e5d34a1b", "address12": "address12_be1f0da92680", "address13": "address13_8efa1d613086", "address14": "address14_bc018fa9224c", "address15": "address15_0b688348a46c", "address16": "address16", "address21": "address21_5755a54630e5", "address22": "address22_f34952621115", "address23": "address23_8efdbab0c26a", "address24": "address24_b4d0029a33d3", "address25": "address25_96e9dd8c0d4b", "zipCode": "zipCode_9e044565119b", "gisCode": "gisCode_0213e76aceb1", "organizationAddressType": "REGISTER", "serialNo": "serialNo_1ae0ac62b732", "extensions": {}}], "accountList": [{"accountId": 0, "bankCode": "bankCode_c201c47a9b04", "bankBranchCode": "bankBranchCode_7d132d92f935", "bankBranchName": "bankBranchName_89c8d3fa73fe", "accountType": "DEBIT_CARD", "accountSubType": "VISA_CARD", "cardHolderName": "cardHolderName_7de0a07980f3", "cardNumber": "cardNumber_b5ca5f2c4534", "expiryDate": "2024-03-27", "mobileNo": "mobileNo_9dc6a0bf18af", "safeNo": "safeNo_4afe230cc735", "bankName": "bankName_b3d58c1c958b", "bankCity": "bankCity_3a56bad9cd0f", "payAccountId": 0, "payMethod": "SBPS_CREDIT_CARD", "status": 0, "thirdPartyPayVoucher": "thirdPartyPayVoucher_6d51ff1b363f", "partyType": "INDIVIDUAL", "isModified": false, "iban": "iban_4b94cea99ac4", "summaryId": 0, "accountUniqueKey": "accountUniqueKey_53fe06a25699", "swiftCode": "swiftCode_142efa2bdfdb", "bankAddress": "bankAddress_7773df8fd90c", "bankBranchAddress": "bankBranchAddress_8f295483a61f", "serialNo": "serialNo_9b72c100081d", "extensions": {}}], "socialAccountList": [{"socialAccountId": 0, "socialAccountType": "socialAccountType_0655b34213ef", "socialAccountNumber": "socialAccountNumber_ba1a76ac9ee2", "serialNo": "serialNo_46efb4c1e13e", "extensions": {}}], "contactPersonList": [{"contactPersonId": 0, "contactPersonName": "contactPersonName_489e5e3f1b35", "countryCode": "countryCode_7a7b82f7563c", "contactPersonPhoneNumber": "contactPersonPhoneNumber_2ab853f0d5c3", "contactPersonEmail": "contactPersonEmail_e4d53f3534a9", "position": "position_6c6cbf0d7163", "certiType": "IDCARD", "certiNo": "certiNo_f6ba5f8735ce", "isDefault": false, "remark": "remark_e078d6b2bd7c", "serialNo": "serialNo_056eaf5c52c9", "extensions": {}}], "taxList": [{"taxId": 0, "taxNo": "taxNo_b01854591cef", "taxResidentialCountry": "AFGHANISTAN", "serialNo": "serialNo_febd80560067", "extensions": {}}], "issueWithoutPayment": false, "holderRelation": "SELF", "serialNo": "serialNo_b702d862d270", "extensions": {}}, "serialNo": "serialNo_e811f780c355", "extensions": {}}], "goodsName": "goodsName_c16b88cad93d", "groupPolicyNo": "groupPolicyNo_d3ca571c2500", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "premium": "premium_43d09ca3a063", "attachmentList": "*", "premiumDetailList": [{"periodNo": "1", "stampDuty": "1", "taxList": "*", "serviceFeeList": "*", "commissionList": "*"}], "ruleDecisionList": "*", "task": "*", "userAuthInfo": "*", "insuredRelationList": [{"serialNo": "7665de1f-72cf-497c-b30a-bb7f85e04f1b", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["1", "1"]}], "renewalSource": "*", "issuanceRelationList": [{"type": "RENEWAL_POLICY", "subType": "HOST_RELATION_NO", "relationNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "clauseFileList": "*", "claimStackTemplateList": "*", "claimStackConfigList": "*"}}