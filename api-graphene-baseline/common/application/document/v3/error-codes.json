{"400": [{"sourceCode": "BIZ_MARKET_210088", "targetCode": "openapi.common.coverage.period.type.or.value.cannot.be.empty", "message": "Coverage period type or value cannot be empty."}, {"sourceCode": "BAD_REQUEST_CHANNEL_NOT_MATCH", "targetCode": "openapi.common.channel.not.match", "message": "No level1 only channel predefined by request level1 channel code."}, {"sourceCode": "BIZ_INSURED_DATE_IS_NULL", "targetCode": "openapi.common.application.date.cannot.be.empty", "message": "ApplicationDate can not be empty."}, {"sourceCode": "PLANID_NOT_MATCH", "targetCode": "openapi.common.plan.code.not.match.goodsid", "message": "PlanCode {0} is not match goodsId {1}."}, {"sourceCode": "GOODSID_CANNOT_EMPTY", "targetCode": "openapi.common.goodsid.not.be.empty", "message": "GoodsId can not be empty."}, {"targetCode": "openapi.common.application.not.found", "message": "Application is not found."}]}