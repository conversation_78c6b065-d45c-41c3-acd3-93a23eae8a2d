{"request": {"feeAmount": "131", "proposalConfirmDate": "2023-08-16T17:43:02.857+08:00", "tradeNo": "12312346666", "issuancePayerList": [{"payMethod": "SBPS_CREDIT_CARD", "payerType": "FIRST", "payAmount": "131", "mainInsuredRelation": "OTHER", "autoDeduction": false, "individual": {"certiType": "IDCARD", "holderRelation": "OTHER", "certiNo": "********", "fullName": "ken 124124", "birthday": "2022-03-12", "sex": "MALE", "residenceCountry": "TURKEY", "emailList": [{"email": "<EMAIL>"}], "phoneList": [{"phoneNo": "*********", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town"}], "accountList": [{"accountType": "CASH", "bankCode": "*************", "bankName": "CIBB", "bankBranchCode": "CIBB-AI", "cardNumber": "*************", "cardHolderName": "Test"}]}}]}, "response": {"policyNo": "63202311300015400001"}}