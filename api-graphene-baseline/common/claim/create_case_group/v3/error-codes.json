{"400": [{"sourceCode": "BIZ_CLM_DUPLICATED_BILL", "targetCode": "openapi.claim.create.duplicate.bill", "message": "Duplicate propertyBillList.bill No."}, {"sourceCode": "BIZ_CLM_CREATE_CASE_REPET", "targetCode": "openapi.claim.create.case.duplicate.apply", "message": "Duplicate Apply By/Transaction The claim case has been accepted and the policy is under claim."}, {"sourceCode": "", "targetCode": "openapi.claim.create.case.error", "message": "claim create case parameter error"}], "404": [{"sourceCode": "market.common.not.found", "targetCode": "openapi.market.goods.not.found", "message": "Goods {0} is not found."}, {"sourceCode": "BIZ_RESOURCE_NOT_FOUND", "targetCode": "openapi.claim.create.policy.not.found", "message": "Policy not found exception, identifier: {0}."}]}