{"request": {"reportNo": "reportNo", "applicationDate": "2024-12-30T10:00:00Z", "appliedBy": "<PERSON>", "*thirdPartyTransactionNo": "T*********9", "preAuthorizationCase": "NON_PRE_AUTHORIZED", "caseTypeList": ["PROPERTY_LOSS", "COMPENSATION"], "*caseList": [{"incidentNo": "INC202412300001", "reasonCodeList": ["RC001", "RC002"], "comment": "Incident approved based on review.", "payoutAmount": "1500.00", "baseCurrency": "USD", "paymentStatus": "PENDING", "paymentDate": "2024-12-31T12:00:00Z", "extensions": {"E_IsDiplomat": "NO"}, "lossParty": {"*lossPartyRole": "INSURED", "lossPartyType": "PERSON", "lossPartyInsuredObjectRelationList": [{"policyNo": "POL20231001001", "policyScenario": "NORMAL", "eventPolicyNo": "EVT20231001001", "insuredObjectType": "DEVICE", "insuredObjectNo": "OBJ20231001001"}], "property": {"description": "This is a test description", "propertyList": [{"description": "This is a test description", "serialNo": "tjOEPhcEYKo"}], "propertyOwner": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "birthday": "1939-01-02", "certiType": "PASSPORT", "certiNo": "*********", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "position": "position"}}, "animal": {"relationshipWithInsuredObject": "INSURED_OBJECT", "animalName": "<PERSON>", "animalAge": "5", "animalType": "DOG", "animalBreed": "Border collie", "animalGender": "MALE", "animalBirthday": "2020-02-01", "description": "LOSS_PARTY", "animalOwner": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "certiType": "PASSPORT", "certiNo": "*********", "birthday": "1990-12-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "medicalInsuranceCardNo": "medicalInsuranceCardNo"}}, "vehicle": {"thirdPartyType": "INSURED_OBJECT", "insuredProportion": "80%", "plateNo": "ABC123", "plateNo1": "plateNo1", "plateNo2": "plateNo2", "plateNo3": "plateNo3", "plateNo4": "plateNo4", "plateRegistrationZone": "Asia/Shanghai", "vinNo": "VIN*********0XYZ", "engineNo": "ENG987654321", "continueDrive": true, "registrationNo": "REG456789", "carOwnerName": "<PERSON>", "vehicleMake": "Toyota", "vehicleModel": "<PERSON><PERSON><PERSON>", "vehicleGroup": "Sedan", "vehicleSubGroup": "Compact Sedan", "manufacturingYear": "2020", "vehicleType": "Car", "vehicleCapacity": "5 passengers", "modelDescription": "2020 Toyota Corolla LE", "accidentResponsibilityRatio": "50%", "vehicleUsage": "Personal", "currency": "USD", "purchasePrice": "20000", "enginePower": "130", "engineVolume": "1.8L", "numberOfSeat": "5", "chassisNo": "CHASSIS*********0A", "mainDrivingArea": "North America", "marketPrice": "18000", "registrationArea": "California, USA", "registrationDate": "2020-05-15", "extensions": {"E_IsDiplomat": "NO"}, "totalLoss": "NO", "totalAssessmentAmount": "5000", "vatRate": "7%", "color": "White", "numberOfDoors": "4", "mileage": "45000", "engineType": "Gasoline", "enginePowerUnit": "horsepower", "vehicleOwner": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "certiType": "PASSPORT", "certiNo": "*********", "birthday": "1990-12-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "position": "position", "vehicleOwnerRole": "SELF"}}, "customer": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "*certiType": "PASSPORT", "*certiNo": "*********", "birthday": "1990-12-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "medicalInsuranceCardNo": "medicalInsuranceCardNo"}}, "lossPartyList": [{"*lossPartyRole": "INSURED", "lossPartyType": "PERSON", "lossPartyInsuredObjectRelationList": [{"policyNo": "POL20231001001", "policyScenario": "NORMAL", "eventPolicyNo": "EVT20231001001", "insuredObjectType": "DEVICE", "insuredObjectNo": "OBJ20231001001"}], "property": {"description": "This is a test description", "propertyList": [{"description": "This is a test description", "serialNo": "tjOEPhcEYKo"}], "propertyOwner": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "birthday": "1939-01-02", "certiType": "PASSPORT", "certiNo": "*********", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "position": "position"}}, "animal": {"relationshipWithInsuredObject": "INSURED_OBJECT", "animalName": "<PERSON>", "animalAge": "5", "animalType": "DOG", "animalBreed": "Border collie", "animalGender": "MALE", "animalBirthday": "2020-02-01", "description": "LOSS_PARTY", "animalOwner": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "certiType": "PASSPORT", "certiNo": "*********", "birthday": "1990-12-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "medicalInsuranceCardNo": "medicalInsuranceCardNo"}}, "vehicle": {"thirdPartyType": "INSURED_OBJECT", "insuredProportion": "80%", "plateNo": "ABC123", "plateNo1": "plateNo1", "plateNo2": "plateNo2", "plateNo3": "plateNo3", "plateNo4": "plateNo4", "plateRegistrationZone": "Asia/Shanghai", "vinNo": "VIN*********0XYZ", "engineNo": "ENG987654321", "continueDrive": true, "registrationNo": "REG456789", "carOwnerName": "<PERSON>", "vehicleMake": "Toyota", "vehicleModel": "<PERSON><PERSON><PERSON>", "vehicleGroup": "Sedan", "vehicleSubGroup": "Compact Sedan", "manufacturingYear": "2020", "vehicleType": "Car", "vehicleCapacity": "5 passengers", "modelDescription": "2020 Toyota Corolla LE", "accidentResponsibilityRatio": "50%", "vehicleUsage": "Personal", "currency": "USD", "purchasePrice": "20000", "enginePower": "130", "engineVolume": "1.8L", "numberOfSeat": "5", "chassisNo": "CHASSIS*********0A", "mainDrivingArea": "North America", "marketPrice": "18000", "registrationArea": "California, USA", "registrationDate": "2020-05-15", "extensions": {"E_IsDiplomat": "NO"}, "totalLoss": "NO", "totalAssessmentAmount": "5000", "vatRate": "7%", "color": "White", "numberOfDoors": "4", "mileage": "45000", "engineType": "Gasoline", "enginePowerUnit": "horsepower", "vehicleOwner": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "certiType": "PASSPORT", "certiNo": "*********", "birthday": "1990-12-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "position": "position", "vehicleOwnerRole": "SELF"}}, "customer": {"address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "fullName": "<PERSON> 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "*certiType": "PASSPORT", "*certiNo": "*********", "birthday": "1990-12-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName", "remark": "123123131323123", "medicalInsuranceCardNo": "medicalInsuranceCardNo"}}], "incidentContent": {"incidentDate": "2024-12-29T15:30:00Z", "zoneId": "Europe/Zagreb", "incidentReason": "Fire damage", "*claimTypeList": ["PROPERTY_LOSS"], "delayList": [{"delayType": "BAGGAGE_DELAY", "transportationType": "BUS", "transportationStatus": "DELAY", "transportationNo": "AA123", "transportationDate": "2023-10-05T14:30:00Z", "departureCode": "JFK", "arrivalCode": "LAX", "departureZoneId": "America/New_York", "arrivalZoneId": "America/Los_Angeles", "originalDepartureTime": "2023-10-05T14:30:00Z", "actualDepartureTime": "2023-10-05T15:00:00Z", "originalArrivalTime": "2023-10-05T17:30:00Z", "actualArrivalTime": "2023-10-05T18:00:00Z", "departureDelayTime": 30, "arrivalDelayTime": 30, "delayReason": "Weather conditions", "departureAddress": "New York, USA", "arrivalAddress": "Los Angeles, USA", "occurrenceType": "DELAY", "departureCountry": "US", "arrivalCountry": "US", "transportationClass": "ECONOMY", "bookingDateZoneId": "America/New_York", "bookingDate": "2023-09-20T10:00:00Z", "changeDateZoneId": "America/New_York", "changeDate": "2023-10-04T12:00:00Z", "bookingAmount": "500.00", "bookingCurrency": "USD", "assessmentAmount": "450.00", "assessmentCurrency": "USD", "insuredPositionType": "DRIVER"}], "propertyLoss": {"lossType": "Theft", "lossSeverity": "MINOR_LOSS", "lossRatio": "50%", "lossAmount": "5000.00", "purchasePrice": "10000.00", "replacementCost": "9000.00", "marketPrice": "8000.00", "currency": "USD", "totalDepreciationRatio": "10%", "description": "Stolen during transit, partial recovery expected."}, "businessInterruptionList": [{"startDate": "2025-05-20T15:37:11.780+08:00", "endDate": "2025-05-20T15:37:11.780+08:00", "zoneId": "Asia/Shanghai", "interruptionDays": 10, "dailyAverageIncomeAmount": "5000.00", "actualDailyIncomeAmount": "1000.00", "currency": "USD", "profitRate": "20%", "adjustmentCoefficient": "0.85", "additionalCosts": "2000.00"}], "travel": {"travelStartDate": "2025-05-20T15:37:11.780+08:00", "travelEndDate": "2025-05-20T15:37:11.780+08:00", "travelZoneId": "Asia/Shanghai", "departureCountry": "China", "departureCity": "Beijing", "destinationCountry": "USA", "destinationCity": "New York"}, "propertyBillList": [{"billItem": "Repair Costs", "billNo": "BILL001", "billCurrency": "USD", "billIssuedBy": "Repair Co.", "billObject": "House", "billType": "INVOICE", "billDate": "2025-06-11T21:55:59.099+08:00", "zoneId": "Europe/Zagreb", "tax": "470.00", "billAmount": "1500.00"}], "accommodationList": [{"occurrenceType": "CHANGE_OR_CANCEL", "accommodationHotel": "Grand Plaza Hotel", "roomClass": "Deluxe", "timeZoneId": "Asia/Shanghai", "startDate": "2024-04-10T16:55:30.185+08:00", "endDate": "2024-04-10T16:55:30.185+08:00", "accommodationDays": 5, "perAmount": "200.00", "amount": "1000.00", "currency": "USD", "assessmentAmount": "950.00", "assessmentCurrency": "USD", "bookingDateZoneId": "Asia/Shanghai", "bookingDate": "2024-04-10T16:55:30.185+08:00", "changeDateZoneId": "Asia/Shanghai", "changeDate": "2024-04-10T16:55:30.185+08:00", "changeReason": "Trip rescheduled due to flight delay.", "extensions": {"E_IsDiplomat": "NO"}}], "allowanceList": [{"allowanceType": "HOSPITALIZATION", "startDate": "2024-04-10T16:55:30.185+08:00", "endDate": "2024-04-10T16:55:30.185+08:00", "description": "Hospitalization allowance due to accident.", "zoneId": "Asia/Shanghai", "days": 5, "hospitalName": "Shanghai General Hospital", "roomLevel": "VIP", "incurredPlace": "CN", "currency": "USD", "industryBenchmarkDailyWage": "100.00", "employeeAverageDailyWage": "120.00"}], "weatherList": [{"weatherType": "TEMPERATURE", "currency": "USD", "physicalUnit": "mm/h", "magnitude": "7.0", "intensity": "VII", "numericalValue": "150", "areaName": "Shanghai", "areaCode": "SH001", "ticketPrice": "50.00", "extensions": {"E_IsDiplomat": "NO"}}], "compensationBillList": [{"billAmount": "1300", "billCurrency": "USD", "billDate": "2025-05-20T15:37:11.780+08:00", "billItem": "99", "billNo": "cyber_invblsTcgdLwgDj", "billType": "INVOICE", "tax": "470.00", "claimableAmount": "1300", "zoneId": "Asia/Shanghai"}], "personalInjury": {"disabilityZoneId": "Asia/Shanghai", "disabilityGroupCode": "disabilityGroupCode", "disabilityGrade": "disabilityGrade", "disabilityDate": "2025-05-12T17:49:41.992+08:00", "disabilityCategory": "TOTAL_PERMANENT_DISABILITY", "criticalIllnessCode": "criticalIllnessCode", "criticalIllnessStage": "criticalIllnessStage", "criticalIllnessConfirmDate": "2025-05-13T17:49:41.992+08:00", "criticalIllnessConfirmZoneId": "Asia/Shanghai", "deathDate": "2025-05-12T17:49:41.992+08:00", "deathPlace": "deathPlace", "deathCause": "deathCause", "deathCertificateDate": "2025-05-12T17:49:41.992+08:00", "zoneId": "Asia/Shanghai", "deathCertificateZoneId": "Asia/Shanghai", "description": "This is a test description personalInjury"}, "medicalBillList": [{"billHierarchy": "BY_ITEM", "billType": "OUTPATIENT", "billNo": "billN11wwwo2221", "billCategory": "INVOICE", "billCurrency": "USD", "zoneId": "Asia/Shanghai", "billDate": "2025-05-12T17:49:41.992+08:00", "incurredPlace": "AFG", "hospitalCode": "zy_test_full_hospital_2", "hospitalName": "zy_test_full_hospital_2", "panelHospital": true, "hospitalType": "Private", "admissionDate": "2025-05-10T17:49:41.992+08:00", "dischargeDate": "2025-05-13T17:49:42.992+08:00", "outpatientDate": "2025-05-12T17:49:41.992+08:00", "roomLevel": "11", "hospitalizationDays": 3, "departmentName": "www", "patientName": "patientName", "address": {"zipCode": "wwww", "address13": "22", "address14": "33", "address11": "HB003", "address12": "11", "address15": "Address5", "address16": "county", "address17": "strret", "address18": "Address8"}, "doctorCode": "<PERSON><PERSON><PERSON>", "doctorName": "<PERSON><PERSON><PERSON>", "certificateType": "certificate test2", "certificateNo": "certificateNo", "surgeryId": 141234, "surgeryCode": "test1", "medicalBillItemList": [{"billItem": "0006", "doctorCode": "<PERSON><PERSON><PERSON>", "doctorName": "medicalBillItemListdoctorName", "quantity": "1", "itemAmount": "2000", "payoutAmount": "213", "nonClaimableAmount": "234", "thirdPartyReimbursement": "234", "claimableAmount": "234", "discountAmount": "23", "description": "2.8 ค่ารักษาพยาบาลผู้ป่วยนอก", "occurrenceTime": "2025-05-12T17:49:41.992+08:00", "subItemList": [{"billSubItem": "098S", "quantity": "12", "itemAmount": "2000", "payoutAmount": "213", "nonClaimableAmount": "234", "thirdPartyReimbursement": "234", "claimableAmount": "234", "discountAmount": "23", "description": "2.8 ค่ารักษาพยาบาลผู้ป่วยนอก1", "occurrenceTime": "2025-05-12T17:49:41.992+08:00"}]}]}], "doctorList": [{"doctorName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hospitalName": "hospitalName", "consultationDate": "2025-05-12T17:49:41.992+08:00", "consultationDetail": "consultationDetail", "doctorType": "CURRENT", "zoneId": "Asia/Shanghai", "hospitalCode": "hospitalCode", "doctorCode": "<PERSON><PERSON><PERSON>", "departmentCode": "doctorListdepartmentCode", "departmentName": "doctorListdepartmentName", "certiType": "IDCARD", "certiNo": "holdesssr1_SOgARTSlgS"}], "outpatientList": [{"hospitalCode": "hospitalCode", "hospitalName": "hospitalName", "zoneId": "Asia/Shanghai", "outpatientDate": "2025-05-12T17:49:41.992+08:00", "medicineSource": "INTERNAL"}], "hospitalList": [{"extensions": {"patientName": "patient1Name"}, "hospitalType": "Private", "hospitalCode": "zy_test_full_hospital_2", "hospitalName": "zy_test_full_hospital_2", "panelHospital": true, "zoneId": "Asia/Shanghai", "startDate": "2025-05-12T17:49:41.992+08:00", "endDate": "2025-05-15T17:49:41.992+08:00", "roomLevel": "11", "hospitalizationDays": 3, "hospitalLevel": "11", "departmentCode": "departmentCode", "departmentName": "departmentName", "address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "234324"}, "description": "description"}], "diagnosisList": [{"diagnosisGroupCode": "diagnosis-7d7dc93a49154185b76a8fb5f248e03f", "diagnosisCode": "CodeSun125", "diagnosisName": "CodeSun125", "severity": "String", "zoneId": "Asia/Shanghai", "diagnoseDate": "2025-05-12T17:49:41.992+08:00", "anamnesis": true}], "medicalLeaveList": [{"zoneId": "Asia/Shanghai", "startDate": "2025-05-12T17:49:41.992+08:00", "endDate": "2025-05-15T17:49:41.992+08:00", "leaveDays": 1, "extensions": {"E_IsDiplomat": "NO"}}], "surgeryList": [{"daySurgery": true, "surgeryDate": "2025-05-12T17:49:41.992+08:00", "surgeryCode": "test1", "surgeryName": "test1", "surgeryDetail": "String", "surgeryLevel": "1", "zoneId": "Asia/Shanghai", "anatomicalSystem": "Cardiovascular", "bodyPart": "<PERSON><PERSON>"}], "vehicleList": [{"vehicleLoss": {"estimatedLossAmount": "5000.00", "assessmentType": "ON_SITE", "assessmentPlace": "Shanghai, China", "currency": "USD", "vehicleLossLaborList": [{"item": "test11", "code": "test11", "priceType": "FOURS", "unitPrice": "1", "quantity": "1", "amount": "1", "estimationAmount": "1", "laborType": "PAINT"}], "vehicleLossMaterialList": [{"item": "Paint", "code": "MAT_001", "priceType": "FOURS", "unitPrice": "75.00", "quantity": "2", "amount": "150.00", "estimationAmount": "145.00"}], "vehicleLossPartList": [{"item": "Front Bumper", "code": "PART_001", "priceType": "FOURS", "unitPrice": "1000.00", "quantity": "1", "amount": "1000.00", "salvageRecovery": true, "salvageValue": "200.00", "estimationAmount": "920.00"}], "vehicleLossSundryList": [{"salvageRecovery": true, "salvageValue": "200.00", "item": "sundry1", "code": "sundry1", "priceType": "NEGOTIATED", "unitPrice": "1", "quantity": "1", "amount": "1", "estimationAmount": "1"}], "damageType": "PARTIAL_LOSS", "totalEstimationAmount": "4850.00", "lumpSumOffer": "NO", "lumpSumAmount": "20.00", "lumpSumFinalAmount": "30.00", "lumpSumAmountOfLiabilityRatio": "4500.00", "goAssessmentCenter": true, "totalLossAssessmentAmountOfLiabilityRatio": "4700.00", "assessmentCenter": {"departmentCode": "DEPT001", "addressType": "ADDRESS", "companyAddressType": "REGISTER", "zipCode": "12345", "address13": "Street 13", "address14": "Street 14", "address11": "A very long address line that can go up to 512 characters.", "address12": "City Name", "address15": "State Name", "address16": "Country", "address17": "Additional Info 1", "address18": "Additional Info 2"}, "assessmentAddress": {"addressType": "ADDRESS", "companyAddressType": "REGISTER", "zipCode": "12345", "address13": "Street 13", "address14": "Street 14", "address11": "A very long address line that can go up to 512 characters.", "address12": "City Name", "address15": "State Name", "address16": "Country", "address17": "Additional Info 1", "address18": "Additional Info 2"}, "assessmentCoverageRegion": "East China", "assessmentTime": "2025-05-12T17:49:41.992+08:00", "tax": "470.00", "taxRate": "10", "partFloatRate": "5", "vehicleLossTotal": {"objectValue": "20000.00", "yearsUsed": "3", "totalDepreciationRatio": "15", "amortizationValue": "3000.00", "salvageAmount": "1000.00", "estimationAmount": "16000.00"}}}], "rentalBillList": [{"billNo": "243234", "zoneId": "Asia/Shanghai", "billDate": "2024-04-10T16:55:30.185+08:00", "rentalFirmName": "ABC Car Rental Co.", "leaseTime": "2024-04-10T16:55:30.185+08:00", "returnTime": "2024-04-15T16:55:30.185+08:00", "rentalDays": 5, "rentPerDay": "80.00", "billAmount": "400.00", "accidentLiabilityRatio": "0.7", "claimableAmount": "280.00", "assessmentAmount": "300.00", "billCurrency": "USD"}], "treatmentList": [{"treatmentType": "treatmentType", "treatmentAddress": "treatmentAddress", "zoneId": "Asia/Shanghai", "startDate": "2025-05-12T17:49:41.992+08:00", "endDate": "2025-05-15T17:49:41.992+08:00", "treatmentDays": 1, "status": "status", "description": "description", "treatmentStatus": "treatmentStatus"}], "incidentPlace": "2222123", "lossPartyIdentifier": "tjOEPhcEYKo", "description": "This is a test description"}, "documentation": {"attachmentList": [{"attachmentName": "Idcard.jpg", "attachmentTypeCodes": ["ID"], "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2", "attachmentUrl": "https://example.com/attachments/Idcard.jpg"}, {"attachmentName": "FireReport.pdf", "attachmentTypeCodes": ["REPORT"], "attachmentUniqueCode": "********-abcd-efgh-ijkl-*********0ab", "attachmentUrl": "https://example.com/attachments/FireReport.pdf"}], "materialCompleteDate": "2024-12-30T10:30:00Z", "lastUpdateDate": "2024-12-30T11:00:00Z"}, "payeeList": [{"accountList": [{"paymentMethod": "BANK_SLIP", "accountType": "DEBIT_CARD", "accountSubType": "VISA_CARD", "bankCode": "BANK123", "bankName": "Example Bank", "bankAddress": "Bank St 123", "storeCode": "STORE001", "storeName": "Example Store", "bankBranchAddress": "Branch St 456", "accountPersonName": "<PERSON>", "accountNo": "*********0123456", "expireDate": "2025-02-09", "bankCity": "Zagreb", "iban": "HR*********0*********0", "swiftCode": "BOFAUS3N", "isDefault": "YES", "bankBranchCode": "BRANCH123", "bankBranchName": "Downtown Branch", "paymentMethodAccountType": "LINE_PAY_WALLET", "cardHolderName": "<PERSON>", "cardNumber": "****************", "expiryDate": "2025-12-31"}], "address": {"address11": "11", "address12": "12", "address13": "13", "address14": "14", "address15": "15", "address16": "16", "address17": "17", "address18": "18", "addressType": "ADDRESS", "zipCode": "21412"}, "relationship": "SELF", "relationshipDesc": "SELF", "fullName": "<PERSON>", "fullName2": "<PERSON><PERSON><PERSON>", "fullName3": "<PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "certiType": "PASSPORT", "certiNo": "*********", "birthday": "1990-02-09", "gender": "MALE", "sex": "MALE", "phoneType": "PHONE", "nature": "ADULT", "industry": "education", "occupation": "teacher", "countryCode": "+1", "phoneNo": "************", "email": "<EMAIL>", "nationality": "US", "residenceCountry": "US", "deathOrNot": false, "customerCategory": "INDIVIDUAL", "organizationIdType": "TAX_ID", "organizationIdNo": "ORG*********", "branchCode": "BR001", "organizationName": "Example Corporation", "birthPlace": "birthPlace", "middleName": "middleName"}]}], "insuredList": "*", "claimant": {"relationship": "SELF", "relationshipDesc": "SELF", "paymentMethod": "BANK_SLIP", "certiType": "IDCARD", "certiNo": "*********", "fullName": "<PERSON>", "sex": "MALE", "gender": "MALE", "birthday": "1985-05-15", "phoneNo": "**********", "phoneType": "PHONE", "nature": "ADULT", "industry": "education", "occupation": "teacher", "address": {"addressType": "RESIDENTIAL", "address11": "456 Elm St"}}, "authorizedParticipantList": ["*"]}, "response": {"applicationNo": "CLAIM2024101304020", "caseNo": "CLMGC20241013000009", "caseIncidents": [{"caseNo": "CLMGC20241013000009", "lossParties": [{"lossPartyType": "PROPERTY", "claimTypes": ["PROPERTY_DAMAGE"]}]}], "caseList": "*"}}