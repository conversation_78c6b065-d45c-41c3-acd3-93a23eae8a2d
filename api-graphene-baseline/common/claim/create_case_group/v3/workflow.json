{"activities": [{"name": "Start", "type": "Start", "dependEvents": ["Start"]}, {"name": "group_createCaseV3", "type": "Procedure", "procedures": [{"tag": "nano", "name": "group_createCaseV3", "type": "RestfulService", "domain": "claim"}], "dependActivities": ["Start"]}, {"name": "End", "type": "End", "using": [{"direction": "OUT", "procedure": "group_createCaseV3"}], "dependActivities": ["group_createCaseV3"]}]}