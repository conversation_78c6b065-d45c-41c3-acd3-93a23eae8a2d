{"400": [{"sourceCode": "PARAM_ERROR", "targetCode": "openapi.payment.request.param.error", "message": "Parameter error, please check"}, {"sourceCode": "openapi.payment.request.param.error", "message": "Parameter error, please check"}, {"sourceCode": "openapi.payment.pay.order.already.success", "message": "Pay order already success"}, {"sourceCode": "GET_LOCK_FAILED", "targetCode": "openapi.payment.call.frequent", "message": "Request is too frequent, please try again later."}], "404": [{"sourceCode": "openapi.payment.pay.order.not.found", "message": "Pay order not found"}], "500": [{"sourceCode": "", "targetCode": "openapi.common.error", "message": "Open API public error: {0}."}]}