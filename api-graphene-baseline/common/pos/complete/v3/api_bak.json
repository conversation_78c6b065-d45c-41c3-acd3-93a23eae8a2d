{"openapi": "3.0.1", "paths": {"/api/v3/policies/{policyNo}/changes/{caseNo}": {"put": {"parameters": [{"name": "policyNo", "in": "path", "description": "policy No", "schema": {"type": "string", "default": "B1001113232233"}, "required": true}, {"name": "caseNo", "in": "path", "description": "The number of POS case", "schema": {"type": "string", "default": "C1001113232233"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"title": "The parameter of POS case operation", "type": "object", "required": [], "properties": {"thirdPartyTransactionNo": {"title": "bizApplyNo", "type": "string", "description": "Third party transaction no, it is used to be idempotent.(Each request requires a new third party transaction number to be passed in through API.)", "maxLength": 64}, "cancellationType": {"type": "string", "enum": ["CANCEL", "SURRENDER", "LAPSED", "TERMINATION", "INVALID", "PERMANENT_LAPSED", "REVOCATION", "TOTAL_LOSS", "POLICY_MATURITY"]}, "cancellationReason": {"type": "integer", "format": "int32"}, "transactionList": {"type": "array", "items": {"type": "object", "properties": {"extensions": {"type": "object", "additionalProperties": {"type": "object"}}, "afterPolicy": {"type": "object", "description": "need to change policy info", "properties": {"policyHolder": {"description": "The policyHolder is an object that typically holds information about an individual who has a particular insurance policy. ", "type": "object", "required": [], "properties": {"individual": {"type": "object", "description": "Person type of policy related roles", "required": [], "properties": {"fullName": {"type": "string", "maxLength": 255, "example": "ken peter"}, "lastName": {"type": "string", "maxLength": 255, "example": "peter"}, "middleName": {"type": "string", "maxLength": 255, "example": "M"}, "firstName": {"type": "string", "maxLength": 255, "example": "ken"}, "formattedLastName": {"type": "string", "maxLength": 255, "example": "PETER"}, "formattedMiddleName": {"type": "string", "maxLength": 255, "example": "M"}, "formattedFirstName": {"type": "string", "maxLength": 255, "example": "KEN"}, "formattedFullName": {"type": "string", "maxLength": 255, "example": "KEN PETER"}, "fullName2": {"type": "string", "maxLength": 255, "example": "ken peter2"}, "lastName2": {"type": "string", "maxLength": 255, "example": "peter2"}, "middleName2": {"type": "string", "maxLength": 255, "example": "M2"}, "firstName2": {"type": "string", "maxLength": 255, "example": "ken2"}, "formattedLastName2": {"type": "string", "maxLength": 255, "example": "PETER2"}, "formattedMiddleName2": {"type": "string", "maxLength": 255, "example": "M2"}, "formattedFirstName2": {"type": "string", "maxLength": 255, "example": "KEN2"}, "formattedFullName2": {"type": "string", "maxLength": 255, "example": "KEN PETER2"}, "birthday": {"type": "string", "format": "date", "example": "1990-02-09", "description": "Example:'1990-03-03'"}, "sex": {"type": "string", "enum": ["MALE", "FEMALE", "UNKNOWN"], "example": "MALE"}, "certiNo": {"type": "string", "maxLength": 255, "example": "43543546"}, "certiType": {"type": "string", "example": "IDCARD", "enum": ["IDCARD", "PASSPORT", "OTHER"]}, "customerGrade": {"type": "string", "maxLength": 10, "example": "A"}, "isPromotional": {"type": "boolean", "example": true}, "socialSecurity": {"type": "boolean", "example": true}, "industryCode": {"type": "string", "maxLength": 64, "example": "IT"}, "occupationCode": {"type": "string", "maxLength": 64, "example": "Software Engineer"}, "nationality": {"type": "string", "maxLength": 64, "example": "UNITED_STATES"}, "secondNationality": {"type": "string", "maxLength": 64, "example": "CANADA"}, "residenceCountry": {"type": "string", "example": "TR", "enum": ["TR", "NL", "HR", "RS", "IE"], "description": "Country code according to ISO. eg. TR,NL,HR,RS,IE"}, "education": {"type": "string", "maxLength": 64, "example": "BACHELOR"}, "income": {"type": "string", "maxLength": 64, "example": "50000"}, "smoke": {"type": "boolean", "example": false}, "smokeGetRidOfDate": {"type": "string", "format": "date", "example": "2010-01-01"}, "height": {"type": "string", "maxLength": 64, "example": "180"}, "weight": {"type": "string", "maxLength": 64, "example": "75"}, "birthPlace": {"type": "string", "maxLength": 64, "example": "Shanghai"}, "race": {"type": "string", "maxLength": 64, "example": "Asian"}, "marriageStatus": {"type": "string", "maxLength": 64, "example": "UNMARRIED"}, "occupationStatus": {"type": "string", "maxLength": 64, "example": "EMPLOYED"}, "preferredLanguage": {"type": "string", "example": "English", "description": "For any other languages sent in the request, system will store the value, but default the notifications in English", "enum": ["English", "German", "French", "Italian", "ROMANSH", "SimplifiedChinese", "TraditionalChinese", "Japanese", "Indonesian", "THAI", "BULGARIAN", "CROATIAN", "CZECH", "DANISH", "DUTCH", "ESTONIAN", "FINNISH", "GREEK", "HUNGARIAN", "IRISH", "LATVIAN", "LITHUANIAN", "MALTESE", "POLISH", "PORTUGUESE", "ROMANIAN", "SLOVAK", "SLOVENIAN", "SPANISH", "SWEDISH", "KOREAN", "BAHASA", "TAKAFUL", "NORWEGIAN", "RUSSIAN", "HINDI", "ARABIC", "TAMIL", "VIETNAMESE", "MALAY", "TURKISH", "SERBIAN", "MONTENEGRIN"]}, "channelUserNo": {"type": "string", "maxLength": 64, "example": "123456"}, "channelCode": {"type": "string", "maxLength": 64, "example": "ChannelA"}, "isCustomer": {"type": "boolean", "example": true}, "countryCode": {"type": "string", "maxLength": 64, "example": "US"}, "missingStatus": {"type": "integer", "example": 1}, "ckaIndicator": {"type": "boolean", "example": true}, "ckaEffectiveDate": {"type": "string", "format": "date", "example": "2024-06-26"}, "ckaExpiryDate": {"type": "string", "format": "date", "example": "2030-01-01"}, "position": {"type": "string", "maxLength": 64, "example": "Manager"}, "title": {"type": "string", "maxLength": 64, "example": "MS"}, "age": {"type": "integer", "maxLength": 2, "example": "30"}, "residentialStatus": {"type": "string", "example": "CITIZEN", "enum": ["UNKNOWN", "CITIZEN", "PR", "FOREIGNERS"]}, "deathOrNot": {"type": "boolean", "example": false}, "dateOfDeath": {"type": "string", "format": "date", "example": "2090-01-01"}, "taxNo": {"type": "string", "maxLength": 64, "example": "*********"}, "blackStatus": {"type": "boolean", "example": false}, "workingPlace": {"type": "string", "maxLength": 64, "example": "Company Ltd."}, "issuanceDateOfCertificate": {"type": "string", "format": "date", "example": "2024-06-26"}, "issuancePlaceOfCertificate": {"type": "string", "maxLength": 64, "example": "City Hall"}, "payerType": {"type": "string", "example": "FIRST", "enum": ["FIRST", "RENEWAL"]}, "extraInfo": {"type": "string", "maxLength": 512, "example": "Some extra information"}, "emailList": {"type": "array", "items": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 256, "example": "<EMAIL>", "format": "email", "description": "Party Email address"}, "emailId": {"type": "integer", "example": "1", "description": "<PERSON><PERSON>d"}, "isDefault": {"type": "boolean", "example": true, "description": "Party Email whether is<PERSON><PERSON><PERSON>"}}}}, "phoneList": {"type": "array", "items": {"type": "object", "properties": {"phoneType": {"type": "string", "enum": ["PHONE", "LANDLINE", "COMPANYPHONE", "FAX"], "example": "PHONE", "description": "Party Phone Type"}, "phoneId": {"type": "integer", "example": "1", "description": "Phone Id"}, "phoneNo": {"type": "string", "maxLength": 64, "example": "*********", "description": "Phone No"}, "countryCode": {"type": "string", "maxLength": 12, "example": "+86", "description": "Phone No country code"}, "isDefault": {"type": "boolean", "example": true, "description": "Whether Default Info"}}}}, "accountList": {"type": "array", "description": "When payMethod is 'CASH', accountList is not required.", "items": {"type": "object", "properties": {"accountId": {"type": "integer", "example": "1", "description": "Account Id"}, "accountType": {"type": "string", "enum": ["DEBIT_CARD", "CREDIT_CARD", "LINE_PAY_WALLET", "CASH", "CHEQUE", "OFF_LINE"], "description": "Payment account type."}, "bankCode": {"type": "string", "maxLength": 128, "description": "Bank Code"}, "bankName": {"type": "string", "maxLength": 256, "description": "Bank name."}, "bankBranchCode": {"type": "string", "maxLength": 128, "description": "Bank branch code."}, "bankBranchName": {"type": "string", "maxLength": 256, "description": "Bank branch name."}, "cardHolderName": {"type": "string", "maxLength": 256, "description": "Card Holder Name."}, "cardNumber": {"type": "string", "maxLength": 128, "description": "Card Number."}}}}, "addressList": {"type": "array", "items": {"type": "object", "required": ["addressType"], "properties": {"addressType": {"type": "string", "enum": ["ADDRESS", "HA", "CA", "DELIVERY", "OVERSEAS", "CITIZENID", "CONTACT", "PERMANENT", "RESIDENTIAL", "MAILING", "BILLING"], "example": "ADDRESS"}, "zipCode": {"type": "string", "maxLength": 12, "example": "A00001"}, "address11": {"type": "string", "example": "state", "maxLength": 512, "description": "For different countries or tenants, the hierarchy of addresses may vary. We have defined several levels for addresses, but the actual business meaning needs to be referred to the business configuration. Please consult the API reference for further details."}, "address12": {"type": "string", "example": "city", "maxLength": 512, "description": "For different countries or tenants, the hierarchy of addresses may vary. We have defined several levels for addresses, but the actual business meaning needs to be referred to the business configuration. Please consult the API reference for further details."}, "address13": {"type": "string", "example": "district", "maxLength": 512, "description": "For different countries or tenants, the hierarchy of addresses may vary. We have defined several levels for addresses, but the actual business meaning needs to be referred to the business configuration. Please consult the API reference for further details."}, "address14": {"type": "string", "example": "town", "maxLength": 512, "description": "For different countries or tenants, the hierarchy of addresses may vary. We have defined several levels for addresses, but the actual business meaning needs to be referred to the business configuration. Please consult the API reference for further details."}, "address": {"type": "string", "maxLength": 512, "example": "state city district town", "description": "If you do not need to collect address information in different hierarchical levels, you can directly pass the complete address information into the \"address\" field."}}}}}}}}}}, "transTypeEnum": {"type": "string", "enum": ["FREELOOKSURRENDER", "POS_CANCELLATION", "AUTO_RENEWAL_SWITCH_CHANGE", "POS_HOLDER_INFO_CHANGES", "POLICY_BASE_INFORMATION_CHANGE"]}, "effectiveDateType": {"type": "string", "enum": ["POS_IMMEDIATELY_DATE", "POS_LAST_PREMIUM_DUE_DATE", "POS_NEXT_PREMIUM_DUE_DATE", "POS_USER_INPUT", "POS_PREMIUM_DUE_DATE_OF_NEXT_BILL", "ANNIVERSARY_DATE", "POLICY_EFFECTIVE_DATE", "RENEWAL_POLICY", "NEXT_CHARGE_DUE_DATE", "LAST_ANNIVERSARY_DATE", "POS_BACKDATED_DATE"]}, "effectiveDate": {"type": "string", "format": "date-time"}, "reasonCode": {"type": "integer", "format": "int32"}, "reason": {"type": "string"}}}}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"title": "The result of POS case application", "type": "object", "properties": {"caseNo": {"title": "caseNo", "type": "string", "description": "Case number."}, "thirdPartyTransactionNo": {"type": "string", "description": "Third party transaction no"}, "policyNo": {"type": "string", "description": "policy No"}}}}}}}}}}}