{"response": {"relationDetailList": [{"relationPolicyNo": "relationPolicyNo"}], "agreementNo": "agreementNo", "systemSource": "systemSource", "insureCreator": "1", "remark": "remark", "policyNo": "B39294032", "proposalNo": "I329403243", "quotationNo": "3294302904", "goodsCode": "EDI_v1", "goodsName": "EDI_v1", "goodsVersion": "V01", "planCode": "B0003213", "eventNo": "B0003213", "orderNo": "z_3294302904", "premium": "300", "actualPremium": 1, "premiumFrequencyType": "SINGLE", "paymentPeriodType": "SINGLE", "tradeNo": "TRADE1234567890", "coveragePeriod": "1 YEAR", "coveragePeriodType": "YEARFULL", "renewalMethod": "NONE_RENEWAL", "policyDeliveryMethod": "E_POLICY", "channelRole": "NON_OFFICE", "salesAgreementCode": "salesChannelName", "policyStatus": "POLICY_EFFECT", "applicationDate": "2023-08-16T17:43:02.857+08:00", "signOffDate": "2023-08-17T17:43:02.857+08:00", "signOffStatus": "CONFIRMED", "canIssueEvent": true, "epolicyDispatchDate": "2023-08-18T17:43:02.857+08:00", "thirdPartyTransactionNo": "TP_TRANS_1234567890", "zoneId": "Europe/Zagreb", "effectiveDate": "2023-08-16T17:43:02.857+08:00", "expiryDate": "2024-08-16T17:43:02.857+08:00", "vestingAge": 45, "assignee": "*", "payeeList": [{"individual": {"accountList": [{"accountSubType": "VISA_CARD", "accountType": "STRIPE_PAY_WALLET", "bankBranchCode": "9831", "bankCode": "536336", "bankName": "CCB", "cardHolderName": "<PERSON>", "cardNumber": "************", "expiryDate": "2025-07-21", "mobileNo": "***********"}]}, "payMethod": "STRIPE_PAY", "policyPayeeId": 1}], "policyByEventList": [{"tradeNo": "T57-************", "policyNo": "B014950007", "levy": "0.027", "eventStatus": "POLICY_EFFECT", "issueDate": "2025-07-16T14:06:37.000+08", "applyDate": "2025-07-16T06:55:47.000+08", "leviedPremium": "0.246", "policyByEventId": ***************, "entryAge": 35, "policyByEventProductList": [{"tax": "0.027", "actualPremium": "0.300", "periodPremium": "0.273", "expiryDate": "2026-07-11T06:55:46.000+08", "totalSumInsured": "246", "policyByEventProductId": ***************, "sumInsured": "246", "effectiveDate": "2025-07-16T06:55:47.000+08", "productId": ****************, "periodStandardPremiumIncTax": "0.3"}], "eventNo": "B014960003"}], "renewalSource": "*", "currency": "*", "extensions": {"E_IsDiplomat": "NO"}, "policyHolder": {"individual": "*", "organization": "*"}, "issuanceRelationList": [{"type": "RENEWAL_POLICY", "subType": "HOST_RELATION_NO", "relationNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "insuredRelationList": [{"serialNo": "7665de1f-72cf-497c-b30a-bb7f85e04f1b", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["1", "1"]}], "insuredList": [{"policyInsurantId": 292883248742405, "serialNo": "insurant_292883235667975", "individual": {"smoke": true, "smokeGetRidOfDate": "1991-02-09", "partyId": 288949134196740, "certiType": "IDCARD", "holderRelation": "SELF", "certiNo": "********", "fullName": "ken peter", "sex": "MALE", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 1, "addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town1", "address15": "address5", "address16": "address5", "address17": "address5", "address18": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "state city district town"}], "accountList": [{"accountId": 0, "expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "*********89", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "attachmentList": "*"}, "organization": {"code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "companyName": "xxxx", "companyName2": "xxxx.", "industrialClassification": "xxx", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT service", "registeredCapital": ********, "registeredCapitalCurrency": "CNY", "numberOfEmployees": 500, "legalRepresentativeIdType": "IDCARD", "legalRepresentativeIdNo": "110101199001011234", "legalRepresentativeName": "xx", "position": "xxxx", "taxpayerIdentificationNumber": "TIN987654321", "taxpayerName": "xxx", "registeredPhone": "************", "registeredDate": "2010-01-01", "organizationType": "ENTERPRISE", "organizationSize": "big", "country": "CHINA", "industry": "tech", "certificateExpiryDate": "2030-12-31", "organizationAbbreviationName": "cccxx", "registeredPhoneCountryCode": "+86", "registeredAddress": "xxx", "branchCode": "BJ001", "isPreStored": false, "typeOfBusiness": "xxx", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE", "partyType": "COMPANY", "issueWithoutPayment": true, "holderRelation": null, "isPromotional": true, "isCrossSell": true, "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 5001, "address": "xxx", "address11": "xx", "address12": "xxx", "address13": "xx", "address14": "xx", "address15": "address5", "address16": "address5", "address17": "address5", "address18": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "zipCode": "xxxx"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "*********89", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}}], "beneficiaryList": [{"individual": {"certiType": "IDCARD", "holderRelation": "SELF", "certiNo": "********", "fullName": "ken peter", "sex": "MALE", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 1, "addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town1", "address15": "address5", "address16": "address5", "address17": "address5", "address18": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "state city district town"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "*********89", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}, "benefitRatio": "100%", "insurantRelation": "SELF", "benefitType": "NORMAL", "beneficiaryOrder": "PRIMARY", "customerId": 10000, "beneficiaryId": 10000}], "productList": [{"id": 1, "productCode": "productCode_2a7cef46c9e9", "productVersion": "productVersion_ce2b194fe400", "premiumFrequencyType": "SINGLE", "premiumFrequencyValue": "1", "productCategory": "TRADITIONAL", "underwriteDecision": "NORMAL", "mainProductId": 0, "productType": "MAIN", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "riskStartDate": "2024-03-27 17:29:25", "unit": 0, "sumInsured": "sumInsured_bb7c12f3f758", "totalSumInsured": "totalSumInsured_d97c18936bec", "riskSa": "riskSa_7f6820930072", "lastPremiumDueDate": "2024-03-27 17:29:25", "dueDate": "2024-03-27 17:29:25", "currentIndex": 0, "totalInstallPremium": "totalInstallPremium_acc34b34bec5", "periodPremium": "periodPremium_9696b2c4acae", "policyFee": "policyFee_e9fabf277c1d", "status": "EFFECTIVE", "statusName": "statusName_0e82e4a85274", "statusChangeCause": "POS_NO_REASON_RESCISSION", "cancelOption": "IMMEDIATELY", "statusChangeDate": "2024-03-27 17:29:25", "currency": "USD", "coveragePeriodType": "DAY", "coveragePeriod": "coveragePeriod_b1f11087d08a", "paymentPeriodType": "YEARFULL", "paymentPeriod": "paymentPeriod_f706f0a54103", "canRenewal": "ENSURE", "issuanceProductId": 0, "pickupType": "CASH_COLLECTION", "pickupPayAccountId": 0, "benefitLevel": 0, "policyScenario": "NORMAL", "productName": "productName_1f108c930bb7", "includingTax": false, "tax": "tax_ba3697a97e11", "taxType": "GST", "coverageTotalDeltaNetPremium": "123", "premiumAfterDiscount": "premiumAfterDiscount_547767b08ac6", "premiumDiscount": "premiumDiscount_6d4241118996", "annualPremium": "annualPremium_4f582a7cdae2", "actualPremium": "actualPremium_5795479c92b7", "annualExtraPremium": "annualExtraPremium_10be670a2401", "annualExtraPremiumIncTax": "annualExtraPremiumIncTax_c82f5debe7ba", "annualDiscountPremium": "annualDiscountPremium_bfa6e8ec09de", "annualDiscountPremiumIncTax": "annualDiscountPremiumIncTax_89c6b0516040", "coverageTotalNoClaimDiscount": "coverageTotalNoClaimDiscount_9524650924b7", "annualTotalTax": "annualTotalTax_9808f9774f04", "productPremiumHolidayList": [{"policyProductPremiumHolidayId": 1, "posCaseId": 1, "posTransactionId": 1, "startDate": "2025-06-30T02:38:19.573+08:00", "endDate": "2026-06-30T02:38:19.573+08:00", "deleteFlag": "NO"}], "productRetirementOption": {"policyProductRegularWithdrawalPlanList": [{"policyProductRegularWithdrawalPlanId": 1, "retirementAge": "60", "frequency": "YEAR", "withdrawalValue": "1000", "withdrawalPeriodStartDate": "2041-02-28T00:00:00.000+08:00", "withdrawalPeriodEndDate": "2053-05-30T00:00:00.000+08:00", "retirementStartDate": "2041-01-28T00:00:00.000+08:00"}], "policyProductReduceCoveragePlanList": [{"policyProductReduceCoveragePlanId": 1, "retirementAge": "60", "sumAssured": "900", "status": "INITIAL"}]}, "productLiabilityList": [{"productLiabilityId": 0, "liabilityId": 0, "annuityDeferPeriodType": "annuityDeferPeriodType_97e4952e52c6", "annuityDeferPeriod": "annuityDeferPeriod_b9266d9c7391", "annuityPaymentPeriodType": "annuityPaymentPeriodType_02f4243f20aa", "annuityPaymentPeriod": "annuityPaymentPeriod_9dc9e75ea256", "annuityGuaranteePeriodType": "annuityGuaranteePeriodType_fd401d63a536", "annuityGuaranteePeriod": "annuityGuaranteePeriod_dd9cf8cc795b", "annuityPaymentFrequencyType": "annuityPaymentFrequencyType_655a3f42629b", "annuityPMTModalFactor": "annuityPMTModalFactor_f3cf39d1ca0e", "annuityPaymentOption": "annuityPaymentOption_52b50004118e", "claimStackCodeList": ["claimStackCodeList_ff0000fff1ca"], "deductible": {"deductibleType": "NO_DEDUCTIBLE", "amount": "amount_6254dafe1c7f", "valueType": "AMOUNT", "serialNo": "serialNo_76a0a3f0c886", "extensions": {}}, "limit": {"limitType": "NO_LIMIT", "valueType": "AMOUNT", "amount": "amount_c72b388f3128", "serialNo": "serialNo_d0d2dd6b3e0a", "extensions": {}}, "premiumDetailList": [{"periodNo": "1", "periodFinalPremium": "1", "periodStandardPremiumIncTax": "1", "periodExtraPremium": "1", "periodExtraPremiumIncTax": "1", "premiumLoadingList": "*", "periodDiscountPremium": "1", "periodDiscountPremiumIncTax": "1", "premiumDiscountList": "*", "periodNetPremium": "1", "periodNetPremiumIncTax": "1", "periodNoClaimDiscount": "1", "periodNoClaimDiscountIncTax": "1", "noClaimDiscountRate": "1", "periodTotalTax": "1", "periodLevy": "1", "periodServiceFee": "1", "serviceFeeList": "*", "periodCommission": "1", "periodCampaignDiscount": "1", "actualPremium": "1", "premiumDeltaRate": "1", "multiCurrencyPremiumList": "*"}], "liabilityBenefitList": [{"productLiabilityBenefitId": 0, "productId": 0, "bonusType": "CASH_BONUS", "policyProductLiabilityId": 0, "policyProductId": 0, "liabilityId": 0, "benefitType": "ANNUITY", "sbPaymentOption": "RECEIVE_BENEFIT_IN_CASH", "sbPaymentFrequencyType": "SINGLE", "paymentFrequencyValue": "paymentFrequencyValue_e79601c53da6", "paymentDeferPeriodType": "IMMEDIATE_PAYMENT", "paymentDeferPeriodValue": "paymentDeferPeriodValue_72a1d58dde3b", "paymentPeriodType": "WHOLE_LIFE", "paymentPeriodValue": "paymentPeriodValue_912c4a3e23b0", "guaranteePeriodType": false, "guaranteePeriodValue": "guaranteePeriodValue_23eb6a0fd597", "depositAccountBalance": "depositAccountBalance_d2ac831045b8", "benefitStatus": "benefitStatus_1a72bb7ee3c9", "serialNo": "serialNo_199d3a0d8068", "extensions": {}}], "liabilityRiskList": [{"riskSa": "riskSa_d7d092051c1a", "riskCategoryCode": "riskCategoryCode_040f51e63487", "riskSubCategoryCode": "riskSubCategoryCode_69ca7b5e0c7d", "serialNo": "serialNo_37729b27f572", "extensions": {}}], "interestList": [{"interestId": 0, "interestName": "interestName_93bcf567de69", "premiumDetailList": [{"periodNo": 0, "stampDuty": "stampDuty_dcf4d18151e2", "periodStandardPremium": "periodStandardPremium_ebdb8d236b4e", "periodStandardPremiumIncTax": "periodStandardPremiumIncTax_5394c9d338ed", "periodExtraPremium": "periodExtraPremium_013be47d818c", "periodExtraPremiumIncTax": "periodExtraPremiumIncTax_d3fb62159635", "premiumLoadingList": [{"periodPremium": "periodPremium_dd2d15e8ce38", "periodPremiumTax": "periodPremiumTax_779edbd34db3", "loadingType": "HEALTH", "loadingMethod": "FIXED_AMOUNT", "loadingValue": "loadingValue_98993f5d9735", "loadingPeriod": 0, "loadingPeriodType": "PERMANENT", "extraRate": "extraRate_a15bc59324d3", "serialNo": "serialNo_18f463eee9a5", "extensions": {}}], "periodDiscountPremium": "periodDiscountPremium_275946d162ca", "periodDiscountPremiumIncTax": "periodDiscountPremiumIncTax_6759ca4641c9", "premiumDiscountList": [{"premiumDiscountType": 0, "periodDiscountPremium": "periodDiscountPremium_04f9968e46ec", "discountRate": "discountRate_08526d192ed6", "serialNo": "serialNo_92da60508b36", "extensions": {}}], "periodNetPremium": "periodNetPremium_1a3a06d5be28", "periodNetPremiumIncTax": "periodNetPremiumIncTax_956596928613", "periodNoClaimDiscount": "periodNoClaimDiscount_30e7d86fb8a3", "periodNoClaimDiscountIncTax": "periodNoClaimDiscountIncTax_c117342a26e7", "noClaimDiscountRate": "noClaimDiscountRate_6fba9ee159d0", "periodTotalTax": "periodTotalTax_6db863548b18", "taxList": [{"feeType": "ORDER_TEMPORARILY", "amount": "amount_1c173458393e", "bizFeeCategory": "TAX", "accumulated": false, "taxList": [{"taxType": "GST", "rateType": "PERCENTAGE", "tax": "tax_ccda66be0168", "annualTax": "annualTax_a2710ad1ca68", "policyTaxId": 0, "promotionDiscountOnTax": "promotionDiscountOnTax_bee984984f40", "taxRate": "taxRate_28b8399ffc34", "serialNo": "serialNo_b6468e468b25", "extensions": {}}], "serialNo": "serialNo_dae452d777c9", "extensions": {}}], "periodLevy": "periodLevy_ebcfacbbe7b3", "periodServiceFee": "periodServiceFee_4191da48b883", "serviceFeeList": [{"amount": "amount_09a0306bd3cf", "partnerCode": "partnerCode_fd26cba3adaf", "serialNo": "serialNo_0f2ad69040f3", "extensions": {}}], "periodCommission": "periodCommission_08effd92c670", "commissionList": [{"amount": "amount_4803a8c27fb7", "channelCode": "channelCode_ab3a5284baa9", "serialNo": "serialNo_cfb9415ca5c2", "extensions": {}}], "periodCampaignDiscount": "periodCampaignDiscount_2501dcf3c8e3", "actualPremium": "actualPremium_370f321b9bba", "premiumDeltaRate": "premiumDeltaRate_579a70efb866", "premiumId": 0, "premiumLevel": "POLICY", "correlationId": "correlationId_f2aeeedbe9be", "serialNo": "serialNo_a737d4e2e4fc", "extensions": {}}], "unit": 0, "sumInsured": "sumInsured_e1ca8502bd5c", "campaignFreeSumInsured": "campaignFreeSumInsured_ef7c435e82cd", "sumInsuredDownSell": "sumInsuredDownSell_6c1b5646e544", "totalSumInsured": "totalSumInsured_89d5d7ed56cd", "riskSa": "riskSa_08e1314be1cb", "freeAmountOfLiabilitySA": "freeAmountOfLiabilitySA_6d2fffd19e52", "discountSumInsured": "discountSumInsured_5fa4bc76d237", "periodPremium": "periodPremium_d2463c3fa346", "periodStandardPremiumIncTax": "periodStandardPremiumIncTax_a164d7815d1a", "annualPremium": "annualPremium_7569d03cc91b", "isOption": false, "underwritingPassed": false, "status": "TERMINATION", "liabilityCode": "liabilityCode_d156709f1bcc", "includingTax": false, "premiumIncTax": "premiumIncTax_c08425b8fc05", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "statusChangeCause": "POS_NO_REASON_RESCISSION", "statusChangeDate": "2024-03-27 17:29:25", "noClaimDiscountRate": "noClaimDiscountRate_45c9e5639e1c", "liabilityCategoryCode": "liabilityCategoryCode_66fe20fc88a1", "sbPaymentFrequencyType": "sbPaymentFrequencyType_17504237475e", "sbModalFactor": "sbModalFactor_45825503628e", "sbPaymentOption": "sbPaymentOption_fe96939deec1", "deductibleAmount": "deductibleAmount_62719aef6e1f", "serialNo": "serialNo_259f8ba8435d", "extensions": {}}], "calculateNcd": false, "unit": 0, "sumInsured": "sumInsured_dcb10255f36d", "campaignFreeSumInsured": "campaignFreeSumInsured_2840b94f9590", "sumInsuredDownSell": "sumInsuredDownSell_f331873983b9", "totalSumInsured": "totalSumInsured_436422e236b9", "riskSa": "riskSa_f1b78f42afeb", "freeAmountOfLiabilitySA": "freeAmountOfLiabilitySA_d68ce67210c9", "discountSumInsured": "discountSumInsured_c296245dd18b", "periodPremium": "periodPremium_f1063e527ae1", "periodStandardPremiumIncTax": "periodStandardPremiumIncTax_a1d02f9e3780", "annualPremium": "annualPremium_f7a7b03a163a", "isOption": false, "underwritingPassed": false, "status": "TERMINATION", "liabilityCode": "liabilityCode_583cf4fc01b9", "includingTax": false, "premiumIncTax": "premiumIncTax_2c94df72960d", "effectiveDate": "2024-03-27 17:29:25", "expiryDate": "2024-03-27 17:29:25", "statusChangeCause": "POS_NO_REASON_RESCISSION", "statusChangeDate": "2024-03-27 17:29:25", "noClaimDiscountRate": "noClaimDiscountRate_3885249c3629", "liabilityCategoryCode": "liabilityCategoryCode_6558e876c6d1", "sbPaymentFrequencyType": "sbPaymentFrequencyType_71f23be50e4a", "sbModalFactor": "sbModalFactor_2f41e1566927", "sbPaymentOption": "sbPaymentOption_1c9e60aab5ce", "deductibleAmount": "deductibleAmount_cfe57c5c9f2a", "serialNo": "serialNo_9cb3e79ebf75", "extensions": {}}], "premiumPeriod": "premiumPeriod_95e2ee14513f", "premiumType": "PLANNED_PREMIUM", "premiumStartDate": "2024-03-27 17:29:25", "premiumExpiryDate": "2024-03-27 17:29:25", "premiumDeductionType": "CASH_PREMIUM", "gracePeriod": 0, "attachedToProductId": 0, "attachedToProductCode": "attachedToProductCode_017dcd0985a4", "waiverStartDate": "2024-03-27 17:29:25", "waiverEndDate": "2024-03-27 17:29:25", "estimateLapseDate": "2024-03-27 17:29:25", "installmentType": "RENEW", "claimRatio": "claimRatio_ef5a0b48cadc", "calculateMethod": "CALCULATE_PREMIUM", "extraCalculateMethod": "CALCULATE_EXTRA_LOADING_BASED_ON_UW_DECISION", "plannedPremiumCalMethod": 0, "premiumDeltaRate": "premiumDeltaRate_d40311993f79", "adjustedAnnualStandardPremium": "adjustedAnnualStandardPremium_19ceac28cbff", "adjustedAnnualStandardPremiumRate": "adjustedAnnualStandardPremiumRate_ab357fa67416", "minimumInvestmentPeriodType": "WHOLE_COVERAGE_PERIOD", "minimumInvestmentPeriodValue": "minimumInvestmentPeriodValue_d4a0fa8ba243", "minimumInvestmentStartDate": "2024-03-27 17:29:25", "minimumInvestmentEndDate": "2024-03-27 17:29:25", "nlgBenefitBefore": false, "nlgBenefitAfter": false, "benefitOptionCode": false, "withinFreeLookPeriod": false, "shortRateCalcMethod": "BY_SHORT_RATE", "sumInsuredMultiplier": "sumInsuredMultiplier_c45cff180e83", "serialNo": "serialNo_c3503f6ff178", "premiumDetailList": [{"periodNo": "1", "periodStandardPremium": "1", "periodStandardPremiumIncTax": "1", "periodExtraPremium": "1", "periodExtraPremiumIncTax": "1", "premiumLoadingList": "*", "periodDiscountPremium": "1", "periodDiscountPremiumIncTax": "1", "premiumDiscountList": "*", "periodNetPremium": "1", "periodNetPremiumIncTax": "1", "periodNoClaimDiscount": "1", "periodNoClaimDiscountIncTax": "1", "noClaimDiscountRate": "1", "periodTotalTax": "1", "periodLevy": "1", "periodServiceFee": "1", "serviceFeeList": "*", "periodCommission": "1", "periodCampaignDiscount": "1", "actualPremium": "1", "premiumDeltaRate": "1", "periodFinalPremium": "1", "multiCurrencyPremiumList": "*"}], "productInvestmentPlanList": [{"premiumType": "PLANNED_PREMIUM", "amount": "300", "paymentFrequencyType": "MONTH", "paymentFrequencyValue": "1", "investmentPlanStatus": "ACTIVE", "startDate": "2023-08-16T17:43:02.857+08:00", "endDate": "2024-08-16T17:43:02.857+08:00", "productFundPlanList": [{"fundCode": "FUND001", "fundName": "Fund Name", "fundAllocation": "50%", "fundAmount": "100", "distributionMethod": "REINVESTMENT", "startDate": "2023-08-16T17:43:02.857+08:00", "endDate": "2024-08-16T17:43:02.857+08:00"}]}], "salesChannelList": "*", "extensions": {}}], "insuredObjectList": [{"insuredType": "INSURED_AUTO", "policyInsuredObjectId": 1001, "serialNo": "AUTO001", "category1": "Vehicle", "category2": "Private Car", "category3": "Sedan", "insuredNo": "AUTO-2024-001", "shareFlag": false, "objectId": "OBJ-AUTO-001", "remark": "Family car for daily commute", "extraInfo": "Additional driver certification required", "insuredName": "Toyota Camry 2023", "relationComponentList": [], "insuredComponentList": [], "autoDriverList": [], "policyAdditionalEquipmentList": [], "autoModel": "Cam<PERSON>", "plateRegistrationZone": "Shanghai", "vehicleBrand": "Toyota", "motorNcd": null, "carOwner": null, "carryingCapacity": "500", "carryingGoodsType": "Personal Items", "chassisNo": "JTDKN3DU5E3123456", "claimExperienceInfo": null, "colorOfPlateNo": "Blue", "deviceAbs": "Yes", "deviceAeb": "Yes", "deviceAirbag": "Front and Side", "deviceAlarm": "Factory Standard", "deviceGearOrSteeringLock": "Electronic", "deviceGps": "Built-in", "deviceImmobiliser": "Yes", "deviceInstalled": "Complete", "deviceTracking": "GPS Enabled", "distanceConfirmationDate": "2024-01-15T10:30:00Z", "drivingDistance": "15000", "averageDrivingDistance": "12000", "lastYearDrivingDistance": "11500", "engineCapacity": "2.5L", "engineNo": "2AR-FE-123456", "plateNo": "沪A12345", "purchaseDate": "2023-03-15T00:00:00Z", "purchasePrice": "280000", "vehicleColor": "<PERSON>", "vehicleEngine": "NON_EV", "vehicleLeasing": false, "engineType": "Gasoline", "vehicleMake": "Toyota", "vehicleModel": "Camry 2.5L", "vehicleType": "Sedan", "vehicleUsage": "Private", "vinNo": "JTDKN3DU5E3123456", "yearOfManufacturing": 2023, "vehicleGroup": "Medium Sedan", "vehicleSubGroup": "Family Car", "enginePower": "203", "engineVolume": "2487", "vehicleInvoiceValue": "280000", "loan": null, "extraEquipmentValue": 5000, "extraEquipmentName": "Premium Sound System", "existHistoryPolicy": false}, {"insuredType": "TRAVEL", "policyInsuredObjectId": 1002, "serialNo": "TRAVEL001", "category1": "Travel", "category2": "International", "category3": "Leisure", "insuredNo": "TRAVEL-2024-001", "shareFlag": false, "objectId": "OBJ-TRAVEL-001", "remark": "Annual family vacation", "extraInfo": "Pre-existing medical conditions covered", "insuredName": "European Tour 2024", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_DEVICE", "policyInsuredObjectId": 1003, "serialNo": "DEVICE001", "category1": "Electronics", "category2": "Mobile", "category3": "Smartphone", "insuredNo": "DEVICE-2024-001", "shareFlag": false, "objectId": "OBJ-DEVICE-001", "remark": "Primary business phone", "extraInfo": "Extended warranty purchased", "insuredName": "iPhone 15 Pro Max", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PRODUCT_LIABILITY", "policyInsuredObjectId": 1004, "serialNo": "LIABILITY001", "category1": "Liability", "category2": "Product", "category3": "Manufacturing", "insuredNo": "LIABILITY-2024-001", "shareFlag": false, "objectId": "OBJ-LIABILITY-001", "remark": "Product liability coverage for electronics", "extraInfo": "Global coverage included", "insuredName": "Consumer Electronics Product Line", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PUBLIC_LIABILITY", "policyInsuredObjectId": 1005, "serialNo": "PUBLIC001", "category1": "Liability", "category2": "Public", "category3": "General", "insuredNo": "PUBLIC-2024-001", "shareFlag": false, "objectId": "OBJ-PUBLIC-001", "remark": "General public liability coverage", "extraInfo": "Professional indemnity included", "insuredName": "Restaurant Public Liability", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "EMPLOYER_LIABILITY", "policyInsuredObjectId": 1006, "serialNo": "EMPLOYER001", "category1": "Liability", "category2": "Employer", "category3": "Workplace", "insuredNo": "EMPLOYER-2024-001", "shareFlag": false, "objectId": "OBJ-EMPLOYER-001", "remark": "Employer liability for workplace injuries", "extraInfo": "Covers all employees including contractors", "insuredName": "Manufacturing Company Employer Liability", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "BUS", "policyInsuredObjectId": 1007, "serialNo": "BUS001", "category1": "Transport", "category2": "Public", "category3": "Bus", "insuredNo": "BUS-2024-001", "shareFlag": false, "objectId": "OBJ-BUS-001", "remark": "City bus delay coverage", "extraInfo": "Peak hour coverage included", "insuredName": "City Bus Route 101", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "ORDER", "policyInsuredObjectId": 1008, "serialNo": "ORDER001", "category1": "Commerce", "category2": "Order", "category3": "E-commerce", "insuredNo": "ORDER-2024-001", "shareFlag": false, "objectId": "OBJ-ORDER-001", "remark": "E-commerce order protection", "extraInfo": "International shipping covered", "insuredName": "Online Purchase Order #ORD-12345", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_CONTRACT", "policyInsuredObjectId": 1009, "serialNo": "CONTRACT001", "category1": "Legal", "category2": "Contract", "category3": "Commercial", "insuredNo": "CONTRACT-2024-001", "shareFlag": false, "objectId": "OBJ-CONTRACT-001", "remark": "Business contract protection", "extraInfo": "Multi-party contract coverage", "insuredName": "Software Development Contract", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_BUILDING", "policyInsuredObjectId": 1010, "serialNo": "BUILDING001", "category1": "Property", "category2": "Building", "category3": "Commercial", "insuredNo": "BUILDING-2024-001", "shareFlag": false, "objectId": "OBJ-BUILDING-001", "remark": "Office building coverage", "extraInfo": "Earthquake and flood coverage included", "insuredName": "Downtown Office Complex", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "LOCATION_INSURED", "policyInsuredObjectId": 1011, "serialNo": "LOCATION001", "category1": "Property", "category2": "Location", "category3": "Multiple", "insuredNo": "LOCATION-2024-001", "shareFlag": false, "objectId": "OBJ-LOCATION-001", "remark": "Multi-location business coverage", "extraInfo": "All subsidiary locations included", "insuredName": "Retail Chain Locations", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "OTHER_PROPERTIES", "policyInsuredObjectId": 1012, "serialNo": "PROPERTY001", "category1": "Property", "category2": "Other", "category3": "Miscellaneous", "insuredNo": "PROPERTY-2024-001", "shareFlag": false, "objectId": "OBJ-PROPERTY-001", "remark": "Miscellaneous property coverage", "extraInfo": "Includes outdoor equipment and fixtures", "insuredName": "Outdoor Equipment and Fixtures", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PET", "policyInsuredObjectId": 1013, "serialNo": "PET001", "category1": "Animal", "category2": "Pet", "category3": "Domestic", "insuredNo": "PET-2024-001", "shareFlag": false, "objectId": "OBJ-PET-001", "remark": "Family pet health coverage", "extraInfo": "Routine checkups and vaccinations covered", "insuredName": "Golden Retriever - Max", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "INSURED_PERSON", "policyInsuredObjectId": 1014, "serialNo": "PERSON001", "category1": "Person", "category2": "Individual", "category3": "Life", "insuredNo": "PERSON-2024-001", "shareFlag": false, "objectId": "OBJ-PERSON-001", "remark": "Life insurance coverage", "extraInfo": "Critical illness benefit included", "insuredName": "<PERSON>", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PARCEL", "policyInsuredObjectId": 1015, "serialNo": "PARCEL001", "category1": "Cargo", "category2": "<PERSON><PERSON><PERSON>", "category3": "Express", "insuredNo": "PARCEL-2024-001", "shareFlag": false, "objectId": "OBJ-PARCEL-001", "remark": "Express parcel delivery coverage", "extraInfo": "International shipping protection", "insuredName": "Express Delivery Package", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "FLIGHT", "policyInsuredObjectId": 1016, "serialNo": "FLIGHT001", "category1": "Transport", "category2": "Aviation", "category3": "Commercial", "insuredNo": "FLIGHT-2024-001", "shareFlag": false, "objectId": "OBJ-FLIGHT-001", "remark": "Flight delay compensation coverage", "extraInfo": "Weather-related delays covered", "insuredName": "Flight MU5735 Shanghai to Beijing", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "TAKEAWAY", "policyInsuredObjectId": 1017, "serialNo": "TAKEAWAY001", "category1": "Food", "category2": "Delivery", "category3": "Express", "insuredNo": "TAKEAWAY-2024-001", "shareFlag": false, "objectId": "OBJ-TAKEAWAY-001", "remark": "Food delivery delay coverage", "extraInfo": "Hot food guarantee included", "insuredName": "McDonald's Delivery Order", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "LOAN_GUARANTEE", "policyInsuredObjectId": 1018, "serialNo": "LOAN001", "category1": "Financial", "category2": "Loan", "category3": "Guarantee", "insuredNo": "LOAN-2024-001", "shareFlag": false, "objectId": "OBJ-LOAN-001", "remark": "Loan guarantee protection", "extraInfo": "Default risk coverage", "insuredName": "Home Mortgage Loan Guarantee", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "GROSS_PROFIT", "policyInsuredObjectId": 1019, "serialNo": "PROFIT001", "category1": "Business", "category2": "Income", "category3": "Profit", "insuredNo": "PROFIT-2024-001", "shareFlag": false, "objectId": "OBJ-PROFIT-001", "remark": "Business interruption profit coverage", "extraInfo": "Loss of revenue protection", "insuredName": "Restaurant Business Profit", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "LOSS_OF_RENT", "policyInsuredObjectId": 1020, "serialNo": "RENT001", "category1": "Property", "category2": "Rental", "category3": "Income", "insuredNo": "RENT-2024-001", "shareFlag": false, "objectId": "OBJ-RENT-001", "remark": "Loss of rental income coverage", "extraInfo": "Tenant default protection", "insuredName": "Apartment Building Rental Income", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "AUXILIARY_BUILDINGS", "policyInsuredObjectId": 1021, "serialNo": "AUXILIARY001", "category1": "Property", "category2": "Building", "category3": "Auxiliary", "insuredNo": "AUXILIARY-2024-001", "shareFlag": false, "objectId": "OBJ-AUXILIARY-001", "remark": "Auxiliary building structures coverage", "extraInfo": "Garage, shed, and outbuildings included", "insuredName": "Property Auxiliary Structures", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "ENGINEERING_STRUCTURES", "policyInsuredObjectId": 1022, "serialNo": "ENGINEERING001", "category1": "Infrastructure", "category2": "Engineering", "category3": "Civil", "insuredNo": "ENGINEERING-2024-001", "shareFlag": false, "objectId": "OBJ-ENGINEERING-001", "remark": "Civil engineering structures coverage", "extraInfo": "Bridges, tunnels, and infrastructure", "insuredName": "Highway Bridge Construction", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "REFRIGERATED_GOODS", "policyInsuredObjectId": 1023, "serialNo": "REFRIGERATED001", "category1": "Cargo", "category2": "Food", "category3": "Cold Chain", "insuredNo": "REFRIGERATED-2024-001", "shareFlag": false, "objectId": "OBJ-REFRIGERATED-001", "remark": "Temperature-controlled goods coverage", "extraInfo": "Cold chain integrity protection", "insuredName": "Frozen Food Shipment", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "SWIMMING_POOLS", "policyInsuredObjectId": 1024, "serialNo": "POOL001", "category1": "Property", "category2": "Recreation", "category3": "Pool", "insuredNo": "POOL-2024-001", "shareFlag": false, "objectId": "OBJ-POOL-001", "remark": "Swimming pool and spa coverage", "extraInfo": "Equipment and liability included", "insuredName": "Residential Swimming Pool", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "RAW_MATERIALS_PRODUCTS_MERCHANDISES", "policyInsuredObjectId": 1025, "serialNo": "MATERIALS001", "category1": "Inventory", "category2": "Materials", "category3": "Raw", "insuredNo": "MATERIALS-2024-001", "shareFlag": false, "objectId": "OBJ-MATERIALS-001", "remark": "Raw materials and finished goods coverage", "extraInfo": "Work in progress included", "insuredName": "Manufacturing Inventory Stock", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "OUTDOOR_SUPPLIES", "policyInsuredObjectId": 1026, "serialNo": "OUTDOOR001", "category1": "Equipment", "category2": "Outdoor", "category3": "Recreation", "insuredNo": "OUTDOOR-2024-001", "shareFlag": false, "objectId": "OBJ-OUTDOOR-001", "remark": "Outdoor recreational equipment coverage", "extraInfo": "Camping and sports gear included", "insuredName": "Outdoor Recreation Equipment", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PROFESSIONAL_EMPLOYEES_PERSON_INFO", "policyInsuredObjectId": 1027, "serialNo": "PROFESSIONAL001", "category1": "Personnel", "category2": "Professional", "category3": "Employee", "insuredNo": "PROFESSIONAL-2024-001", "shareFlag": false, "objectId": "OBJ-PROFESSIONAL-001", "remark": "Professional employee coverage", "extraInfo": "Key person insurance included", "insuredName": "Senior Software Engineer Coverage", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PUBLIC_PLACES", "policyInsuredObjectId": 1028, "serialNo": "PUBLICPLACE001", "category1": "Location", "category2": "Public", "category3": "Venue", "insuredNo": "PUBLICPLACE-2024-001", "shareFlag": false, "objectId": "OBJ-PUBLICPLACE-001", "remark": "Public venue liability coverage", "extraInfo": "Event and gathering protection", "insuredName": "Community Center Public Venue", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "PRODUCT_GOODS_EQUIPMENTS", "policyInsuredObjectId": 1029, "serialNo": "EQUIPMENT001", "category1": "Equipment", "category2": "Business", "category3": "Industrial", "insuredNo": "EQUIPMENT-2024-001", "shareFlag": false, "objectId": "OBJ-EQUIPMENT-001", "remark": "Business equipment and machinery coverage", "extraInfo": "Breakdown and theft protection", "insuredName": "Manufacturing Equipment Suite", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "OPERATION_LIABILITY", "policyInsuredObjectId": 1030, "serialNo": "OPERATION001", "category1": "Liability", "category2": "Operations", "category3": "Business", "insuredNo": "OPERATION-2024-001", "shareFlag": false, "objectId": "OBJ-OPERATION-001", "remark": "Business operations liability coverage", "extraInfo": "Third-party damage protection", "insuredName": "Construction Operations Liability", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "EMPLOYEES_PERSONAL_EFFECTS", "policyInsuredObjectId": 1031, "serialNo": "EFFECTS001", "category1": "Property", "category2": "Personal", "category3": "Employee", "insuredNo": "EFFECTS-2024-001", "shareFlag": false, "objectId": "OBJ-EFFECTS-001", "remark": "Employee personal belongings coverage", "extraInfo": "Workplace theft and damage protection", "insuredName": "Employee Personal Effects Coverage", "relationComponentList": "*", "insuredComponentList": "*"}, {"insuredType": "ROADS_AND_PATHWAYS", "policyInsuredObjectId": 1032, "serialNo": "ROADS001", "category1": "Infrastructure", "category2": "Transportation", "category3": "Roads", "insuredNo": "ROADS-2024-001", "shareFlag": false, "objectId": "OBJ-ROADS-001", "remark": "Roads and pathways infrastructure coverage", "extraInfo": "Maintenance and liability protection", "insuredName": "Municipal Road Network", "relationComponentList": "*", "insuredComponentList": "*"}], "payerList": [{"individual": {"certiType": "IDCARD", "holderRelation": "SELF", "certiNo": "********", "fullName": "ken peter", "sex": "MALE", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": "*", "phoneList": "*", "addressList": [{"addressId": 1, "addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town1", "address15": "address5", "address16": "address5", "address17": "address5", "address18": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "state city district town"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "*********89", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}, "organization": {"code": "COMP123", "idType": "BUSINESS_LICENSE_NUMBER", "businessLicenseNumber": "BLN123456", "companyName": "xxxx", "companyName2": "xxxx.", "industrialClassification": "xxx", "roleType": "HOLDER", "email": "<EMAIL>", "cifNumber": "CIF123456", "userInputOrganizationType": "IT service", "registeredCapital": ********, "registeredCapitalCurrency": "CNY", "numberOfEmployees": 500, "legalRepresentativeIdType": "IDCARD", "legalRepresentativeIdNo": "110101199001011234", "legalRepresentativeName": "xx", "position": "xxxx", "taxpayerIdentificationNumber": "TIN987654321", "taxpayerName": "xxx", "registeredPhone": "************", "registeredDate": "2010-01-01", "organizationType": "ENTERPRISE", "organizationSize": "big", "country": "CHINA", "industry": "tech", "certificateExpiryDate": "2030-12-31", "organizationAbbreviationName": "cccxx", "registeredPhoneCountryCode": "+86", "registeredAddress": "xxx", "branchCode": "BJ001", "isPreStored": false, "typeOfBusiness": "xxx", "receiveInvoiceMethod": "EMAIL", "invoiceType": "INDIVIDUAL_INVOICE", "partyType": "COMPANY", "issueWithoutPayment": true, "holderRelation": null, "isPromotional": true, "isCrossSell": true, "emailList": "*", "phoneList": "*", "organizationIdNo": "", "organizationName": "", "organizationName2": "", "organizationIdType": "BUSINESS_LICENSE_NUMBER", "extraIdentificationNumber": "", "addressList": [{"addressId": 5001, "address": "xxx", "address11": "xx", "address12": "xxx", "address13": "xx", "address14": "xx", "address15": "address5", "address16": "address5", "address17": "address5", "address18": "address5", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "zipCode": "xxxx"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "*********89", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}, "payMethod": "CREDIT_CARD", "policyPayerId": ***************, "customerId": ***************, "autoDeduction": false, "isPromotional": false, "isCrossSell": false, "issueWithoutPayment": false}], "attachmentList": "*", "userAuthInfo": "*", "premiumDetailList": [{"periodNo": "1", "stampDuty": "1", "taxList": "*", "serviceFeeList": "*", "commissionList": "*"}], "campaignList": "*", "paymentPlan": {"payMethod": "CREDIT_CARD", "samePaymentMethodForAllInstallments": true, "installmentList": [{"paymentNum": 1, "premium": "320.00"}]}, "effectiveWithoutPay": "true", "clauseFileList": "*", "claimStackTemplateList": "*", "claimStackConfigList": "*"}}