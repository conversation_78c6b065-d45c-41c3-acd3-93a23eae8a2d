{"400": [{"sourceCode": "BIZ_MARKET_210088", "targetCode": "openapi.common.coverage.period.type.or.value.cannot.be.empty", "message": "Coverage period type or value cannot be empty."}, {"sourceCode": "BAD_REQUEST_CHANNEL_NOT_MATCH", "targetCode": "openapi.common.channel.not.match", "message": "No level1 only channel predefined by request level1 channel code."}, {"sourceCode": "BIZ_ISS_100215", "targetCode": "openapi.application.already.exists.for.duplicate", "message": "application already exists for duplicate."}, {"sourceCode": "SYS_ISS_310000", "targetCode": "openapi.policy.temporaryIssuance.not.found"}, {"sourceCode": "ZATECH-MARKET.MARKET_PARAMS_ELEMENT_LACK", "targetCode": "openapi.market.application.elements.not.be.empty", "message": "some application elements not be empty."}], "404": [{"sourceCode": "GOODS_NOT_FOUND", "targetCode": "openapi.market.goods.not.found", "message": "Goods {0} is not found."}], "500": [{"sourceCode": "", "targetCode": "openapi.common.error", "message": "Open API public error: {0}."}]}