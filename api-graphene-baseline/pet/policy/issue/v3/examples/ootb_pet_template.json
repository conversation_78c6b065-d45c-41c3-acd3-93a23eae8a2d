{"request": {"applicationDate": "2025-10-28T09:55:11.133+08:00", "coveragePeriod": 366, "coveragePeriodType": "DAY", "effectiveDate": "2025-10-29T07:55:11.133+08:00", "goodsCode": "Pet_Template", "insuredList": [{"individual": {"birthday": "1952-01-06", "certiNo": "insured_petohLatGLdKgq", "certiType": "PASSPORT", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "fullName": "ken peter", "gender": "MALE", "holderRelation": "SELF", "nationality": "KUWAIT", "phoneList": [{"countryCode": "+31", "isDefault": true, "phoneNo": "********", "phoneType": "PHONE"}]}, "serialNo": "pet_insure_oseriMNgeCGYOMr"}], "insuredObjectList": [{"extensions": {}, "insuredComponentList": [{"age": "2", "bodyImg": "28cb52b8-2977-424e-8991-9e01e429f560", "componentType": "PET", "frontImg": "28cb52b8-2977-424e-8991-9e01e429f560", "gender": "MALE", "medicalCondition": "Good", "petBirthday": "2023-01-01", "petBreed": "<PERSON><PERSON>", "petId": "1", "petImg": "28cb52b8-2977-424e-8991-9e01e429f560", "petName": "Dog1", "petType": "dog"}], "insuredType": "PET", "relationComponentList": ["PET"], "serialNo": "pet_object_oseriDOXpbjqtRK"}], "insuredRelationList": [{"liabilitySerialNos": ["pet_lia1_miseripmjQqymwbd", "pet_lia2_miseriRaHqvqChOA"], "productSerialNo": "pet_product_miseriacVprOiChK", "relationType": "PRODUCT_INSURED", "serialNo": "pet_insure_oseriMNgeCGYOMr"}, {"liabilitySerialNos": ["pet_lia1_miseripmjQqymwbd", "pet_lia2_miseriRaHqvqChOA"], "productSerialNo": "pet_product_miseriacVprOiChK", "relationType": "PRODUCT_OBJECT", "serialNo": "pet_object_oseriDOXpbjqtRK"}, {"liabilitySerialNos": ["pet_lia1_riserieMPfNdUtdC", "pet_lia2_riseriwkwfxTAVGB", "pet_lia3_riseritUNWbnGtHb"], "productSerialNo": "pet_product_riseriMtSAHFMlXT", "relationType": "PRODUCT_INSURED", "serialNo": "pet_insure_oseriMNgeCGYOMr"}, {"liabilitySerialNos": ["pet_lia1_riserieMPfNdUtdC", "pet_lia2_riseriwkwfxTAVGB", "pet_lia3_riseritUNWbnGtHb"], "productSerialNo": "pet_product_riseriMtSAHFMlXT", "relationType": "PRODUCT_OBJECT", "serialNo": "pet_object_oseriDOXpbjqtRK"}], "paidPremium": 67.07, "payerList": [{"individual": {"accountList": [{"accountType": "DEBIT_CARD", "bankBranchCode": "123", "bankBranchName": "Main Branch", "bankCode": "001", "bankName": "Bank of America", "cardHolderName": "ken peter2", "cardNumber": "DWLrSWkpUrPB"}], "addressList": [{"address": "beijin", "address11": "北京市", "address12": "朝阳区", "address13": "上海路", "addressCategory": "DEFAULT", "addressType": "ADDRESS", "extensions": {"E_posaddress1": "address test", "E_posaddress2": "pos address1", "E_posaddress3": "2021-07-26", "E_posaddress4": "343"}, "jisCode": "3551614", "town": "358号"}], "birthday": "2001-02-20", "certiNo": "payer_petorRbdeQTHjA", "certiType": "PASSPORT", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "firstName": "ken", "fullName": "ken peter", "gender": "MALE", "holderRelation": "SPOUSE", "lastName": "peter", "nationality": "KUWAIT", "phoneList": [{"countryCode": "+31", "isDefault": true, "phoneNo": "********", "phoneType": "PHONE"}]}, "payMethod": "BANK_SLIP", "payerType": "FIRST"}, {"individual": {"accountList": [{"accountType": "DEBIT_CARD", "bankBranchCode": "123", "bankBranchName": "Main Branch", "bankCode": "001", "bankName": "Bank of America", "cardHolderName": "ken peter2", "cardNumber": "DWLrSWkpUrPB"}], "addressList": [{"address": "beijin", "address11": "北京市", "address12": "朝阳区", "address13": "上海路", "addressCategory": "DEFAULT", "addressType": "ADDRESS", "extensions": {"E_posaddress1": "address test", "E_posaddress2": "pos address1", "E_posaddress3": "2021-07-26", "E_posaddress4": "343"}, "jisCode": "3551614", "town": "358号"}], "birthday": "2001-02-20", "certiNo": "payer_petorRbdeQTHjA", "certiType": "PASSPORT", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "firstName": "ken", "fullName": "ken peter", "gender": "MALE", "holderRelation": "SPOUSE", "lastName": "peter", "nationality": "KUWAIT", "phoneList": [{"countryCode": "+31", "isDefault": true, "phoneNo": "********", "phoneType": "PHONE"}]}, "payMethod": "BANK_SLIP", "payerType": "RENEWAL"}], "paymentPeriod": 1, "paymentPeriodType": "YEARFULL", "paymentPlan": {"installmentList": [{"dueDate": "2023-06-25T23:59:59.000+08:00", "payMethod": "CASH", "paymentDate": "2001-05-20T10:00:00.000+08:00", "paymentNum": 1, "paymentStatus": "NORMAL", "premium": "5000.00"}], "payMethod": "CASH", "samePaymentMethodForAllInstallments": true}, "planCode": "Pet_01_Template", "policyHolder": {"individual": {"birthday": "1952-01-06", "certiNo": "insured_petohLatGLdKgq", "certiType": "PASSPORT", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "fullName": "ken peter", "gender": "MALE", "nationality": "KUWAIT", "phoneList": [{"countryCode": "+31", "isDefault": true, "phoneNo": "********", "phoneType": "PHONE"}]}}, "premiumFrequencyType": "YEAR", "productList": [{"productCode": "Pet_Template", "productLiabilityList": [{"liabilityCode": "1021", "liabilityId": 696, "serialNo": "pet_lia1_miseripmjQqymwbd", "sumInsured": 800}, {"liabilityCode": "1359", "liabilityId": 704, "serialNo": "pet_lia2_miseriRaHqvqChOA", "sumInsured": 1000}], "serialNo": "pet_product_miseriacVprOiChK", "sumInsured": 1800}, {"productCode": "<PERSON>_<PERSON>_Template", "productLiabilityList": [{"liabilityCode": "1605", "liabilityId": 268, "serialNo": "pet_lia1_riserieMPfNdUtdC", "sumInsured": 1000}, {"liabilityCode": "2141", "liabilityId": 785, "serialNo": "pet_lia2_riseriwkwfxTAVGB", "sumInsured": 25}, {"liabilityCode": "2142", "liabilityId": 786, "serialNo": "pet_lia3_riseritUNWbnGtHb", "sumInsured": 25}], "serialNo": "pet_product_riseriMtSAHFMlXT", "sumInsured": 1050}], "thirdPartyTransactionNo": "pet_combine1_trs44891353350", "tradeNo": "pet_combine1_trs44891353350", "zoneId": "Asia/Shanghai"}, "response": {"200": {"policies": [{"proposalNo": "20250704697858", "policyNo": "B014870008", "thirdPartyTransactionNo": "T56302519407874851", "proposalStatus": "EFFECTIVE"}]}}}