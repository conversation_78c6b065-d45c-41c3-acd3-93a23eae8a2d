{"request": {"insuredList": [{"individual": {"lastName": "WODE", "birthday": "2004-11-06", "certiNo": "611001197612294277", "certiType": "IDCARD", "holderRelation": "SELF", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "fullName": "ken peter", "gender": "MALE", "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}]}}], "coveragePeriod": 1, "tradeNo": "T@natural", "premiumFrequencyType": "MONTH", "thirdPartyTransactionNo": "T@natural", "paidPremium": 2.5, "planCode": "Pet_01_Template", "coveragePeriodType": "DAY", "insuredRelationList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0433c", "relationType": "PRODUCT_OBJECT", "productSerialNo": "d6ca9655-403d-4a9c-9140-3485a63c93a8", "liabilitySerialNoList": ["3365de1f-72cf-497c-b30a-bb7f85e0431b", "3365de1f-72cf-497c-b30a-bb7f85e0432b", "3365de1f-72cf-497c-b30a-bb7f85e0433b", "3365de1f-72cf-497c-b30a-bb7f85e0434b", "3365de1f-72cf-497c-b30a-bb7f85e0435b", "3365de1f-72cf-497c-b30a-bb7f85e0436b"]}], "insuredObjectList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0433c", "insuredType": "PET", "extensions": {}, "insuredComponentList": [{"componentType": "PET", "bodyImg": "28cb52b8-2977-424e-8991-9e01e429f560", "chipId": "1", "frontImg": "28cb52b8-2977-424e-8991-9e01e429f560", "gender": "MALE", "petBreed": "<PERSON><PERSON><PERSON><PERSON>", "petBirthday": "2022-01-01", "petId": "1123112", "petImg": "28cb52b8-2977-424e-8991-9e01e429f560", "petName": "Dog1", "petType": "dog", "age": "10", "extensions": {}, "medicalCondition": "1"}]}], "zoneId": "Europe/Zagreb", "goodsCode": "Pet_Template", "paymentPeriod": 1, "paymentPeriodType": "YEARFULL", "effectiveDate": "2025-10-20T18:37:34.126+08:00", "productList": [{"serialNo": "d6ca9655-403d-4a9c-9140-3485a63c93a8", "productCode": "Pet_Template", "sumInsured": 25000, "productLiabilityList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431b", "liabilityCode": "1007", "liabilityId": 114, "sumInsured": 800}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0432b", "liabilityCode": "1605", "liabilityId": 268, "sumInsured": 25000}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0433b", "liabilityCode": "1021", "liabilityId": 696, "sumInsured": 8500}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0434b", "liabilityCode": "1359", "liabilityId": 704, "deductibleAmount": "500", "sumInsured": 6000}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0435b", "liabilityCode": "2141", "liabilityId": 785, "sumInsured": 50}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0436b", "liabilityCode": "2142", "liabilityId": 786, "sumInsured": 50}]}], "applicationDate": "2025-10-20T18:37:34.126+08:00", "policyHolder": {"individual": {"birthday": "2004-11-06", "certiNo": "611001197612294277", "certiType": "IDCARD", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "fullName": "ken peter", "lastName": "SSAA", "gender": "MALE", "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}]}}}, "response": {"200": {"value": {"taxList": [{"taxType": "IPT", "tax": "77.42181818182"}], "standardGrossPremium": "774.21818181818", "totalPremium": "851.64", "standardNetPremium": "774.21818181818"}}}}