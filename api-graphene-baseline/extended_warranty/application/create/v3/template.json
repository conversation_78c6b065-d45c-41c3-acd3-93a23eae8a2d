{"request": {"issueWithoutPayment": true, "policyDeliveryMethod": "E_POLICY", "renewalMethod": "NONE_RENEWAL", "salesChannelList": [{"channelRole": "NON_OFFICE", "agentCode": "1", "branchCode": "1", "agentPathCode": "1", "salesAgreementCode": "salesChannelName"}], "issuanceRelationList": [{"type": "RENEWAL_POLICY", "subType": "HOST_RELATION_NO", "relationNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a"}], "insureCreator": "1", "systemSource": "systemSource", "enableFullSync": false, "remark": "remark", "*goodsCode": "EDI_v1", "temporary": false, "currency": {"premiumCurrency": "SGD", "saCurrency": "SGD"}, "*planCode": "All_risk", "questionnaire": {"applyNo": "123456789", "purpose": "GENERAL_DECLARATION"}, "*thirdPartyTransactionNo": "issue@natural", "premium": 6.739, "coveragePeriodType": "YEARFULL", "coveragePeriod": 1, "paymentPeriodType": "YEARFULL", "paymentPeriod": 1, "premiumFrequencyType": "YEAR", "orderNo": "10000000000000000000000000000000000000000000000000000000000", "applicationDate": "2024-04-22T17:21:58.299+08:00", "effectiveDate": "2024-05-01T17:21:58.299+08:00", "expiryDate": "2024-05-01T17:21:58.299+08:00", "zoneId": "Europe/Zagreb", "policyHolder": {"individual": {"certiType": "IDCARD", "certiNo": "********", "fullName": "A", "birthday": "1990-03-03", "sex": "MALE", "residenceCountry": "RS", "emailList": [{"email": "<EMAIL>"}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE"}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN123456789", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "attachmentList": [{"refId": 1, "refType": "POLICY", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}]}}, "insuredList": [{"individual": {"certiType": "IDCARD", "holderRelation": "SELF", "certiNo": "********", "fullName": "ken peter", "sex": "MALE", "birthday": "1989-06-06", "residenceCountry": "RS", "emailList": [{"email": "<EMAIL>"}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE"}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN123456789", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}], "attachmentList": [{"refId": 1, "refType": "POLICY", "attachmentName": "Policy Document", "attachmentType": "ID_CARD", "attachmentTypeCode": "POLICY_DOC", "attachmentUniqueCode": "2551ccc4-ac03-44ca-8406-d11a89737ba2"}]}}], "payerList": [{"individual": {"certiType": "IDCARD", "holderRelation": "SELF", "certiNo": "********", "fullName": "ken peter", "sex": "MALE", "birthday": "1989-06-06", "residenceCountry": "RS", "emailList": [{"email": "<EMAIL>"}], "phoneList": [{"phoneNo": "********", "phoneType": "PHONE"}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house", "address15": "address5", "address16": "address16", "address21": "address21_03b23535d7a5", "address22": "address22_c1f412d39031", "address23": "address23_bdd683824881", "address24": "address24_772cfe42f5a9", "address25": "address25_258aaa651ab9", "gisCode": "gisCode_eb9f6dd4f869", "organizationAddressType": "REGISTER", "extensions": {}, "address": "province city street house"}], "accountList": [{"expiryDate": "2025-12-31", "bankCode": "ICBC", "bankBranchCode": "ICBC001", "bankBranchName": "we", "accountType": "CREDIT_CARD", "accountSubType": "MASTER_CARD", "cardHolderName": "wer", "cardNumber": "6222021234567890123", "mobileNo": "***********", "safeNo": "123", "bankName": "wer", "bankCity": "ewr", "payMethod": "CREDIT_CARD", "thirdPartyPayVoucher": "VOUCHER12345", "iban": "CN123456789", "swiftCode": "ICBKCNBJ", "bankAddress": "123", "bankBranchAddress": "24", "extensions": {"VIP_LEVEL": "3", "CREDIT_LIMIT": "50000"}}]}, "payMethod": "CASH", "payerType": "FIRST", "autoDeduction": true}], "productList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0432b", "*productCode": "EDI_v1", "sumInsured": 112.32, "productLiabilityList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0433b", "*liabilityCode": "1002", "sumInsured": 112.32, "loadingList": "*", "discountList": "*"}]}], "campaignList": [{"campaignCode": "campaignCode", "periodNo": "1", "promotionCode": "promotionCode", "campaignPayerList": "*"}], "attachmentList": [{"refType": "DEVICE", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}], "insuredRelationList": [{"serialNo": "SN1234567890", "relationType": "PRODUCT_OBJECT", "productSerialNo": "2b58aa27-0492-42a9-ae9b-dc6d18816b3a", "liabilitySerialNoList": ["1", "1"]}], "insuredObjectList": [{"extensions": {"E_IsDiplomat": "NO"}, "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "serialNo": "SN1234567890", "insuredType": "INSURED_DEVICE", "insuredComponentList": [{"extensions": {"E_IsDiplomat": "NO"}, "componentType": "DEVICE", "deviceNo": "3294039243234", "deviceName": "iPhone 15", "deviceBrand": "Apple", "deviceModel": "Apple15", "deviceCategory": "PHONE", "deviceCategoryName": "Smartphone", "deviceMarketValue": 5000, "deviceBuyerReviewScore": "5", "deviceSellerReviewScore": "5", "warrantyPeriod": "24 months", "warrantyPeriodCustomer": "<PERSON>", "devicePurchaseTime": "2023-08-16T17:43:02.857+08:00", "applicablePeriod": "36 months", "deviceUser": "<PERSON>", "deviceManufacturer": "Apple Inc.", "deviceDesc": "Latest Apple iPhone with advanced features.", "deviceImgUrl": "http://example.com/device.jpg", "premiumCode": "PC123456", "deviceStatus": "Brand", "deviceSellerId": "11", "deviceId": "1", "attachmentList": [{"refType": "ORDER", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}]}]}], "extensions": {"E_IsDiplomat": "NO"}, "userAuthInfo": "*", "claimStackTemplateList": "*", "claimStackConfigList": "*"}, "response": {"policies": [{"thirdPartyTransactionNo": "t_329439232434322", "proposalNo": "ZA09202400000015", "orderNo": "B015730005", "proposalStatus": "EFFECTIVE", "ruleValidateResult": {"decision": "DECLINE", "details": [{"ruleCode": "MSIG_VN_Travel_manualUW", "decisionType": "DECLINE", "msgValue": "pls error", "msgCode": "pls error"}]}}]}}