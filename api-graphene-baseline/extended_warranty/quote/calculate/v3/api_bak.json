{"openapi": "3.0.1", "paths": {"/api/v3/extended_warranty/quotes/calculate": {"post": {"requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["goodsCode", "planCode", "coveragePeriodType", "coveragePeriod", "paymentPeriodType", "applicationDate", "productList", "insuredObjectList"], "properties": {"goodsCode": {"type": "string", "maxLength": 128, "description": "The code of goods that the customer wants to get the quote"}, "currency": {"type": "object", "properties": {"premiumCurrency": {"type": "string", "enum": ["USD", "EUR", "JPY", "HKD", "GBP", "TWD", "THB", "SGD", "MYR", "KRW", "AED", "ARS", "AUD", "BGN", "BRL", "BSD", "CAD", "CHF", "CLP", "CNY", "COP", "CZK", "DKK", "DOP", "EGP", "FJD", "GTQ", "HRK", "HUF", "ILS", "INR", "ISK", "KZT", "MXN", "NOK", "NZD", "PAB", "PEN", "PHP", "PKR", "PLN", "PYG", "RON", "RUB", "SAR", "SEK", "TRY", "UAH", "UYU", "VND", "ZAR", "GRL", "MMK", "BAM", "RSD", "ALL", "MKD", "IDR", "KES", "OTHER", "AFN", "DZD", "AOA", "XCD", "AMD", "AWG", "SHP", "AZN", "BHD", "BDT", "BBD", "BYN", "BZD", "XOF", "BMD", "BTN", "BOB", "BWP", "BND", "BIF", "CVE", "KHR", "XAF", "KYD", "KMF", "CDF", "CRC", "CUP", "ANG", "DJF", "ERN", "SZL", "ETB", "FKP", "XPF", "GMD", "GEL", "GHS", "GIP", "GGP", "GNF", "GYD", "HTG", "HNL", "IRR", "IQD", "JMD", "JEP", "JOD", "KWD", "KGS", "LAK", "LBP", "LSL", "LRD", "LYD", "MOP", "MGA", "MWK", "MVR", "MRU", "MUR", "MDL", "MNT", "MAD", "MZN", "NAD", "NPR", "NIO", "NGN", "KPW", "OMR", "PGK", "QAR", "RWF", "WST", "STN", "SCR", "SBD", "SOS", "SSP", "LKR", "SDG", "SRD", "SYP", "TJS", "TZS", "TTD", "TND", "TMT", "UGX", "UZS", "VUV", "VES", "YER", "ZMW"], "description": "The currency of the premium. This is a three character ISO 4217 currency code. For example, USD for US Dollars.", "example": "USD"}, "saCurrency": {"type": "string", "enum": ["USD", "EUR", "JPY", "HKD", "GBP", "TWD", "THB", "SGD", "MYR", "KRW", "AED", "ARS", "AUD", "BGN", "BRL", "BSD", "CAD", "CHF", "CLP", "CNY", "COP", "CZK", "DKK", "DOP", "EGP", "FJD", "GTQ", "HRK", "HUF", "ILS", "INR", "ISK", "KZT", "MXN", "NOK", "NZD", "PAB", "PEN", "PHP", "PKR", "PLN", "PYG", "RON", "RUB", "SAR", "SEK", "TRY", "UAH", "UYU", "VND", "ZAR", "GRL", "MMK", "BAM", "RSD", "ALL", "MKD", "IDR", "KES", "OTHER", "AFN", "DZD", "AOA", "XCD", "AMD", "AWG", "SHP", "AZN", "BHD", "BDT", "BBD", "BYN", "BZD", "XOF", "BMD", "BTN", "BOB", "BWP", "BND", "BIF", "CVE", "KHR", "XAF", "KYD", "KMF", "CDF", "CRC", "CUP", "ANG", "DJF", "ERN", "SZL", "ETB", "FKP", "XPF", "GMD", "GEL", "GHS", "GIP", "GGP", "GNF", "GYD", "HTG", "HNL", "IRR", "IQD", "JMD", "JEP", "JOD", "KWD", "KGS", "LAK", "LBP", "LSL", "LRD", "LYD", "MOP", "MGA", "MWK", "MVR", "MRU", "MUR", "MDL", "MNT", "MAD", "MZN", "NAD", "NPR", "NIO", "NGN", "KPW", "OMR", "PGK", "QAR", "RWF", "WST", "STN", "SCR", "SBD", "SOS", "SSP", "LKR", "SDG", "SRD", "SYP", "TJS", "TZS", "TTD", "TND", "TMT", "UGX", "UZS", "VUV", "VES", "YER", "ZMW"], "description": "The currency of the sa. This is a three character ISO 4217 currency code. For example, USD for US Dollars.", "example": "USD"}}}, "planCode": {"type": "string", "description": "The code of goods plan for sale. ", "maxLength": 128}, "coveragePeriodType": {"type": "string", "enum": ["DAY", "WEEK", "MONTH", "YEARFULL", "SUIFULL", "HOUR", "MINUTES"], "example": "YEARFULL", "description": "The type of coverage period.  \"Suifull\" indicates that the customer must maintain the policy within a specified age scope. Refer to the API reference [Detail](/openx/docs/solutions/extended_warranty/guide/intro/?name=%2Fdir%2Fextended_warranty%2Fquote%2Fcalculate%40api%2Fdetail_en_US.md#request-parameters)"}, "coveragePeriod": {"type": "integer", "minimum": 0, "example": 1, "description": "Protection period value."}, "paymentPeriodType": {"type": "string", "enum": ["YEARFULL", "SUIFULL", "SINGLE", "MONTHFULL", "HOUR", "MINUTES"], "example": "SINGLE", "description": "The type of payment period. \"Suifull\" indicates that the customer must maintain the policy within a specified age scope. Refer to the API reference."}, "paymentPeriod": {"type": "integer", "minimum": 0, "description": "`conditional required`\r-  You need to set this parameter working together with the parameter \"paymentPeriodType\" if the parameter \"paymentPeriodType\" is NOT \"Single\"."}, "premiumFrequencyType": {"type": "string", "example": "SINGLE", "enum": ["SINGLE", "YEAR", "HALFYEAR", "QUARTER", "MONTH", "DAY"], "description": "`conditional required`\r- You need to set this parameter working together with the parameter \"paymentPeriodType\" if the parameter \"paymentPeriodType\" is NOT \"Single\"."}, "applicationDate": {"type": "string", "format": "date-time", "description": "application Date refer to the API reference. [Detail](/openx/docs/solutions/extended_warranty/guide/intro/?name=%2Fdir%2Fextended_warranty%2Fquote%2Fcalculate%40api%2Fdetail_en_US.md#applicationdate)"}, "effectiveDate": {"type": "string", "format": "date-time", "description": "`conditional required`\r- The date when the quotation is effective. Refer to the API reference. [Detail](/openx/docs/solutions/extended_warranty/guide/intro/?name=%2Fdir%2Fextended_warranty%2Fquote%2Fcalculate%40api%2Fdetail_en_US.md#effectivedate--expirydate)"}, "zoneId": {"type": "string", "description": "`conditional required`\r- By default, the system uses the configured time zone of tenant.", "example": "Europe/Zagreb", "maxLength": 64}, "productList": {"type": "array", "description": "List of products to be insured", "items": {"type": "object", "required": ["productCode"], "properties": {"productCode": {"type": "string", "description": "Goods product code for sale.", "maxLength": 128}, "sumInsured": {"type": "number", "minimum": 0, "description": "Sum insured refers to the maximum coverage amount specified in an insurance policy."}, "productLiabilityList": {"type": "array", "items": {"type": "object", "required": ["liabilityCode"], "properties": {"liabilityCode": {"type": "string", "description": "Liability code for sale.", "maxLength": 128}, "sumInsured": {"type": "number", "minimum": 0, "description": "Sum insured refers to the maximum coverage amount specified in an insurance policy."}}}, "description": "product optional liability List"}}}}, "insuredObjectList": {"type": "array", "description": "Insured object list", "items": {"type": "object", "required": ["deviceNo", "deviceBrand", "deviceModel", "deviceCategory", "deviceMarketValue", "insuredType"], "properties": {"insuredType": {"type": "string", "enum": ["INSURED_DEVICE", "TRAVEL", "PET", "HOUSE", "OTHER_PROPERTIES"], "description": "Extended Warranty coverage insuredType default value: DEVICE"}, "deviceNo": {"type": "string", "example": "3294039243234", "description": "Unique identification of the insurance device.", "maxLength": 128}, "deviceName": {"type": "string", "example": "3294039243234", "description": "Unique identification of the insurance device name.", "maxLength": 256}, "deviceBrand": {"type": "string", "example": "Apple", "description": "Brand or manufacturer of a device.", "maxLength": 128}, "deviceModel": {"type": "string", "example": "Apple15", "description": "Model of a device.", "maxLength": 128}, "deviceCategory": {"type": "string", "example": "PHONE", "maxLength": 128, "description": "Device Category.Please refer to the API reference. [Detail](/openx/docs/solutions/extended_warranty/guide/intro/?name=%2Fdir%2Fextended_warranty%2Fpolicy%2Fissue%40api%2Fdetail_en_US.md#object-information)"}, "deviceCategoryName": {"type": "string", "example": "PHONE", "maxLength": 256, "description": "Device Category Name"}, "deviceMarketValue": {"type": "number", "example": 5000, "minimum": 0, "description": "Device Market Value."}, "deviceBuyerReviewScore": {"type": "string", "example": "1", "maxLength": 128, "description": "Device Buyer Review Score."}, "deviceSellerReviewScore": {"type": "string", "example": "1", "description": "<PERSON><PERSON> Review Score.", "maxLength": 128}, "warrantyPeriod": {"type": "string", "example": "1", "description": "Device Market protect period.", "maxLength": 10}, "warrantyPeriodCustomer": {"type": "string", "example": "morgan", "description": "Device warranty period customer.", "maxLength": 256}, "devicePurchaseTime": {"type": "string", "example": "2023-08-16T17:43:02.857+08:00", "description": "Device purchase time.", "format": "date-time"}, "applicablePeriod": {"type": "string", "example": "7", "description": "Device applicable Period.", "maxLength": 64}, "deviceUser": {"type": "string", "example": "morgan", "description": "Device user.", "maxLength": 256}, "deviceManufacturer": {"type": "string", "example": "canon", "description": "Device manufacturer.", "maxLength": 256}, "deviceDesc": {"type": "string", "example": "something", "description": "Device description.", "maxLength": 512}, "deviceImgUrl": {"type": "string", "example": "xxx", "description": "Device image URL.", "maxLength": 512}, "premiumCode": {"type": "string", "example": "xxx", "description": "Device premium code.", "maxLength": 64}, "deviceStatus": {"type": "string", "example": "new", "description": "Device status.", "maxLength": 6}, "serialNo": {"type": "string", "example": "11103321", "description": "Device serial number", "maxLength": 128}, "extensions": {"type": "object", "example": " \"E_IsDiplomat\": \"NO\"", "description": "Dynamic field - extensions filed,Consists of key and value ", "maxLength": 1024}, "insuredNo": {"type": "string", "example": "12345", "description": "The number of insured object", "maxLength": 64}, "shareFlag": {"type": "boolean", "example": true, "description": "Share flag"}, "remark": {"type": "string", "example": "This is a remark", "description": "Remark", "maxLength": 512}, "extraInfo": {"type": "string", "example": "Some extra information", "description": "Extra information", "maxLength": 1024}}}}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"standardGrossPremium": {"type": "string", "description": "Standard gross premium."}, "totalPremium": {"type": "string", "description": "The total premium should be paid including total tax."}, "loading": {"type": "string", "description": "Refers to the additional premium or rate increase applied to an insurance policy."}, "discount": {"type": "string", "description": "Premium discount refers to a reduction in the cost of an insurance premium.  It is a way for insurance companies to incentivize policyholders and offer savings on their insurance policies."}, "standardNetPremium": {"type": "string", "description": "Standard net premium = stand gross premium - discount + loading."}, "campaignDiscount": {"type": "string", "description": "Campaign discount amount."}, "taxList": {"type": "array", "description": "Refers to additional fees imposed by the government on insurance products or transactions.", "items": {"type": "object", "properties": {"taxType": {"type": "string", "description": "Tax type, eg:vat."}, "tax": {"type": "string", "description": "Tax amount."}}}}, "installmentList": {"type": "array", "description": "Installment premium list. Only installment products require to provide this parameter.", "items": {"type": "object", "properties": {"installmentNo": {"type": "integer", "description": "Installment number"}, "standardNetPremiumIncCampaign": {"type": "string", "description": "Standard net premium included campaign of current installment."}, "taxList": {"type": "array", "description": "Refers to additional fees imposed by the government on insurance products or transactions.", "items": {"type": "object", "properties": {"taxType": {"type": "string", "description": "Tax type, eg:vat."}, "tax": {"type": "string", "description": "Tax amount."}}}}}}}, "productList": {"type": "array", "description": "List of products to be insured", "items": {"type": "object", "properties": {"productCode": {"type": "string", "description": "Product code."}, "standardGrossPremium": {"type": "string", "description": "Standard gross premium of this product."}, "loading": {"type": "string", "description": "Loading of this product."}, "discount": {"type": "string", "description": "Discount of this product."}, "standardNetPremium": {"type": "string", "description": "Standard net premium of this product."}, "taxList": {"type": "array", "description": "Refers to additional fees imposed by the government on insurance products or transactions.", "items": {"type": "object", "properties": {"taxType": {"type": "string", "description": "Tax type, eg:vat."}, "tax": {"type": "string", "description": "Tax amount."}}}}}}}, "totalCommission": {"type": "string", "description": "Total commission, commission are used as compensation for showing an agent or broker"}, "commissionList": {"type": "array", "description": "Commission item", "items": {"type": "object", "properties": {"commission": {"type": "string", "description": "Commission which generate in this partner code"}, "partnerCode": {"type": "string", "description": "Partner code"}}}}, "totalServiceFee": {"type": "string", "description": "Total service fee, service fees are used as insurance services provided by insurance companies."}, "serviceFeeList": {"type": "array", "description": "Service fee item", "items": {"type": "object", "properties": {"serviceFee": {"type": "string", "description": "Service fee which generate in this partner code"}, "partnerCode": {"type": "string", "description": "Partner code"}}}}}}}}}}}}}}