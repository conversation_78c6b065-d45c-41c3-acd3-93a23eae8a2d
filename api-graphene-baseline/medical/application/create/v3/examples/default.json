{"request": {"coveragePeriod": 3, "coveragePeriodType": "MONTH", "thirdPartyTransactionNo": "Third{{$randomPhoneNumberExt}}", "tradeNo": "T{{$randomPhoneNumberExt}}", "paidPremium": 616000, "applicationDate": "2025-05-27T22:55:47.333+00:00", "goodsCode": "MSIGVN_HC_Inpatient", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "planCode": "MSIGVNHC_PlanA", "effectiveDate": "2025-04-30T00:00:47.333+08:00", "expiryDate": "2025-05-30T22:55:47.333+08:00", "premiumFrequencyType": "SINGLE", "policyHolder": {"individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1905-06-06", "gender": "MALE", "certiNo": "43543546-89984894", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 10, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "payerType": "FIRST", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house"}]}}, "isLegalBeneficiary": true, "insuredList": [{"individual": {"certiType": "IDCARD", "certiNo": "1114462242", "firstName": "ken my2", "lastName": "peter", "fullName": "ken peter", "gender": "MALE", "birthday": "1905-02-09", "residenceCountry": "TR", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10012200001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A000201", "address11": "state", "address12": "city", "address13": "district", "address14": "town"}], "isPromotional": true, "countryCode": "US", "residentialStatus": "CITIZEN"}}], "payerList": [{"individual": {"holderRelation": "SPOUSE", "certiType": "IDCARD", "certiNo": "43543546", "firstName": "ken", "lastName": "peter", "fullName": "ken peter", "birthPlace": "Shanghai", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town", "address": "state city district town"}], "middleName": "middle", "formattedLastName": "peter", "formattedMiddleName": "middle", "formattedFirstName": "ken", "formattedFullName": "ken middle peter", "fullName2": "ken peter", "lastName2": "peter2", "middleName2": "middle2", "firstName2": "ken2", "formattedLastName2": "peter2", "formattedMiddleName2": "middle2", "formattedFirstName2": "ken2", "formattedFullName2": "ken2 middle2 peter2", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "SE", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2020-01-01", "height": "180", "weight": "75", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "1001", "channelCode": "CC", "channelUserCodePartyId": "CUP1001", "isCustomer": true, "countryCode": "US", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2025-01-01", "position": "Developer", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "TN123456", "blackStatus": false, "workingPlace": "Company", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "Office", "payerType": "FIRST", "extraInfo": "Some extra info"}, "payerType": "FIRST", "payMethod": "CASH"}]}, "response": {"200": {"policies": [{"proposalNo": "778B1000000725", "proposalStatus": "WAITING_FOR_ISSUANCE", "policyNo": "BBB2025010600027015", "thirdPartyTransactionNo": "P12-496-523-1875"}]}}}