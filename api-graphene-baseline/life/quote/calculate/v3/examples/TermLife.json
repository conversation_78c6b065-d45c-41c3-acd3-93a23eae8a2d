{"request": {"goodsCode": "ZAcare_TL", "planCode": "ZAcare_TL_01", "applicationType": "PROPOSAL", "issueDate": "2024-11-06T08:37:33.144+08:00", "applicationDate": "2024-11-08T13:59:10.181+08:00", "zoneId": "Asia/Bangkok", "coveragePeriodType": "YEARFULL", "coveragePeriod": 15, "paymentPeriod": 10, "paymentPeriodType": "YEARFULL", "premiumFrequencyType": "YEAR", "premiumFrequency": "1", "insuredList": [{"customerType": "INDIVIDUAL", "individual": {"emailList": [{"email": "<EMAIL>", "isDefault": false}], "phoneList": [{"phoneType": "PHONE", "phoneNo": "***********", "countryCode": "+86", "isDefault": false}], "addressList": [{"address11": "state", "addressType": "ADDRESS", "address12": "city", "address13": "district", "address14": "town", "address15": "detail", "zipCode": "A00001"}], "accountList": [{"bankCode": "A01", "bankBranchCode": "A001", "bankBranchName": "branch name", "accountType": "DEBIT_CARD", "accountSubType": "VISA_CARD", "cardHolderName": "test", "cardNumber": "****************", "expiryDate": "2050-01-01", "mobileNo": "***********", "safeNo": "001", "bankName": "bank name"}], "education": "BACHELOR", "birthday": "2004-11-06", "gender": "FEMALE", "fullName": "<PERSON>", "certiNo": "131126198807295547", "certiType": "IDCARD", "nationality": "AFGHANISTAN", "smoke": false}}, {"customerType": "INDIVIDUAL", "individual": {"emailList": [{"email": "<EMAIL>", "isDefault": false}], "phoneList": [{"phoneType": "PHONE", "phoneNo": "***********", "countryCode": "+86", "isDefault": false}], "addressList": [{"address11": "state", "addressType": "ADDRESS", "address12": "city", "address13": "district", "address14": "town", "address15": "detail", "zipCode": "A00001"}], "accountList": [{"bankCode": "A01", "bankBranchCode": "A001", "bankBranchName": "branch name", "accountType": "DEBIT_CARD", "accountSubType": "VISA_CARD", "cardHolderName": "test", "cardNumber": "****************", "expiryDate": "2050-01-01", "mobileNo": "***********", "safeNo": "001", "bankName": "bank name"}], "birthday": "2004-11-06", "gender": "FEMALE", "fullName": "<PERSON>", "certiNo": "130827198001012361", "certiType": "IDCARD", "nationality": "AFGHANISTAN", "smoke": false, "education": "BACHELOR"}}], "productList": [{"productCode": "ZAcare_Termlife", "sumInsured": 100000, "productLiabilityList": [{"sumInsured": 0, "liabilityCode": "1016"}, {"sumInsured": 0, "liabilityCode": "1105"}, {"sumInsured": 0, "liabilityCode": "3101"}]}], "effectiveDate": "2024-11-06T08:37:33.144+08:00"}, "response": {"200": {"value": {"taxList": [{"taxType": "IPT", "tax": "77.***********"}], "standardGrossPremium": "774.***********", "totalPremium": "851.64", "standardNetPremium": "774.***********"}}}}