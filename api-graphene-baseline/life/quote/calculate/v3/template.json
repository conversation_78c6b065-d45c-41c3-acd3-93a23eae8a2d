{"request": {"policyDeliveryMethod": "E_POLICY", "renewalMethod": "NONE_RENEWAL", "salesChannelList": [{"channelRole": "NON_OFFICE", "salesAgreementCode": "salesChannelName"}], "systemSource": "systemSource", "enableFullSync": false, "*paymentPeriod": 1, "*coveragePeriodType": "MONTH", "thirdPartyTransactionNo": "thirdPartyTransactionNo", "expiryDate": "2024-10-27T22:55:47.333+00:00", "extensions": {"2": "2"}, "*planCode": "OOTB_ZA_6M_ScreenCrack", "*insuredList": [{"insuredOrder": 1, "mainInsurantRelation": "MAIN_INSURANT", "occupationClass": "Software Engineer", "extensions": {"2": "2"}, "insuredExpiryDate": "2024-10-27T22:55:47.333+08:00", "individual": {"residenceCountry": "RS", "isPromotional": true, "education": "BACHELOR", "formattedFirstName2": "KEN2", "emailList": [{"isDefault": true, "email": "<EMAIL>"}], "deathOrNot": false, "ckaIndicator": true, "phoneList": [{"phoneType": "PHONE", "isDefault": true, "countryCode": "+31", "phoneNo": "10000001"}], "industryCode": "IT", "smokeGetRidOfDate": "2010-01-01", "lastName2": "peter2", "height": "180", "formattedFullName2": "KEN PETER2", "occupationStatus": "EMPLOYED", "formattedFirstName": "KEN", "weight": "75", "blackStatus": false, "certiType": "IDCARD", "fullName2": "ken peter2", "secondNationality": "CANADA", "*certiNo": "43543546", "ckaEffectiveDate": "2020-01-01", "formattedFullName": "KEN PETER", "firstName": "ken", "channelUserNo": "123456", "nationality": "UNITED_STATES", "position": "Manager", "issuanceDateOfCertificate": "2020-01-01", "extraInfo": "Some extra information", "formattedLastName2": "PETER2", "birthday": "1989-06-06", "income": "50000", "lastName": "peter", "formattedMiddleName": "M", "preferredLanguage": "English", "firstName2": "ken2", "dateOfDeath": "2090-01-01", "title": "MS", "middleName2": "M2", "birthPlace": "Shanghai", "countryCode": "US", "addressList": [{"zipCode": "1000001", "address": "province city street house", "*addressType": "HA", "organizationAddressType": "REGISTER", "extensions": {}, "address21": "address21_03b23535d7a5", "address11": "province", "address22": "address22_c1f412d39031", "address12": "city", "address23": "address23_bdd683824881", "address13": "street", "address24": "address24_772cfe42f5a9", "address14": "house", "address25": "address25_258aaa651ab9", "address15": "address5", "gisCode": "gisCode_eb9f6dd4f869"}], "residentialStatus": "CITIZEN", "socialSecurity": true, "taxNo": "*********", "channelCode": "ChannelA", "race": "Asian", "workingPlace": "Company Ltd.", "sex": "MALE", "smoke": false, "*fullName": "ken peter", "marriageStatus": "UNMARRIED", "formattedMiddleName2": "M2", "ckaExpiryDate": "2030-01-01", "isCustomer": true, "issuancePlaceOfCertificate": "City Hall", "payerType": "FIRST", "occupationCode": "Software Engineer", "middleName": "M", "formattedLastName": "PETER", "customerGrade": "A", "age": 30}, "relationshipWithMainInsured": "SELF", "insuredTerminationDate": "2024-10-27T22:55:47.333+08:00", "organization": {"registeredPhoneCountryCode": "+1", "country": "UNITED_STATES", "receiveInvoiceMethod": "EMAIL", "code": "COMP123", "registeredDate": "2023-01-01", "companyName": "Company Ltd.", "legalRepresentativeName": "<PERSON>", "organizationAbbreviationName": "CompLtd", "industry": "Technology", "roleType": "HOLDER", "taxpayerIdentificationNumber": "TIN123456", "companyName2": "Company Ltd. 2", "userInputOrganizationType": "IT", "registeredAddress": "1234 Elm Street", "registeredCapital": 1000000, "taxpayerName": "Company Taxpayer", "invoiceType": "INDIVIDUAL_INVOICE", "typeOfBusiness": "Software Development", "email": "<EMAIL>", "isPreStored": false, "cifNumber": "CIF123456", "idType": "BUSINESS_LICENSE_NUMBER", "organizationSize": "Large", "registeredPhone": "******-123456", "numberOfEmployees": 29, "registeredCapitalCurrency": "USD", "organizationType": "ENTERPRISE", "branchCode": "BR123", "certificateExpiryDate": "2025-12-31", "industrialClassification": "Software", "legalRepresentativeIdNo": "LRID123456", "businessLicenseNumber": "BLN123456", "legalRepresentativeIdType": "PASSPORT", "position": "CEO"}, "insuredEffectiveDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationReason": "DUE_TO_POS_INSURED"}], "policyType": "NORMAL", "*coveragePeriod": 6, "zoneId": "Europe/Zagreb", "currency": {"premiumCurrency": "SGD", "saCurrency": "SGD"}, "productList": [{"sumInsured": 112.32, "*productCode": "EDI_v1", "productLiabilityList": [{"sumInsured": 112.32, "liabilityCode": "1002", "loadingList": "*", "discountList": "*"}]}], "campaignList": [{"campaignCode": "campaignCode", "periodNo": "1", "promotionCode": "promotionCode", "campaignPayerList": "*"}], "*premiumFrequencyType": "SINGLE", "applicationDate": "2024-10-27T22:55:47.333+00:00", "*paymentPeriodType": "SINGLE", "*goodsCode": "OOTB_ZA_ScreenCrack", "effectiveDate": "2024-10-27T22:55:47.333+00:00"}}