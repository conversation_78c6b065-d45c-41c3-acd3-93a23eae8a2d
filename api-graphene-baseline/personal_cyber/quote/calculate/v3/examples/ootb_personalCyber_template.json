{"request": {"insuredList": [{"insuredOrder": 1, "virtualKidNumber": 0, "virtualAdultNumber": 0, "virtualTotalNumber": 0, "mainInsurantRelation": "MAIN_INSURANT", "relationshipWithMainInsured": "SELF", "occupationClass": "Software Engineer", "insuredEffectiveDate": "2024-10-27T22:55:47.333+08:00", "insuredExpiryDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationDate": "2024-10-27T22:55:47.333+08:00", "insuredTerminationReason": "DUE_TO_POS_INSURED", "status": "VALID", "individual": {"certiType": "IDCARD", "holderRelation": "SELF", "certiNo": "43543546", "firstName": "ken", "lastName": "peter", "fullName": "ken peter", "birthPlace": "Shanghai", "gender": "MALE", "birthday": "1990-02-09", "residenceCountry": "TR", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "ADDRESS", "zipCode": "A00001", "address11": "state", "address12": "city", "address13": "district", "address14": "town"}], "middleName": "middle", "formattedLastName": "peter", "formattedMiddleName": "middle", "formattedFirstName": "ken", "formattedFullName": "ken middle peter", "fullName2": "ken peter", "lastName2": "peter2", "middleName2": "middle2", "firstName2": "ken2", "formattedLastName2": "peter2", "formattedMiddleName2": "middle2", "formattedFirstName2": "ken2", "formattedFullName2": "ken2 middle2 peter2", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "SE", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2020-01-01", "height": "180", "weight": "75", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "1001", "channelCode": "CC", "channelUserCodePartyId": "CUP1001", "isCustomer": true, "countryCode": "US", "missingStatus": 0, "ckaIndicator": false, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2025-01-01", "position": "Developer", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "TN123456", "blackStatus": false, "workingPlace": "Company", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "Office", "payerType": "FIRST", "extraInfo": "Some extra info"}}], "coveragePeriod": 1, "orderNo": "order66dd178020003623832", "tradeNo": "tradeNo", "premiumFrequencyType": "YEAR", "thirdPartyTransactionNo": "quote13299236dd094827428", "paidPremium": 6.739, "planCode": "PersonalCyber_PremierPlan_Template", "coveragePeriodType": "YEARFULL", "attachmentList": [{"refType": "DEVICE", "attachmentTypeCode": "attachmentTypeCode", "attachmentUniqueCode": "8r932-r32r3-sr33r-3r3r", "attachmentName": "Idcard.jpg"}], "insuredRelationList": [{"serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "relationType": "PRODUCT_OBJECT", "productSerialNo": "d6ca9655-403d-4a9c-9140-3485a63c93a8", "liabilitySerialNoList": ["3365de1f-72cf-497c-b30a-bb7f85e0431b", "3365de1f-72cf-497c-b30a-bb7f85e0431c", "3365de1f-72cf-497c-b30a-bb7f85e0431d", "3365de1f-72cf-497c-b30a-bb7f85e0431e", "3365de1f-72cf-497c-b30a-bb7f85e0431f", "3365de1f-72cf-497c-b30a-bb7f85e0431g", "3365de1f-72cf-497c-b30a-bb7f85e0431h", "3365de1f-72cf-497c-b30a-bb7f85e0431i", "3365de1f-72cf-497c-b30a-bb7f85e0431j", "3365de1f-72cf-497c-b30a-bb7f85e0431k"]}], "insuredObjectList": [{"serialNo": "device_1738740663712_Rt6KmfxEoDRilzt8zJ0c5L2XZgjVUaw6", "insuredType": "INSURED_DEVICE", "insuredNo": "12345", "shareFlag": true, "remark": "This is a remark", "extensions": {"E_IsDiplomat": "NO"}, "insuredComponentList": [{"componentType": "DEVICE", "deviceId": "1", "deviceSellerId": "1", "deviceNo": "3294039243234", "deviceName": "iPhone 15", "deviceBrand": "Apple", "deviceModel": "Apple15", "deviceCategory": "PHONE", "deviceCategoryName": "Smartphone", "deviceSellerReviewScore": "1", "deviceMarketValue": "5000", "deviceBuyerReviewScore": "5", "warrantyPeriod": "24 months", "warrantyPeriodCustomer": "<PERSON>", "devicePurchaseTime": "2023-08-16T17:43:02.857+08:00", "applicablePeriod": "36 months", "deviceUser": "<PERSON>", "deviceManufacturer": "Apple Inc.", "deviceDesc": "Latest Apple iPhone with advanced features.", "deviceImgUrl": "http://example.com/device.jpg", "premiumCode": "PC123456", "deviceStatus": "Brand"}]}], "zoneId": "Europe/Zagreb", "goodsCode": "PersonalCyber_Template", "paymentPeriod": 1, "paymentPeriodType": "SINGLE", "productList": [{"serialNo": "d6ca9655-403d-4a9c-9140-3485a63c93a8", "productCode": "PersonalCyber_Template", "sumInsured": 112.32, "productLiabilityList": [{"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431b", "liabilityCode": "17124", "sumInsured": 100}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431c", "liabilityCode": "17125", "sumInsured": 100}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431d", "liabilityCode": "17126", "sumInsured": 100}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431e", "liabilityCode": "17127", "sumInsured": 100}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431f", "liabilityCode": "17128"}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431g", "liabilityCode": "17119"}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431h", "liabilityCode": "17120"}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431i", "liabilityCode": "17121"}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431j", "liabilityCode": "17122"}, {"serialNo": "3365de1f-72cf-497c-b30a-bb7f85e0431k", "liabilityCode": "17123"}]}], "applicationDate": "2024-04-22T17:21:58.299+08:00", "effectiveDate": "2024-04-22T17:21:58.299+08:00", "policyHolder": {"individual": {"fullName": "ken peter", "lastName": "peter", "middleName": "M", "firstName": "ken", "formattedLastName": "PETER", "formattedMiddleName": "M", "formattedFirstName": "KEN", "formattedFullName": "KEN PETER", "fullName2": "ken peter2", "lastName2": "peter2", "middleName2": "M2", "firstName2": "ken2", "formattedLastName2": "PETER2", "formattedMiddleName2": "M2", "formattedFirstName2": "KEN2", "formattedFullName2": "KEN PETER2", "birthday": "1989-06-06", "gender": "MALE", "certiNo": "43543546", "certiType": "IDCARD", "customerGrade": "A", "isPromotional": true, "socialSecurity": true, "industryCode": "IT", "occupationCode": "Software Engineer", "nationality": "UNITED_STATES", "secondNationality": "CANADA", "residenceCountry": "RS", "education": "BACHELOR", "income": "50000", "smoke": false, "smokeGetRidOfDate": "2010-01-01", "height": "180", "weight": "75", "birthPlace": "Shanghai", "race": "Asian", "marriageStatus": "UNMARRIED", "occupationStatus": "EMPLOYED", "preferredLanguage": "English", "channelUserNo": "123456", "channelCode": "ChannelA", "isCustomer": true, "countryCode": "US", "missingStatus": 1, "ckaIndicator": true, "ckaEffectiveDate": "2020-01-01", "ckaExpiryDate": "2030-01-01", "position": "Manager", "title": "MS", "age": 30, "residentialStatus": "CITIZEN", "deathOrNot": false, "dateOfDeath": "2090-01-01", "taxNo": "*********", "blackStatus": false, "workingPlace": "Company Ltd.", "issuanceDateOfCertificate": "2020-01-01", "issuancePlaceOfCertificate": "City Hall", "payerType": "FIRST", "extraInfo": "Some extra information", "emailList": [{"email": "<EMAIL>", "isDefault": true}], "phoneList": [{"phoneNo": "10000001", "phoneType": "PHONE", "countryCode": "+31", "isDefault": true}], "addressList": [{"addressType": "HA", "zipCode": "1000001", "address11": "province", "address12": "city", "address13": "street", "address14": "house"}]}}}, "response": {"200": {"value": {"standardGrossPremium": "247.934", "loading": "string", "discount": "string", "standardNetPremium": "247.934", "campaignDiscount": "string", "totalPremium": "300", "taxList": [{"taxType": "IPT", "tax": "52.066"}], "installmentList": [{"installmentNo": 0, "standardNetPremiumIncCampaign": "string", "taxValueList": [{"installmentTaxType": "string", "tax": "string"}]}], "productList": [{"productCode": "string", "standardGrossPremium": "string", "loading": "string", "discount": "string", "standardNetPremium": "string", "taxValueList": [{"productTaxType": "string", "tax": "string"}]}], "commissionFeeInfo": {"totalCommissionFee": "12.45", "commissionFeeList": [{"commissionFee": "12.45", "partnerCode": "String"}]}, "serviceFeeInfo": {"totalServiceFee": "12.45", "serviceFeeList": [{"serviceFee": "12.45", "partnerCode": "String"}]}, "insuredObjectList": [{"deviceMarketValue": 5000}]}}}}