package com.zatech.genesis.openapi.platform.application.scenario.instance.exception;

import com.zatech.genesis.openapi.platform.share.exception.OpenApiException;

/**
 * <AUTHOR>
 * @Date 2022/4/29
 **/
public class ScenarioInstanceJsonSchemaBuilderDuplicateException extends OpenApiException {

    public ScenarioInstanceJsonSchemaBuilderDuplicateException(String identity, Class builderCls) {
        super(ApplicationStartStageErrorCode.already_exists_for_builder, null, identity, builderCls.getName());
    }

}
